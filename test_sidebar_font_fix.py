#!/usr/bin/env python3
"""
اختبار إصلاح حجم خط القائمة الجانبية
Test sidebar font size fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from views.main_window import MainWindow, SidebarButton
from models.user import User
import time

def test_sidebar_font_sizes():
    """اختبار أحجام خطوط القائمة الجانبية"""
    print("🔤 اختبار أحجام خطوط القائمة الجانبية")
    print("=" * 50)
    
    try:
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # الحصول على مستخدم للاختبار
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow(admin_user)
        
        print("🔍 فحص أحجام خطوط أزرار القائمة الجانبية...")
        
        # فحص أزرار القائمة الجانبية
        if hasattr(main_window, 'nav_buttons'):
            nav_buttons = main_window.nav_buttons
            print(f"✅ تم العثور على {len(nav_buttons)} أزرار في القائمة الجانبية")
            
            font_sizes_correct = True
            button_heights_correct = True
            
            for button_name, button in nav_buttons.items():
                # فحص حجم الخط
                font = button.font()
                font_size = font.pointSize()
                font_family = font.family()
                font_weight = font.weight()
                
                print(f"  📝 {button_name}:")
                print(f"     - النص: {button.text()}")
                print(f"     - حجم الخط: {font_size}px")
                print(f"     - عائلة الخط: {font_family}")
                print(f"     - وزن الخط: {font_weight}")
                print(f"     - ارتفاع الزر: {button.height()}px")
                
                # التحقق من أن حجم الخط مثالي (12px)
                if font_size != 12:
                    print(f"     ⚠️ حجم الخط غير مثالي! متوقع: 12px، الحالي: {font_size}px")
                    font_sizes_correct = False
                else:
                    print(f"     ✅ حجم الخط مثالي")
                
                # التحقق من ارتفاع الزر (45px)
                if button.height() != 45:
                    print(f"     ⚠️ ارتفاع الزر غير مثالي! متوقع: 45px، الحالي: {button.height()}px")
                    button_heights_correct = False
                else:
                    print(f"     ✅ ارتفاع الزر مثالي")
                
                print()
            
            # النتائج
            if font_sizes_correct and button_heights_correct:
                print("🎉 جميع أحجام الخطوط والأزرار مثالية!")
                return True
            else:
                if not font_sizes_correct:
                    print("❌ بعض أحجام الخطوط غير مثالية")
                if not button_heights_correct:
                    print("❌ بعض ارتفاعات الأزرار غير مثالية")
                return False
        else:
            print("❌ لم يتم العثور على أزرار القائمة الجانبية")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False
    finally:
        # تنظيف
        if 'main_window' in locals():
            main_window.close()

def test_sidebar_button_class():
    """اختبار كلاس SidebarButton مباشرة"""
    print("\n🔧 اختبار كلاس SidebarButton مباشرة")
    print("=" * 40)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء زر اختبار
        test_button = SidebarButton("🧾 اختبار")
        
        # فحص الخصائص
        font = test_button.font()
        font_size = font.pointSize()
        height = test_button.height()
        
        print(f"📏 خصائص زر الاختبار:")
        print(f"   - النص: {test_button.text()}")
        print(f"   - حجم الخط: {font_size}px")
        print(f"   - عائلة الخط: {font.family()}")
        print(f"   - ارتفاع الزر: {height}px")
        
        # التحقق من القيم
        font_correct = font_size == 12
        height_correct = height == 45
        
        if font_correct and height_correct:
            print("✅ جميع خصائص الزر صحيحة!")
            return True
        else:
            if not font_correct:
                print(f"❌ حجم الخط غير صحيح! متوقع: 12px، الحالي: {font_size}px")
            if not height_correct:
                print(f"❌ ارتفاع الزر غير صحيح! متوقع: 45px، الحالي: {height}px")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الكلاس: {e}")
        return False

def run_sidebar_font_test():
    """تشغيل جميع اختبارات خط القائمة الجانبية"""
    print("🎯 بدء اختبار إصلاح خط القائمة الجانبية")
    print("=" * 60)
    
    results = []
    
    # اختبار 1: كلاس SidebarButton
    results.append(("اختبار كلاس SidebarButton", test_sidebar_button_class()))
    
    # اختبار 2: أحجام خطوط القائمة الجانبية
    results.append(("اختبار أحجام خطوط القائمة الجانبية", test_sidebar_font_sizes()))
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار خط القائمة الجانبية")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 تم إصلاح جميع أحجام خطوط القائمة الجانبية بنجاح!")
        print("✨ الآن أحجام الخطوط مثالية ومقروءة")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_sidebar_font_test()
