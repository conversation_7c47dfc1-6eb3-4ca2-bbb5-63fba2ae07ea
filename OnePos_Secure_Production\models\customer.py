"""
Customer model for OnePos POS System
"""

from datetime import datetime
from .database import db


class Customer:
    """Customer model"""
    
    def __init__(self, customer_id=None, name=None, phone=None, email=None, 
                 address=None, tax_number=None, credit_limit=0, current_balance=0, is_active=True):
        self.id = customer_id
        self.name = name
        self.phone = phone
        self.email = email
        self.address = address
        self.tax_number = tax_number
        self.credit_limit = credit_limit
        self.current_balance = current_balance
        self.is_active = is_active
        self.created_at = None
        self.updated_at = None
    
    @classmethod
    def create(cls, name, **kwargs):
        """Create a new customer"""
        # التحقق من صحة البيانات
        if not name or not name.strip():
            raise ValueError("Customer name is required")

        # التحقق من عدم تكرار الهاتف
        phone = kwargs.get('phone')
        if phone and cls.get_by_phone(phone):
            raise ValueError("Phone number already exists")

        # التحقق من عدم تكرار البريد الإلكتروني
        email = kwargs.get('email')
        if email and cls.get_by_email(email):
            raise ValueError("Email already exists")

        customer_id = db.execute_update("""
            INSERT INTO customers (name, phone, email, address, tax_number, credit_limit)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            name.strip(),
            kwargs.get('phone'),
            kwargs.get('email'),
            kwargs.get('address'),
            kwargs.get('tax_number'),
            kwargs.get('credit_limit', 0)
        ))

        return cls.get_by_id(customer_id)
    
    @classmethod
    def get_by_id(cls, customer_id):
        """Get customer by ID"""
        result = db.execute_query("SELECT * FROM customers WHERE id = ?", (customer_id,))
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def get_by_phone(cls, phone):
        """Get customer by phone number"""
        result = db.execute_query("SELECT * FROM customers WHERE phone = ? AND is_active = 1", (phone,))
        if result:
            return cls._from_db_row(result[0])
        return None

    @classmethod
    def get_by_email(cls, email):
        """Get customer by email"""
        result = db.execute_query("SELECT * FROM customers WHERE email = ? AND is_active = 1", (email,))
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def search(cls, query, active_only=True):
        """Search customers by name, phone, or email"""
        sql = """
            SELECT * FROM customers 
            WHERE (name LIKE ? OR phone LIKE ? OR email LIKE ?)
        """
        params = [f"%{query}%", f"%{query}%", f"%{query}%"]
        
        if active_only:
            sql += " AND is_active = 1"
        
        sql += " ORDER BY name"
        
        results = db.execute_query(sql, params)
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def get_all(cls, active_only=True):
        """Get all customers"""
        query = "SELECT * FROM customers"
        if active_only:
            query += " WHERE is_active = 1"
        query += " ORDER BY name"
        
        results = db.execute_query(query)
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def get_walk_in_customer(cls):
        """Get the default walk-in customer"""
        result = db.execute_query("SELECT * FROM customers WHERE name = 'Walk-in Customer'")
        if result:
            return cls._from_db_row(result[0])
        return None
    
    def update(self, **kwargs):
        """Update customer information"""
        allowed_fields = ['name', 'phone', 'email', 'address', 'tax_number', 
                         'credit_limit', 'current_balance', 'is_active']
        update_fields = []
        params = []
        
        for field, value in kwargs.items():
            if field in allowed_fields:
                update_fields.append(f"{field} = ?")
                params.append(value)
        
        if update_fields:
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(self.id)
            
            query = f"UPDATE customers SET {', '.join(update_fields)} WHERE id = ?"
            db.execute_update(query, params)
            
            # Refresh object
            updated_customer = self.get_by_id(self.id)
            if updated_customer:
                self.__dict__.update(updated_customer.__dict__)
    
    def delete(self):
        """Soft delete customer"""
        self.update(is_active=False)
    
    def add_credit(self, amount, notes=None):
        """Add credit to customer balance"""
        new_balance = self.current_balance + amount
        self.update(current_balance=new_balance)
        
        # Record transaction
        self._record_balance_transaction('credit', amount, notes)
    
    def add_debt(self, amount, notes=None):
        """Add debt to customer balance"""
        new_balance = self.current_balance - amount
        self.update(current_balance=new_balance)
        
        # Record transaction
        self._record_balance_transaction('debt', amount, notes)
    
    def _record_balance_transaction(self, transaction_type, amount, notes):
        """Record balance transaction"""
        # This would be implemented with a customer transactions table
        pass
    
    def get_purchase_history(self, limit=None):
        """Get customer purchase history"""
        query = """
            SELECT s.*, u.full_name as cashier_name
            FROM sales s
            LEFT JOIN users u ON s.user_id = u.id
            WHERE s.customer_id = ?
            ORDER BY s.created_at DESC
        """
        
        params = [self.id]
        if limit:
            query += " LIMIT ?"
            params.append(limit)
        
        results = db.execute_query(query, params)
        return results
    
    def get_total_purchases(self):
        """Get total purchase amount"""
        result = db.execute_query("""
            SELECT COALESCE(SUM(total_amount), 0) as total
            FROM sales
            WHERE customer_id = ? AND status = 'completed'
        """, (self.id,))
        
        return result[0]['total'] if result else 0
    
    def get_purchase_count(self):
        """Get total number of purchases"""
        result = db.execute_query("""
            SELECT COUNT(*) as count
            FROM sales
            WHERE customer_id = ? AND status = 'completed'
        """, (self.id,))
        
        return result[0]['count'] if result else 0
    
    def can_purchase(self, amount):
        """Check if customer can make a purchase (credit limit check)"""
        if self.credit_limit <= 0:
            return True  # No credit limit
        
        potential_balance = self.current_balance - amount
        return potential_balance >= -self.credit_limit
    
    def to_dict(self):
        """Convert customer to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'tax_number': self.tax_number,
            'credit_limit': self.credit_limit,
            'current_balance': self.current_balance,
            'is_active': self.is_active,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def _from_db_row(cls, row):
        """Create Customer object from database row"""
        customer = cls(
            customer_id=row['id'],
            name=row['name'],
            phone=row['phone'],
            email=row['email'],
            address=row['address'],
            tax_number=row['tax_number'],
            credit_limit=row['credit_limit'],
            current_balance=row['current_balance'],
            is_active=bool(row['is_active'])
        )
        customer.created_at = row['created_at']
        customer.updated_at = row['updated_at']
        return customer
