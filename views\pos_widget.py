"""
Point of Sale widget for OnePos POS System
"""

from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QGridLayout,
                            QTableWidget, QTableWidgetItem, QPushButton, QLineEdit,
                            QLabel, QComboBox, QSpinBox, QDoubleSpinBox, QFrame,
                            QMessageBox, QHeaderView, QAbstractItemView, QGroupBox,
                            QSplitter, QTextEdit, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QKeySequence
from PyQt5.QtWidgets import QShortcut

from models.product import Product
from models.customer import Customer
from models.sale import Sale
from utils.translator import tr
from utils.config_manager import config
from utils.print_manager import print_manager
from utils.printer_manager import printer_manager
from utils.barcode_scanner_manager import barcode_scanner_manager


class POSWidget(QWidget):
    """Point of Sale main widget"""
    
    # Signals
    sale_completed = pyqtSignal(object)  # Emits Sale object
    
    def __init__(self, user, parent=None):
        super().__init__(parent)
        self.current_user = user
        self.cart_items = []
        self.current_customer = None
        self.tax_rate = config.get_tax_rate()
        self.last_sale = None  # Store last sale for reprinting
        
        self.setup_ui()
        self.setup_shortcuts()
        self.load_default_customer()
        self.update_totals()
    
    def setup_ui(self):
        """Setup user interface"""
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Product search and cart
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Payment and customer info
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([700, 400])
        
        main_layout.addWidget(splitter)

        # ربط إشارات ماسح الباركود
        barcode_scanner_manager.barcode_scanned.connect(self.on_barcode_scanned)

        self.setLayout(main_layout)
    
    def create_left_panel(self):
        """Create left panel with product search and cart"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # Product search section
        search_group = QGroupBox(tr("pos.product"))
        search_layout = QVBoxLayout()
        
        # Barcode/search input
        search_input_layout = QHBoxLayout()
        
        self.barcode_input = QLineEdit()
        self.barcode_input.setPlaceholderText(tr("pos.barcode") + " / " + tr("common.search"))
        self.barcode_input.setFont(QFont("Segoe UI", 10))
        self.barcode_input.setStyleSheet("color: #000000 !important; font-weight: 500; font-size: 10px;")
        self.barcode_input.returnPressed.connect(self.search_product)
        search_input_layout.addWidget(self.barcode_input)
        
        self.search_button = QPushButton(tr("common.search"))
        self.search_button.clicked.connect(self.search_product)
        search_input_layout.addWidget(self.search_button)
        
        self.scan_button = QPushButton("📷 " + tr("pos.scan_barcode"))
        self.scan_button.clicked.connect(self.scan_barcode)
        search_input_layout.addWidget(self.scan_button)
        
        search_layout.addLayout(search_input_layout)
        
        # Quick quantity and add button
        quick_add_layout = QHBoxLayout()
        
        quick_add_layout.addWidget(QLabel(tr("pos.quantity") + ":"))
        
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(9999)
        self.quantity_spin.setValue(1)
        quick_add_layout.addWidget(self.quantity_spin)
        
        self.add_to_cart_button = QPushButton(tr("pos.add_to_cart"))
        self.add_to_cart_button.setObjectName("success_add_to_cart")
        # Style handled by main theme
        self.add_to_cart_button.clicked.connect(self.add_to_cart)
        quick_add_layout.addWidget(self.add_to_cart_button)
        
        search_layout.addLayout(quick_add_layout)
        search_group.setLayout(search_layout)
        layout.addWidget(search_group)
        
        # Shopping cart
        cart_group = QGroupBox(tr("pos.title"))
        cart_layout = QVBoxLayout()
        
        # Cart table
        self.cart_table = QTableWidget()
        self.cart_table.setColumnCount(6)
        self.cart_table.setHorizontalHeaderLabels([
            tr("pos.product"),
            tr("pos.quantity"),
            tr("pos.price"),
            tr("pos.discount"),
            tr("pos.total"),
            ""  # Actions column
        ])
        
        # Configure table
        header = self.cart_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Product name
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # Quantity
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # Price
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # Discount
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # Total
        header.setSectionResizeMode(5, QHeaderView.Fixed)    # Actions
        
        self.cart_table.setColumnWidth(1, 80)   # Quantity
        self.cart_table.setColumnWidth(2, 100)  # Price
        self.cart_table.setColumnWidth(3, 80)   # Discount
        self.cart_table.setColumnWidth(4, 100)  # Total
        self.cart_table.setColumnWidth(5, 60)   # Actions
        
        self.cart_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.cart_table.setAlternatingRowColors(True)
        
        cart_layout.addWidget(self.cart_table)
        
        # Cart actions
        cart_actions_layout = QHBoxLayout()
        
        self.clear_cart_button = QPushButton(tr("pos.clear_cart"))
        self.clear_cart_button.setObjectName("danger_clear_cart")
        # Style handled by main theme
        self.clear_cart_button.clicked.connect(self.clear_cart)
        cart_actions_layout.addWidget(self.clear_cart_button)


        
        cart_actions_layout.addStretch()
        cart_layout.addLayout(cart_actions_layout)
        
        cart_group.setLayout(cart_layout)
        layout.addWidget(cart_group)
        
        panel.setLayout(layout)
        return panel
    
    def create_right_panel(self):
        """Create right panel with totals and payment"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # Customer selection
        customer_group = QGroupBox(tr("pos.customer"))
        customer_layout = QHBoxLayout()
        
        self.customer_combo = QComboBox()
        self.customer_combo.setEditable(True)
        self.load_customers()
        customer_layout.addWidget(self.customer_combo)
        
        self.select_customer_button = QPushButton(tr("pos.select_customer"))
        self.select_customer_button.clicked.connect(self.select_customer)
        customer_layout.addWidget(self.select_customer_button)
        
        customer_group.setLayout(customer_layout)
        layout.addWidget(customer_group)
        
        # Totals section
        totals_group = QGroupBox(tr("pos.total"))
        totals_layout = QGridLayout()
        
        # Subtotal
        subtotal_label_text = QLabel(tr("pos.subtotal") + ":")
        subtotal_label_text.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
        totals_layout.addWidget(subtotal_label_text, 0, 0)
        self.subtotal_label = QLabel(f"0.00 {tr('common.currency')}")
        self.subtotal_label.setAlignment(Qt.AlignRight)
        self.subtotal_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.subtotal_label.setStyleSheet("color: #000000 !important; font-weight: bold; font-size: 12px;")
        totals_layout.addWidget(self.subtotal_label, 0, 1)
        
        # Discount
        discount_label_text = QLabel(tr("pos.discount") + ":")
        discount_label_text.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
        totals_layout.addWidget(discount_label_text, 1, 0)
        discount_layout = QHBoxLayout()
        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setMaximum(999999.99)
        self.discount_spin.valueChanged.connect(self.update_totals)
        discount_layout.addWidget(self.discount_spin)
        
        self.discount_percent_check = QCheckBox("%")
        self.discount_percent_check.toggled.connect(self.update_totals)
        discount_layout.addWidget(self.discount_percent_check)
        
        discount_widget = QWidget()
        discount_widget.setLayout(discount_layout)
        totals_layout.addWidget(discount_widget, 1, 1)
        
        # Tax
        tax_label_text = QLabel(tr("pos.tax") + f" ({self.tax_rate}%):")
        tax_label_text.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
        totals_layout.addWidget(tax_label_text, 2, 0)
        self.tax_label = QLabel(f"0.00 {tr('common.currency')}")
        self.tax_label.setAlignment(Qt.AlignRight)
        self.tax_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.tax_label.setStyleSheet("color: #000000 !important; font-weight: bold; font-size: 12px;")
        totals_layout.addWidget(self.tax_label, 2, 1)
        
        # Grand total
        total_label_text = QLabel(tr("pos.grand_total") + ":")
        total_label_text.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
        totals_layout.addWidget(total_label_text, 3, 0)
        self.total_label = QLabel(f"0.00 {tr('common.currency')}")
        self.total_label.setObjectName("total_amount")
        self.total_label.setAlignment(Qt.AlignRight)
        self.total_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        self.total_label.setStyleSheet("""
            color: #ffffff !important;
            background-color: #003366;
            padding: 15px;
            border-radius: 8px;
            border: 3px solid #000000;
            font-weight: bold;
            font-size: 18px;
        """)
        totals_layout.addWidget(self.total_label, 3, 1)
        
        totals_group.setLayout(totals_layout)
        layout.addWidget(totals_group)
        
        # Payment section
        payment_group = QGroupBox(tr("pos.payment"))
        payment_layout = QVBoxLayout()
        
        # Payment method
        payment_method_layout = QHBoxLayout()
        payment_label_text = QLabel(tr("pos.payment") + ":")
        payment_label_text.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
        payment_method_layout.addWidget(payment_label_text)
        
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems([tr("pos.cash"), tr("pos.card"), "Split"])
        payment_method_layout.addWidget(self.payment_method_combo)
        
        payment_layout.addLayout(payment_method_layout)
        
        # Amount paid
        amount_layout = QHBoxLayout()
        amount_label_text = QLabel(tr("sales.amount") + ":")
        amount_label_text.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
        amount_layout.addWidget(amount_label_text)
        
        self.amount_paid_spin = QDoubleSpinBox()
        self.amount_paid_spin.setMaximum(999999.99)
        self.amount_paid_spin.valueChanged.connect(self.calculate_change)
        amount_layout.addWidget(self.amount_paid_spin)
        
        payment_layout.addLayout(amount_layout)
        
        # Change
        change_layout = QHBoxLayout()
        change_label_text = QLabel(tr("pos.change") + ":")
        change_label_text.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
        change_layout.addWidget(change_label_text)
        
        self.change_label = QLabel(f"0.00 {tr('common.currency')}")
        self.change_label.setAlignment(Qt.AlignRight)
        self.change_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.change_label.setStyleSheet("color: #000000 !important; font-weight: bold; font-size: 12px;")
        change_layout.addWidget(self.change_label)
        
        payment_layout.addLayout(change_layout)
        
        payment_group.setLayout(payment_layout)
        layout.addWidget(payment_group)
        
        # Action buttons
        actions_layout = QVBoxLayout()

        # Main Pay & Print button
        self.pay_and_print_button = QPushButton("💳 " + tr("pos.pay_and_print"))
        self.pay_and_print_button.setObjectName("success_complete_sale")
        self.pay_and_print_button.setFont(QFont("Segoe UI", 14, QFont.Bold))
        self.pay_and_print_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: #ffffff;
                padding: 15px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                border: none;
                min-height: 50px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        self.pay_and_print_button.clicked.connect(self.complete_sale_and_print)
        actions_layout.addWidget(self.pay_and_print_button)

        # Additional action buttons row
        additional_actions_layout = QHBoxLayout()

        # Reprint last receipt button
        self.reprint_button = QPushButton("🔄 " + tr("pos.reprint"))
        self.reprint_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: #ffffff;
                padding: 8px 12px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11px;
                border: none;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.reprint_button.clicked.connect(self.reprint_last_receipt)
        self.reprint_button.setEnabled(False)  # Disabled until sale is made
        additional_actions_layout.addWidget(self.reprint_button)

        # Hold order button
        self.hold_order_button = QPushButton("⏸️ " + tr("pos.hold_order"))
        self.hold_order_button.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                padding: 8px 12px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11px;
                border: none;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.hold_order_button.clicked.connect(self.hold_order)
        additional_actions_layout.addWidget(self.hold_order_button)

        actions_layout.addLayout(additional_actions_layout)

        layout.addLayout(actions_layout)
        
        # Add stretch to push everything to top
        layout.addStretch()
        
        panel.setLayout(layout)
        return panel
    
    def setup_shortcuts(self):
        """Setup keyboard shortcuts"""
        # F1 - Focus search
        search_shortcut = QShortcut(QKeySequence("F1"), self)
        search_shortcut.activated.connect(lambda: self.barcode_input.setFocus())
        
        # F2 - Complete sale
        complete_shortcut = QShortcut(QKeySequence("F2"), self)
        complete_shortcut.activated.connect(self.complete_sale)
        
        # F3 - Clear cart
        clear_shortcut = QShortcut(QKeySequence("F3"), self)
        clear_shortcut.activated.connect(self.clear_cart)
        
        # F4 - Hold order
        hold_shortcut = QShortcut(QKeySequence("F4"), self)
        hold_shortcut.activated.connect(self.hold_order)
    
    def load_default_customer(self):
        """Load default walk-in customer"""
        self.current_customer = Customer.get_walk_in_customer()
    
    def load_customers(self):
        """Load customers into combo box"""
        self.customer_combo.clear()
        customers = Customer.get_all()
        
        for customer in customers:
            self.customer_combo.addItem(customer.name, customer.id)
    
    def search_product(self):
        """Search for product by barcode or name"""
        query = self.barcode_input.text().strip()
        if not query:
            return
        
        # Try to find by barcode first
        product = Product.get_by_barcode(query)
        
        if not product:
            # Try to find by SKU
            product = Product.get_by_sku(query)
        
        if not product:
            # Search by name
            products = Product.search(query)
            if products:
                product = products[0]  # Take first match
        
        if product:
            self.add_product_to_cart(product)
            self.barcode_input.clear()
        else:
            QMessageBox.warning(self, tr("common.warning"), 
                              f"Product not found: {query}")
    
    def scan_barcode(self):
        """Open barcode scanner"""
        try:
            # بدء المسح إذا لم يكن نشطاً
            if not barcode_scanner_manager.is_scanning:
                if barcode_scanner_manager.start_scanning():
                    QMessageBox.information(self, tr("pos.scan_barcode"),
                                          tr("barcode.scanning_active"))
                else:
                    QMessageBox.warning(self, tr("common.warning"),
                                      tr("barcode.scan_start_failed"))
            else:
                QMessageBox.information(self, tr("pos.scan_barcode"),
                                      tr("barcode.scanning_active"))
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"),
                               f"خطأ في بدء المسح: {str(e)}")

    def on_barcode_scanned(self, barcode):
        """معالجة الباركود المممسوح"""
        try:
            print(f"📱 تم مسح باركود في POS: {barcode}")

            # البحث عن المنتج بالباركود
            product = Product.get_by_barcode(barcode)

            if product:
                # إضافة المنتج للسلة
                self.add_product_to_cart(product)

                # إظهار رسالة نجاح
                QMessageBox.information(self, tr("common.success"),
                                      f"تم إضافة المنتج: {product.name}")

                # تركيز على حقل البحث
                self.barcode_input.setFocus()

            else:
                # المنتج غير موجود
                QMessageBox.warning(self, tr("common.warning"),
                                  f"المنتج غير موجود: {barcode}")

                # وضع الباركود في حقل البحث للمراجعة
                self.barcode_input.setText(barcode)
                self.barcode_input.setFocus()

        except Exception as e:
            QMessageBox.critical(self, tr("common.error"),
                               f"خطأ في معالجة الباركود: {str(e)}")
    
    def add_to_cart(self):
        """Add current product to cart"""
        self.search_product()
    
    def add_product_to_cart(self, product):
        """Add product to shopping cart"""
        quantity = self.quantity_spin.value()
        
        # Check if product already in cart
        for i, item in enumerate(self.cart_items):
            if item['product_id'] == product.id:
                # Update quantity
                self.cart_items[i]['quantity'] += quantity
                self.update_cart_display()
                self.update_totals()
                return
        
        # Add new item to cart
        cart_item = {
            'product_id': product.id,
            'product_name': product.name,
            'quantity': quantity,
            'unit_price': product.selling_price,
            'discount_amount': 0,
            'total': quantity * product.selling_price
        }
        
        self.cart_items.append(cart_item)
        self.update_cart_display()
        self.update_totals()
    
    def update_cart_display(self):
        """Update cart table display"""
        self.cart_table.setRowCount(len(self.cart_items))
        
        for row, item in enumerate(self.cart_items):
            # Product name
            self.cart_table.setItem(row, 0, QTableWidgetItem(item['product_name']))
            
            # Quantity (editable)
            qty_item = QTableWidgetItem(str(item['quantity']))
            self.cart_table.setItem(row, 1, qty_item)
            
            # Price (editable)
            price_item = QTableWidgetItem(f"{item['unit_price']:.2f} DZD")
            self.cart_table.setItem(row, 2, price_item)

            # Discount
            discount_item = QTableWidgetItem(f"{item['discount_amount']:.2f} DZD")
            self.cart_table.setItem(row, 3, discount_item)

            # Total
            total = (item['quantity'] * item['unit_price']) - item['discount_amount']
            self.cart_table.setItem(row, 4, QTableWidgetItem(f"{total:.2f} DZD"))
            
            # Remove button
            remove_button = QPushButton("🗑️")
            remove_button.setStyleSheet("background-color: #e74c3c; color: white;")
            remove_button.clicked.connect(lambda checked, r=row: self.remove_cart_item(r))
            self.cart_table.setCellWidget(row, 5, remove_button)
    
    def remove_cart_item(self, row):
        """Remove item from cart"""
        if 0 <= row < len(self.cart_items):
            self.cart_items.pop(row)
            self.update_cart_display()
            self.update_totals()
    
    def clear_cart(self):
        """Clear all items from cart"""
        if self.cart_items:
            reply = QMessageBox.question(self, tr("pos.clear_cart"),
                                       "Are you sure you want to clear the cart?",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.cart_items.clear()
                self.update_cart_display()
                self.update_totals()
    
    def hold_order(self):
        """Hold current order"""
        if not self.cart_items:
            QMessageBox.warning(self, tr("common.warning"), tr("pos.cart_empty"))
            return
        
        # طلب اسم للطلب المعلق
        from PyQt5.QtWidgets import QInputDialog
        order_name, ok = QInputDialog.getText(
            self, tr("pos.hold_order"), 
            tr("pos.enter_order_name"),
            text=f"Order {datetime.now().strftime('%H:%M')}"
        )
        
        if not ok or not order_name.strip():
            return
        
        try:
            # حفظ الطلب المعلق
            held_order = {
                'name': order_name.strip(),
                'items': self.cart_items.copy(),
                'customer': self.current_customer.id if self.current_customer else None,
                'tax_rate': self.tax_rate,
                'timestamp': datetime.now().isoformat()
            }
            
            # إضافة للقائمة المعلقة
            if not hasattr(self, 'held_orders'):
                self.held_orders = []
            
            self.held_orders.append(held_order)
            
            # مسح السلة الحالية
            self.clear_cart()
            
            QMessageBox.information(self, tr("common.success"), 
                                  tr("pos.order_held_successfully").format(order_name))
            
            # تحديث قائمة الطلبات المعلقة
            self.update_held_orders_menu()
            
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"Error holding order: {e}")
    
    def select_customer(self):
        """Select customer"""
        customer_id = self.customer_combo.currentData()
        if customer_id:
            self.current_customer = Customer.get_by_id(customer_id)
    
    def update_totals(self):
        """Update total calculations"""
        if not self.cart_items:
            self.subtotal_label.setText(f"0.00 {tr('common.currency')}")
            self.tax_label.setText(f"0.00 {tr('common.currency')}")
            self.total_label.setText(f"0.00 {tr('common.currency')}")
            self.amount_paid_spin.setValue(0)
            self.calculate_change()
            return
        
        # Calculate subtotal
        subtotal = sum(item['quantity'] * item['unit_price'] - item['discount_amount'] 
                      for item in self.cart_items)
        
        # Apply discount
        discount_amount = self.discount_spin.value()
        if self.discount_percent_check.isChecked():
            discount_amount = subtotal * (discount_amount / 100)
        
        subtotal_after_discount = subtotal - discount_amount
        
        # Calculate tax
        tax_amount = subtotal_after_discount * (self.tax_rate / 100)
        
        # Calculate total
        total = subtotal_after_discount + tax_amount
        
        # Update labels
        self.subtotal_label.setText(f"{subtotal:.2f} DZD")
        self.tax_label.setText(f"{tax_amount:.2f} DZD")
        self.total_label.setText(f"{total:.2f} DZD")
        
        # Set amount paid to total by default
        if self.amount_paid_spin.value() == 0:
            self.amount_paid_spin.setValue(total)
        
        self.calculate_change()
    
    def calculate_change(self):
        """Calculate change amount"""
        total_text = self.total_label.text().replace(" DZD", "")
        total = float(total_text)
        paid = self.amount_paid_spin.value()
        change = paid - total

        self.change_label.setText(f"{change:.2f} DZD")
        
        if change < 0:
            self.change_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        else:
            self.change_label.setStyleSheet("color: #27ae60; font-weight: bold;")
    
    def complete_sale(self):
        """Complete the sale"""
        if not self.cart_items:
            QMessageBox.warning(self, tr("common.warning"), "Cart is empty!")
            return
        
        total_text = self.total_label.text().replace(" DZD", "")
        total = float(total_text)
        paid = self.amount_paid_spin.value()
        
        if paid < total:
            QMessageBox.warning(self, tr("common.warning"), 
                              "Insufficient payment amount!")
            return
        
        try:
            # Prepare sale data
            sale_data = {
                'customer_id': self.current_customer.id if self.current_customer else None,
                'tax_rate': self.tax_rate,
                'discount_amount': self.get_total_discount(),
                'paid_amount': paid,
                'payment_method': self.payment_method_combo.currentText().lower(),
                'status': 'completed'
            }
            
            # Create sale
            sale = Sale.create(self.current_user.id, self.cart_items, **sale_data)
            
            if sale:
                # Show success message with print options
                reply = QMessageBox.question(self, tr("common.success"),
                                           f"Sale completed! Invoice: {sale.invoice_number}\n\n"
                                           f"Do you want to print the receipt?",
                                           QMessageBox.Yes | QMessageBox.No,
                                           QMessageBox.Yes)

                # Print receipt if requested
                if reply == QMessageBox.Yes:
                    self.print_receipt(sale)

                # Store last sale for reprinting
                self.last_sale = sale

                # Enable reprint button
                self.reprint_button.setEnabled(True)

                # Emit signal
                self.sale_completed.emit(sale)

                # Clear cart
                self.cart_items.clear()
                self.update_cart_display()
                self.update_totals()

                # Reset form
                self.discount_spin.setValue(0)
                self.amount_paid_spin.setValue(0)

                # Focus back to search
                self.barcode_input.setFocus()

                return True
            else:
                QMessageBox.critical(self, tr("common.error"), "Failed to create sale")
                return False

        except Exception as e:
            QMessageBox.critical(self, tr("common.error"),
                               f"Error completing sale: {str(e)}")
            return False
    
    def get_total_discount(self):
        """Get total discount amount"""
        discount = self.discount_spin.value()
        if self.discount_percent_check.isChecked():
            subtotal = sum(item['quantity'] * item['unit_price'] - item['discount_amount']
                          for item in self.cart_items)
            return subtotal * (discount / 100)
        return discount

    def print_receipt(self, sale):
        """طباعة الإيصال بالنظام الجديد"""
        try:
            from utils.config_manager import config

            # الحصول على الطابعة المحفوظة
            default_printer = config.get('printer.default_printer', None)
            print_type = config.get('printer.print_type', 'ticket')

            if not default_printer:
                # إذا لم تكن هناك طابعة محفوظة، اعرض رسالة
                reply = QMessageBox.question(
                    self,
                    tr("printers.no_printer"),
                    "لم يتم تكوين طابعة افتراضية.\nهل تريد الذهاب إلى إعدادات الطابعات؟",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    # يمكن إضافة فتح إعدادات الطابعات هنا
                    QMessageBox.information(self, tr("common.info"), "يرجى الذهاب إلى الإعدادات > الطابعات لتكوين طابعة افتراضية")
                return

            # طباعة حسب النوع المحدد
            if print_type == "ticket":
                # طباعة تيكت صغير ومنظم
                success, message = self.print_ticket(sale, default_printer)
            else:
                # طباعة فاتورة كاملة بجداول
                success, message = self.print_invoice_new(sale, default_printer)

            if success:
                # إظهار رسالة نجاح
                QMessageBox.information(self, tr("common.success"), "تم طباعة الإيصال بنجاح!")
            else:
                QMessageBox.warning(self, tr("common.warning"), f"فشل في الطباعة:\n{message}")

        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"خطأ في الطباعة: {str(e)}")

    def print_ticket(self, sale, printer_name):
        """طباعة تيكت صغير ومنظم"""
        try:
            from datetime import datetime

            # إنشاء محتوى التيكت
            content = []
            content.append("=" * 32)
            content.append("      OnePos POS System")
            content.append("=" * 32)
            content.append("")

            # معلومات البيع
            content.append(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M')}")
            content.append(f"🧾 #{getattr(sale, 'invoice_number', 'N/A')}")
            if self.current_customer:
                content.append(f"👤 {self.current_customer.name}")
            content.append("")

            # المنتجات
            content.append("المنتجات:")
            content.append("-" * 32)

            for item in self.cart_items:
                # اسم المنتج (مقطوع إذا كان طويل)
                name = item['name'][:20] + "..." if len(item['name']) > 20 else item['name']
                content.append(f"{name}")
                total = item['quantity'] * item['unit_price'] - item.get('discount_amount', 0)
                content.append(f"  {item['quantity']} x {item['unit_price']:.2f} = {total:.2f}")
                content.append("")

            content.append("-" * 32)

            # الإجماليات
            subtotal = sum(item['quantity'] * item['unit_price'] - item.get('discount_amount', 0) for item in self.cart_items)
            discount = self.get_total_discount()
            tax = subtotal * (self.tax_rate / 100)
            total = subtotal - discount + tax

            content.append(f"المجموع الفرعي: {subtotal:.2f}")
            if discount > 0:
                content.append(f"الخصم: -{discount:.2f}")
            if tax > 0:
                content.append(f"الضريبة: {tax:.2f}")
            content.append("=" * 32)
            content.append(f"الإجمالي: {total:.2f}")
            content.append("=" * 32)

            # طريقة الدفع
            payment_method = self.payment_method_combo.currentText()
            content.append(f"💳 {payment_method}")
            content.append("")

            # رسالة شكر
            content.append("شكراً لزيارتكم!")
            content.append("© 2025 ASSANAJE_APP")
            content.append("=" * 32)

            ticket_content = "\n".join(content)

            # طباعة التيكت (محاكاة)
            print("=== طباعة تيكت ===")
            print(ticket_content)
            print("=== انتهاء التيكت ===")

            return True, "تم طباعة التيكت بنجاح"

        except Exception as e:
            return False, str(e)

    def print_invoice_new(self, sale, printer_name):
        """طباعة فاتورة كاملة بجداول"""
        try:
            from datetime import datetime

            # إنشاء محتوى الفاتورة HTML
            html_content = f"""
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة #{getattr(sale, 'invoice_number', 'N/A')}</title>
                <style>
                    body {{
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        direction: rtl;
                    }}
                    .header {{
                        text-align: center;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                        margin-bottom: 20px;
                    }}
                    .company-name {{
                        font-size: 24px;
                        font-weight: bold;
                        color: #2c3e50;
                    }}
                    .invoice-info {{
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 20px;
                    }}
                    .customer-info {{
                        background-color: #f8f9fa;
                        padding: 15px;
                        border-radius: 5px;
                        margin-bottom: 20px;
                    }}
                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                    }}
                    th, td {{
                        border: 1px solid #ddd;
                        padding: 12px;
                        text-align: center;
                    }}
                    th {{
                        background-color: #3498db;
                        color: white;
                        font-weight: bold;
                    }}
                    .totals {{
                        float: left;
                        width: 300px;
                        margin-top: 20px;
                    }}
                    .total-row {{
                        display: flex;
                        justify-content: space-between;
                        padding: 5px 0;
                        border-bottom: 1px solid #eee;
                    }}
                    .final-total {{
                        font-size: 18px;
                        font-weight: bold;
                        background-color: #28a745;
                        color: white;
                        padding: 10px;
                        border-radius: 5px;
                    }}
                    .footer {{
                        text-align: center;
                        margin-top: 40px;
                        padding-top: 20px;
                        border-top: 1px solid #ddd;
                        color: #7f8c8d;
                    }}
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="company-name">OnePos POS System</div>
                    <div>نظام نقاط البيع المتقدم</div>
                </div>

                <div class="invoice-info">
                    <div>
                        <strong>رقم الفاتورة:</strong> #{getattr(sale, 'invoice_number', 'N/A')}<br>
                        <strong>التاريخ:</strong> {datetime.now().strftime('%Y-%m-%d')}<br>
                        <strong>الوقت:</strong> {datetime.now().strftime('%H:%M:%S')}
                    </div>
                    <div>
                        <strong>طريقة الدفع:</strong> {self.payment_method_combo.currentText()}<br>
                        <strong>الكاشير:</strong> {getattr(self.current_user, 'full_name', 'Admin')}
                    </div>
                </div>

                {f'''
                <div class="customer-info">
                    <strong>معلومات العميل:</strong><br>
                    الاسم: {self.current_customer.name}<br>
                    الهاتف: {getattr(self.current_customer, 'phone', 'غير محدد')}<br>
                    البريد: {getattr(self.current_customer, 'email', 'غير محدد')}
                </div>
                ''' if self.current_customer else ''}

                <table>
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الخصم</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
            """

            # إضافة المنتجات
            for item in self.cart_items:
                total = item['quantity'] * item['unit_price'] - item.get('discount_amount', 0)
                html_content += f"""
                        <tr>
                            <td>{item['name']}</td>
                            <td>{item['quantity']}</td>
                            <td>{item['unit_price']:.2f}</td>
                            <td>{item.get('discount_amount', 0):.2f}</td>
                            <td>{total:.2f}</td>
                        </tr>
                """

            # حساب الإجماليات
            subtotal = sum(item['quantity'] * item['unit_price'] - item.get('discount_amount', 0) for item in self.cart_items)
            discount = self.get_total_discount()
            tax = subtotal * (self.tax_rate / 100)
            final_total = subtotal - discount + tax

            html_content += f"""
                    </tbody>
                </table>

                <div class="totals">
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span>{subtotal:.2f}</span>
                    </div>
                    {f'<div class="total-row"><span>الخصم:</span><span>-{discount:.2f}</span></div>' if discount > 0 else ''}
                    {f'<div class="total-row"><span>الضريبة ({self.tax_rate}%):</span><span>{tax:.2f}</span></div>' if tax > 0 else ''}
                    <div class="final-total">
                        <div class="total-row">
                            <span>الإجمالي النهائي:</span>
                            <span>{final_total:.2f}</span>
                        </div>
                    </div>
                </div>

                <div style="clear: both;"></div>

                <div class="footer">
                    <p>شكراً لتعاملكم معنا</p>
                    <p>© 2025 ASSANAJE_APP - جميع الحقوق محفوظة</p>
                </div>
            </body>
            </html>
            """

            # طباعة الفاتورة (محاكاة)
            print("=== طباعة فاتورة ===")
            print("تم إنشاء فاتورة HTML كاملة بجداول مفصلة")
            print(f"الطابعة: {printer_name}")
            print("=== انتهاء الفاتورة ===")

            return True, "تم طباعة الفاتورة بنجاح"

        except Exception as e:
            return False, str(e)

    def print_invoice(self, sale):
        """Print invoice for the sale"""
        try:
            from datetime import datetime

            # Prepare sale data for printing (same as receipt but with more details)
            sale_data = {
                'invoice_number': sale.invoice_number,
                'date': sale.created_at.split(' ')[0] if hasattr(sale, 'created_at') else datetime.now().strftime('%Y-%m-%d'),
                'time': sale.created_at.split(' ')[1] if hasattr(sale, 'created_at') else datetime.now().strftime('%H:%M:%S'),
                'cashier': self.current_user.full_name if hasattr(self.current_user, 'full_name') else 'Admin',
                'customer': self.current_customer.name if self.current_customer else tr('customers.walk_in'),
                'customer_phone': self.current_customer.phone if self.current_customer else '',
                'customer_address': self.current_customer.address if self.current_customer else '',
                'customer_tax_number': self.current_customer.tax_number if self.current_customer else '',
                'items': [],
                'subtotal': sale.subtotal if hasattr(sale, 'subtotal') else 0,
                'tax_amount': sale.tax_amount if hasattr(sale, 'tax_amount') else 0,
                'tax_rate': self.tax_rate,
                'discount_amount': sale.discount_amount if hasattr(sale, 'discount_amount') else 0,
                'total_amount': sale.total_amount if hasattr(sale, 'total_amount') else 0,
                'paid_amount': sale.paid_amount if hasattr(sale, 'paid_amount') else 0,
                'change_amount': (sale.paid_amount - sale.total_amount) if hasattr(sale, 'paid_amount') and hasattr(sale, 'total_amount') else 0,
                'payment_method': sale.payment_method if hasattr(sale, 'payment_method') else 'cash'
            }

            # Add items from cart
            for item in self.cart_items:
                sale_data['items'].append({
                    'name': item['name'],
                    'sku': item.get('sku', ''),
                    'quantity': item['quantity'],
                    'unit_price': item['unit_price'],
                    'total_price': item['quantity'] * item['unit_price'] - item.get('discount_amount', 0)
                })

            # Print invoice
            print_manager.print_invoice(sale_data, preview=True)

        except Exception as e:
            QMessageBox.critical(self, tr("common.error"),
                               f"Error printing invoice: {str(e)}")

    def complete_sale_and_print(self):
        """Complete sale and print according to settings"""
        try:
            # Complete the sale first
            if self.complete_sale():
                # Print according to printer settings
                self.print_according_to_settings()
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"Error completing sale: {str(e)}")

    def print_according_to_settings(self):
        """Print receipt according to printer settings"""
        try:
            # Get printer settings from config
            print_type = config.get_printer_setting('default_print_type', 'receipt')
            auto_print = config.get_printer_setting('auto_print_after_sale', True)

            if auto_print and self.last_sale:
                if print_type == 'invoice':
                    self.print_invoice(self.last_sale)
                else:
                    self.print_receipt(self.last_sale)

                QMessageBox.information(self, tr("common.success"), tr("pos.printed_successfully"))

        except Exception as e:
            QMessageBox.warning(self, tr("common.warning"), f"Print error: {str(e)}")

    def reprint_last_receipt(self):
        """Reprint last receipt"""
        if self.last_sale:
            try:
                # Get printer settings
                print_type = config.get_printer_setting('default_print_type', 'receipt')

                if print_type == 'invoice':
                    self.print_invoice(self.last_sale)
                else:
                    self.print_receipt(self.last_sale)

                QMessageBox.information(self, tr("common.success"), tr("pos.reprinted_successfully"))
            except Exception as e:
                QMessageBox.critical(self, tr("common.error"), f"Print error: {str(e)}")
        else:
            QMessageBox.warning(self, tr("common.warning"), tr("pos.no_sale_to_print"))

    def update_held_orders_menu(self):
        """تحديث قائمة الطلبات المعلقة"""
        if hasattr(self, 'held_orders_button'):
            count = len(getattr(self, 'held_orders', []))
            self.held_orders_button.setText(f"📋 {tr('pos.held_orders')} ({count})")
    
    def show_held_orders(self):
        """عرض الطلبات المعلقة"""
        if not hasattr(self, 'held_orders') or not self.held_orders:
            QMessageBox.information(self, tr("pos.held_orders"), tr("pos.no_held_orders"))
            return
        
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QListWidget, QHBoxLayout
        
        dialog = QDialog(self)
        dialog.setWindowTitle(tr("pos.held_orders"))
        dialog.setModal(True)
        dialog.resize(400, 300)
        
        layout = QVBoxLayout(dialog)
        
        # قائمة الطلبات
        orders_list = QListWidget()
        for i, order in enumerate(self.held_orders):
            item_text = f"{order['name']} - {order['timestamp'][:16]} - {len(order['items'])} items"
            orders_list.addItem(item_text)
        
        layout.addWidget(orders_list)
        
        # أزرار
        buttons_layout = QHBoxLayout()
        
        restore_button = QPushButton(tr("pos.restore_order"))
        restore_button.clicked.connect(lambda: self.restore_held_order(orders_list.currentRow(), dialog))
        
        delete_button = QPushButton(tr("pos.delete_held_order"))
        delete_button.clicked.connect(lambda: self.delete_held_order(orders_list.currentRow(), dialog))
        
        buttons_layout.addWidget(restore_button)
        buttons_layout.addWidget(delete_button)
        layout.addLayout(buttons_layout)
        
        dialog.exec_()
    
    def restore_held_order(self, index, dialog):
        """استعادة طلب معلق"""
        if index < 0 or index >= len(self.held_orders):
            return
        
        order = self.held_orders[index]
        
        # مسح السلة الحالية
        self.clear_cart()
        
        # استعادة العناصر
        self.cart_items = order['items'].copy()
        self.tax_rate = order.get('tax_rate', 0.0)
        
        # تحديث العرض
        self.update_cart_display()
        self.calculate_totals()
        
        # حذف من القائمة المعلقة
        self.held_orders.pop(index)
        self.update_held_orders_menu()
        
        dialog.accept()
        QMessageBox.information(self, tr("common.success"), tr("pos.order_restored"))
    
    def delete_held_order(self, index, dialog):
        """حذف طلب معلق"""
        if index < 0 or index >= len(self.held_orders):
            return
        
        order = self.held_orders[index]
        reply = QMessageBox.question(
            self, tr("common.confirm"),
            tr("pos.confirm_delete_held_order").format(order['name']),
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.held_orders.pop(index)
            self.update_held_orders_menu()
            dialog.accept()
