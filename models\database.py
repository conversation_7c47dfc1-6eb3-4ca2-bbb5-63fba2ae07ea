"""
Database connection and initialization module for OnePos POS System
"""

import sqlite3
import os
import json
import time
from datetime import datetime
import bcrypt


class Database:
    """Database manager for OnePos POS System"""

    def __init__(self, db_path=None):
        """Initialize database connection"""
        if db_path is None:
            # Use user's Documents directory for better permissions
            user_docs = os.path.expanduser("~/Documents")
            data_dir = os.path.join(user_docs, "OnePos")
            self.db_path = os.path.join(data_dir, "onepos.db")
        else:
            self.db_path = db_path

        self.ensure_data_directory()
        self.connection = None
        self.connect()
        self.create_tables()
        self.create_default_data()

        # Initialize cache after database setup
        self._init_cache()

    def ensure_data_directory(self):
        """Ensure data directory exists"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        except PermissionError:
            # Fallback to temp directory if AppData is not accessible
            import tempfile
            temp_dir = tempfile.gettempdir()
            data_dir = os.path.join(temp_dir, "OnePos")
            os.makedirs(data_dir, exist_ok=True)
            self.db_path = os.path.join(data_dir, "onepos.db")
    
    def connect(self):
        """Connect to SQLite database"""
        try:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row
            print(f"Connected to database: {self.db_path}")
        except Exception as e:
            print(f"Error connecting to database: {e}")
            raise
    
    def execute_query(self, query, params=None):
        """Execute a query and return results"""
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.fetchall()
        except Exception as e:
            print(f"Error executing query: {e}")
            raise
    
    def execute_update(self, query, params=None):
        """Execute an update/insert/delete query"""
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            self.connection.commit()
            return cursor.lastrowid
        except Exception as e:
            print(f"Error executing update: {e}")
            self.connection.rollback()
            raise
    
    def create_tables(self):
        """Create all database tables"""
        
        # Users table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                role TEXT NOT NULL DEFAULT 'cashier',
                permissions TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Categories table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                parent_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES categories (id)
            )
        """)
        
        # Products table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                barcode TEXT UNIQUE,
                sku TEXT UNIQUE,
                category_id INTEGER,
                cost_price REAL DEFAULT 0,
                selling_price REAL NOT NULL,
                stock_quantity INTEGER DEFAULT 0,
                min_stock_level INTEGER DEFAULT 0,
                unit TEXT DEFAULT 'piece',
                image_path TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        """)
        
        # Customers table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                tax_number TEXT,
                credit_limit REAL DEFAULT 0,
                current_balance REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Suppliers table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                tax_number TEXT,
                current_balance REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                last_login TIMESTAMP,
                failed_attempts INTEGER DEFAULT 0,
                locked_until TIMESTAMP,
                session_token TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Sales table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                user_id INTEGER NOT NULL,
                subtotal REAL NOT NULL,
                tax_amount REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                total_amount REAL NOT NULL,
                paid_amount REAL DEFAULT 0,
                payment_method TEXT DEFAULT 'cash',
                status TEXT DEFAULT 'completed',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # Sale items table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                discount_amount REAL DEFAULT 0,
                total_amount REAL NOT NULL,
                FOREIGN KEY (sale_id) REFERENCES sales (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        """)
        


        
        # Stock movements table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL,
                quantity REAL NOT NULL,
                reference_id INTEGER,
                reference_type TEXT,
                notes TEXT,
                user_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # User sessions table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                logout_time TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # Settings table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Audit logs table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                details TEXT,
                ip_address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)

        # Suppliers table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                tax_number TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Purchases table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS purchases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_id INTEGER NOT NULL,
                purchase_number TEXT UNIQUE NOT NULL,
                purchase_date DATE NOT NULL,
                total_amount DECIMAL(10,2) DEFAULT 0.00,
                tax_amount DECIMAL(10,2) DEFAULT 0.00,
                discount_amount DECIMAL(10,2) DEFAULT 0.00,
                net_amount DECIMAL(10,2) DEFAULT 0.00,
                status TEXT DEFAULT 'pending',
                notes TEXT,
                user_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)

        # Purchase items table
        self.execute_update("""
            CREATE TABLE IF NOT EXISTS purchase_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                purchase_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_cost DECIMAL(10,2) NOT NULL,
                total_cost DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (purchase_id) REFERENCES purchases (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        """)

        print("Database tables created successfully")
    
    def create_default_data(self):
        """Create default data for the application"""
        
        # Check if admin user exists
        admin_exists = self.execute_query("SELECT id FROM users WHERE username = 'admin'")
        
        if not admin_exists:
            # Create default admin user
            password_hash = bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt())
            self.execute_update("""
                INSERT INTO users (username, password_hash, full_name, role, permissions)
                VALUES (?, ?, ?, ?, ?)
            """, ("admin", password_hash.decode('utf-8'), "System Administrator", "admin", "all"))
            
            print("Default admin user created (username: admin, password: admin123)")
        
        # Create default categories
        default_categories = [
            ("General", "General products"),
            ("Food & Beverages", "Food and drink items"),
            ("Electronics", "Electronic devices and accessories"),
            ("Clothing", "Clothing and fashion items"),
            ("Health & Beauty", "Health and beauty products")
        ]
        
        for name, description in default_categories:
            existing = self.execute_query("SELECT id FROM categories WHERE name = ?", (name,))
            if not existing:
                self.execute_update("""
                    INSERT INTO categories (name, description)
                    VALUES (?, ?)
                """, (name, description))
        
        # Create default customer (walk-in)
        walk_in_exists = self.execute_query("SELECT id FROM customers WHERE name = 'Walk-in Customer'")
        if not walk_in_exists:
            self.execute_update("""
                INSERT INTO customers (name, phone, address)
                VALUES (?, ?, ?)
            """, ("Walk-in Customer", "", ""))

        # Create default suppliers
        self.create_default_suppliers()

        print("Default data created successfully")

        # Create indexes for better performance
        self.create_indexes()

    def create_default_suppliers(self):
        """Create default suppliers"""
        # Check if suppliers already exist
        result = self.execute_query("SELECT COUNT(*) as count FROM suppliers")
        if result and result[0]['count'] > 0:
            return

        # Create default suppliers
        default_suppliers = [
            ("General Supplier", "John Doe", "************", "<EMAIL>", "123 Main St", "TAX123"),
            ("Local Distributor", "Jane Smith", "************", "<EMAIL>", "456 Oak Ave", "TAX456"),
            ("Wholesale Company", "Bob Johnson", "************", "<EMAIL>", "789 Pine Rd", "TAX789")
        ]

        for supplier_data in default_suppliers:
            self.execute_update("""
                INSERT INTO suppliers (name, contact_person, phone, email, address, tax_number, is_active)
                VALUES (?, ?, ?, ?, ?, ?, 1)
            """, supplier_data)

    def create_indexes(self):
        """Create database indexes for better performance"""

        # Products indexes
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock_quantity)")

        # Sales indexes
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(created_at)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customer_id)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_sales_user ON sales(user_id)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_sales_status ON sales(status)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_sales_invoice ON sales(invoice_number)")

        # Sale items indexes
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_sale_items_sale ON sale_items(sale_id)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_sale_items_product ON sale_items(product_id)")

        # Customers indexes
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_customers_active ON customers(is_active)")

        # Users indexes
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)")

        # Categories indexes
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_categories_active ON categories(is_active)")

        # Stock movements indexes
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_stock_movements_product ON stock_movements(product_id)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_stock_movements_date ON stock_movements(created_at)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_stock_movements_type ON stock_movements(movement_type)")

        # User sessions indexes
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON user_sessions(user_id)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active)")

        # Audit logs indexes
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_audit_logs_user ON audit_logs(user_id)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action)")
        self.execute_update("CREATE INDEX IF NOT EXISTS idx_audit_logs_date ON audit_logs(created_at)")

        # Purchases indexes (with error handling)
        try:
            self.execute_update("CREATE INDEX IF NOT EXISTS idx_purchases_supplier ON purchases(supplier_id)")
            self.execute_update("CREATE INDEX IF NOT EXISTS idx_purchases_number ON purchases(purchase_number)")
            self.execute_update("CREATE INDEX IF NOT EXISTS idx_purchases_date ON purchases(purchase_date)")
            self.execute_update("CREATE INDEX IF NOT EXISTS idx_purchases_status ON purchases(status)")
            self.execute_update("CREATE INDEX IF NOT EXISTS idx_purchases_user ON purchases(user_id)")
        except Exception as e:
            print(f"Warning: Could not create purchases indexes: {e}")

        # Purchase items indexes (with error handling)
        try:
            self.execute_update("CREATE INDEX IF NOT EXISTS idx_purchase_items_purchase ON purchase_items(purchase_id)")
            self.execute_update("CREATE INDEX IF NOT EXISTS idx_purchase_items_product ON purchase_items(product_id)")
        except Exception as e:
            print(f"Warning: Could not create purchase items indexes: {e}")

        # Suppliers indexes (with error handling)
        try:
            self.execute_update("CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name)")
            self.execute_update("CREATE INDEX IF NOT EXISTS idx_suppliers_active ON suppliers(is_active)")
        except Exception as e:
            print(f"Warning: Could not create suppliers indexes: {e}")

        print("Database indexes created successfully")

    def _init_cache(self):
        """Initialize caching system"""
        try:
            from utils.cache_manager import query_cache, performance_monitor
            self.query_cache = query_cache
            self.performance_monitor = performance_monitor
            self.cache_enabled = True
            print("Cache system initialized successfully")
        except ImportError:
            self.query_cache = None
            self.performance_monitor = None
            self.cache_enabled = False
            print("Cache system not available")

    def execute_query_with_cache(self, query, params=(), table_name="unknown"):
        """Execute query with caching support"""
        start_time = time.time()

        # Try to get from cache first
        if self.cache_enabled and self.query_cache:
            cached_result = self.query_cache.get_query(table_name, query, params)
            if cached_result is not None:
                duration = time.time() - start_time
                if self.performance_monitor:
                    self.performance_monitor.record_query_time(query, duration, cached=True)
                return cached_result

        # Execute query
        result = self.execute_query(query, params)
        duration = time.time() - start_time

        # Cache the result
        if self.cache_enabled and self.query_cache and result:
            self.query_cache.set_query(table_name, query, params, result)

        # Record performance
        if self.performance_monitor:
            self.performance_monitor.record_query_time(query, duration, cached=False)

        return result

    def invalidate_cache(self, table_name):
        """Invalidate cache for a specific table"""
        if self.cache_enabled and self.query_cache:
            self.query_cache.invalidate_table(table_name)
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            print("Database connection closed")


# Global database instance
db = Database()

def get_db_connection():
    """Get database connection for testing purposes"""
    return db.connection
