"""
Comprehensive Testing for OnePos Reports System
Tests all reporting functionality including UI components and business logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QDate
from models.database import db
from models.sale import Sale, SaleItem
from models.product import Product
from models.customer import Customer
from models.user import User
from views.reports_widget import ReportsWidget
from datetime import datetime, timedelta

def test_reports_database_setup():
    """Test reports database setup and required data"""
    print("🔗 Testing Reports Database Setup...")
    
    try:
        # Check required tables
        result = db.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row['name'] for row in result]
        
        required_tables = ['sales', 'sale_items', 'products', 'customers', 'users']
        missing_tables = [table for table in required_tables if table not in tables]
        
        if missing_tables:
            print(f"❌ Missing tables: {missing_tables}")
            return False
        else:
            print(f"✅ All required tables exist: {required_tables}")
        
        # Check if we have data for reports
        sales = Sale.get_all()
        print(f"✅ Found {len(sales)} sales for reporting")
        
        products = Product.get_all()
        print(f"✅ Found {len(products)} products for reporting")
        
        customers = Customer.get_all()
        print(f"✅ Found {len(customers)} customers for reporting")
        
        users = User.get_all()
        print(f"✅ Found {len(users)} users for reporting")
        
        # Check sales data quality
        completed_sales = [s for s in sales if s.status == 'completed']
        cancelled_sales = [s for s in sales if s.status == 'cancelled']
        
        print(f"✅ Sales breakdown: {len(completed_sales)} completed, {len(cancelled_sales)} cancelled")
        
        # Check date range of sales
        if sales:
            dates = [s.created_at for s in sales if s.created_at]
            if dates:
                min_date = min(dates)
                max_date = max(dates)
                print(f"✅ Sales date range: {min_date} to {max_date}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database setup test failed: {e}")
        return False

def test_reports_widget_creation():
    """Test reports widget creation and initialization"""
    print("\n🖥️ Testing Reports Widget Creation...")
    
    try:
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create reports widget
        reports_widget = ReportsWidget()
        
        if reports_widget:
            print("✅ ReportsWidget created successfully")
            
            # Check main components
            if hasattr(reports_widget, 'report_type_combo'):
                print("✅ Report type combo exists")
                types_count = reports_widget.report_type_combo.count()
                print(f"   - Report types: {types_count}")
            
            if hasattr(reports_widget, 'date_from'):
                print("✅ Date from field exists")
            
            if hasattr(reports_widget, 'date_to'):
                print("✅ Date to field exists")
            
            if hasattr(reports_widget, 'results_table'):
                print("✅ Results table exists")
            
            # Check main components
            if hasattr(reports_widget, 'tab_widget'):
                print("✅ Tab widget exists")
                tabs_count = reports_widget.tab_widget.count()
                print(f"   - Tabs: {tabs_count}")

            if hasattr(reports_widget, 'daily_table'):
                print("✅ Daily table exists")

            if hasattr(reports_widget, 'top_products_table'):
                print("✅ Top products table exists")

            if hasattr(reports_widget, 'top_customers_table'):
                print("✅ Top customers table exists")
            
            # Check date components
            date_components = [
                'daily_date_edit',
                'weekly_date_edit',
                'month_combo',
                'year_spin'
            ]

            for component_name in date_components:
                if hasattr(reports_widget, component_name):
                    print(f"✅ {component_name} exists")
                else:
                    print(f"⚠️ {component_name} missing")
            
            return reports_widget
        else:
            print("❌ Failed to create ReportsWidget")
            return False
            
    except Exception as e:
        print(f"❌ Reports widget creation test failed: {e}")
        return False

def test_sales_reports(reports_widget):
    """Test sales reports functionality"""
    print("\n📊 Testing Sales Reports...")
    
    try:
        # Test daily sales report
        if hasattr(reports_widget, 'generate_daily_sales_report'):
            today = datetime.now().date()
            daily_report = reports_widget.generate_daily_sales_report(today)
            print(f"✅ Daily sales report generated: {len(daily_report) if daily_report else 0} records")
        
        # Test sales summary report
        if hasattr(reports_widget, 'generate_sales_summary'):
            date_from = datetime.now().date() - timedelta(days=30)
            date_to = datetime.now().date()
            summary = reports_widget.generate_sales_summary(date_from, date_to)
            
            if summary:
                print(f"✅ Sales summary generated:")
                print(f"   - Total sales: {summary.get('total_sales', 0)}")
                print(f"   - Total revenue: ${summary.get('total_revenue', 0):.2f}")
                print(f"   - Average sale: ${summary.get('average_sale', 0):.2f}")
            else:
                print("⚠️ Sales summary returned empty")
        
        # Test sales by period
        if hasattr(reports_widget, 'generate_sales_by_period'):
            period_report = reports_widget.generate_sales_by_period('daily', date_from, date_to)
            print(f"✅ Sales by period report: {len(period_report) if period_report else 0} periods")
        
        return True
        
    except Exception as e:
        print(f"❌ Sales reports test failed: {e}")
        return False

def test_product_reports(reports_widget):
    """Test product reports functionality"""
    print("\n📦 Testing Product Reports...")
    
    try:
        # Test top selling products
        if hasattr(reports_widget, 'generate_top_products_report'):
            top_products = reports_widget.generate_top_products_report(limit=10)
            print(f"✅ Top products report: {len(top_products) if top_products else 0} products")
            
            if top_products:
                for i, product in enumerate(top_products[:3]):
                    print(f"   {i+1}. {product.get('name', 'Unknown')}: {product.get('quantity_sold', 0)} sold")
        
        # Test low stock report
        if hasattr(reports_widget, 'generate_low_stock_report'):
            low_stock = reports_widget.generate_low_stock_report()
            print(f"✅ Low stock report: {len(low_stock) if low_stock else 0} products")
        
        # Test product performance
        if hasattr(reports_widget, 'generate_product_performance'):
            date_from = datetime.now().date() - timedelta(days=30)
            date_to = datetime.now().date()
            performance = reports_widget.generate_product_performance(date_from, date_to)
            print(f"✅ Product performance report: {len(performance) if performance else 0} products")
        
        return True
        
    except Exception as e:
        print(f"❌ Product reports test failed: {e}")
        return False

def test_customer_reports(reports_widget):
    """Test customer reports functionality"""
    print("\n👥 Testing Customer Reports...")
    
    try:
        # Test top customers report
        if hasattr(reports_widget, 'generate_top_customers_report'):
            top_customers = reports_widget.generate_top_customers_report(limit=10)
            print(f"✅ Top customers report: {len(top_customers) if top_customers else 0} customers")
            
            if top_customers:
                for i, customer in enumerate(top_customers[:3]):
                    print(f"   {i+1}. {customer.get('name', 'Unknown')}: ${customer.get('total_spent', 0):.2f}")
        
        # Test customer activity
        if hasattr(reports_widget, 'generate_customer_activity'):
            date_from = datetime.now().date() - timedelta(days=30)
            date_to = datetime.now().date()
            activity = reports_widget.generate_customer_activity(date_from, date_to)
            print(f"✅ Customer activity report: {len(activity) if activity else 0} customers")
        
        # Test new customers report
        if hasattr(reports_widget, 'generate_new_customers_report'):
            new_customers = reports_widget.generate_new_customers_report(date_from, date_to)
            print(f"✅ New customers report: {len(new_customers) if new_customers else 0} customers")
        
        return True
        
    except Exception as e:
        print(f"❌ Customer reports test failed: {e}")
        return False

def test_financial_reports(reports_widget):
    """Test financial reports functionality"""
    print("\n💰 Testing Financial Reports...")
    
    try:
        # Test revenue report
        if hasattr(reports_widget, 'generate_revenue_report'):
            date_from = datetime.now().date() - timedelta(days=30)
            date_to = datetime.now().date()
            revenue = reports_widget.generate_revenue_report(date_from, date_to)
            
            if revenue:
                print(f"✅ Revenue report generated:")
                print(f"   - Total revenue: ${revenue.get('total_revenue', 0):.2f}")
                print(f"   - Total tax: ${revenue.get('total_tax', 0):.2f}")
                print(f"   - Total discount: ${revenue.get('total_discount', 0):.2f}")
                print(f"   - Net revenue: ${revenue.get('net_revenue', 0):.2f}")
        
        # Test profit report
        if hasattr(reports_widget, 'generate_profit_report'):
            profit = reports_widget.generate_profit_report(date_from, date_to)
            print(f"✅ Profit report: {profit if profit else 'No data'}")
        
        # Test payment methods report
        if hasattr(reports_widget, 'generate_payment_methods_report'):
            payment_methods = reports_widget.generate_payment_methods_report(date_from, date_to)
            print(f"✅ Payment methods report: {len(payment_methods) if payment_methods else 0} methods")
        
        return True
        
    except Exception as e:
        print(f"❌ Financial reports test failed: {e}")
        return False

def test_report_generation_ui(reports_widget):
    """Test report generation through UI"""
    print("\n🎛️ Testing Report Generation UI...")

    try:
        # Test setting dates
        today = QDate.currentDate()
        last_week = today.addDays(-7)

        if hasattr(reports_widget, 'daily_date_edit'):
            reports_widget.daily_date_edit.setDate(today)
            print(f"✅ Daily date set: {today.toString()}")

        if hasattr(reports_widget, 'weekly_date_edit'):
            reports_widget.weekly_date_edit.setDate(last_week)
            print(f"✅ Weekly date set: {last_week.toString()}")

        # Test tab switching
        if hasattr(reports_widget, 'tab_widget'):
            for i in range(reports_widget.tab_widget.count()):
                tab_text = reports_widget.tab_widget.tabText(i)
                reports_widget.tab_widget.setCurrentIndex(i)
                print(f"✅ Switched to tab: {tab_text}")

        # Test loading reports
        if hasattr(reports_widget, 'load_reports'):
            reports_widget.load_reports()
            print("✅ Reports loaded successfully")

        return True

    except Exception as e:
        print(f"❌ Report generation UI test failed: {e}")
        return False

def test_report_export_functionality(reports_widget):
    """Test report export functionality"""
    print("\n📤 Testing Report Export...")
    
    try:
        # Generate a report first
        if hasattr(reports_widget, 'generate_report'):
            reports_widget.generate_report()
            rows = reports_widget.results_table.rowCount()
            
            if rows > 0:
                print(f"✅ Generated report with {rows} rows for export testing")
                
                # Test export functionality
                if hasattr(reports_widget, 'export_report'):
                    try:
                        # Mock file dialog
                        import unittest.mock
                        with unittest.mock.patch('PyQt5.QtWidgets.QFileDialog.getSaveFileName', 
                                               return_value=('test_report_export.csv', 'CSV Files (*.csv)')):
                            result = reports_widget.export_report()
                            
                            if result is not False:
                                print("✅ Report export executed successfully")
                            else:
                                print("⚠️ Report export returned False")
                    except Exception as e:
                        print(f"⚠️ Report export failed: {e}")
                else:
                    print("⚠️ Export functionality not available")
            else:
                print("⚠️ No data to export")
        
        return True
        
    except Exception as e:
        print(f"❌ Report export test failed: {e}")
        return False

def test_report_printing(reports_widget):
    """Test report printing functionality"""
    print("\n🖨️ Testing Report Printing...")
    
    try:
        # Test print functionality
        if hasattr(reports_widget, 'print_report'):
            try:
                # Mock print dialog
                import unittest.mock
                with unittest.mock.patch('PyQt5.QtPrintSupport.QPrintDialog.exec_', return_value=1):
                    result = reports_widget.print_report()
                    print("✅ Report printing functionality available")
            except Exception as e:
                print(f"⚠️ Report printing failed: {e}")
        else:
            print("⚠️ Print functionality not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Report printing test failed: {e}")
        return False

def test_report_data_accuracy():
    """Test report data accuracy"""
    print("\n🎯 Testing Report Data Accuracy...")
    
    try:
        # Get raw data from database
        sales = Sale.get_all(status='completed')
        
        if sales:
            # Calculate manual totals
            manual_total_revenue = sum(sale.total_amount for sale in sales)
            manual_total_tax = sum(sale.tax_amount for sale in sales)
            manual_total_discount = sum(sale.discount_amount for sale in sales)
            manual_sales_count = len(sales)
            manual_average_sale = manual_total_revenue / manual_sales_count if manual_sales_count > 0 else 0
            
            print(f"✅ Manual calculations:")
            print(f"   - Total revenue: ${manual_total_revenue:.2f}")
            print(f"   - Total tax: ${manual_total_tax:.2f}")
            print(f"   - Total discount: ${manual_total_discount:.2f}")
            print(f"   - Sales count: {manual_sales_count}")
            print(f"   - Average sale: ${manual_average_sale:.2f}")
            
            # Test against reports manager
            try:
                from models.reports import ReportsManager
                reports_manager = ReportsManager()

                # Get today's summary
                today = datetime.now().date()
                daily_summary = reports_manager.get_daily_sales_summary(today)

                if daily_summary:
                    print(f"✅ Reports manager calculations:")
                    print(f"   - Total sales: {daily_summary['total_sales']}")
                    print(f"   - Total revenue: ${daily_summary['total_revenue']:.2f}")
                    print(f"   - Average sale: ${daily_summary['average_sale']:.2f}")
                    print(f"   - Unique customers: {daily_summary['unique_customers']}")

                    # Compare with manual calculations for today's sales
                    today_sales = [s for s in sales if s.created_at and s.created_at.startswith(today.strftime('%Y-%m-%d'))]
                    manual_today_revenue = sum(sale.total_amount for sale in today_sales)
                    manual_today_count = len(today_sales)

                    print(f"✅ Manual today calculations:")
                    print(f"   - Today sales: {manual_today_count}")
                    print(f"   - Today revenue: ${manual_today_revenue:.2f}")

                    if abs(daily_summary['total_revenue'] - manual_today_revenue) < 0.01:
                        print("✅ Today's calculations match")
                    else:
                        print("⚠️ Today's calculations differ (may be due to date filtering)")
                else:
                    print("⚠️ Daily summary not available")

            except ImportError:
                print("⚠️ ReportsManager not available")
        else:
            print("⚠️ No completed sales for accuracy testing")
        
        return True
        
    except Exception as e:
        print(f"❌ Report data accuracy test failed: {e}")
        return False

def test_report_performance():
    """Test report generation performance"""
    print("\n⚡ Testing Report Performance...")
    
    try:
        import time
        
        # Test sales summary performance
        start_time = time.time()
        summary = Sale.get_sales_summary()
        summary_time = time.time() - start_time
        print(f"✅ Sales summary generated in {summary_time:.4f} seconds")
        
        # Test sales retrieval performance
        start_time = time.time()
        sales = Sale.get_all()
        retrieval_time = time.time() - start_time
        print(f"✅ All sales retrieved in {retrieval_time:.4f} seconds ({len(sales)} records)")
        
        # Test product performance
        start_time = time.time()
        products = Product.get_all()
        product_time = time.time() - start_time
        print(f"✅ All products retrieved in {product_time:.4f} seconds ({len(products)} records)")
        
        # Test customer performance
        start_time = time.time()
        customers = Customer.get_all()
        customer_time = time.time() - start_time
        print(f"✅ All customers retrieved in {customer_time:.4f} seconds ({len(customers)} records)")
        
        # Performance thresholds
        if summary_time < 0.1 and retrieval_time < 0.1:
            print("✅ Report performance excellent")
        elif summary_time < 0.5 and retrieval_time < 0.5:
            print("✅ Report performance good")
        else:
            print("⚠️ Report performance could be improved")
        
        return True
        
    except Exception as e:
        print(f"❌ Report performance test failed: {e}")
        return False

def run_comprehensive_reports_test():
    """Run all reports tests"""
    print("🧪 STARTING COMPREHENSIVE REPORTS SYSTEM TEST")
    print("=" * 60)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    reports_widget = None
    
    # Test 1: Database Setup
    test_results.append(("Database Setup", test_reports_database_setup()))
    
    # Test 2: Widget Creation
    reports_widget = test_reports_widget_creation()
    test_results.append(("Widget Creation", reports_widget is not False))
    
    if reports_widget:
        # Test 3: Sales Reports
        test_results.append(("Sales Reports", test_sales_reports(reports_widget)))
        
        # Test 4: Product Reports
        test_results.append(("Product Reports", test_product_reports(reports_widget)))
        
        # Test 5: Customer Reports
        test_results.append(("Customer Reports", test_customer_reports(reports_widget)))
        
        # Test 6: Financial Reports
        test_results.append(("Financial Reports", test_financial_reports(reports_widget)))
        
        # Test 7: Report Generation UI
        test_results.append(("Report Generation UI", test_report_generation_ui(reports_widget)))
        
        # Test 8: Export Functionality
        test_results.append(("Export Functionality", test_report_export_functionality(reports_widget)))
        
        # Test 9: Printing
        test_results.append(("Printing", test_report_printing(reports_widget)))
    
    # Test 10: Data Accuracy
    test_results.append(("Data Accuracy", test_report_data_accuracy()))
    
    # Test 11: Performance
    test_results.append(("Performance", test_report_performance()))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 REPORTS TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL REPORTS TESTS PASSED! Reports system is working perfectly!")
    else:
        print("⚠️  Some reports tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_comprehensive_reports_test()
