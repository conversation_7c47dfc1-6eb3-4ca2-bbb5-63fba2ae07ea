#!/usr/bin/env python3
"""
تثبيت المكتبات المطلوبة لأنظمة الطابعات وماسح الباركود
Install Required Libraries for Printer and Barcode Scanner Systems
"""

import subprocess
import sys
import os


def install_package(package_name, description=""):
    """تثبيت مكتبة"""
    try:
        print(f"📦 تثبيت {package_name}...")
        if description:
            print(f"   📝 {description}")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ تم تثبيت {package_name} بنجاح")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت {package_name}")
        print(f"   خطأ: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع في تثبيت {package_name}: {e}")
        return False


def check_package(package_name):
    """فحص وجود مكتبة"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False


def install_printer_dependencies():
    """تثبيت مكتبات الطابعات"""
    print("\n🖨️ تثبيت مكتبات الطابعات:")
    print("=" * 50)
    
    dependencies = [
        ("PyQt5", "واجهة المستخدم الرسومية"),
        ("reportlab", "إنشاء ملفات PDF للطباعة"),
        ("pillow", "معالجة الصور"),
        ("python-escpos", "طباعة الطابعات الحرارية"),
    ]
    
    success_count = 0
    
    for package, description in dependencies:
        if install_package(package, description):
            success_count += 1
    
    print(f"\n📊 تم تثبيت {success_count}/{len(dependencies)} مكتبة للطابعات")
    return success_count == len(dependencies)


def install_barcode_dependencies():
    """تثبيت مكتبات ماسح الباركود"""
    print("\n📱 تثبيت مكتبات ماسح الباركود:")
    print("=" * 50)
    
    dependencies = [
        ("pyserial", "التواصل مع المنافذ التسلسلية"),
        ("hidapi", "التواصل مع أجهزة USB HID"),
        ("opencv-python", "معالجة الكاميرا"),
        ("pyzbar", "قراءة الباركود من الصور"),
        ("pillow", "معالجة الصور"),
        ("numpy", "معالجة البيانات الرقمية"),
    ]
    
    success_count = 0
    
    for package, description in dependencies:
        if install_package(package, description):
            success_count += 1
    
    print(f"\n📊 تم تثبيت {success_count}/{len(dependencies)} مكتبة لماسح الباركود")
    return success_count == len(dependencies)


def install_optional_dependencies():
    """تثبيت المكتبات الاختيارية"""
    print("\n🔧 تثبيت المكتبات الاختيارية:")
    print("=" * 50)
    
    optional_deps = [
        ("pybluez", "دعم Bluetooth (اختياري)"),
        ("wmi", "معلومات النظام في Windows (اختياري)"),
        ("psutil", "معلومات النظام (اختياري)"),
    ]
    
    success_count = 0
    
    for package, description in optional_deps:
        print(f"🔄 محاولة تثبيت {package} (اختياري)...")
        if install_package(package, description):
            success_count += 1
        else:
            print(f"⚠️ تخطي {package} - غير ضروري للتشغيل الأساسي")
    
    print(f"\n📊 تم تثبيت {success_count}/{len(optional_deps)} مكتبة اختيارية")
    return True


def verify_installations():
    """التحقق من التثبيتات"""
    print("\n✅ التحقق من التثبيتات:")
    print("=" * 50)
    
    # فحص مكتبات الطابعات
    printer_libs = {
        "PyQt5": "PyQt5",
        "reportlab": "reportlab", 
        "PIL": "pillow",
        "escpos": "python-escpos"
    }
    
    print("🖨️ مكتبات الطابعات:")
    for lib_name, package_name in printer_libs.items():
        if check_package(lib_name):
            print(f"  ✅ {package_name}")
        else:
            print(f"  ❌ {package_name}")
    
    # فحص مكتبات ماسح الباركود
    scanner_libs = {
        "serial": "pyserial",
        "hid": "hidapi",
        "cv2": "opencv-python",
        "pyzbar": "pyzbar",
        "numpy": "numpy"
    }
    
    print("\n📱 مكتبات ماسح الباركود:")
    for lib_name, package_name in scanner_libs.items():
        if check_package(lib_name):
            print(f"  ✅ {package_name}")
        else:
            print(f"  ❌ {package_name}")
    
    # فحص المكتبات الاختيارية
    optional_libs = {
        "bluetooth": "pybluez",
        "wmi": "wmi",
        "psutil": "psutil"
    }
    
    print("\n🔧 المكتبات الاختيارية:")
    for lib_name, package_name in optional_libs.items():
        if check_package(lib_name):
            print(f"  ✅ {package_name}")
        else:
            print(f"  ⚠️ {package_name} (اختياري)")


def create_requirements_file():
    """إنشاء ملف requirements.txt"""
    print("\n📄 إنشاء ملف requirements.txt:")
    print("=" * 50)
    
    requirements = [
        "# مكتبات الطابعات",
        "PyQt5>=5.15.0",
        "reportlab>=3.6.0",
        "pillow>=8.0.0",
        "python-escpos>=3.0.0",
        "",
        "# مكتبات ماسح الباركود",
        "pyserial>=3.5",
        "hidapi>=0.12.0",
        "opencv-python>=4.5.0",
        "pyzbar>=0.1.8",
        "numpy>=1.21.0",
        "",
        "# مكتبات اختيارية",
        "# pybluez>=0.23  # للـ Bluetooth",
        "# wmi>=1.5.1  # لـ Windows فقط",
        "# psutil>=5.8.0  # معلومات النظام",
    ]
    
    try:
        with open("requirements.txt", "w", encoding="utf-8") as f:
            f.write("\n".join(requirements))
        
        print("✅ تم إنشاء ملف requirements.txt")
        print("📝 يمكنك استخدام: pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ فشل في إنشاء ملف requirements.txt: {e}")


def main():
    """الدالة الرئيسية"""
    print("🚀 تثبيت مكتبات أنظمة الطابعات وماسح الباركود")
    print("=" * 60)
    print("📋 سيتم تثبيت جميع المكتبات المطلوبة للتشغيل الصحيح")
    print("⏱️ قد يستغرق هذا بضع دقائق...")
    
    # التحقق من Python و pip
    print(f"\n🐍 إصدار Python: {sys.version}")
    
    try:
        pip_version = subprocess.run([
            sys.executable, "-m", "pip", "--version"
        ], capture_output=True, text=True, check=True)
        print(f"📦 إصدار pip: {pip_version.stdout.strip()}")
    except:
        print("❌ pip غير متاح - يرجى تثبيت pip أولاً")
        return False
    
    # تحديث pip
    print("\n🔄 تحديث pip...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], capture_output=True, text=True, check=True)
        print("✅ تم تحديث pip")
    except:
        print("⚠️ فشل في تحديث pip - المتابعة بالإصدار الحالي")
    
    # تثبيت المكتبات
    printer_success = install_printer_dependencies()
    scanner_success = install_barcode_dependencies()
    install_optional_dependencies()
    
    # التحقق من التثبيتات
    verify_installations()
    
    # إنشاء ملف requirements
    create_requirements_file()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if printer_success and scanner_success:
        print("🎉 تم تثبيت جميع المكتبات الأساسية بنجاح!")
        print("✅ أنظمة الطابعات وماسح الباركود جاهزة للعمل")
        print("\n📋 الخطوات التالية:")
        print("1. تشغيل test_printer_barcode_systems.py لاختبار الأنظمة")
        print("2. التأكد من توصيل الطابعات وماسحات الباركود")
        print("3. تكوين الإعدادات في التطبيق")
    else:
        print("⚠️ تم تثبيت بعض المكتبات مع وجود مشاكل")
        print("🔧 يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة")
        print("\n💡 نصائح:")
        print("- تأكد من اتصال الإنترنت")
        print("- قم بتشغيل الأمر كمدير (Administrator)")
        print("- استخدم بيئة افتراضية (virtual environment)")
    
    print("\n🔗 للمساعدة:")
    print("- راجع ملف README.md")
    print("- تواصل مع الدعم الفني")
    
    return printer_success and scanner_success


if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ اكتمل التثبيت بنجاح!")
        else:
            print("\n❌ فشل في التثبيت")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء التثبيت بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
