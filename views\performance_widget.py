"""
Performance Monitoring Widget for OnePos POS System
Displays real-time performance metrics and optimization suggestions
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QGroupBox, QProgressBar,
                            QTableWidget, QTableWidgetItem, QTabWidget)
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtGui import QFont
from utils.translator import tr

def create_dark_label(text):
    """Create a dark label with proper styling"""
    label = QLabel(text)
    label.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
    return label


class PerformanceWidget(QWidget):
    """Performance monitoring and optimization widget"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("⚡ " + tr("performance.title"))
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title_label.setStyleSheet("color: #000000; font-weight: 800; font-size: 18px;")
        header_layout.addWidget(title_label)
        
        # Refresh button
        self.refresh_button = QPushButton("🔄 " + tr("common.refresh"))
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_metrics)
        header_layout.addWidget(self.refresh_button)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        
        # Cache Performance Tab
        self.cache_tab = self.create_cache_tab()
        self.tab_widget.addTab(self.cache_tab, "💾 " + tr("performance.cache"))
        
        # Query Performance Tab
        self.query_tab = self.create_query_tab()
        self.tab_widget.addTab(self.query_tab, "🔍 " + tr("performance.queries"))
        
        # System Resources Tab
        self.system_tab = self.create_system_tab()
        self.tab_widget.addTab(self.system_tab, "🖥️ " + tr("performance.system"))
        
        # Optimization Tab
        self.optimization_tab = self.create_optimization_tab()
        self.tab_widget.addTab(self.optimization_tab, "🚀 " + tr("performance.optimization"))
        
        layout.addWidget(self.tab_widget)
    
    def create_cache_tab(self):
        """Create cache performance tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Cache statistics
        cache_group = QGroupBox("📊 " + tr("performance.cache_stats"))
        cache_layout = QVBoxLayout(cache_group)
        
        # Cache metrics
        metrics_layout = QHBoxLayout()
        
        # Hit rate
        self.hit_rate_label = create_dark_label("Hit Rate: 0%")
        metrics_layout.addWidget(self.hit_rate_label)
        
        # Cache size
        self.cache_size_label = create_dark_label("Size: 0/1000")
        metrics_layout.addWidget(self.cache_size_label)
        
        # Total requests
        self.requests_label = create_dark_label("Requests: 0")
        metrics_layout.addWidget(self.requests_label)
        
        metrics_layout.addStretch()
        cache_layout.addLayout(metrics_layout)
        
        # Cache performance chart (text-based)
        self.cache_chart = QTextEdit()
        self.cache_chart.setMaximumHeight(200)
        self.cache_chart.setReadOnly(True)
        cache_layout.addWidget(self.cache_chart)
        
        layout.addWidget(cache_group)
        
        # Cache controls
        controls_group = QGroupBox("🔧 " + tr("performance.cache_controls"))
        controls_layout = QHBoxLayout(controls_group)
        
        self.clear_cache_button = QPushButton("🗑️ " + tr("performance.clear_cache"))
        self.clear_cache_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: #ffffff;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.clear_cache_button.clicked.connect(self.clear_cache)
        controls_layout.addWidget(self.clear_cache_button)
        
        self.cleanup_cache_button = QPushButton("🧹 " + tr("performance.cleanup_cache"))
        self.cleanup_cache_button.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #000000;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.cleanup_cache_button.clicked.connect(self.cleanup_cache)
        controls_layout.addWidget(self.cleanup_cache_button)
        
        controls_layout.addStretch()
        layout.addWidget(controls_group)
        
        return widget
    
    def create_query_tab(self):
        """Create query performance tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Query statistics
        query_group = QGroupBox("📈 " + tr("performance.query_stats"))
        query_layout = QVBoxLayout(query_group)
        
        # Query metrics
        metrics_layout = QHBoxLayout()
        
        self.avg_query_time_label = create_dark_label("Avg Time: 0ms")
        metrics_layout.addWidget(self.avg_query_time_label)
        
        self.slow_queries_label = create_dark_label("Slow Queries: 0")
        metrics_layout.addWidget(self.slow_queries_label)
        
        self.total_queries_label = create_dark_label("Total: 0")
        metrics_layout.addWidget(self.total_queries_label)
        
        metrics_layout.addStretch()
        query_layout.addLayout(metrics_layout)
        
        # Slow queries table
        self.slow_queries_table = QTableWidget()
        self.slow_queries_table.setColumnCount(3)
        self.slow_queries_table.setHorizontalHeaderLabels([
            tr("performance.query"),
            tr("performance.duration"),
            tr("performance.timestamp")
        ])
        self.slow_queries_table.horizontalHeader().setStretchLastSection(True)
        self.slow_queries_table.setMaximumHeight(200)
        query_layout.addWidget(self.slow_queries_table)
        
        layout.addWidget(query_group)
        
        return widget
    
    def create_system_tab(self):
        """Create system resources tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # System metrics
        system_group = QGroupBox("💻 " + tr("performance.system_metrics"))
        system_layout = QVBoxLayout(system_group)
        
        # Memory usage
        memory_layout = QHBoxLayout()
        memory_layout.addWidget(create_dark_label(tr("performance.memory_usage") + ":"))
        
        self.memory_progress = QProgressBar()
        self.memory_progress.setMaximum(100)
        memory_layout.addWidget(self.memory_progress)
        
        self.memory_label = create_dark_label("0 MB")
        memory_layout.addWidget(self.memory_label)
        
        system_layout.addLayout(memory_layout)
        
        # Database size
        db_layout = QHBoxLayout()
        db_layout.addWidget(create_dark_label(tr("performance.database_size") + ":"))
        
        self.db_size_label = create_dark_label("0 MB")
        db_layout.addWidget(self.db_size_label)
        db_layout.addStretch()
        
        system_layout.addLayout(db_layout)
        
        # Application uptime
        uptime_layout = QHBoxLayout()
        uptime_layout.addWidget(create_dark_label(tr("performance.uptime") + ":"))
        
        self.uptime_label = create_dark_label("0:00:00")
        uptime_layout.addWidget(self.uptime_label)
        uptime_layout.addStretch()
        
        system_layout.addLayout(uptime_layout)
        
        layout.addWidget(system_group)
        
        return widget
    
    def create_optimization_tab(self):
        """Create optimization suggestions tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Optimization suggestions
        suggestions_group = QGroupBox("💡 " + tr("performance.suggestions"))
        suggestions_layout = QVBoxLayout(suggestions_group)
        
        self.suggestions_text = QTextEdit()
        self.suggestions_text.setReadOnly(True)
        suggestions_layout.addWidget(self.suggestions_text)
        
        layout.addWidget(suggestions_group)
        
        # Optimization actions
        actions_group = QGroupBox("🔧 " + tr("performance.actions"))
        actions_layout = QHBoxLayout(actions_group)
        
        self.optimize_db_button = QPushButton("🗃️ " + tr("performance.optimize_database"))
        self.optimize_db_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: #ffffff;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.optimize_db_button.clicked.connect(self.optimize_database)
        actions_layout.addWidget(self.optimize_db_button)
        
        self.rebuild_indexes_button = QPushButton("🔍 " + tr("performance.rebuild_indexes"))
        self.rebuild_indexes_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: #ffffff;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.rebuild_indexes_button.clicked.connect(self.rebuild_indexes)
        actions_layout.addWidget(self.rebuild_indexes_button)
        
        actions_layout.addStretch()
        layout.addWidget(actions_group)
        
        return widget
    
    def setup_timer(self):
        """Setup automatic refresh timer"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_metrics)
        self.timer.start(5000)  # Refresh every 5 seconds
    
    def refresh_metrics(self):
        """Refresh all performance metrics"""
        self.update_cache_metrics()
        self.update_query_metrics()
        self.update_system_metrics()
        self.update_optimization_suggestions()
    
    def update_cache_metrics(self):
        """Update cache performance metrics"""
        try:
            from utils.cache_manager import cache_manager
            stats = cache_manager.get_stats()
            
            self.hit_rate_label.setText(f"Hit Rate: {stats.get('hit_rate', '0%')}")
            self.cache_size_label.setText(f"Size: {stats.get('size', 0)}/{stats.get('max_size', 1000)}")
            self.requests_label.setText(f"Requests: {stats.get('hits', 0) + stats.get('misses', 0)}")
            
            # Update cache chart
            chart_text = f"""Cache Performance Summary:
            
Hits: {stats.get('hits', 0)}
Misses: {stats.get('misses', 0)}
Hit Rate: {stats.get('hit_rate', '0%')}
Evictions: {stats.get('evictions', 0)}
Cleanups: {stats.get('cleanups', 0)}

Cache is {'performing well' if float(stats.get('hit_rate', '0%').rstrip('%')) > 70 else 'needs optimization'}
            """
            self.cache_chart.setText(chart_text)
            
        except Exception as e:
            self.cache_chart.setText(f"Error loading cache metrics: {e}")
    
    def update_query_metrics(self):
        """Update query performance metrics"""
        try:
            from utils.cache_manager import performance_monitor
            stats = performance_monitor.get_performance_stats()
            
            if 'message' in stats:
                self.avg_query_time_label.setText(f"{tr('performance.avg_time')}: {tr('common.not_available')}")
                self.slow_queries_label.setText(f"{tr('performance.slow_queries')}: {tr('common.not_available')}")
                self.total_queries_label.setText(f"{tr('common.total')}: {tr('common.not_available')}")
                return
            
            self.avg_query_time_label.setText(f"Avg Time: {stats.get('avg_uncached_time', '0ms')}")
            self.slow_queries_label.setText(f"Slow Queries: {stats.get('slow_queries_count', 0)}")
            self.total_queries_label.setText(f"Total: {stats.get('total_queries', 0)}")
            
            # Update slow queries table
            slow_queries = performance_monitor.metrics.get('slow_queries', [])
            self.slow_queries_table.setRowCount(len(slow_queries))
            
            for row, query_info in enumerate(slow_queries[-10:]):  # Show last 10
                self.slow_queries_table.setItem(row, 0, QTableWidgetItem(query_info['query']))
                self.slow_queries_table.setItem(row, 1, QTableWidgetItem(f"{query_info['duration']*1000:.1f}ms"))
                self.slow_queries_table.setItem(row, 2, QTableWidgetItem(str(query_info['timestamp'])))
            
        except Exception as e:
            self.avg_query_time_label.setText(f"Error: {e}")
    
    def update_system_metrics(self):
        """Update system resource metrics"""
        try:
            import psutil
            import os
            
            # Memory usage
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            self.memory_label.setText(f"{memory_mb:.1f} MB")
            
            # Database size
            try:
                db_size = os.path.getsize("data/onepos.db") / 1024 / 1024
                self.db_size_label.setText(f"{db_size:.1f} MB")
            except:
                self.db_size_label.setText(tr("common.not_available"))
            
            # Uptime (simplified)
            import time
            uptime_seconds = int(time.time() % 86400)  # Seconds since midnight
            hours = uptime_seconds // 3600
            minutes = (uptime_seconds % 3600) // 60
            seconds = uptime_seconds % 60
            self.uptime_label.setText(f"{hours:02d}:{minutes:02d}:{seconds:02d}")
            
        except ImportError:
            self.memory_label.setText(tr("performance.psutil_not_available"))
        except Exception as e:
            self.memory_label.setText(f"Error: {e}")
    
    def update_optimization_suggestions(self):
        """Update optimization suggestions"""
        suggestions = []
        
        try:
            from utils.cache_manager import cache_manager
            stats = cache_manager.get_stats()
            
            hit_rate = float(stats.get('hit_rate', '0%').rstrip('%'))
            
            if hit_rate < 50:
                suggestions.append("• Cache hit rate is low. Consider increasing cache size or TTL.")
            
            if stats.get('evictions', 0) > 100:
                suggestions.append("• High cache evictions detected. Consider increasing cache size.")
            
            if stats.get('size', 0) > stats.get('max_size', 1000) * 0.9:
                suggestions.append("• Cache is nearly full. Consider cleanup or size increase.")
            
        except:
            pass
        
        if not suggestions:
            suggestions.append("• System is performing well!")
            suggestions.append("• No optimization needed at this time.")
        
        self.suggestions_text.setText("\n".join(suggestions))
    
    def clear_cache(self):
        """Clear all cache"""
        try:
            from utils.cache_manager import cache_manager
            cache_manager.clear()
            self.refresh_metrics()
        except Exception as e:
            print(f"Error clearing cache: {e}")
    
    def cleanup_cache(self):
        """Cleanup expired cache entries"""
        try:
            from utils.cache_manager import cache_manager
            cleaned = cache_manager.cleanup_expired()
            print(f"Cleaned {cleaned} expired entries")
            self.refresh_metrics()
        except Exception as e:
            print(f"Error cleaning cache: {e}")
    
    def optimize_database(self):
        """Optimize database"""
        try:
            from models.database import db
            db.execute_update("VACUUM")
            db.execute_update("ANALYZE")
            print("Database optimized successfully")
        except Exception as e:
            print(f"Error optimizing database: {e}")
    
    def rebuild_indexes(self):
        """Rebuild database indexes"""
        try:
            from models.database import db
            db.create_indexes()
            print("Indexes rebuilt successfully")
        except Exception as e:
            print(f"Error rebuilding indexes: {e}")
