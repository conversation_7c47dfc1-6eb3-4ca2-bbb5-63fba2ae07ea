"""
Purchases Management Widget for OnePos POS System
Comprehensive purchase management with suppliers and purchase orders
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem, 
                            QLineEdit, QComboBox, QTabWidget, QGroupBox,
                            QFormLayout, QMessageBox, QDialog, QDialogButtonBox,
                            QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont
from models.purchase import Purchase, Supplier, PurchaseItem
from models.product import Product
from utils.translator import tr

def create_dark_label(text):
    """Create a dark label with proper styling for high contrast"""
    label = QLabel(text)
    label.setStyleSheet("""
        QLabel {
            color: #ffffff !important;
            font-weight: 700;
            font-size: 13px;
            background-color: transparent;
            padding: 2px;
        }
    """)
    return label


class SupplierDialog(QDialog):
    """Dialog for creating/editing suppliers"""
    
    def __init__(self, supplier=None, parent=None):
        super().__init__(parent)
        self.supplier = supplier
        self.setWindowTitle(tr("purchases.edit_supplier") if supplier else tr("purchases.add_supplier"))
        self.setModal(True)
        self.resize(400, 350)

        # Apply dialog styling for better contrast
        self.setStyleSheet("""
            QDialog {
                background-color: #2c3e50;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff !important;
                font-weight: 700;
                font-size: 13px;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                background-color: #ffffff;
                color: #000000;
                border: 2px solid #34495e;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
            QGroupBox {
                color: #ffffff;
                font-weight: 700;
                border: 2px solid #34495e;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                color: #ffffff;
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #3498db;
                color: #ffffff;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        self.init_ui()

        if supplier:
            self.load_supplier_data()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # Supplier information
        info_group = QGroupBox(tr("purchases.supplier_info"))
        info_layout = QFormLayout(info_group)
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText(tr("purchases.supplier_name"))
        info_layout.addRow(create_dark_label(tr("purchases.supplier_name") + ":"), self.name_edit)
        
        self.contact_person_edit = QLineEdit()
        self.contact_person_edit.setPlaceholderText(tr("purchases.contact_person"))
        info_layout.addRow(create_dark_label(tr("purchases.contact_person") + ":"), self.contact_person_edit)
        
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText(tr("purchases.phone"))
        info_layout.addRow(create_dark_label(tr("purchases.phone") + ":"), self.phone_edit)
        
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText(tr("purchases.email"))
        info_layout.addRow(create_dark_label(tr("purchases.email") + ":"), self.email_edit)
        
        self.address_edit = QTextEdit()
        self.address_edit.setPlaceholderText(tr("purchases.address"))
        self.address_edit.setMaximumHeight(80)
        info_layout.addRow(create_dark_label(tr("purchases.address") + ":"), self.address_edit)
        
        self.tax_number_edit = QLineEdit()
        self.tax_number_edit.setPlaceholderText(tr("purchases.tax_number"))
        info_layout.addRow(create_dark_label(tr("purchases.tax_number") + ":"), self.tax_number_edit)
        
        layout.addWidget(info_group)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def load_supplier_data(self):
        """Load supplier data into form"""
        if self.supplier:
            self.name_edit.setText(self.supplier.name or "")
            self.contact_person_edit.setText(self.supplier.contact_person or "")
            self.phone_edit.setText(self.supplier.phone or "")
            self.email_edit.setText(self.supplier.email or "")
            self.address_edit.setText(self.supplier.address or "")
            self.tax_number_edit.setText(self.supplier.tax_number or "")
    
    def get_supplier_data(self):
        """Get supplier data from form"""
        return {
            'name': self.name_edit.text().strip(),
            'contact_person': self.contact_person_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'address': self.address_edit.toPlainText().strip(),
            'tax_number': self.tax_number_edit.text().strip()
        }
    
    def validate_data(self, data):
        """Validate supplier data"""
        if not data['name']:
            QMessageBox.warning(self, tr("common.warning"), tr("purchases.supplier_name_required"))
            return False
        return True


class PurchaseDialog(QDialog):
    """Dialog for creating/editing purchases"""
    
    def __init__(self, purchase=None, parent=None):
        super().__init__(parent)
        self.purchase = purchase
        self.setWindowTitle(tr("purchases.edit_purchase") if purchase else tr("purchases.add_purchase"))
        self.setModal(True)
        self.resize(800, 600)

        # Apply dialog styling for better contrast
        self.setStyleSheet("""
            QDialog {
                background-color: #2c3e50;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff !important;
                font-weight: 700;
                font-size: 13px;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                background-color: #ffffff;
                color: #000000;
                border: 2px solid #34495e;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
            QGroupBox {
                color: #ffffff;
                font-weight: 700;
                border: 2px solid #34495e;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                color: #ffffff;
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #3498db;
                color: #ffffff;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QTableWidget {
                background-color: #ffffff;
                color: #000000;
                border: 2px solid #34495e;
                border-radius: 4px;
            }
        """)

        self.init_ui()

        if purchase:
            self.load_purchase_data()

    def load_purchase_data(self):
        """Load purchase data into form"""
        if self.purchase:
            # Set supplier
            for i in range(self.supplier_combo.count()):
                if self.supplier_combo.itemData(i) == self.purchase.supplier_id:
                    self.supplier_combo.setCurrentIndex(i)
                    break

            # Set date
            from PyQt5.QtCore import QDate
            date = QDate.fromString(self.purchase.purchase_date, "yyyy-MM-dd")
            self.date_edit.setDate(date)

            # Set notes
            if self.purchase.notes:
                self.notes_edit.setText(self.purchase.notes)

            # Load items
            items = self.purchase.get_items()
            for item in items:
                product = item.get_product()
                if product:
                    item_data = {
                        'product_id': item.product_id,
                        'product_name': product.name,
                        'quantity': item.quantity,
                        'unit_cost': item.unit_cost,
                        'total_cost': item.total_cost
                    }
                    self.items.append(item_data)

            self.update_items_table()
            self.update_total()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # Purchase header
        header_group = QGroupBox(tr("purchases.purchase_info"))
        header_layout = QFormLayout(header_group)
        
        # Supplier selection
        self.supplier_combo = QComboBox()
        self.load_suppliers()
        header_layout.addRow(create_dark_label(tr("purchases.supplier") + ":"), self.supplier_combo)
        
        # Purchase date
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        header_layout.addRow(create_dark_label(tr("purchases.purchase_date") + ":"), self.date_edit)
        
        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(60)
        self.notes_edit.setPlaceholderText(tr("purchases.notes"))
        header_layout.addRow(create_dark_label(tr("purchases.notes") + ":"), self.notes_edit)
        
        layout.addWidget(header_group)
        
        # Purchase items
        items_group = QGroupBox(tr("purchases.purchase_items"))
        items_layout = QVBoxLayout(items_group)
        
        # Add item controls
        add_item_layout = QHBoxLayout()
        
        self.product_combo = QComboBox()
        self.load_products()
        add_item_layout.addWidget(QLabel(tr("purchases.product") + ":"))
        add_item_layout.addWidget(self.product_combo)
        
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(9999)
        self.quantity_spin.setValue(1)
        add_item_layout.addWidget(QLabel(tr("purchases.quantity") + ":"))
        add_item_layout.addWidget(self.quantity_spin)
        
        self.unit_cost_spin = QDoubleSpinBox()
        self.unit_cost_spin.setMinimum(0.01)
        self.unit_cost_spin.setMaximum(999999.99)
        self.unit_cost_spin.setDecimals(2)
        add_item_layout.addWidget(QLabel(tr("purchases.unit_cost") + ":"))
        add_item_layout.addWidget(self.unit_cost_spin)
        
        self.add_item_button = QPushButton(tr("purchases.add_item"))
        self.add_item_button.clicked.connect(self.add_item)
        add_item_layout.addWidget(self.add_item_button)
        
        items_layout.addLayout(add_item_layout)
        
        # Items table
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            tr("purchases.product"),
            tr("purchases.quantity"),
            tr("purchases.unit_cost"),
            tr("purchases.total_cost"),
            tr("common.actions")
        ])
        
        header = self.items_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.items_table.setAlternatingRowColors(True)
        
        items_layout.addWidget(self.items_table)
        
        # Totals
        totals_layout = QHBoxLayout()
        totals_layout.addStretch()
        
        self.total_label = QLabel(tr("purchases.total") + ": $0.00")
        self.total_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.total_label.setStyleSheet("color: #000000; font-weight: 800;")
        totals_layout.addWidget(self.total_label)
        
        items_layout.addLayout(totals_layout)
        layout.addWidget(items_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton(tr("common.save"))
        self.save_button.clicked.connect(self.save_purchase)
        button_layout.addWidget(self.save_button)
        
        self.cancel_button = QPushButton(tr("common.cancel"))
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # Initialize items list
        self.items = []
    
    def load_suppliers(self):
        """Load suppliers into combo box"""
        self.supplier_combo.clear()
        suppliers = Supplier.get_all()
        
        for supplier in suppliers:
            self.supplier_combo.addItem(supplier.name, supplier.id)
    
    def load_products(self):
        """Load products into combo box"""
        self.product_combo.clear()
        products = Product.get_all()
        
        for product in products:
            self.product_combo.addItem(f"{product.name} - {product.sku}", product.id)
    
    def add_item(self):
        """Add item to purchase"""
        if self.product_combo.currentData() is None:
            QMessageBox.warning(self, tr("common.warning"), tr("purchases.select_product"))
            return
        
        product_id = self.product_combo.currentData()
        product_name = self.product_combo.currentText()
        quantity = self.quantity_spin.value()
        unit_cost = self.unit_cost_spin.value()
        total_cost = quantity * unit_cost
        
        # Add to items list
        item = {
            'product_id': product_id,
            'product_name': product_name,
            'quantity': quantity,
            'unit_cost': unit_cost,
            'total_cost': total_cost
        }
        
        self.items.append(item)
        self.update_items_table()
        self.update_total()
        
        # Reset form
        self.quantity_spin.setValue(1)
        self.unit_cost_spin.setValue(0.00)
    
    def update_items_table(self):
        """Update items table"""
        self.items_table.setRowCount(len(self.items))
        
        for row, item in enumerate(self.items):
            self.items_table.setItem(row, 0, QTableWidgetItem(item['product_name']))
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item['quantity'])))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"${item['unit_cost']:.2f}"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"${item['total_cost']:.2f}"))
            
            # Remove button
            remove_button = QPushButton(tr("common.remove"))
            remove_button.clicked.connect(lambda checked, r=row: self.remove_item(r))
            self.items_table.setCellWidget(row, 4, remove_button)
    
    def remove_item(self, row):
        """Remove item from purchase"""
        if 0 <= row < len(self.items):
            self.items.pop(row)
            self.update_items_table()
            self.update_total()
    
    def update_total(self):
        """Update total amount"""
        total = sum(item['total_cost'] for item in self.items)
        self.total_label.setText(f"{tr('purchases.total')}: ${total:.2f}")
    
    def save_purchase(self):
        """Save purchase"""
        if self.supplier_combo.currentData() is None:
            QMessageBox.warning(self, tr("common.warning"), tr("purchases.select_supplier"))
            return
        
        if not self.items:
            QMessageBox.warning(self, tr("common.warning"), tr("purchases.add_items"))
            return
        
        try:
            supplier_id = self.supplier_combo.currentData()
            purchase_date = self.date_edit.date().toString("yyyy-MM-dd")
            notes = self.notes_edit.toPlainText().strip()
            
            if self.purchase:
                # Update existing purchase
                purchase = self.purchase
            else:
                # Create new purchase
                purchase = Purchase.create(
                    supplier_id=supplier_id,
                    purchase_date=purchase_date,
                    user_id=1,  # TODO: Get current user ID
                    notes=notes
                )
            
            # Add items
            for item in self.items:
                purchase.add_item(
                    product_id=item['product_id'],
                    quantity=item['quantity'],
                    unit_cost=item['unit_cost']
                )
            
            QMessageBox.information(self, tr("common.success"), tr("purchases.purchase_saved"))
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"Error saving purchase: {e}")


class PurchasesWidget(QWidget):
    """Main purchases management widget"""
    
    purchase_created = pyqtSignal(object)
    purchase_updated = pyqtSignal(object)
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("📥 " + tr("purchases.title"))
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title_label.setStyleSheet("color: #000000; font-weight: 800; font-size: 18px;")
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        
        # Purchases tab
        self.purchases_tab = self.create_purchases_tab()
        self.tab_widget.addTab(self.purchases_tab, "📥 " + tr("purchases.purchases"))
        
        # Suppliers tab
        self.suppliers_tab = self.create_suppliers_tab()
        self.tab_widget.addTab(self.suppliers_tab, "🏢 " + tr("purchases.suppliers"))
        
        layout.addWidget(self.tab_widget)
    
    def create_purchases_tab(self):
        """Create purchases management tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Toolbar
        toolbar_layout = QHBoxLayout()
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(tr("purchases.search_purchases"))
        self.search_edit.textChanged.connect(self.search_purchases)
        toolbar_layout.addWidget(self.search_edit)
        
        # Add purchase button
        self.add_purchase_button = QPushButton("➕ " + tr("purchases.add_purchase"))
        self.add_purchase_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.add_purchase_button.clicked.connect(self.add_purchase)
        toolbar_layout.addWidget(self.add_purchase_button)
        
        toolbar_layout.addStretch()
        layout.addLayout(toolbar_layout)
        
        # Purchases table
        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(7)
        self.purchases_table.setHorizontalHeaderLabels([
            tr("purchases.purchase_number"),
            tr("purchases.supplier"),
            tr("purchases.purchase_date"),
            tr("purchases.total_amount"),
            tr("purchases.status"),
            tr("purchases.created_at"),
            tr("common.actions")
        ])
        
        header = self.purchases_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.purchases_table.setAlternatingRowColors(True)
        self.purchases_table.setSelectionBehavior(self.purchases_table.SelectRows)
        
        layout.addWidget(self.purchases_table)
        
        return widget
    
    def create_suppliers_tab(self):
        """Create suppliers management tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Toolbar
        toolbar_layout = QHBoxLayout()
        
        # Search
        self.suppliers_search_edit = QLineEdit()
        self.suppliers_search_edit.setPlaceholderText(tr("purchases.search_suppliers"))
        self.suppliers_search_edit.textChanged.connect(self.search_suppliers)
        toolbar_layout.addWidget(self.suppliers_search_edit)
        
        # Add supplier button
        self.add_supplier_button = QPushButton("➕ " + tr("purchases.add_supplier"))
        self.add_supplier_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        self.add_supplier_button.clicked.connect(self.add_supplier)
        toolbar_layout.addWidget(self.add_supplier_button)
        
        toolbar_layout.addStretch()
        layout.addLayout(toolbar_layout)
        
        # Suppliers table
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(6)
        self.suppliers_table.setHorizontalHeaderLabels([
            tr("purchases.supplier_name"),
            tr("purchases.contact_person"),
            tr("purchases.phone"),
            tr("purchases.email"),
            tr("purchases.status"),
            tr("common.actions")
        ])
        
        header = self.suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSelectionBehavior(self.suppliers_table.SelectRows)
        
        layout.addWidget(self.suppliers_table)
        
        return widget
    
    def load_data(self):
        """Load all data"""
        self.load_purchases()
        self.load_suppliers()
    
    def load_purchases(self):
        """Load purchases into table"""
        purchases = Purchase.get_all()
        self.populate_purchases_table(purchases)
    
    def load_suppliers(self):
        """Load suppliers into table"""
        suppliers = Supplier.get_all()
        self.populate_suppliers_table(suppliers)
    
    def populate_purchases_table(self, purchases):
        """Populate purchases table"""
        self.purchases_table.setRowCount(len(purchases))
        
        for row, purchase in enumerate(purchases):
            supplier = purchase.get_supplier()
            supplier_name = supplier.name if supplier else "Unknown"
            
            self.purchases_table.setItem(row, 0, QTableWidgetItem(purchase.purchase_number))
            self.purchases_table.setItem(row, 1, QTableWidgetItem(supplier_name))
            self.purchases_table.setItem(row, 2, QTableWidgetItem(purchase.purchase_date))
            self.purchases_table.setItem(row, 3, QTableWidgetItem(f"${purchase.net_amount:.2f}"))
            self.purchases_table.setItem(row, 4, QTableWidgetItem(purchase.status.title()))
            self.purchases_table.setItem(row, 5, QTableWidgetItem(purchase.created_at))
            
            # Actions
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 0, 5, 0)
            
            view_button = QPushButton("👁️")
            view_button.setToolTip(tr("common.view"))
            view_button.clicked.connect(lambda checked, p=purchase: self.view_purchase(p))
            actions_layout.addWidget(view_button)
            
            if purchase.status == 'pending':
                receive_button = QPushButton("📦")
                receive_button.setToolTip(tr("purchases.receive"))
                receive_button.clicked.connect(lambda checked, p=purchase: self.receive_purchase(p))
                actions_layout.addWidget(receive_button)
            
            self.purchases_table.setCellWidget(row, 6, actions_widget)
            
            # Store purchase ID
            self.purchases_table.item(row, 0).setData(Qt.UserRole, purchase.id)
    
    def populate_suppliers_table(self, suppliers):
        """Populate suppliers table"""
        self.suppliers_table.setRowCount(len(suppliers))
        
        for row, supplier in enumerate(suppliers):
            self.suppliers_table.setItem(row, 0, QTableWidgetItem(supplier.name))
            self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier.contact_person or ""))
            self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier.phone or ""))
            self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier.email or ""))
            
            status = "Active" if supplier.is_active else "Inactive"
            self.suppliers_table.setItem(row, 4, QTableWidgetItem(status))
            
            # Actions
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 0, 5, 0)
            
            edit_button = QPushButton("✏️")
            edit_button.setToolTip(tr("common.edit"))
            edit_button.clicked.connect(lambda checked, s=supplier: self.edit_supplier(s))
            actions_layout.addWidget(edit_button)
            
            self.suppliers_table.setCellWidget(row, 5, actions_widget)
            
            # Store supplier ID
            self.suppliers_table.item(row, 0).setData(Qt.UserRole, supplier.id)
    
    def add_purchase(self):
        """Add new purchase"""
        dialog = PurchaseDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_purchases()
    
    def add_supplier(self):
        """Add new supplier"""
        dialog = SupplierDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_supplier_data()
            if dialog.validate_data(data):
                try:
                    Supplier.create(**data)
                    QMessageBox.information(self, tr("common.success"), tr("purchases.supplier_created"))
                    self.load_suppliers()
                except Exception as e:
                    QMessageBox.critical(self, tr("common.error"), f"Error creating supplier: {e}")
    
    def edit_supplier(self, supplier):
        """Edit supplier"""
        dialog = SupplierDialog(supplier, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_supplier_data()
            if dialog.validate_data(data):
                try:
                    for key, value in data.items():
                        setattr(supplier, key, value)
                    supplier.update()
                    QMessageBox.information(self, tr("common.success"), tr("purchases.supplier_updated"))
                    self.load_suppliers()
                except Exception as e:
                    QMessageBox.critical(self, tr("common.error"), f"Error updating supplier: {e}")
    
    def view_purchase(self, purchase):
        """View purchase details"""
        # TODO: Implement purchase view dialog
        QMessageBox.information(self, tr("common.info"), f"Purchase: {purchase.purchase_number}")
    
    def receive_purchase(self, purchase):
        """Mark purchase as received"""
        reply = QMessageBox.question(
            self, tr("common.confirm"),
            tr("purchases.confirm_receive").format(purchase.purchase_number),
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                purchase.update_status('received')
                QMessageBox.information(self, tr("common.success"), tr("purchases.purchase_received"))
                self.load_purchases()
            except Exception as e:
                QMessageBox.critical(self, tr("common.error"), f"Error receiving purchase: {e}")
    
    def search_purchases(self):
        """Search purchases"""
        query = self.search_edit.text().strip()
        if query:
            purchases = Purchase.search(query)
        else:
            purchases = Purchase.get_all()
        
        self.populate_purchases_table(purchases)
    
    def search_suppliers(self):
        """Search suppliers"""
        query = self.suppliers_search_edit.text().strip()
        if query:
            suppliers = Supplier.search(query)
        else:
            suppliers = Supplier.get_all()
        
        self.populate_suppliers_table(suppliers)
