"""
Test Purchases UI Components
Tests the graphical user interface for purchases management
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from views.purchases_widget import PurchasesWidget, SupplierDialog, PurchaseDialog
from models.purchase import Supplier, Purchase
from models.product import Product

def test_purchases_widget():
    """Test the main purchases widget"""
    print("🖥️ Testing Purchases Widget...")
    
    try:
        # Create the widget
        widget = PurchasesWidget()
        
        # Check if widget was created
        if widget:
            print("✅ PurchasesWidget created successfully")
            
            # Check if tabs exist
            if hasattr(widget, 'tab_widget'):
                tab_count = widget.tab_widget.count()
                print(f"✅ Found {tab_count} tabs in widget")
                
                # Check tab names
                for i in range(tab_count):
                    tab_text = widget.tab_widget.tabText(i)
                    print(f"   - Tab {i+1}: {tab_text}")
            
            # Check if tables exist
            if hasattr(widget, 'purchases_table'):
                print("✅ Purchases table exists")
                column_count = widget.purchases_table.columnCount()
                print(f"   - Columns: {column_count}")
            
            if hasattr(widget, 'suppliers_table'):
                print("✅ Suppliers table exists")
                column_count = widget.suppliers_table.columnCount()
                print(f"   - Columns: {column_count}")
            
            # Test loading data
            try:
                widget.load_data()
                print("✅ Data loading successful")
                
                # Check purchases table data
                purchases_rows = widget.purchases_table.rowCount()
                print(f"   - Purchases table has {purchases_rows} rows")
                
                # Check suppliers table data
                suppliers_rows = widget.suppliers_table.rowCount()
                print(f"   - Suppliers table has {suppliers_rows} rows")
                
            except Exception as e:
                print(f"❌ Data loading failed: {e}")
                return False
            
            return True
        else:
            print("❌ Failed to create PurchasesWidget")
            return False
            
    except Exception as e:
        print(f"❌ PurchasesWidget test failed: {e}")
        return False

def test_supplier_dialog():
    """Test the supplier dialog"""
    print("\n🏢 Testing Supplier Dialog...")
    
    try:
        # Test creating new supplier dialog
        dialog = SupplierDialog()
        
        if dialog:
            print("✅ SupplierDialog created successfully")
            
            # Check if form fields exist
            if hasattr(dialog, 'name_edit'):
                print("✅ Name field exists")
            if hasattr(dialog, 'contact_person_edit'):
                print("✅ Contact person field exists")
            if hasattr(dialog, 'phone_edit'):
                print("✅ Phone field exists")
            if hasattr(dialog, 'email_edit'):
                print("✅ Email field exists")
            if hasattr(dialog, 'address_edit'):
                print("✅ Address field exists")
            if hasattr(dialog, 'tax_number_edit'):
                print("✅ Tax number field exists")
            
            # Test with existing supplier
            suppliers = Supplier.get_all()
            if suppliers:
                existing_supplier = suppliers[0]
                edit_dialog = SupplierDialog(existing_supplier)
                
                if edit_dialog:
                    print("✅ Edit supplier dialog created successfully")
                    
                    # Check if data is loaded
                    if edit_dialog.name_edit.text() == existing_supplier.name:
                        print("✅ Supplier data loaded correctly")
                    else:
                        print("❌ Supplier data not loaded correctly")
                        return False
            
            return True
        else:
            print("❌ Failed to create SupplierDialog")
            return False
            
    except Exception as e:
        print(f"❌ SupplierDialog test failed: {e}")
        return False

def test_purchase_dialog():
    """Test the purchase dialog"""
    print("\n📥 Testing Purchase Dialog...")
    
    try:
        # Test creating new purchase dialog
        dialog = PurchaseDialog()
        
        if dialog:
            print("✅ PurchaseDialog created successfully")
            
            # Check if form fields exist
            if hasattr(dialog, 'supplier_combo'):
                print("✅ Supplier combo box exists")
                supplier_count = dialog.supplier_combo.count()
                print(f"   - Found {supplier_count} suppliers in combo")
            
            if hasattr(dialog, 'date_edit'):
                print("✅ Date field exists")
            
            if hasattr(dialog, 'notes_edit'):
                print("✅ Notes field exists")
            
            if hasattr(dialog, 'product_combo'):
                print("✅ Product combo box exists")
                product_count = dialog.product_combo.count()
                print(f"   - Found {product_count} products in combo")
            
            if hasattr(dialog, 'quantity_spin'):
                print("✅ Quantity spinner exists")
            
            if hasattr(dialog, 'unit_cost_spin'):
                print("✅ Unit cost spinner exists")
            
            if hasattr(dialog, 'items_table'):
                print("✅ Items table exists")
                column_count = dialog.items_table.columnCount()
                print(f"   - Columns: {column_count}")
            
            if hasattr(dialog, 'total_label'):
                print("✅ Total label exists")
            
            # Test with existing purchase
            purchases = Purchase.get_all()
            if purchases:
                existing_purchase = purchases[0]
                edit_dialog = PurchaseDialog(existing_purchase)
                
                if edit_dialog:
                    print("✅ Edit purchase dialog created successfully")
            
            return True
        else:
            print("❌ Failed to create PurchaseDialog")
            return False
            
    except Exception as e:
        print(f"❌ PurchaseDialog test failed: {e}")
        return False

def test_ui_integration():
    """Test UI integration with data"""
    print("\n🔗 Testing UI Integration...")
    
    try:
        # Create widget
        widget = PurchasesWidget()
        
        # Load data
        widget.load_data()
        
        # Check if data is displayed correctly
        purchases_count = widget.purchases_table.rowCount()
        suppliers_count = widget.suppliers_table.rowCount()
        
        # Get actual data counts
        actual_purchases = len(Purchase.get_all())
        actual_suppliers = len(Supplier.get_all())
        
        print(f"📊 Data comparison:")
        print(f"   - Purchases: UI={purchases_count}, DB={actual_purchases}")
        print(f"   - Suppliers: UI={suppliers_count}, DB={actual_suppliers}")
        
        if purchases_count == actual_purchases:
            print("✅ Purchases data matches")
        else:
            print("❌ Purchases data mismatch")
            return False
        
        if suppliers_count == actual_suppliers:
            print("✅ Suppliers data matches")
        else:
            print("❌ Suppliers data mismatch")
            return False
        
        # Test search functionality
        if hasattr(widget, 'search_edit'):
            # Test purchases search
            widget.search_edit.setText("PO2025")
            widget.search_purchases()
            search_results = widget.purchases_table.rowCount()
            print(f"✅ Purchases search returned {search_results} results")
        
        if hasattr(widget, 'suppliers_search_edit'):
            # Test suppliers search
            widget.suppliers_search_edit.setText("General")
            widget.search_suppliers()
            search_results = widget.suppliers_table.rowCount()
            print(f"✅ Suppliers search returned {search_results} results")
        
        return True
        
    except Exception as e:
        print(f"❌ UI integration test failed: {e}")
        return False

def run_ui_tests():
    """Run all UI tests"""
    print("🖥️ STARTING PURCHASES UI TESTS")
    print("=" * 50)
    
    # Create QApplication if it doesn't exist
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # Test 1: Purchases Widget
    test_results.append(("Purchases Widget", test_purchases_widget()))
    
    # Test 2: Supplier Dialog
    test_results.append(("Supplier Dialog", test_supplier_dialog()))
    
    # Test 3: Purchase Dialog
    test_results.append(("Purchase Dialog", test_purchase_dialog()))
    
    # Test 4: UI Integration
    test_results.append(("UI Integration", test_ui_integration()))
    
    # Print results summary
    print("\n" + "=" * 50)
    print("🏁 UI TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL UI TESTS PASSED! Interface is working perfectly!")
    else:
        print("⚠️  Some UI tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_ui_tests()
