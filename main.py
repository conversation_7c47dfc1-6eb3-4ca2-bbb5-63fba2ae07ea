#!/usr/bin/env python3
"""
OnePos - Professional Point of Sale System
Main application entry point

Author: OnePos Development Team
Version: 1.0.0
"""

import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QPixmap, QFont
# qdarkstyle will be imported when needed

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import application modules
from utils.config_manager import config
from models.database import db
from views.main_window import MainWindow
from views.login_widget import LoginWidget
from utils.license_manager import license_manager


class DatabaseInitThread(QThread):
    """Thread for database initialization"""
    finished = pyqtSignal()
    error = pyqtSignal(str)
    
    def run(self):
        try:
            # Database is already initialized in the import
            # This thread is for any additional setup if needed
            self.finished.emit()
        except Exception as e:
            self.error.emit(str(e))


class OnePos(QApplication):
    """Main application class"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # Set application properties
        self.setApplicationName(config.get_app_name())
        self.setApplicationVersion(config.get_app_version())
        self.setOrganizationName("OnePos")
        
        # Set application font with better readability
        font = QFont("Segoe UI", 10, QFont.Medium)
        font.setHintingPreference(QFont.PreferFullHinting)
        font.setStyleStrategy(QFont.PreferAntialias)
        self.setFont(font)
        
        # Initialize variables
        self.main_window = None
        self.splash = None
        self.current_user = None
        
        # Setup application
        self.setup_application()
    
    def setup_application(self):
        """Setup application theme and style"""
        try:
            # Apply beautiful theme
            from utils.beautiful_theme import get_beautiful_theme_stylesheet
            self.setStyleSheet(get_beautiful_theme_stylesheet())
            print("Beautiful theme applied successfully!")

        except Exception as e:
            print(f"Error setting up application theme: {e}")
            # Fallback to basic theme
            self.setStyleSheet(self.get_light_theme_stylesheet())
    
    def get_light_theme_stylesheet(self):
        """Get light theme stylesheet with better contrast"""
        return """
        QMainWindow {
            background-color: #f8f9fa;
            color: #212529;
        }

        QWidget {
            background-color: #ffffff;
            color: #212529;
            font-weight: 500;
        }

        QPushButton {
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 8px 16px;
            color: #212529;
            font-weight: 600;
        }

        QPushButton:hover {
            background-color: #dee2e6;
            color: #000000;
        }

        QPushButton:pressed {
            background-color: #ced4da;
            color: #000000;
        }

        QLineEdit, QTextEdit, QComboBox {
            background-color: #ffffff;
            border: 2px solid #ced4da;
            border-radius: 4px;
            padding: 8px;
            color: #212529;
            font-weight: 500;
        }

        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border: 2px solid #0d6efd;
            background-color: #ffffff;
        }

        QTableWidget {
            background-color: #ffffff;
            alternate-background-color: #f8f9fa;
            gridline-color: #dee2e6;
            color: #212529;
            font-weight: 500;
        }

        QTableWidget::item {
            padding: 8px;
            color: #212529;
        }

        QTableWidget::item:selected {
            background-color: #0d6efd;
            color: #ffffff;
        }

        QLabel {
            color: #212529;
            font-weight: 500;
        }

        QGroupBox {
            color: #212529;
            font-weight: 600;
            border: 2px solid #dee2e6;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #495057;
            font-weight: bold;
        }
        """
    
    def get_custom_stylesheet(self):
        """Get custom application stylesheet with better contrast"""
        return """
        /* Custom styles for OnePos with improved readability */
        .sidebar {
            background-color: #2c3e50;
            border-right: 2px solid #34495e;
        }

        .sidebar QPushButton {
            text-align: left;
            padding: 10px 16px;
            border: none;
            border-radius: 0;
            color: #ffffff;
            background-color: transparent;
            font-weight: 900;
            font-size: 12px;
        }

        .sidebar QPushButton:hover {
            background-color: #34495e;
            color: #ffffff;
        }

        .sidebar QPushButton:checked {
            background-color: #0d6efd;
            color: #ffffff;
            font-weight: bold;
        }

        .header {
            background-color: #34495e;
            color: #ffffff;
            border-bottom: 2px solid #2c3e50;
            font-weight: 600;
        }

        .status-bar {
            background-color: #2c3e50;
            color: #ffffff;
            font-weight: 500;
        }

        .card {
            background-color: #ffffff;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 16px;
            margin: 8px;
            color: #212529;
        }

        .primary-button {
            background-color: #0d6efd;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            padding: 12px 24px;
            font-weight: bold;
            font-size: 12px;
        }

        .primary-button:hover {
            background-color: #0b5ed7;
            color: #ffffff;
        }

        .success-button {
            background-color: #198754;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            padding: 12px 24px;
            font-weight: bold;
            font-size: 12px;
        }

        .success-button:hover {
            background-color: #157347;
            color: #ffffff;
        }

        .danger-button {
            background-color: #dc3545;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            padding: 12px 24px;
            font-weight: bold;
            font-size: 12px;
        }

        .danger-button:hover {
            background-color: #bb2d3b;
            color: #ffffff;
        }

        .warning-button {
            background-color: #fd7e14;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            padding: 12px 24px;
            font-weight: bold;
            font-size: 12px;
        }

        .warning-button:hover {
            background-color: #e85d04;
            color: #ffffff;
        }

        /* Improve text readability in all widgets */
        QSpinBox, QDoubleSpinBox {
            background-color: #ffffff;
            border: 2px solid #ced4da;
            border-radius: 4px;
            padding: 6px;
            color: #212529;
            font-weight: 500;
        }

        QSpinBox:focus, QDoubleSpinBox:focus {
            border: 2px solid #0d6efd;
        }

        QCheckBox {
            color: #212529;
            font-weight: 500;
        }

        QTabWidget::pane {
            border: 2px solid #dee2e6;
            background-color: #ffffff;
        }

        QTabBar::tab {
            background-color: #f8f9fa;
            color: #212529;
            padding: 8px 16px;
            margin-right: 2px;
            font-weight: 600;
        }

        QTabBar::tab:selected {
            background-color: #0d6efd;
            color: #ffffff;
        }

        QTabBar::tab:hover {
            background-color: #e9ecef;
            color: #000000;
        }
        """
    
    def show_splash_screen(self):
        """Show splash screen during application startup"""
        try:
            # Create splash screen
            splash_pixmap = QPixmap(400, 300)
            splash_pixmap.fill(Qt.white)
            
            self.splash = QSplashScreen(splash_pixmap)
            self.splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
            self.splash.show()
            
            # Show loading message
            self.splash.showMessage(
                f"Loading {config.get_app_name()}...",
                Qt.AlignBottom | Qt.AlignCenter,
                Qt.black
            )
            
            # Process events to show splash
            self.processEvents()
            
        except Exception as e:
            print(f"Error showing splash screen: {e}")
    
    def hide_splash_screen(self):
        """Hide splash screen"""
        if self.splash:
            self.splash.close()
            self.splash = None
    
    def show_login_dialog(self):
        """Show login dialog"""
        try:
            print("Showing login dialog...")

            # Hide splash first
            if self.splash:
                print("Hiding splash screen...")
                self.splash.hide()
                self.splash = None

            # Create and show login widget
            print("Creating login widget...")
            self.login_widget = LoginWidget()
            self.login_widget.login_successful.connect(self.handle_successful_login)

            # Show login widget
            print("Showing login widget...")
            self.login_widget.show()
            self.login_widget.raise_()
            self.login_widget.activateWindow()

            return True

        except Exception as e:
            print(f"Error showing login dialog: {e}")
            QMessageBox.critical(None, "Error", f"Login error: {str(e)}")
            return False

    def handle_successful_login(self, user):
        """Handle successful login"""
        try:
            self.current_user = user
            print(f"Login successful for user: {user.username}")

            # Hide login widget and show main window
            if hasattr(self, 'login_widget'):
                self.login_widget.hide()
                self.login_widget.close()

            self.show_main_window()

        except Exception as e:
            print(f"Error in handle_successful_login: {e}")
            QMessageBox.critical(None, "Error", f"Error loading main window: {str(e)}")
            QApplication.quit()

    def show_main_window(self):
        """Show main application window"""
        try:
            if not self.current_user:
                print("Error: No user logged in!")
                return False

            # Check license before showing main window
            if not self.check_license():
                return False

            print(f"Showing main window for user: {self.current_user.username}")
            self.main_window = MainWindow(self.current_user)

            # Set window properties
            window_size = config.get_window_size()
            self.main_window.resize(*window_size)

            if config.is_fullscreen():
                self.main_window.showMaximized()
            else:
                self.main_window.show()

            # Center window on screen
            self.main_window.center_on_screen()

        except Exception as e:
            print(f"Error showing main window: {e}")
            QMessageBox.critical(None, "Error", f"Application error: {str(e)}")
            return False

        return True

    def check_license(self):
        """Check application license"""
        try:
            status = license_manager.get_license_status()

            if status['status'] == 'expired':
                # License expired - force activation
                from views.activation_dialog import ActivationDialog
                dialog = ActivationDialog(None, force_activation=True)
                result = dialog.exec_()

                if result != dialog.Accepted:
                    QMessageBox.critical(None, "تفعيل مطلوب",
                                       "يجب تفعيل التطبيق للمتابعة")
                    return False

                # Check again after activation attempt
                status = license_manager.get_license_status()
                return status['status'] != 'expired'

            return True

        except Exception as e:
            print(f"License check error: {e}")
            QMessageBox.warning(None, "تحذير", f"خطأ في فحص الترخيص: {str(e)}")
            return True  # Allow to continue on error
    
    def run(self):
        """Run the application"""
        try:
            # Show splash screen
            self.show_splash_screen()
            
            # Initialize database in background
            self.init_database()
            
            # Always require login for security
            QTimer.singleShot(2100, self.handle_login)
            
            # Start event loop
            return self.exec_()
            
        except Exception as e:
            print(f"Application error: {e}")
            traceback.print_exc()
            
            QMessageBox.critical(
                None,
                "Critical Error",
                f"A critical error occurred:\n\n{str(e)}\n\nThe application will now exit."
            )
            return 1
    
    def init_database(self):
        """Initialize database"""
        try:
            # Database is already initialized in the import
            # Update splash message
            if self.splash:
                self.splash.showMessage(
                    "Initializing database...",
                    Qt.AlignBottom | Qt.AlignCenter,
                    Qt.black
                )
                self.processEvents()
            
        except Exception as e:
            print(f"Database initialization error: {e}")
            raise
    
    def handle_login(self):
        """Handle login process"""
        print("Starting login process...")
        self.show_login_dialog()

    def skip_login(self):
        """Skip login for development"""
        from models.user import User
        # Get admin user for development
        self.current_user = User.get_by_username('admin')
        if self.current_user:
            self.show_main_window()
        else:
            print("Error: Admin user not found")
            self.quit()
    
    def closeEvent(self, event):
        """Handle application close event"""
        try:
            # Close database connection
            if db:
                db.close()
            
            # Accept the close event
            event.accept()
            
        except Exception as e:
            print(f"Error during application shutdown: {e}")
            event.accept()


def main():
    """Main function"""
    try:
        # Create application instance
        app = OnePos(sys.argv)
        
        # Run application
        exit_code = app.run()
        
        # Exit with proper code
        sys.exit(exit_code)
        
    except Exception as e:
        print(f"Fatal error: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
