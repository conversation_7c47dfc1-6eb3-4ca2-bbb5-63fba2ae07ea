"""
Product model for OnePos POS System
"""

from datetime import datetime
from .database import db


class Category:
    """Product category model"""
    
    def __init__(self, category_id=None, name=None, description=None, parent_id=None, is_active=True):
        self.id = category_id
        self.name = name
        self.description = description
        self.parent_id = parent_id
        self.is_active = is_active
        self.created_at = None
    
    @classmethod
    def create(cls, name, description=None, parent_id=None):
        """Create a new category"""
        category_id = db.execute_update("""
            INSERT INTO categories (name, description, parent_id)
            VALUES (?, ?, ?)
        """, (name, description, parent_id))
        
        return cls.get_by_id(category_id)
    
    @classmethod
    def get_by_id(cls, category_id):
        """Get category by ID"""
        result = db.execute_query("SELECT * FROM categories WHERE id = ?", (category_id,))
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def get_all(cls, active_only=True):
        """Get all categories"""
        query = "SELECT * FROM categories"
        if active_only:
            query += " WHERE is_active = 1"
        query += " ORDER BY name"
        
        results = db.execute_query(query)
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def get_by_parent(cls, parent_id=None, active_only=True):
        """Get categories by parent ID"""
        query = "SELECT * FROM categories WHERE parent_id "
        params = []
        
        if parent_id is None:
            query += "IS NULL"
        else:
            query += "= ?"
            params.append(parent_id)
        
        if active_only:
            query += " AND is_active = 1"
        
        query += " ORDER BY name"
        
        results = db.execute_query(query, params if params else None)
        return [cls._from_db_row(row) for row in results]
    
    def update(self, **kwargs):
        """Update category information"""
        allowed_fields = ['name', 'description', 'parent_id', 'is_active']
        update_fields = []
        params = []
        
        for field, value in kwargs.items():
            if field in allowed_fields:
                update_fields.append(f"{field} = ?")
                params.append(value)
        
        if update_fields:
            params.append(self.id)
            query = f"UPDATE categories SET {', '.join(update_fields)} WHERE id = ?"
            db.execute_update(query, params)
            
            # Refresh object
            updated_category = self.get_by_id(self.id)
            if updated_category:
                self.__dict__.update(updated_category.__dict__)
    
    def delete(self):
        """Soft delete category"""
        self.update(is_active=False)
    
    def get_products_count(self):
        """Get number of products in this category"""
        result = db.execute_query("""
            SELECT COUNT(*) as count FROM products 
            WHERE category_id = ? AND is_active = 1
        """, (self.id,))
        return result[0]['count'] if result else 0
    
    def to_dict(self):
        """Convert category to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'parent_id': self.parent_id,
            'is_active': self.is_active,
            'created_at': self.created_at
        }
    
    @classmethod
    def _from_db_row(cls, row):
        """Create Category object from database row"""
        category = cls(
            category_id=row['id'],
            name=row['name'],
            description=row['description'],
            parent_id=row['parent_id'],
            is_active=bool(row['is_active'])
        )
        category.created_at = row['created_at']
        return category


class Product:
    """Product model"""
    
    def __init__(self, product_id=None, name=None, description=None, barcode=None, sku=None,
                 category_id=None, cost_price=0, selling_price=0, stock_quantity=0,
                 min_stock_level=0, unit='piece', image_path=None, is_active=True):
        self.id = product_id
        self.name = name
        self.description = description
        self.barcode = barcode
        self.sku = sku
        self.category_id = category_id
        self.cost_price = cost_price
        self.selling_price = selling_price
        self.stock_quantity = stock_quantity
        self.min_stock_level = min_stock_level
        self.unit = unit
        self.image_path = image_path
        self.is_active = is_active
        self.created_at = None
        self.updated_at = None
    
    @classmethod
    def create(cls, name, selling_price, **kwargs):
        """Create a new product"""
        # Generate barcode if not provided
        barcode = kwargs.get('barcode')
        if not barcode:
            barcode = cls._generate_barcode()
        
        # Generate SKU if not provided
        sku = kwargs.get('sku')
        if not sku:
            sku = cls._generate_sku(name)
        
        product_id = db.execute_update("""
            INSERT INTO products (name, description, barcode, sku, category_id, cost_price, 
                                selling_price, stock_quantity, min_stock_level, unit, image_path)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            name,
            kwargs.get('description'),
            barcode,
            sku,
            kwargs.get('category_id'),
            kwargs.get('cost_price', 0),
            selling_price,
            kwargs.get('stock_quantity', 0),
            kwargs.get('min_stock_level', 0),
            kwargs.get('unit', 'piece'),
            kwargs.get('image_path')
        ))

        # Invalidate cache
        db.invalidate_cache("products")
        return cls.get_by_id(product_id)
    
    @classmethod
    def get_by_id(cls, product_id):
        """Get product by ID"""
        result = db.execute_query("SELECT * FROM products WHERE id = ?", (product_id,))
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def get_by_barcode(cls, barcode):
        """Get product by barcode"""
        result = db.execute_query("SELECT * FROM products WHERE barcode = ? AND is_active = 1", (barcode,))
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def get_by_sku(cls, sku):
        """Get product by SKU"""
        result = db.execute_query("SELECT * FROM products WHERE sku = ? AND is_active = 1", (sku,))
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def search(cls, query, category_id=None, active_only=True):
        """Search products by name, barcode, or SKU"""
        sql = """
            SELECT * FROM products 
            WHERE (name LIKE ? OR barcode LIKE ? OR sku LIKE ?)
        """
        params = [f"%{query}%", f"%{query}%", f"%{query}%"]
        
        if category_id:
            sql += " AND category_id = ?"
            params.append(category_id)
        
        if active_only:
            sql += " AND is_active = 1"
        
        sql += " ORDER BY name"
        
        results = db.execute_query(sql, params)
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def get_all(cls, category_id=None, active_only=True):
        """Get all products"""
        query = "SELECT * FROM products"
        params = []
        conditions = []
        
        if category_id:
            conditions.append("category_id = ?")
            params.append(category_id)
        
        if active_only:
            conditions.append("is_active = 1")
        
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        query += " ORDER BY name"
        
        results = db.execute_query_with_cache(query, tuple(params) if params else (), "products")
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def get_low_stock(cls, threshold=None):
        """Get products with low stock"""
        if threshold is None:
            query = "SELECT * FROM products WHERE stock_quantity <= min_stock_level AND is_active = 1"
            params = None
        else:
            query = "SELECT * FROM products WHERE stock_quantity <= ? AND is_active = 1"
            params = (threshold,)
        
        results = db.execute_query(query, params)
        return [cls._from_db_row(row) for row in results]
    
    def update(self, **kwargs):
        """Update product information"""
        allowed_fields = ['name', 'description', 'barcode', 'sku', 'category_id', 
                         'cost_price', 'selling_price', 'stock_quantity', 'min_stock_level',
                         'unit', 'image_path', 'is_active']
        update_fields = []
        params = []
        
        for field, value in kwargs.items():
            if field in allowed_fields:
                update_fields.append(f"{field} = ?")
                params.append(value)
        
        if update_fields:
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(self.id)
            
            query = f"UPDATE products SET {', '.join(update_fields)} WHERE id = ?"
            db.execute_update(query, params)

            # Invalidate cache
            db.invalidate_cache("products")

            # Refresh object
            updated_product = self.get_by_id(self.id)
            if updated_product:
                self.__dict__.update(updated_product.__dict__)
    
    def adjust_stock(self, quantity, movement_type, reference_id=None, reference_type=None, notes=None, user_id=None):
        """Adjust product stock and record movement"""
        new_quantity = self.stock_quantity + quantity
        
        # Update product stock
        self.update(stock_quantity=new_quantity)
        
        # Record stock movement
        from .stock import StockMovement
        StockMovement.create(
            product_id=self.id,
            movement_type=movement_type,
            quantity=quantity,
            reference_id=reference_id,
            reference_type=reference_type,
            notes=notes,
            user_id=user_id
        )
    
    def delete(self):
        """Soft delete product"""
        self.update(is_active=False)
    
    def get_category(self):
        """Get product category"""
        if self.category_id:
            return Category.get_by_id(self.category_id)
        return None
    
    def get_profit_margin(self):
        """Calculate profit margin percentage"""
        if self.cost_price > 0:
            return ((self.selling_price - self.cost_price) / self.cost_price) * 100
        return 0
    
    def is_low_stock(self):
        """Check if product is low on stock"""
        return self.stock_quantity <= self.min_stock_level
    
    def to_dict(self):
        """Convert product to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'barcode': self.barcode,
            'sku': self.sku,
            'category_id': self.category_id,
            'cost_price': self.cost_price,
            'selling_price': self.selling_price,
            'stock_quantity': self.stock_quantity,
            'min_stock_level': self.min_stock_level,
            'unit': self.unit,
            'image_path': self.image_path,
            'is_active': self.is_active,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def _generate_barcode(cls):
        """Generate unique barcode"""
        import random
        import string
        
        while True:
            # Generate 13-digit barcode
            barcode = ''.join(random.choices(string.digits, k=13))
            
            # Check if barcode already exists
            existing = cls.get_by_barcode(barcode)
            if not existing:
                return barcode
    
    @classmethod
    def _generate_sku(cls, name):
        """Generate SKU from product name"""
        import re
        
        # Clean name and take first 3 characters
        clean_name = re.sub(r'[^a-zA-Z0-9]', '', name.upper())[:3]
        
        # Get next sequence number
        result = db.execute_query("SELECT COUNT(*) as count FROM products")
        count = result[0]['count'] if result else 0
        
        return f"{clean_name}{count + 1:04d}"
    
    @classmethod
    def _from_db_row(cls, row):
        """Create Product object from database row"""
        product = cls(
            product_id=row['id'],
            name=row['name'],
            description=row['description'],
            barcode=row['barcode'],
            sku=row['sku'],
            category_id=row['category_id'],
            cost_price=row['cost_price'],
            selling_price=row['selling_price'],
            stock_quantity=row['stock_quantity'],
            min_stock_level=row['min_stock_level'],
            unit=row['unit'],
            image_path=row['image_path'],
            is_active=bool(row['is_active'])
        )
        product.created_at = row['created_at']
        product.updated_at = row['updated_at']
        return product
