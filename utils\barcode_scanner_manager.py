"""
نظام إدارة ماسحات الباركود المتقدم - OnePos
Advanced Barcode Scanner Management System
"""

import os
import sys
import platform
import threading
import time
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QThread
from PyQt5.QtWidgets import QMessageBox
from utils.config_manager import config
from utils.translator import tr
import subprocess
import json
# فحص المكتبات المتاحة
try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False
    print("⚠️ مكتبة pyserial غير متاحة - ماسحات Serial معطلة")

try:
    import hid
    HID_AVAILABLE = True
except ImportError:
    HID_AVAILABLE = False
    print("⚠️ مكتبة hidapi غير متاحة - ماسحات USB HID معطلة")

try:
    import bluetooth
    BLUETOOTH_AVAILABLE = True
except ImportError:
    BLUETOOTH_AVAILABLE = False
    print("⚠️ مكتبة pybluez غير متاحة - ماسحات Bluetooth معطلة")

try:
    import cv2
    from pyzbar import pyzbar
    CAMERA_AVAILABLE = True
except (ImportError, FileNotFoundError, OSError) as e:
    CAMERA_AVAILABLE = False
    print(f"⚠️ مكتبات الكاميرا غير متاحة - ماسح الكاميرا معطل: {e}")

try:
    import wmi
    WMI_AVAILABLE = True
except ImportError:
    WMI_AVAILABLE = False
    print("⚠️ مكتبة WMI غير متاحة - كشف USB محدود")

class BarcodeScannerManager(QObject):
    """مدير ماسحات الباركود المتقدم"""
    
    scanner_connected = pyqtSignal(str)  # عند اتصال ماسح
    scanner_disconnected = pyqtSignal(str)  # عند انقطاع ماسح
    barcode_scanned = pyqtSignal(str)  # عند مسح باركود
    
    def __init__(self):
        super().__init__()
        self.available_scanners = {}
        self.usb_scanners = {}
        self.serial_scanners = {}
        self.bluetooth_scanners = {}
        self.active_scanner = None
        self.scanner_thread = None
        self.is_scanning = False
        
        # إعدادات الماسح
        self.scan_timeout = 5000  # 5 ثوان
        self.auto_detect = True
        self.scan_mode = 'continuous'  # continuous, single, manual
        
        # تحديث قائمة الماسحات
        self.refresh_scanners()
        
        # تحميل الإعدادات المحفوظة
        self.load_scanner_settings()
        
        # بدء المراقبة التلقائية
        self.start_auto_detection()
    
    def refresh_scanners(self):
        """تحديث قائمة ماسحات الباركود المتاحة"""
        try:
            self.available_scanners.clear()
            self.usb_scanners.clear()
            self.serial_scanners.clear()
            self.bluetooth_scanners.clear()
            
            # البحث عن ماسحات USB
            self.detect_usb_scanners()
            
            # البحث عن ماسحات Serial/COM
            self.detect_serial_scanners()
            
            # البحث عن ماسحات Bluetooth
            self.detect_bluetooth_scanners()
            
            # البحث عن ماسحات الكاميرا
            self.detect_camera_scanners()
            
            print(f"✅ تم العثور على {len(self.available_scanners)} ماسح باركود")
            print(f"📱 منها {len(self.usb_scanners)} ماسح USB")
            print(f"🔌 منها {len(self.serial_scanners)} ماسح Serial")
            print(f"📶 منها {len(self.bluetooth_scanners)} ماسح Bluetooth")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحديث ماسحات الباركود: {e}")
            return False
    
    def detect_usb_scanners(self):
        """كشف ماسحات USB"""
        try:
            # قائمة معرفات ماسحات الباركود المعروفة
            known_scanner_ids = [
                # Honeywell
                ('0c2e', '0200'), ('0c2e', '0890'), ('0c2e', '0b00'),
                # Symbol/Zebra
                ('05e0', '1200'), ('05e0', '1300'), ('05e0', '1900'),
                # Datalogic
                ('05f9', '1204'), ('05f9', '4204'), ('05f9', '4304'),
                # Code Corporation
                ('11f6', '0100'), ('11f6', '0200'), ('11f6', '0300'),
                # Opticon
                ('065a', '0001'), ('065a', '0009'), ('065a', '0011'),
                # Generic HID scanners
                ('ffff', 'ffff'), ('0000', '0000')
            ]
            
            if platform.system() == "Windows":
                # استخدام WMI للبحث عن أجهزة USB
                try:
                    if not WMI_AVAILABLE:
                        raise ImportError("WMI not available")
                    import wmi
                    c = wmi.WMI()
                    
                    for device in c.Win32_PnPEntity():
                        if device.DeviceID and 'USB' in device.DeviceID:
                            device_name = device.Name or "Unknown USB Device"
                            
                            # فحص إذا كان ماسح باركود
                            if self.is_barcode_scanner_device(device_name, device.DeviceID):
                                scanner_id = f"usb_{len(self.usb_scanners)}"
                                
                                scanner_data = {
                                    'id': scanner_id,
                                    'name': device_name,
                                    'type': 'USB',
                                    'device_id': device.DeviceID,
                                    'status': 'متاح',
                                    'connection': 'USB HID',
                                    'manufacturer': self.extract_manufacturer(device_name),
                                    'model': self.extract_model(device_name)
                                }
                                
                                self.usb_scanners[scanner_id] = scanner_data
                                self.available_scanners[scanner_id] = scanner_data
                
                except ImportError:
                    print("⚠️ مكتبة WMI غير متاحة، استخدام طريقة بديلة")
                    self.detect_usb_scanners_alternative()
            
            else:
                # Linux/Mac - استخدام lsusb أو system_profiler
                self.detect_usb_scanners_unix()
                
        except Exception as e:
            print(f"خطأ في كشف ماسحات USB: {e}")
    
    def detect_usb_scanners_alternative(self):
        """طريقة بديلة لكشف ماسحات USB"""
        try:
            if not HID_AVAILABLE:
                return
            # محاولة قراءة أجهزة HID
            import hid
            
            for device in hid.enumerate():
                device_name = f"{device['manufacturer_string']} {device['product_string']}"
                
                if self.is_barcode_scanner_device(device_name, ""):
                    scanner_id = f"usb_hid_{len(self.usb_scanners)}"
                    
                    scanner_data = {
                        'id': scanner_id,
                        'name': device_name,
                        'type': 'USB HID',
                        'vendor_id': device['vendor_id'],
                        'product_id': device['product_id'],
                        'status': 'متاح',
                        'connection': 'USB HID',
                        'manufacturer': device['manufacturer_string'],
                        'model': device['product_string']
                    }
                    
                    self.usb_scanners[scanner_id] = scanner_data
                    self.available_scanners[scanner_id] = scanner_data
                    
        except ImportError:
            print("⚠️ مكتبة HID غير متاحة")
        except Exception as e:
            print(f"خطأ في الطريقة البديلة لكشف USB: {e}")
    
    def detect_serial_scanners(self):
        """كشف ماسحات Serial/COM"""
        try:
            if not SERIAL_AVAILABLE:
                return

            ports = serial.tools.list_ports.comports()
            
            for port in ports:
                port_name = port.device
                port_description = port.description or "Unknown Serial Device"
                
                # فحص إذا كان ماسح باركود
                if self.is_barcode_scanner_device(port_description, port_name):
                    scanner_id = f"serial_{port_name.replace('/', '_').replace('\\', '_')}"
                    
                    scanner_data = {
                        'id': scanner_id,
                        'name': f"{port_description} ({port_name})",
                        'type': 'Serial',
                        'port': port_name,
                        'description': port_description,
                        'status': 'متاح',
                        'connection': 'Serial/COM',
                        'manufacturer': self.extract_manufacturer(port_description),
                        'model': self.extract_model(port_description),
                        'baudrate': 9600,  # افتراضي
                        'timeout': 1
                    }
                    
                    self.serial_scanners[scanner_id] = scanner_data
                    self.available_scanners[scanner_id] = scanner_data
                    
        except Exception as e:
            print(f"خطأ في كشف ماسحات Serial: {e}")
    
    def detect_bluetooth_scanners(self):
        """كشف ماسحات Bluetooth"""
        try:
            # محاولة استخدام مكتبة bluetooth
            try:
                if not BLUETOOTH_AVAILABLE:
                    return
                import bluetooth
                
                nearby_devices = bluetooth.discover_devices(lookup_names=True)
                
                for addr, name in nearby_devices:
                    if self.is_barcode_scanner_device(name, addr):
                        scanner_id = f"bluetooth_{addr.replace(':', '_')}"
                        
                        scanner_data = {
                            'id': scanner_id,
                            'name': f"{name} ({addr})",
                            'type': 'Bluetooth',
                            'address': addr,
                            'device_name': name,
                            'status': 'متاح',
                            'connection': 'Bluetooth',
                            'manufacturer': self.extract_manufacturer(name),
                            'model': self.extract_model(name)
                        }
                        
                        self.bluetooth_scanners[scanner_id] = scanner_data
                        self.available_scanners[scanner_id] = scanner_data
                        
            except ImportError:
                print("⚠️ مكتبة Bluetooth غير متاحة")
                
        except Exception as e:
            print(f"خطأ في كشف ماسحات Bluetooth: {e}")
    
    def detect_camera_scanners(self):
        """كشف كاميرات للمسح"""
        try:
            if not CAMERA_AVAILABLE:
                return

            # إضافة خيار الكاميرا كماسح باركود
            scanner_id = "camera_0"

            scanner_data = {
                'id': scanner_id,
                'name': 'كاميرا الجهاز (مسح باركود)',
                'type': 'Camera',
                'camera_index': 0,
                'status': 'متاح',
                'connection': 'Camera',
                'manufacturer': 'Built-in',
                'model': 'Camera Scanner'
            }

            self.available_scanners[scanner_id] = scanner_data

        except Exception as e:
            print(f"خطأ في إعداد ماسح الكاميرا: {e}")
    
    def is_barcode_scanner_device(self, device_name, device_id):
        """فحص إذا كان الجهاز ماسح باركود"""
        scanner_keywords = [
            'barcode', 'scanner', 'symbol', 'honeywell', 'datalogic',
            'zebra', 'code', 'opticon', 'metrologic', 'psc', 'hand',
            'laser', 'imager', 'ccd', 'scan', 'reader', 'pos',
            'باركود', 'ماسح', 'قارئ'
        ]
        
        device_text = (device_name + " " + device_id).lower()
        
        return any(keyword in device_text for keyword in scanner_keywords)
    
    def extract_manufacturer(self, device_name):
        """استخراج اسم الشركة المصنعة"""
        manufacturers = {
            'honeywell': 'Honeywell',
            'symbol': 'Symbol/Zebra',
            'zebra': 'Zebra',
            'datalogic': 'Datalogic',
            'code': 'Code Corporation',
            'opticon': 'Opticon',
            'metrologic': 'Metrologic',
            'psc': 'PSC'
        }
        
        device_lower = device_name.lower()
        for key, value in manufacturers.items():
            if key in device_lower:
                return value
        
        return 'Unknown'
    
    def extract_model(self, device_name):
        """استخراج موديل الجهاز"""
        # محاولة استخراج رقم الموديل من اسم الجهاز
        import re
        
        # البحث عن أرقام الموديل الشائعة
        model_patterns = [
            r'(\d{4}[A-Z]*)',  # مثل 1900i, 3800g
            r'([A-Z]{2,}\d+)',  # مثل MS916, CR1000
            r'(\w+\d+\w*)'     # أنماط عامة
        ]
        
        for pattern in model_patterns:
            match = re.search(pattern, device_name)
            if match:
                return match.group(1)
        
        return 'Unknown Model'
    
    def get_available_scanners(self):
        """الحصول على قائمة الماسحات المتاحة"""
        return self.available_scanners
    
    def get_usb_scanners(self):
        """الحصول على ماسحات USB فقط"""
        return self.usb_scanners
    
    def get_serial_scanners(self):
        """الحصول على ماسحات Serial فقط"""
        return self.serial_scanners
    
    def get_bluetooth_scanners(self):
        """الحصول على ماسحات Bluetooth فقط"""
        return self.bluetooth_scanners
    
    def set_active_scanner(self, scanner_id):
        """تعيين الماسح النشط"""
        if scanner_id in self.available_scanners:
            self.active_scanner = scanner_id
            config.set('barcode.active_scanner', scanner_id)
            print(f"✅ تم تعيين الماسح النشط: {scanner_id}")
            return True
        return False
    
    def get_active_scanner(self):
        """الحصول على الماسح النشط"""
        return self.active_scanner
    
    def load_scanner_settings(self):
        """تحميل إعدادات الماسحات المحفوظة"""
        try:
            self.active_scanner = config.get('barcode.active_scanner', None)
            self.auto_detect = config.get('barcode.auto_detect', True)
            self.scan_mode = config.get('barcode.scan_mode', 'continuous')
            self.scan_timeout = config.get('barcode.scan_timeout', 5000)
            
            # التحقق من وجود الماسح المحفوظ
            if self.active_scanner and self.active_scanner not in self.available_scanners:
                print(f"⚠️ الماسح المحفوظ غير متاح: {self.active_scanner}")
                self.active_scanner = None
            
            # تعيين ماسح افتراضي إذا لم يكن محدد
            self.auto_assign_scanner()
            
        except Exception as e:
            print(f"خطأ في تحميل إعدادات الماسحات: {e}")
    
    def auto_assign_scanner(self):
        """تعيين ماسح تلقائياً"""
        try:
            if not self.active_scanner and self.available_scanners:
                # أولوية للماسحات USB
                if self.usb_scanners:
                    scanner_id = list(self.usb_scanners.keys())[0]
                    self.set_active_scanner(scanner_id)
                    print(f"🔄 تم تعيين ماسح USB تلقائياً: {scanner_id}")
                
                # ثم Serial
                elif self.serial_scanners:
                    scanner_id = list(self.serial_scanners.keys())[0]
                    self.set_active_scanner(scanner_id)
                    print(f"🔄 تم تعيين ماسح Serial تلقائياً: {scanner_id}")
                
                # ثم Bluetooth
                elif self.bluetooth_scanners:
                    scanner_id = list(self.bluetooth_scanners.keys())[0]
                    self.set_active_scanner(scanner_id)
                    print(f"🔄 تم تعيين ماسح Bluetooth تلقائياً: {scanner_id}")
                
                # أخيراً الكاميرا
                elif 'camera_0' in self.available_scanners:
                    self.set_active_scanner('camera_0')
                    print(f"🔄 تم تعيين ماسح الكاميرا تلقائياً")
            
        except Exception as e:
            print(f"خطأ في التعيين التلقائي للماسح: {e}")
    
    def start_auto_detection(self):
        """بدء المراقبة التلقائية للماسحات"""
        if self.auto_detect:
            self.detection_timer = QTimer()
            self.detection_timer.timeout.connect(self.check_scanners)
            self.detection_timer.start(10000)  # فحص كل 10 ثوان
    
    def check_scanners(self):
        """فحص الماسحات دورياً"""
        try:
            old_count = len(self.available_scanners)
            self.refresh_scanners()
            new_count = len(self.available_scanners)
            
            if new_count != old_count:
                if new_count > old_count:
                    print(f"🔌 تم اكتشاف ماسح جديد")
                    self.scanner_connected.emit("new_scanner")
                else:
                    print(f"🔌 تم فصل ماسح")
                    self.scanner_disconnected.emit("scanner_removed")
                
                # إعادة تعيين الماسح إذا لزم الأمر
                if not self.active_scanner or self.active_scanner not in self.available_scanners:
                    self.auto_assign_scanner()
                    
        except Exception as e:
            print(f"خطأ في فحص الماسحات: {e}")
    
    def save_settings(self):
        """حفظ إعدادات الماسحات"""
        try:
            config.set('barcode.active_scanner', self.active_scanner)
            config.set('barcode.auto_detect', self.auto_detect)
            config.set('barcode.scan_mode', self.scan_mode)
            config.set('barcode.scan_timeout', self.scan_timeout)
            
            if hasattr(config, 'save'):
                config.save()
            
            return True
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الماسحات: {e}")
            return False


    def start_scanning(self):
        """بدء المسح المحسن"""
        try:
            if not self.active_scanner:
                print("❌ لا يوجد ماسح نشط")
                return False

            if self.is_scanning:
                print("⚠️ المسح قيد التشغيل بالفعل")
                return True

            scanner_data = self.available_scanners[self.active_scanner]
            scanner_type = scanner_data['type']

            print(f"🔄 بدء المسح باستخدام: {scanner_data['name']}")

            if scanner_type == 'USB' or scanner_type == 'USB HID':
                return self.start_usb_scanning(scanner_data)
            elif scanner_type == 'Serial':
                return self.start_serial_scanning(scanner_data)
            elif scanner_type == 'Bluetooth':
                return self.start_bluetooth_scanning(scanner_data)
            elif scanner_type == 'Camera':
                return self.start_camera_scanning(scanner_data)
            else:
                print(f"❌ نوع ماسح غير مدعوم: {scanner_type}")
                return False

        except Exception as e:
            print(f"❌ خطأ في بدء المسح: {e}")
            return False

    def start_usb_scanning(self, scanner_data):
        """بدء مسح USB محسن"""
        try:
            if not HID_AVAILABLE:
                print("❌ مكتبة HID غير متاحة")
                return False

            import hid

            # محاولة فتح الجهاز
            vendor_id = scanner_data.get('vendor_id')
            product_id = scanner_data.get('product_id')

            if vendor_id and product_id:
                device = hid.device()
                device.open(vendor_id, product_id)

                # بدء thread للقراءة
                self.scanner_thread = USBScannerThread(device, self)
                self.scanner_thread.barcode_detected.connect(self.on_barcode_detected)
                self.scanner_thread.start()

                self.is_scanning = True
                print(f"✅ تم بدء مسح USB: {scanner_data['name']}")
                return True
            else:
                print("❌ معرفات USB غير متاحة")
                return False

        except Exception as e:
            print(f"❌ خطأ في بدء مسح USB: {e}")
            return False

    def start_serial_scanning(self, scanner_data):
        """بدء مسح Serial محسن"""
        try:
            if not SERIAL_AVAILABLE:
                print("❌ مكتبة Serial غير متاحة")
                return False

            import serial

            port = scanner_data.get('port')
            baudrate = scanner_data.get('baudrate', 9600)
            timeout = scanner_data.get('timeout', 1)

            if port:
                # فتح المنفذ التسلسلي
                ser = serial.Serial(port, baudrate, timeout=timeout)

                # بدء thread للقراءة
                self.scanner_thread = SerialScannerThread(ser, self)
                self.scanner_thread.barcode_detected.connect(self.on_barcode_detected)
                self.scanner_thread.start()

                self.is_scanning = True
                print(f"✅ تم بدء مسح Serial: {scanner_data['name']}")
                return True
            else:
                print("❌ منفذ Serial غير محدد")
                return False

        except Exception as e:
            print(f"❌ خطأ في بدء مسح Serial: {e}")
            return False

    def start_bluetooth_scanning(self, scanner_data):
        """بدء مسح Bluetooth محسن"""
        try:
            if not BLUETOOTH_AVAILABLE:
                print("❌ مكتبة Bluetooth غير متاحة")
                return False

            import bluetooth

            address = scanner_data.get('address')

            if address:
                # الاتصال بجهاز Bluetooth
                sock = bluetooth.BluetoothSocket(bluetooth.RFCOMM)
                sock.connect((address, 1))  # منفذ RFCOMM

                # بدء thread للقراءة
                self.scanner_thread = BluetoothScannerThread(sock, self)
                self.scanner_thread.barcode_detected.connect(self.on_barcode_detected)
                self.scanner_thread.start()

                self.is_scanning = True
                print(f"✅ تم بدء مسح Bluetooth: {scanner_data['name']}")
                return True
            else:
                print("❌ عنوان Bluetooth غير محدد")
                return False

        except Exception as e:
            print(f"❌ خطأ في بدء مسح Bluetooth: {e}")
            return False

    def start_camera_scanning(self, scanner_data):
        """بدء مسح الكاميرا محسن"""
        try:
            if not CAMERA_AVAILABLE:
                print("❌ مكتبات الكاميرا غير متاحة")
                return False

            import cv2

            camera_index = scanner_data.get('camera_index', 0)

            # فتح الكاميرا
            cap = cv2.VideoCapture(camera_index)

            if not cap.isOpened():
                print("❌ فشل في فتح الكاميرا")
                return False

            # بدء thread للقراءة
            self.scanner_thread = CameraScannerThread(cap, self)
            self.scanner_thread.barcode_detected.connect(self.on_barcode_detected)
            self.scanner_thread.start()

            self.is_scanning = True
            print(f"✅ تم بدء مسح الكاميرا: {scanner_data['name']}")
            return True

        except Exception as e:
            print(f"❌ خطأ في بدء مسح الكاميرا: {e}")
            return False

    def stop_scanning(self):
        """إيقاف المسح"""
        try:
            if not self.is_scanning:
                print("⚠️ المسح غير نشط")
                return True

            if self.scanner_thread:
                self.scanner_thread.stop()
                self.scanner_thread.wait(3000)  # انتظار 3 ثوان
                self.scanner_thread = None

            self.is_scanning = False
            print("✅ تم إيقاف المسح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إيقاف المسح: {e}")
            return False

    def on_barcode_detected(self, barcode_data):
        """معالجة الباركود المكتشف"""
        try:
            # تنظيف البيانات
            cleaned_barcode = self.clean_barcode_data(barcode_data)

            if cleaned_barcode and len(cleaned_barcode) > 3:
                print(f"📱 تم مسح باركود: {cleaned_barcode}")
                self.barcode_scanned.emit(cleaned_barcode)
            else:
                print(f"⚠️ باركود غير صالح: {barcode_data}")

        except Exception as e:
            print(f"❌ خطأ في معالجة الباركود: {e}")

    def clean_barcode_data(self, raw_data):
        """تنظيف بيانات الباركود"""
        try:
            if isinstance(raw_data, bytes):
                # تحويل من bytes إلى string
                cleaned = raw_data.decode('utf-8', errors='ignore')
            else:
                cleaned = str(raw_data)

            # إزالة المسافات والأحرف الخاصة
            cleaned = cleaned.strip()
            cleaned = cleaned.replace('\r', '').replace('\n', '')

            # إزالة الأحرف غير المرغوب فيها
            import re
            cleaned = re.sub(r'[^\w\-]', '', cleaned)

            return cleaned

        except Exception as e:
            print(f"خطأ في تنظيف الباركود: {e}")
            return raw_data

    def test_scanner(self, scanner_id):
        """اختبار ماسح باركود"""
        try:
            if scanner_id not in self.available_scanners:
                return False, "الماسح غير متاح"

            scanner_data = self.available_scanners[scanner_id]

            # حفظ الماسح النشط الحالي
            old_scanner = self.active_scanner
            old_scanning = self.is_scanning

            # إيقاف المسح الحالي
            if self.is_scanning:
                self.stop_scanning()

            # تعيين الماسح للاختبار
            self.active_scanner = scanner_id

            # بدء الاختبار
            test_result = self.start_scanning()

            if test_result:
                # انتظار قصير ثم إيقاف
                import time
                time.sleep(2)
                self.stop_scanning()

                # استعادة الإعدادات السابقة
                self.active_scanner = old_scanner
                if old_scanning and old_scanner:
                    self.start_scanning()

                return True, f"تم اختبار الماسح بنجاح: {scanner_data['name']}"
            else:
                # استعادة الإعدادات السابقة
                self.active_scanner = old_scanner
                if old_scanning and old_scanner:
                    self.start_scanning()

                return False, f"فشل في اختبار الماسح: {scanner_data['name']}"

        except Exception as e:
            return False, f"خطأ في اختبار الماسح: {str(e)}"

    def scan_single_barcode(self, timeout=10):
        """مسح باركود واحد مع timeout"""
        try:
            if not self.active_scanner:
                return None, "لا يوجد ماسح نشط"

            # بدء المسح المؤقت
            was_scanning = self.is_scanning
            if not was_scanning:
                if not self.start_scanning():
                    return None, "فشل في بدء المسح"

            # انتظار الباركود
            import time
            start_time = time.time()
            scanned_barcode = None

            def on_single_scan(barcode):
                nonlocal scanned_barcode
                scanned_barcode = barcode

            # ربط مؤقت للإشارة
            self.barcode_scanned.connect(on_single_scan)

            try:
                while time.time() - start_time < timeout:
                    if scanned_barcode:
                        break
                    time.sleep(0.1)
                    # معالجة الأحداث
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()

                # فصل الإشارة
                self.barcode_scanned.disconnect(on_single_scan)

                # إيقاف المسح إذا لم يكن نشطاً من قبل
                if not was_scanning:
                    self.stop_scanning()

                if scanned_barcode:
                    return scanned_barcode, "تم مسح الباركود بنجاح"
                else:
                    return None, f"انتهت مهلة المسح ({timeout} ثانية)"

            except Exception as e:
                self.barcode_scanned.disconnect(on_single_scan)
                if not was_scanning:
                    self.stop_scanning()
                raise e

        except Exception as e:
            return None, f"خطأ في المسح المفرد: {str(e)}"


# فئات الـ Threads للماسحات المختلفة

class USBScannerThread(QThread):
    """Thread لمسح USB"""
    barcode_detected = pyqtSignal(str)

    def __init__(self, device, parent=None):
        super().__init__(parent)
        self.device = device
        self.running = True

    def run(self):
        """تشغيل thread المسح"""
        try:
            while self.running:
                try:
                    # قراءة البيانات من الجهاز
                    data = self.device.read(64, timeout_ms=1000)
                    if data:
                        # تحويل البيانات إلى نص
                        barcode = self.parse_hid_data(data)
                        if barcode:
                            self.barcode_detected.emit(barcode)
                except Exception as e:
                    if self.running:  # تجنب الأخطاء عند الإيقاف
                        print(f"خطأ في قراءة USB: {e}")
                        time.sleep(0.1)
        except Exception as e:
            print(f"خطأ في USB thread: {e}")
        finally:
            try:
                self.device.close()
            except:
                pass

    def parse_hid_data(self, data):
        """تحليل بيانات HID"""
        try:
            # تحويل البيانات إلى أحرف
            chars = []
            for byte in data:
                if byte > 0:
                    # تحويل HID scan codes إلى أحرف
                    char = self.hid_to_char(byte)
                    if char:
                        chars.append(char)

            barcode = ''.join(chars).strip()
            return barcode if len(barcode) > 3 else None

        except Exception as e:
            print(f"خطأ في تحليل HID: {e}")
            return None

    def hid_to_char(self, scan_code):
        """تحويل HID scan code إلى حرف"""
        # جدول تحويل مبسط
        hid_map = {
            30: '1', 31: '2', 32: '3', 33: '4', 34: '5',
            35: '6', 36: '7', 37: '8', 38: '9', 39: '0',
            4: 'a', 5: 'b', 6: 'c', 7: 'd', 8: 'e',
            9: 'f', 10: 'g', 11: 'h', 12: 'i', 13: 'j',
            14: 'k', 15: 'l', 16: 'm', 17: 'n', 18: 'o',
            19: 'p', 20: 'q', 21: 'r', 22: 's', 23: 't',
            24: 'u', 25: 'v', 26: 'w', 27: 'x', 28: 'y', 29: 'z'
        }
        return hid_map.get(scan_code, '')

    def stop(self):
        """إيقاف Thread"""
        self.running = False


class SerialScannerThread(QThread):
    """Thread لمسح Serial"""
    barcode_detected = pyqtSignal(str)

    def __init__(self, serial_port, parent=None):
        super().__init__(parent)
        self.serial_port = serial_port
        self.running = True

    def run(self):
        """تشغيل thread المسح"""
        try:
            buffer = ""
            while self.running:
                try:
                    if self.serial_port.in_waiting > 0:
                        # قراءة البيانات
                        data = self.serial_port.read(self.serial_port.in_waiting)
                        text = data.decode('utf-8', errors='ignore')
                        buffer += text

                        # البحث عن نهاية الباركود
                        if '\r' in buffer or '\n' in buffer:
                            lines = buffer.split('\n')
                            for line in lines[:-1]:
                                barcode = line.strip().replace('\r', '')
                                if len(barcode) > 3:
                                    self.barcode_detected.emit(barcode)
                            buffer = lines[-1]
                    else:
                        time.sleep(0.01)  # انتظار قصير

                except Exception as e:
                    if self.running:
                        print(f"خطأ في قراءة Serial: {e}")
                        time.sleep(0.1)
        except Exception as e:
            print(f"خطأ في Serial thread: {e}")
        finally:
            try:
                self.serial_port.close()
            except:
                pass

    def stop(self):
        """إيقاف Thread"""
        self.running = False


class BluetoothScannerThread(QThread):
    """Thread لمسح Bluetooth"""
    barcode_detected = pyqtSignal(str)

    def __init__(self, socket, parent=None):
        super().__init__(parent)
        self.socket = socket
        self.running = True

    def run(self):
        """تشغيل thread المسح"""
        try:
            buffer = ""
            while self.running:
                try:
                    # قراءة البيانات
                    data = self.socket.recv(1024)
                    if data:
                        text = data.decode('utf-8', errors='ignore')
                        buffer += text

                        # البحث عن نهاية الباركود
                        if '\r' in buffer or '\n' in buffer:
                            lines = buffer.split('\n')
                            for line in lines[:-1]:
                                barcode = line.strip().replace('\r', '')
                                if len(barcode) > 3:
                                    self.barcode_detected.emit(barcode)
                            buffer = lines[-1]
                    else:
                        time.sleep(0.01)

                except Exception as e:
                    if self.running:
                        print(f"خطأ في قراءة Bluetooth: {e}")
                        time.sleep(0.1)
        except Exception as e:
            print(f"خطأ في Bluetooth thread: {e}")
        finally:
            try:
                self.socket.close()
            except:
                pass

    def stop(self):
        """إيقاف Thread"""
        self.running = False


class CameraScannerThread(QThread):
    """Thread لمسح الكاميرا"""
    barcode_detected = pyqtSignal(str)

    def __init__(self, camera, parent=None):
        super().__init__(parent)
        self.camera = camera
        self.running = True

    def run(self):
        """تشغيل thread المسح"""
        try:
            import cv2
            from pyzbar import pyzbar

            while self.running:
                try:
                    # قراءة إطار من الكاميرا
                    ret, frame = self.camera.read()
                    if ret:
                        # البحث عن باركود في الإطار
                        barcodes = pyzbar.decode(frame)

                        for barcode in barcodes:
                            # استخراج البيانات
                            barcode_data = barcode.data.decode('utf-8')
                            if len(barcode_data) > 3:
                                self.barcode_detected.emit(barcode_data)
                                time.sleep(1)  # تجنب المسح المتكرر

                    time.sleep(0.1)  # تقليل استهلاك المعالج

                except Exception as e:
                    if self.running:
                        print(f"خطأ في قراءة الكاميرا: {e}")
                        time.sleep(0.5)
        except Exception as e:
            print(f"خطأ في Camera thread: {e}")
        finally:
            try:
                self.camera.release()
            except:
                pass

    def stop(self):
        """إيقاف Thread"""
        self.running = False

    def stop_scanning(self):
        """إيقاف المسح"""
        try:
            self.is_scanning = False

            if self.scanner_thread and self.scanner_thread.is_alive():
                self.scanner_thread.join(timeout=2)

            print("⏹️ تم إيقاف المسح")
            return True

        except Exception as e:
            print(f"خطأ في إيقاف المسح: {e}")
            return False

    def start_usb_scanning(self, scanner_data):
        """بدء مسح USB"""
        try:
            self.is_scanning = True

            def usb_scan_worker():
                try:
                    import hid

                    vendor_id = scanner_data.get('vendor_id', 0)
                    product_id = scanner_data.get('product_id', 0)

                    if vendor_id and product_id:
                        device = hid.device()
                        device.open(vendor_id, product_id)
                        device.set_nonblocking(1)

                        print(f"🔌 بدء مسح USB: {scanner_data['name']}")

                        while self.is_scanning:
                            try:
                                data = device.read(64, timeout_ms=100)
                                if data:
                                    barcode = self.parse_hid_data(data)
                                    if barcode:
                                        self.barcode_scanned.emit(barcode)
                                        print(f"📱 تم مسح باركود: {barcode}")

                                        if self.scan_mode == 'single':
                                            break

                            except Exception as e:
                                if "timeout" not in str(e).lower():
                                    print(f"خطأ في قراءة USB: {e}")
                                time.sleep(0.1)

                        device.close()

                except Exception as e:
                    print(f"خطأ في مسح USB: {e}")
                    self.is_scanning = False

            self.scanner_thread = threading.Thread(target=usb_scan_worker)
            self.scanner_thread.daemon = True
            self.scanner_thread.start()

            return True

        except Exception as e:
            print(f"خطأ في بدء مسح USB: {e}")
            return False

    def start_serial_scanning(self, scanner_data):
        """بدء مسح Serial"""
        try:
            self.is_scanning = True

            def serial_scan_worker():
                try:
                    port = scanner_data['port']
                    baudrate = scanner_data.get('baudrate', 9600)
                    timeout = scanner_data.get('timeout', 1)

                    ser = serial.Serial(port, baudrate, timeout=timeout)
                    print(f"🔌 بدء مسح Serial: {port}")

                    while self.is_scanning:
                        try:
                            if ser.in_waiting > 0:
                                data = ser.readline().decode('utf-8').strip()
                                if data:
                                    barcode = self.clean_barcode_data(data)
                                    if barcode:
                                        self.barcode_scanned.emit(barcode)
                                        print(f"📱 تم مسح باركود: {barcode}")

                                        if self.scan_mode == 'single':
                                            break

                            time.sleep(0.1)

                        except Exception as e:
                            print(f"خطأ في قراءة Serial: {e}")
                            time.sleep(0.5)

                    ser.close()

                except Exception as e:
                    print(f"خطأ في مسح Serial: {e}")
                    self.is_scanning = False

            self.scanner_thread = threading.Thread(target=serial_scan_worker)
            self.scanner_thread.daemon = True
            self.scanner_thread.start()

            return True

        except Exception as e:
            print(f"خطأ في بدء مسح Serial: {e}")
            return False

    def start_bluetooth_scanning(self, scanner_data):
        """بدء مسح Bluetooth"""
        try:
            self.is_scanning = True
            print(f"📶 بدء مسح Bluetooth: {scanner_data['name']}")

            # محاكاة مسح Bluetooth (يحتاج تطوير أكثر)
            def bluetooth_scan_worker():
                try:
                    import bluetooth

                    address = scanner_data['address']
                    port = 1  # منفذ افتراضي

                    sock = bluetooth.BluetoothSocket(bluetooth.RFCOMM)
                    sock.connect((address, port))

                    while self.is_scanning:
                        try:
                            data = sock.recv(1024).decode('utf-8').strip()
                            if data:
                                barcode = self.clean_barcode_data(data)
                                if barcode:
                                    self.barcode_scanned.emit(barcode)
                                    print(f"📱 تم مسح باركود: {barcode}")

                                    if self.scan_mode == 'single':
                                        break

                        except Exception as e:
                            if "timeout" not in str(e).lower():
                                print(f"خطأ في قراءة Bluetooth: {e}")
                            time.sleep(0.1)

                    sock.close()

                except Exception as e:
                    print(f"خطأ في مسح Bluetooth: {e}")
                    self.is_scanning = False

            self.scanner_thread = threading.Thread(target=bluetooth_scan_worker)
            self.scanner_thread.daemon = True
            self.scanner_thread.start()

            return True

        except Exception as e:
            print(f"خطأ في بدء مسح Bluetooth: {e}")
            return False

    def start_camera_scanning(self, scanner_data):
        """بدء مسح الكاميرا"""
        try:
            self.is_scanning = True

            def camera_scan_worker():
                try:
                    import cv2
                    from pyzbar import pyzbar

                    camera_index = scanner_data.get('camera_index', 0)
                    cap = cv2.VideoCapture(camera_index)

                    print(f"📷 بدء مسح الكاميرا")

                    while self.is_scanning:
                        try:
                            ret, frame = cap.read()
                            if ret:
                                # البحث عن باركود في الإطار
                                barcodes = pyzbar.decode(frame)

                                for barcode in barcodes:
                                    barcode_data = barcode.data.decode('utf-8')
                                    if barcode_data:
                                        self.barcode_scanned.emit(barcode_data)
                                        print(f"📱 تم مسح باركود: {barcode_data}")

                                        if self.scan_mode == 'single':
                                            self.is_scanning = False
                                            break

                            time.sleep(0.1)

                        except Exception as e:
                            print(f"خطأ في قراءة الكاميرا: {e}")
                            time.sleep(0.5)

                    cap.release()

                except ImportError:
                    print("❌ مكتبات الكاميرا غير متاحة (cv2, pyzbar)")
                    self.is_scanning = False
                except Exception as e:
                    print(f"خطأ في مسح الكاميرا: {e}")
                    self.is_scanning = False

            self.scanner_thread = threading.Thread(target=camera_scan_worker)
            self.scanner_thread.daemon = True
            self.scanner_thread.start()

            return True

        except Exception as e:
            print(f"خطأ في بدء مسح الكاميرا: {e}")
            return False

    def parse_hid_data(self, data):
        """تحليل بيانات HID لاستخراج الباركود"""
        try:
            # تحويل بيانات HID إلى نص
            # هذا يعتمد على نوع الماسح
            barcode = ""

            for byte in data:
                if byte > 0:
                    # تحويل رمز المفتاح إلى حرف
                    char = self.hid_keycode_to_char(byte)
                    if char:
                        barcode += char

            return self.clean_barcode_data(barcode)

        except Exception as e:
            print(f"خطأ في تحليل بيانات HID: {e}")
            return None

    def hid_keycode_to_char(self, keycode):
        """تحويل رمز المفتاح إلى حرف"""
        # خريطة رموز المفاتيح الأساسية
        keymap = {
            30: '1', 31: '2', 32: '3', 33: '4', 34: '5',
            35: '6', 36: '7', 37: '8', 38: '9', 39: '0',
            4: 'a', 5: 'b', 6: 'c', 7: 'd', 8: 'e',
            9: 'f', 10: 'g', 11: 'h', 12: 'i', 13: 'j',
            14: 'k', 15: 'l', 16: 'm', 17: 'n', 18: 'o',
            19: 'p', 20: 'q', 21: 'r', 22: 's', 23: 't',
            24: 'u', 25: 'v', 26: 'w', 27: 'x', 28: 'y', 29: 'z'
        }

        return keymap.get(keycode, '')

    def clean_barcode_data(self, data):
        """تنظيف بيانات الباركود"""
        if not data:
            return None

        # إزالة المسافات والأحرف غير المرغوبة
        cleaned = data.strip()

        # إزالة أحرف التحكم
        cleaned = ''.join(char for char in cleaned if ord(char) >= 32)

        # التحقق من صحة الباركود
        if len(cleaned) >= 3:  # الحد الأدنى لطول الباركود
            return cleaned

        return None

    def test_scanner(self, scanner_id):
        """اختبار ماسح باركود"""
        try:
            if scanner_id not in self.available_scanners:
                return False, "الماسح غير موجود"

            scanner_data = self.available_scanners[scanner_id]
            scanner_type = scanner_data['type']

            if scanner_type == 'Camera':
                # اختبار الكاميرا
                try:
                    import cv2
                    cap = cv2.VideoCapture(scanner_data.get('camera_index', 0))
                    ret, frame = cap.read()
                    cap.release()

                    if ret:
                        return True, "الكاميرا تعمل بشكل صحيح"
                    else:
                        return False, "فشل في الوصول للكاميرا"

                except ImportError:
                    return False, "مكتبات الكاميرا غير متاحة"

            elif scanner_type == 'Serial':
                # اختبار المنفذ التسلسلي
                try:
                    port = scanner_data['port']
                    baudrate = scanner_data.get('baudrate', 9600)

                    ser = serial.Serial(port, baudrate, timeout=1)
                    ser.close()

                    return True, "المنفذ التسلسلي متاح"

                except Exception as e:
                    return False, f"فشل في الوصول للمنفذ: {e}"

            elif scanner_type in ['USB', 'USB HID']:
                # اختبار USB
                try:
                    import hid

                    vendor_id = scanner_data.get('vendor_id', 0)
                    product_id = scanner_data.get('product_id', 0)

                    if vendor_id and product_id:
                        device = hid.device()
                        device.open(vendor_id, product_id)
                        device.close()

                        return True, "جهاز USB متاح"
                    else:
                        return True, "جهاز USB مكتشف"

                except Exception as e:
                    return False, f"فشل في الوصول لجهاز USB: {e}"

            elif scanner_type == 'Bluetooth':
                # اختبار Bluetooth
                return True, "جهاز Bluetooth مكتشف"

            return False, "نوع ماسح غير مدعوم"

        except Exception as e:
            return False, f"خطأ في اختبار الماسح: {e}"


# إنشاء مثيل عام لمدير الماسحات
barcode_scanner_manager = BarcodeScannerManager()
