# 📋 دليل تثبيت وتشغيل OnePos للعميل

## 🎯 مرحباً بك في OnePos!

هذا الدليل سيساعدك على تثبيت وتشغيل نظام OnePos بسهولة.

---

## 📦 **محتويات الحزمة:**

عند فتح CD/فلاشة ستجد:
```
📂 OnePos_Secure_Production/
├── 📄 install.bat          ← ملف التثبيت
├── 📄 run.bat              ← ملف التشغيل  
├── 📄 README.txt           ← دليل الاستخدام
├── 📄 main.py              ← التطبيق الرئيسي
├── 📄 requirements.txt     ← متطلبات النظام
├── 📁 models/              ← ملفات النظام
├── 📁 views/               ← واجهات المستخدم
├── 📁 utils/               ← أدوات مساعدة
├── 📁 translations/        ← ملفات الترجمة
└── 📁 data/                ← قاعدة البيانات
```

---

## 🚀 **خطوات التثبيت والتشغيل:**

### **الخطوة 1: نسخ الملفات 📁**

1. **أدخل CD/فلاشة** في الكمبيوتر
2. **افتح محتويات** CD/فلاشة
3. **انسخ مجلد OnePos_Secure_Production** 
4. **الصق المجلد** في مكان على الكمبيوتر مثل:
   - `C:\OnePos`
   - `D:\Programs\OnePos`
   - `Desktop\OnePos`

### **الخطوة 2: التثبيت الأولي ⚙️**

1. **افتح المجلد** المنسوخ
2. **ابحث عن ملف** `install.bat`
3. **انقر بزر الماوس الأيمن** على `install.bat`
4. **اختر** `Run as Administrator` (تشغيل كمدير)
5. **انتظر** انتهاء التثبيت (قد يستغرق 5-10 دقائق)

### **الخطوة 3: تشغيل التطبيق 🖥️**

1. **انقر مرتين** على ملف `run.bat`
2. **انتظر** تحميل التطبيق
3. **ستظهر نافذة التفعيل** تلقائياً

### **الخطوة 4: تفعيل التطبيق 🔐**

1. **أدخل كود التفعيل** المطبوع على الغلاف
2. **اضغط زر "تفعيل"**
3. **انتظر رسالة** "تم التفعيل بنجاح"
4. **ابدأ الاستخدام!** 🎉

---

## 🔧 **متطلبات النظام:**

### **الحد الأدنى:**
- **نظام التشغيل:** Windows 10 أو أحدث
- **الذاكرة:** 4 GB RAM
- **المساحة:** 1 GB مساحة فارغة
- **الاتصال:** إنترنت للتثبيت الأولي

### **المستحسن:**
- **نظام التشغيل:** Windows 11
- **الذاكرة:** 8 GB RAM أو أكثر
- **المساحة:** 2 GB مساحة فارغة
- **المعالج:** Intel i5 أو AMD Ryzen 5

---

## ❓ **الأسئلة الشائعة:**

### **س: ماذا لو لم يكن Python مثبت؟**
**ج:** ملف `install.bat` سيخبرك بتحميل Python من python.org وتثبيته أولاً.

### **س: ماذا لو ظهرت رسالة خطأ أثناء التثبيت؟**
**ج:** تأكد من:
- تشغيل `install.bat` كمدير
- وجود اتصال إنترنت
- عدم وجود برامج مكافحة فيروسات تمنع التثبيت

### **س: ماذا لو لم يعمل كود التفعيل؟**
**ج:** تأكد من:
- إدخال الكود بشكل صحيح (انتبه للأحرف الكبيرة والصغيرة)
- وجود اتصال إنترنت
- عدم استخدام الكود مسبقاً على جهاز آخر

### **س: هل يمكنني نقل التطبيق لجهاز آخر؟**
**ج:** لا، كود التفعيل صالح لجهاز واحد فقط. تحتاج كود جديد للجهاز الآخر.

---

## 🛠️ **حل المشاكل الشائعة:**

### **مشكلة: "Python is not installed"**
**الحل:**
1. اذهب إلى python.org
2. حمل Python 3.8 أو أحدث
3. ثبت Python مع تفعيل "Add to PATH"
4. أعد تشغيل `install.bat`

### **مشكلة: "Permission denied"**
**الحل:**
1. انقر بزر الماوس الأيمن على `install.bat`
2. اختر "Run as Administrator"
3. اضغط "Yes" عند السؤال

### **مشكلة: "Network error during activation"**
**الحل:**
1. تأكد من اتصال الإنترنت
2. أغلق برامج مكافحة الفيروسات مؤقتاً
3. أعد المحاولة

### **مشكلة: "Code already activated"**
**الحل:**
- هذا الكود تم استخدامه مسبقاً
- تواصل مع الدعم الفني للحصول على كود جديد

---

## 📞 **الدعم الفني:**

### **للمساعدة:**
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +1234567890
- **الموقع:** www.onepos.com/support

### **ساعات العمل:**
- **الأحد - الخميس:** 9:00 ص - 6:00 م
- **الجمعة - السبت:** 10:00 ص - 4:00 م

---

## 🎯 **نصائح مهمة:**

### **للحصول على أفضل تجربة:**
1. **احتفظ بكود التفعيل** في مكان آمن
2. **اعمل نسخة احتياطية** من بياناتك بانتظام
3. **لا تشارك كود التفعيل** مع الآخرين
4. **تواصل معنا** عند أي مشكلة

### **الأمان:**
- كود التفعيل صالح لجهاز واحد فقط
- لا تحاول نسخ التطبيق لأجهزة أخرى
- احتفظ بالـ CD/فلاشة الأصلية

---

## ✅ **قائمة المراجعة:**

### **قبل البدء:**
- [ ] تأكد من متطلبات النظام
- [ ] احصل على صلاحيات المدير
- [ ] تأكد من اتصال الإنترنت
- [ ] احضر كود التفعيل

### **أثناء التثبيت:**
- [ ] انسخ الملفات لمكان آمن
- [ ] شغل install.bat كمدير
- [ ] انتظر انتهاء التثبيت
- [ ] لا تغلق النوافذ أثناء التثبيت

### **بعد التثبيت:**
- [ ] شغل run.bat
- [ ] فعل التطبيق بالكود
- [ ] اختبر الوظائف الأساسية
- [ ] اعمل نسخة احتياطية

---

## 🎉 **مبروك!**

**إذا اتبعت هذه الخطوات، ستحصل على:**
- ✅ نظام OnePos يعمل بكامل طاقته
- ✅ واجهة سهلة الاستخدام
- ✅ دعم متعدد اللغات
- ✅ جميع الميزات المتقدمة

**مرحباً بك في عائلة OnePos! 🚀**

---

## 📋 **ملاحظة مهمة:**

هذا التطبيق مرخص لك شخصياً ولجهازك فقط. 
أي محاولة لنسخه أو توزيعه تعتبر انتهاكاً لحقوق الطبع والنشر.

**© 2024 OnePos. جميع الحقوق محفوظة.**
