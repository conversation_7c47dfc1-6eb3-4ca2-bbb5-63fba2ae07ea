"""
Quick Theme Change Test
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from utils.config_manager import config
from views.settings_widget import SettingsWidget, GeneralSettingsTab

def test_theme_quick():
    """Quick theme test"""
    print("🎨 Quick Theme Test...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create settings widget
        settings_widget = SettingsWidget()
        
        # Find general tab
        general_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, GeneralSettingsTab):
                general_tab = tab_widget
                break
        
        if general_tab:
            theme_combo = general_tab.theme_combo
            
            print(f"Theme combo options: {theme_combo.count()}")
            for i in range(theme_combo.count()):
                print(f"  {i}: {theme_combo.itemText(i)}")
            
            # Test theme change
            original_theme = config.get_theme()
            original_index = theme_combo.currentIndex()
            
            print(f"Original theme: {original_theme}")
            print(f"Original index: {original_index}")
            
            # Change to different index
            new_index = 1 if original_index == 0 else 0
            theme_combo.setCurrentIndex(new_index)
            
            print(f"Changed to index: {new_index}")
            print(f"New combo text: {theme_combo.currentText()}")
            
            # Call theme change
            general_tab.on_theme_changed()
            
            new_theme = config.get_theme()
            print(f"New theme: {new_theme}")
            
            if new_theme != original_theme:
                print("✅ Theme change successful!")
                return True
            else:
                print("❌ Theme change failed!")
                return False
        else:
            print("❌ General tab not found")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    test_theme_quick()
