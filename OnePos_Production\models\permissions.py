"""
Advanced Permissions System for OnePos POS System
Provides role-based access control and fine-grained permissions
"""

from enum import Enum
from typing import List, Dict, Set
from models.database import db


class Permission(Enum):
    """System permissions enumeration"""
    
    # POS Operations
    POS_ACCESS = "pos_access"
    POS_SELL = "pos_sell"
    POS_REFUND = "pos_refund"
    POS_DISCOUNT = "pos_discount"
    POS_VOID_SALE = "pos_void_sale"
    
    # Product Management
    PRODUCTS_VIEW = "products_view"
    PRODUCTS_CREATE = "products_create"
    PRODUCTS_EDIT = "products_edit"
    PRODUCTS_DELETE = "products_delete"
    PRODUCTS_IMPORT = "products_import"
    PRODUCTS_EXPORT = "products_export"
    
    # Customer Management
    CUSTOMERS_VIEW = "customers_view"
    CUSTOMERS_CREATE = "customers_create"
    CUSTOMERS_EDIT = "customers_edit"
    CUSTOMERS_DELETE = "customers_delete"
    CUSTOMERS_IMPORT = "customers_import"
    CUSTOMERS_EXPORT = "customers_export"
    
    # Sales Management
    SALES_VIEW = "sales_view"
    SALES_VIEW_ALL = "sales_view_all"
    SALES_EDIT = "sales_edit"
    SALES_DELETE = "sales_delete"
    SALES_REPORTS = "sales_reports"
    
    # Reports and Analytics
    REPORTS_VIEW = "reports_view"
    REPORTS_EXPORT = "reports_export"
    REPORTS_ADVANCED = "reports_advanced"
    
    # User Management
    USERS_VIEW = "users_view"
    USERS_CREATE = "users_create"
    USERS_EDIT = "users_edit"
    USERS_DELETE = "users_delete"
    USERS_MANAGE = "users_manage"
    USERS_PERMISSIONS = "users_permissions"
    
    # System Settings
    SETTINGS_VIEW = "settings_view"
    SETTINGS_EDIT = "settings_edit"
    SETTINGS_BACKUP = "settings_backup"
    SETTINGS_RESTORE = "settings_restore"
    
    # Advanced Features
    PERFORMANCE_VIEW = "performance_view"
    PERFORMANCE_OPTIMIZE = "performance_optimize"
    AUDIT_LOGS = "audit_logs"
    SYSTEM_ADMIN = "system_admin"


class Role:
    """User role with associated permissions"""
    
    def __init__(self, name: str, permissions: List[Permission], description: str = ""):
        self.name = name
        self.permissions = set(permissions)
        self.description = description
    
    def has_permission(self, permission: Permission) -> bool:
        """Check if role has specific permission"""
        return permission in self.permissions
    
    def add_permission(self, permission: Permission):
        """Add permission to role"""
        self.permissions.add(permission)
    
    def remove_permission(self, permission: Permission):
        """Remove permission from role"""
        self.permissions.discard(permission)


class RoleManager:
    """Manages system roles and permissions"""
    
    def __init__(self):
        self.roles = {}
        self.setup_default_roles()
    
    def setup_default_roles(self):
        """Setup default system roles"""
        
        # Cashier Role - Basic POS operations
        cashier_permissions = [
            Permission.POS_ACCESS,
            Permission.POS_SELL,
            Permission.POS_DISCOUNT,
            Permission.PRODUCTS_VIEW,
            Permission.CUSTOMERS_VIEW,
            Permission.CUSTOMERS_CREATE,
            Permission.SALES_VIEW
        ]
        self.roles['cashier'] = Role(
            "Cashier",
            cashier_permissions,
            "Basic POS operations and customer service"
        )
        
        # Sales Manager Role - Extended sales operations
        sales_manager_permissions = cashier_permissions + [
            Permission.POS_REFUND,
            Permission.POS_VOID_SALE,
            Permission.CUSTOMERS_EDIT,
            Permission.SALES_VIEW_ALL,
            Permission.SALES_EDIT,
            Permission.SALES_REPORTS,
            Permission.REPORTS_VIEW
        ]
        self.roles['sales_manager'] = Role(
            "Sales Manager",
            sales_manager_permissions,
            "Sales operations and basic reporting"
        )
        
        # Store Manager Role - Store operations
        store_manager_permissions = sales_manager_permissions + [
            Permission.PRODUCTS_CREATE,
            Permission.PRODUCTS_EDIT,
            Permission.PRODUCTS_IMPORT,
            Permission.PRODUCTS_EXPORT,
            Permission.CUSTOMERS_DELETE,
            Permission.CUSTOMERS_IMPORT,
            Permission.CUSTOMERS_EXPORT,
            Permission.REPORTS_EXPORT,
            Permission.SETTINGS_VIEW,
            Permission.USERS_VIEW
        ]
        self.roles['store_manager'] = Role(
            "Store Manager",
            store_manager_permissions,
            "Complete store management operations"
        )
        
        # Administrator Role - Full system access
        admin_permissions = [perm for perm in Permission]
        self.roles['administrator'] = Role(
            "Administrator",
            admin_permissions,
            "Full system access and administration"
        )
        
        # Owner Role - Complete control
        self.roles['owner'] = Role(
            "Owner",
            admin_permissions,
            "Complete system control and ownership"
        )
    
    def get_role(self, role_name: str) -> Role:
        """Get role by name"""
        return self.roles.get(role_name)
    
    def get_all_roles(self) -> Dict[str, Role]:
        """Get all available roles"""
        return self.roles.copy()
    
    def create_custom_role(self, name: str, permissions: List[Permission], description: str = "") -> Role:
        """Create a custom role"""
        role = Role(name, permissions, description)
        self.roles[name] = role
        return role


class PermissionChecker:
    """Checks user permissions for various operations"""
    
    def __init__(self, user):
        self.user = user
        self.role_manager = RoleManager()
    
    def has_permission(self, permission: Permission) -> bool:
        """Check if user has specific permission"""
        if not self.user or not hasattr(self.user, 'role'):
            return False
        
        role = self.role_manager.get_role(self.user.role)
        if not role:
            return False
        
        return role.has_permission(permission)
    
    def require_permission(self, permission: Permission) -> bool:
        """Require permission or raise exception"""
        if not self.has_permission(permission):
            raise PermissionError(f"Access denied: {permission.value} permission required")
        return True
    
    def can_access_pos(self) -> bool:
        """Check if user can access POS"""
        return self.has_permission(Permission.POS_ACCESS)
    
    def can_manage_products(self) -> bool:
        """Check if user can manage products"""
        return self.has_permission(Permission.PRODUCTS_EDIT)
    
    def can_manage_customers(self) -> bool:
        """Check if user can manage customers"""
        return self.has_permission(Permission.CUSTOMERS_EDIT)
    
    def can_view_reports(self) -> bool:
        """Check if user can view reports"""
        return self.has_permission(Permission.REPORTS_VIEW)
    
    def can_manage_users(self) -> bool:
        """Check if user can manage users"""
        return self.has_permission(Permission.USERS_EDIT)
    
    def can_access_settings(self) -> bool:
        """Check if user can access settings"""
        return self.has_permission(Permission.SETTINGS_VIEW)
    
    def can_perform_refunds(self) -> bool:
        """Check if user can perform refunds"""
        return self.has_permission(Permission.POS_REFUND)
    
    def can_void_sales(self) -> bool:
        """Check if user can void sales"""
        return self.has_permission(Permission.POS_VOID_SALE)
    
    def can_apply_discounts(self) -> bool:
        """Check if user can apply discounts"""
        return self.has_permission(Permission.POS_DISCOUNT)
    
    def can_view_all_sales(self) -> bool:
        """Check if user can view all sales"""
        return self.has_permission(Permission.SALES_VIEW_ALL)
    
    def can_export_data(self) -> bool:
        """Check if user can export data"""
        return (self.has_permission(Permission.PRODUCTS_EXPORT) or 
                self.has_permission(Permission.CUSTOMERS_EXPORT) or
                self.has_permission(Permission.REPORTS_EXPORT))
    
    def can_backup_system(self) -> bool:
        """Check if user can backup system"""
        return self.has_permission(Permission.SETTINGS_BACKUP)
    
    def is_admin(self) -> bool:
        """Check if user is administrator"""
        return self.has_permission(Permission.SYSTEM_ADMIN)


class AuditLogger:
    """Logs user actions for security auditing"""
    
    @staticmethod
    def log_action(user_id: int, action: str, details: str = "", ip_address: str = ""):
        """Log user action"""
        query = """
        INSERT INTO audit_logs (user_id, action, details, ip_address, created_at)
        VALUES (?, ?, ?, ?, datetime('now'))
        """
        
        try:
            return db.execute_update(query, (user_id, action, details, ip_address))
        except Exception as e:
            print(f"Failed to log audit action: {e}")
            return None
    
    @staticmethod
    def log_login(user_id: int, success: bool, ip_address: str = ""):
        """Log login attempt"""
        action = "LOGIN_SUCCESS" if success else "LOGIN_FAILED"
        AuditLogger.log_action(user_id, action, f"Login attempt", ip_address)
    
    @staticmethod
    def log_logout(user_id: int, ip_address: str = ""):
        """Log logout"""
        AuditLogger.log_action(user_id, "LOGOUT", "User logged out", ip_address)
    
    @staticmethod
    def log_sale(user_id: int, sale_id: int, amount: float):
        """Log sale transaction"""
        AuditLogger.log_action(
            user_id, 
            "SALE_CREATED", 
            f"Sale ID: {sale_id}, Amount: {amount}"
        )
    
    @staticmethod
    def log_refund(user_id: int, sale_id: int, amount: float):
        """Log refund transaction"""
        AuditLogger.log_action(
            user_id, 
            "REFUND_PROCESSED", 
            f"Sale ID: {sale_id}, Refund Amount: {amount}"
        )
    
    @staticmethod
    def log_product_change(user_id: int, product_id: int, action: str):
        """Log product changes"""
        AuditLogger.log_action(
            user_id, 
            f"PRODUCT_{action.upper()}", 
            f"Product ID: {product_id}"
        )
    
    @staticmethod
    def log_customer_change(user_id: int, customer_id: int, action: str):
        """Log customer changes"""
        AuditLogger.log_action(
            user_id, 
            f"CUSTOMER_{action.upper()}", 
            f"Customer ID: {customer_id}"
        )
    
    @staticmethod
    def log_settings_change(user_id: int, setting: str, old_value: str, new_value: str):
        """Log settings changes"""
        AuditLogger.log_action(
            user_id, 
            "SETTINGS_CHANGED", 
            f"Setting: {setting}, Old: {old_value}, New: {new_value}"
        )
    
    @staticmethod
    def log_permission_change(user_id: int, target_user_id: int, action: str):
        """Log permission changes"""
        AuditLogger.log_action(
            user_id, 
            "PERMISSION_CHANGED", 
            f"Target User: {target_user_id}, Action: {action}"
        )
    
    @staticmethod
    def get_audit_logs(limit: int = 100, user_id: int = None):
        """Get audit logs"""
        query = """
        SELECT al.*, u.username, u.full_name
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        """
        params = []
        
        if user_id:
            query += " WHERE al.user_id = ?"
            params.append(user_id)
        
        query += " ORDER BY al.created_at DESC LIMIT ?"
        params.append(limit)
        
        return db.execute_query(query, params)


class SecurityManager:
    """Manages security policies and enforcement"""
    
    def __init__(self):
        self.failed_attempts = {}
        self.max_attempts = 5
        self.lockout_duration = 300  # 5 minutes
    
    def check_password_strength(self, password: str) -> Dict[str, bool]:
        """Check password strength"""
        checks = {
            'length': len(password) >= 8,
            'uppercase': any(c.isupper() for c in password),
            'lowercase': any(c.islower() for c in password),
            'digit': any(c.isdigit() for c in password),
            'special': any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password)
        }
        
        checks['strong'] = sum(checks.values()) >= 4
        return checks

    def is_password_strong(self, password: str) -> bool:
        """Check if password is strong enough"""
        checks = self.check_password_strength(password)
        return checks['strong']

    def generate_session_token(self) -> str:
        """Generate secure session token"""
        import secrets
        return secrets.token_urlsafe(32)

    def is_valid_ip(self, ip_address: str) -> bool:
        """Validate IP address format"""
        import re
        pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        return bool(re.match(pattern, ip_address))
    
    def is_account_locked(self, username: str) -> bool:
        """Check if account is locked due to failed attempts"""
        if username not in self.failed_attempts:
            return False
        
        attempts, last_attempt = self.failed_attempts[username]
        
        # Check if lockout period has expired
        import time
        if time.time() - last_attempt > self.lockout_duration:
            del self.failed_attempts[username]
            return False
        
        return attempts >= self.max_attempts
    
    def record_failed_attempt(self, username: str):
        """Record failed login attempt"""
        import time
        current_time = time.time()
        
        if username in self.failed_attempts:
            attempts, _ = self.failed_attempts[username]
            self.failed_attempts[username] = (attempts + 1, current_time)
        else:
            self.failed_attempts[username] = (1, current_time)
    
    def clear_failed_attempts(self, username: str):
        """Clear failed attempts for successful login"""
        if username in self.failed_attempts:
            del self.failed_attempts[username]
    
    def validate_session(self, session_token: str) -> bool:
        """Validate user session token"""
        from models.user import UserSession
        return UserSession.validate_session(session_token)
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        try:
            import base64
            # Simple base64 encoding (in production, use proper encryption)
            return base64.b64encode(data.encode()).decode()
        except Exception:
            return data
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            import base64
            return base64.b64decode(encrypted_data.encode()).decode()
        except Exception:
            return encrypted_data


# Global instances
role_manager = RoleManager()
security_manager = SecurityManager()
