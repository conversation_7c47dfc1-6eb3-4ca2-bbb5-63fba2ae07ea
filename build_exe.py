#!/usr/bin/env python3
"""
Build script for creating OnePos executable using PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def clean_build_dirs():
    """Clean previous build directories"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"Cleaning {dir_name}...")
            shutil.rmtree(dir_name)
    
    # Clean .pyc files
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))


def create_spec_file():
    """Create PyInstaller spec file"""
    spec_content = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('translations', 'translations'),
        ('assets', 'assets'),
        ('ui', 'ui'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'qdarkstyle',
        'sqlite3',
        'bcrypt',
        'barcode',
        'PIL',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='OnePos',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
"""
    
    with open('onepos.spec', 'w') as f:
        f.write(spec_content.strip())
    
    print("Created onepos.spec file")


def install_pyinstaller():
    """Install PyInstaller if not available"""
    try:
        import PyInstaller
        print("PyInstaller is already installed")
    except ImportError:
        print("Installing PyInstaller...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])


def build_executable():
    """Build the executable"""
    print("Building OnePos executable...")
    
    try:
        # Use the spec file for more control
        cmd = [sys.executable, '-m', 'PyInstaller', 'onepos.spec', '--clean']
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Build successful!")
            print(f"Executable created at: dist/OnePos.exe")
            
            # Copy additional files to dist
            copy_additional_files()
            
        else:
            print("✗ Build failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"Error during build: {e}")
        return False
    
    return True


def copy_additional_files():
    """Copy additional files to dist directory"""
    dist_dir = Path('dist')
    
    # Create directories
    (dist_dir / 'data').mkdir(exist_ok=True)
    (dist_dir / 'backups').mkdir(exist_ok=True)
    
    # Copy sample database if it exists
    if os.path.exists('data/onepos.db'):
        shutil.copy2('data/onepos.db', dist_dir / 'data' / 'onepos.db')
        print("Copied sample database")
    
    # Copy README
    if os.path.exists('README.md'):
        shutil.copy2('README.md', dist_dir / 'README.md')
        print("Copied README.md")
    
    print("Additional files copied to dist directory")


def create_installer_script():
    """Create a simple installer script"""
    installer_content = """
@echo off
echo OnePos Installation
echo ===================

echo Creating application directory...
if not exist "C:\\Program Files\\OnePos" mkdir "C:\\Program Files\\OnePos"

echo Copying files...
xcopy /E /I /Y "." "C:\\Program Files\\OnePos\\"

echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\OnePos.lnk'); $Shortcut.TargetPath = 'C:\\Program Files\\OnePos\\OnePos.exe'; $Shortcut.Save()"

echo Creating start menu shortcut...
if not exist "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\OnePos" mkdir "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\OnePos"
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\OnePos\\OnePos.lnk'); $Shortcut.TargetPath = 'C:\\Program Files\\OnePos\\OnePos.exe'; $Shortcut.Save()"

echo Installation completed!
echo You can now run OnePos from the desktop or start menu.
pause
"""
    
    with open('dist/install.bat', 'w') as f:
        f.write(installer_content.strip())
    
    print("Created installer script: dist/install.bat")


def main():
    """Main build function"""
    print("OnePos Build Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('main.py'):
        print("Error: main.py not found. Please run this script from the OnePos root directory.")
        return 1
    
    # Clean previous builds
    clean_build_dirs()
    
    # Install PyInstaller
    install_pyinstaller()
    
    # Create spec file
    create_spec_file()
    
    # Build executable
    if build_executable():
        create_installer_script()
        
        print("\n" + "=" * 50)
        print("Build completed successfully!")
        print("\nFiles created:")
        print("- dist/OnePos.exe (Main executable)")
        print("- dist/install.bat (Installer script)")
        print("\nTo install OnePos:")
        print("1. Copy the dist folder to target computer")
        print("2. Run install.bat as administrator")
        print("3. Launch OnePos from desktop or start menu")
        
        return 0
    else:
        print("\n" + "=" * 50)
        print("Build failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
