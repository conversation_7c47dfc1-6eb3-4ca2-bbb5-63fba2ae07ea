"""
Final RTL Layout Test for OnePos POS System
Quick and focused test to verify all RTL functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from utils.translator import change_language, get_current_language
from views.main_window import MainWindow
from models.user import User
import time

def test_rtl_layout_final():
    """Final comprehensive RTL layout test"""
    print("🔄 Final RTL Layout Test...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        print("✅ Main window created")
        
        # Test 1: Basic Layout Components
        print("\n1. Testing Basic Layout Components...")
        
        required_components = ['main_layout', 'sidebar_frame', 'content_frame']
        for component in required_components:
            if hasattr(main_window, component):
                print(f"   ✅ {component} exists")
            else:
                print(f"   ❌ {component} missing")
                return False
        
        # Test 2: English Layout (LTR)
        print("\n2. Testing English Layout (LTR)...")
        change_language('en')
        app.processEvents()
        time.sleep(0.1)
        
        layout = main_window.main_layout
        first_widget = layout.itemAt(0).widget()
        second_widget = layout.itemAt(1).widget()
        
        if (first_widget == main_window.sidebar_frame and 
            second_widget == main_window.content_frame):
            print("   ✅ English: Sidebar left, Content right")
        else:
            print("   ❌ English layout incorrect")
            return False
        
        if main_window.layoutDirection() == Qt.LeftToRight:
            print("   ✅ English: Layout direction LTR")
        else:
            print("   ❌ English: Wrong layout direction")
            return False
        
        # Test 3: Arabic Layout (RTL)
        print("\n3. Testing Arabic Layout (RTL)...")
        change_language('ar')
        app.processEvents()
        time.sleep(0.1)
        
        layout = main_window.main_layout
        first_widget = layout.itemAt(0).widget()
        second_widget = layout.itemAt(1).widget()
        
        if (first_widget == main_window.content_frame and 
            second_widget == main_window.sidebar_frame):
            print("   ✅ Arabic: Content left, Sidebar right")
        else:
            print("   ❌ Arabic layout incorrect")
            return False
        
        if main_window.layoutDirection() == Qt.RightToLeft:
            print("   ✅ Arabic: Layout direction RTL")
        else:
            print("   ❌ Arabic: Wrong layout direction")
            return False
        
        # Test 4: French Layout (LTR)
        print("\n4. Testing French Layout (LTR)...")
        change_language('fr')
        app.processEvents()
        time.sleep(0.1)
        
        layout = main_window.main_layout
        first_widget = layout.itemAt(0).widget()
        second_widget = layout.itemAt(1).widget()
        
        if (first_widget == main_window.sidebar_frame and 
            second_widget == main_window.content_frame):
            print("   ✅ French: Sidebar left, Content right")
        else:
            print("   ❌ French layout incorrect")
            return False
        
        if main_window.layoutDirection() == Qt.LeftToRight:
            print("   ✅ French: Layout direction LTR")
        else:
            print("   ❌ French: Wrong layout direction")
            return False
        
        # Test 5: Rapid Language Switching
        print("\n5. Testing Rapid Language Switching...")
        languages = ['ar', 'en', 'fr', 'ar', 'en']
        
        for i, lang in enumerate(languages):
            change_language(lang)
            app.processEvents()
            
            layout = main_window.main_layout
            first_widget = layout.itemAt(0).widget()
            
            if lang == 'ar':
                expected_first = main_window.content_frame
                expected_direction = Qt.RightToLeft
            else:
                expected_first = main_window.sidebar_frame
                expected_direction = Qt.LeftToRight
            
            if first_widget == expected_first:
                print(f"   ✅ Switch {i+1} ({lang}): Layout correct")
            else:
                print(f"   ❌ Switch {i+1} ({lang}): Layout incorrect")
                return False
            
            if main_window.layoutDirection() == expected_direction:
                print(f"   ✅ Switch {i+1} ({lang}): Direction correct")
            else:
                print(f"   ❌ Switch {i+1} ({lang}): Direction incorrect")
                return False
        
        # Test 6: Navigation with Layout Persistence
        print("\n6. Testing Navigation with Layout Persistence...")
        
        # Set to Arabic
        change_language('ar')
        app.processEvents()
        time.sleep(0.1)
        
        modules_to_test = ['pos', 'products', 'customers']
        
        for module in modules_to_test:
            if module in main_window.nav_buttons:
                main_window.nav_buttons[module].click()
                app.processEvents()
                time.sleep(0.05)
                
                layout = main_window.main_layout
                first_widget = layout.itemAt(0).widget()
                
                if first_widget == main_window.content_frame:
                    print(f"   ✅ {module}: Arabic layout maintained")
                else:
                    print(f"   ❌ {module}: Arabic layout lost")
                    return False
        
        # Test 7: Sidebar Styles
        print("\n7. Testing Sidebar Styles...")
        
        # Test Arabic styles
        change_language('ar')
        app.processEvents()
        time.sleep(0.1)
        
        sidebar_style = main_window.sidebar_frame.styleSheet()
        if 'border-left' in sidebar_style:
            print("   ✅ Arabic: Sidebar border on left")
        else:
            print("   ⚠️ Arabic: Sidebar border style may be incorrect")
        
        # Test English styles
        change_language('en')
        app.processEvents()
        time.sleep(0.1)
        
        sidebar_style = main_window.sidebar_frame.styleSheet()
        if 'border-right' in sidebar_style:
            print("   ✅ English: Sidebar border on right")
        else:
            print("   ⚠️ English: Sidebar border style may be incorrect")
        
        # Test 8: Function Availability
        print("\n8. Testing Function Availability...")
        
        functions = [
            'arrange_layout_components',
            'update_sidebar_styles',
            'update_children_layout_direction'
        ]
        
        for func_name in functions:
            if hasattr(main_window, func_name):
                print(f"   ✅ Function {func_name} available")
                
                # Test function call
                try:
                    if func_name == 'arrange_layout_components':
                        main_window.arrange_layout_components()
                    elif func_name == 'update_sidebar_styles':
                        main_window.update_sidebar_styles(True)
                        main_window.update_sidebar_styles(False)
                    elif func_name == 'update_children_layout_direction':
                        main_window.update_children_layout_direction(True)
                        main_window.update_children_layout_direction(False)
                    
                    print(f"   ✅ Function {func_name} works correctly")
                except Exception as e:
                    print(f"   ❌ Function {func_name} failed: {e}")
                    return False
            else:
                print(f"   ❌ Function {func_name} missing")
                return False
        
        # Clean up
        main_window.close()
        
        print("\n✅ All RTL layout tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ RTL layout test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_final_rtl_test():
    """Run final RTL test"""
    print("🔄 STARTING FINAL RTL LAYOUT TEST")
    print("=" * 50)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # Run the test
    result = test_rtl_layout_final()
    
    # Print final result
    print("\n" + "=" * 50)
    print("🏁 FINAL RTL TEST RESULT")
    print("=" * 50)
    
    if result:
        print("🎉 ALL RTL LAYOUT TESTS PASSED!")
        print("✅ RTL layout system is working perfectly!")
        print("✅ Arabic: Content left, Sidebar right")
        print("✅ English/French: Sidebar left, Content right")
        print("✅ Dynamic layout switching works")
        print("✅ Layout persistence across navigation")
        print("✅ All functions available and working")
    else:
        print("❌ SOME RTL LAYOUT TESTS FAILED!")
        print("⚠️  Please check the issues above.")
    
    print("=" * 50)
    
    return result

if __name__ == "__main__":
    run_final_rtl_test()
