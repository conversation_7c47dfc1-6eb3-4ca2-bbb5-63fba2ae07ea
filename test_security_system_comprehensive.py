#!/usr/bin/env python3
"""
اختبار شامل لنظام الأمان والصلاحيات المتقدم - OnePos
Comprehensive Security and Permissions System Test
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.main_window import MainWindow
from models.user import User
from models.permissions import Permission, Role, RoleManager, PermissionChe<PERSON>, AuditLogger, SecurityManager
from utils.translator import translator, tr
import bcrypt

def test_permissions_system():
    """اختبار نظام الصلاحيات"""
    print("🔐 اختبار نظام الصلاحيات")
    print("=" * 35)
    
    try:
        # اختبار تعداد الصلاحيات
        permissions_count = len(list(Permission))
        print(f"✅ عدد الصلاحيات المتاحة: {permissions_count}")
        
        # اختبار بعض الصلاحيات الأساسية
        essential_permissions = [
            Permission.POS_ACCESS,
            Permission.POS_SELL,
            Permission.PRODUCTS_VIEW,
            Permission.CUSTOMERS_VIEW,
            Permission.USERS_MANAGE,
            Permission.SYSTEM_ADMIN
        ]
        
        for perm in essential_permissions:
            print(f"   ✅ {perm.value}: متوفر")
        
        # اختبار إنشاء دور مخصص
        custom_permissions = [Permission.POS_ACCESS, Permission.POS_SELL, Permission.PRODUCTS_VIEW]
        custom_role = Role("Custom Cashier", custom_permissions, "Custom cashier role")
        
        if custom_role.has_permission(Permission.POS_SELL):
            print("✅ إنشاء دور مخصص: نجح")
        else:
            print("❌ إنشاء دور مخصص: فشل")
            return False
        
        # اختبار إضافة وإزالة صلاحيات
        custom_role.add_permission(Permission.CUSTOMERS_CREATE)
        if custom_role.has_permission(Permission.CUSTOMERS_CREATE):
            print("✅ إضافة صلاحية: نجح")
        else:
            print("❌ إضافة صلاحية: فشل")
        
        custom_role.remove_permission(Permission.CUSTOMERS_CREATE)
        if not custom_role.has_permission(Permission.CUSTOMERS_CREATE):
            print("✅ إزالة صلاحية: نجح")
        else:
            print("❌ إزالة صلاحية: فشل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الصلاحيات: {e}")
        return False

def test_role_manager():
    """اختبار مدير الأدوار"""
    print("\n👥 اختبار مدير الأدوار")
    print("=" * 30)
    
    try:
        role_manager = RoleManager()
        
        # اختبار الأدوار الافتراضية
        default_roles = ['cashier', 'sales_manager', 'store_manager', 'administrator', 'owner']
        
        for role_name in default_roles:
            role = role_manager.get_role(role_name)
            if role:
                print(f"✅ دور {role.name}: متوفر ({len(role.permissions)} صلاحية)")
            else:
                print(f"❌ دور {role_name}: مفقود")
                return False
        
        # اختبار صلاحيات الكاشير
        cashier_role = role_manager.get_role('cashier')
        expected_cashier_permissions = [
            Permission.POS_ACCESS,
            Permission.POS_SELL,
            Permission.PRODUCTS_VIEW,
            Permission.CUSTOMERS_VIEW
        ]
        
        for perm in expected_cashier_permissions:
            if cashier_role.has_permission(perm):
                print(f"   ✅ كاشير لديه صلاحية: {perm.value}")
            else:
                print(f"   ❌ كاشير ليس لديه صلاحية: {perm.value}")
        
        # اختبار صلاحيات المدير
        admin_role = role_manager.get_role('administrator')
        if admin_role.has_permission(Permission.SYSTEM_ADMIN):
            print("✅ المدير لديه صلاحيات النظام: نجح")
        else:
            print("❌ المدير ليس لديه صلاحيات النظام: فشل")
        
        # اختبار إنشاء دور مخصص
        custom_permissions = [Permission.POS_ACCESS, Permission.REPORTS_VIEW]
        custom_role = role_manager.create_custom_role(
            "Report Viewer", 
            custom_permissions, 
            "Can only view reports"
        )
        
        if custom_role and role_manager.get_role("Report Viewer"):
            print("✅ إنشاء دور مخصص: نجح")
        else:
            print("❌ إنشاء دور مخصص: فشل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الأدوار: {e}")
        return False

def test_user_authentication():
    """اختبار نظام المصادقة"""
    print("\n🔒 اختبار نظام المصادقة")
    print("=" * 35)
    
    try:
        # اختبار إنشاء مستخدم جديد
        import time
        timestamp = str(int(time.time()))
        test_username = f"test_user_{timestamp}"
        test_password = "TestPassword123!"
        
        user = User.create(
            username=test_username,
            password=test_password,
            full_name="Test User",
            email=f"test{timestamp}@example.com",
            role="cashier"
        )
        
        if user:
            print("✅ إنشاء مستخدم جديد: نجح")
            print(f"   👤 اسم المستخدم: {user.username}")
            print(f"   🎭 الدور: {user.role}")
        else:
            print("❌ إنشاء مستخدم جديد: فشل")
            return False
        
        # اختبار تسجيل الدخول الصحيح
        authenticated_user = User.authenticate(test_username, test_password)
        if authenticated_user:
            print("✅ تسجيل دخول صحيح: نجح")
        else:
            print("❌ تسجيل دخول صحيح: فشل")
            return False
        
        # اختبار تسجيل الدخول بكلمة مرور خاطئة
        wrong_auth = User.authenticate(test_username, "wrong_password")
        if not wrong_auth:
            print("✅ رفض كلمة مرور خاطئة: نجح")
        else:
            print("❌ رفض كلمة مرور خاطئة: فشل")
        
        # اختبار تشفير كلمة المرور
        if user.password_hash and user.password_hash != test_password:
            print("✅ تشفير كلمة المرور: نجح")
        else:
            print("❌ تشفير كلمة المرور: فشل")
        
        # اختبار التحقق من كلمة المرور
        if user.verify_password(test_password):
            print("✅ التحقق من كلمة المرور: نجح")
        else:
            print("❌ التحقق من كلمة المرور: فشل")
        
        # اختبار تحديث كلمة المرور
        new_password = "NewPassword456!"
        user.update_password(new_password)
        if user.verify_password(new_password):
            print("✅ تحديث كلمة المرور: نجح")
        else:
            print("❌ تحديث كلمة المرور: فشل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام المصادقة: {e}")
        return False

def test_permission_checker():
    """اختبار فاحص الصلاحيات"""
    print("\n🔍 اختبار فاحص الصلاحيات")
    print("=" * 40)
    
    try:
        # إنشاء مستخدم للاختبار
        import time
        timestamp = str(int(time.time()))
        cashier_user = User.create(
            username=f"test_cashier_{timestamp}",
            password="password123",
            full_name="Test Cashier",
            role="cashier"
        )
        
        if not cashier_user:
            print("❌ فشل في إنشاء مستخدم للاختبار")
            return False
        
        # اختبار فاحص الصلاحيات
        permission_checker = PermissionChecker(cashier_user)
        
        # اختبار صلاحيات الكاشير
        cashier_permissions = [
            (Permission.POS_ACCESS, True),
            (Permission.POS_SELL, True),
            (Permission.PRODUCTS_VIEW, True),
            (Permission.PRODUCTS_DELETE, False),
            (Permission.USERS_MANAGE, False),
            (Permission.SYSTEM_ADMIN, False)
        ]
        
        for permission, should_have in cashier_permissions:
            has_permission = permission_checker.has_permission(permission)
            if has_permission == should_have:
                status = "✅" if should_have else "✅ (محظور)"
                print(f"   {status} {permission.value}: {has_permission}")
            else:
                print(f"   ❌ {permission.value}: متوقع {should_have}, حصل على {has_permission}")
                return False
        
        # اختبار require_permission
        try:
            permission_checker.require_permission(Permission.POS_SELL)
            print("✅ require_permission للصلاحية المتاحة: نجح")
        except PermissionError:
            print("❌ require_permission للصلاحية المتاحة: فشل")
            return False
        
        try:
            permission_checker.require_permission(Permission.SYSTEM_ADMIN)
            print("❌ require_permission للصلاحية المحظورة: فشل (لم يرفض)")
            return False
        except PermissionError:
            print("✅ require_permission للصلاحية المحظورة: نجح (تم الرفض)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار فاحص الصلاحيات: {e}")
        return False

def test_audit_logging():
    """اختبار نظام تسجيل التدقيق"""
    print("\n📋 اختبار نظام تسجيل التدقيق")
    print("=" * 45)
    
    try:
        # اختبار تسجيل إجراء
        test_user_id = 1  # Admin user
        action = "TEST_ACTION"
        details = "This is a test audit log entry"
        
        log_id = AuditLogger.log_action(test_user_id, action, details)
        if log_id:
            print("✅ تسجيل إجراء التدقيق: نجح")
            print(f"   📝 معرف السجل: {log_id}")
        else:
            print("❌ تسجيل إجراء التدقيق: فشل")
            return False
        
        # اختبار استرجاع سجلات التدقيق
        audit_logs = AuditLogger.get_audit_logs(limit=10)
        if audit_logs and len(audit_logs) > 0:
            print(f"✅ استرجاع سجلات التدقيق: نجح ({len(audit_logs)} سجل)")
            
            # فحص السجل الأخير
            latest_log = audit_logs[0]
            if latest_log['action'] == action:
                print("✅ تطابق بيانات السجل: نجح")
            else:
                print("❌ تطابق بيانات السجل: فشل")
        else:
            print("❌ استرجاع سجلات التدقيق: فشل")
        
        # اختبار تسجيل تسجيل الدخول
        AuditLogger.log_login(test_user_id, True, "127.0.0.1")
        print("✅ تسجيل محاولة دخول: نجح")
        
        # اختبار تسجيل إجراء إضافي
        AuditLogger.log_action(test_user_id, "DATA_CHANGE", "Updated user role")
        print("✅ تسجيل تغيير البيانات: نجح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل التدقيق: {e}")
        return False

def test_security_manager():
    """اختبار مدير الأمان"""
    print("\n🛡️ اختبار مدير الأمان")
    print("=" * 35)
    
    try:
        security_manager = SecurityManager()
        
        # اختبار فحص قوة كلمة المرور
        weak_passwords = ["123", "password", "abc123"]
        strong_passwords = ["MyStr0ngP@ssw0rd!", "SecurePass123#", "C0mpl3x!P@ssw0rd"]
        
        for password in weak_passwords:
            if not security_manager.is_password_strong(password):
                print(f"✅ رفض كلمة مرور ضعيفة: '{password}'")
            else:
                print(f"❌ قبول كلمة مرور ضعيفة: '{password}'")
        
        for password in strong_passwords:
            if security_manager.is_password_strong(password):
                print(f"✅ قبول كلمة مرور قوية: '{password[:8]}...'")
            else:
                print(f"❌ رفض كلمة مرور قوية: '{password[:8]}...'")
        
        # اختبار توليد رمز الجلسة
        session_token = security_manager.generate_session_token()
        if session_token and len(session_token) >= 32:
            print("✅ توليد رمز الجلسة: نجح")
        else:
            print("❌ توليد رمز الجلسة: فشل")
        
        # اختبار التحقق من عنوان IP
        valid_ips = ["***********", "127.0.0.1", "********"]
        for ip in valid_ips:
            if security_manager.is_valid_ip(ip):
                print(f"✅ عنوان IP صحيح: {ip}")
            else:
                print(f"❌ عنوان IP صحيح مرفوض: {ip}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الأمان: {e}")
        return False

def test_users_ui():
    """اختبار واجهة إدارة المستخدمين"""
    print("\n🖼️ اختبار واجهة إدارة المستخدمين")
    print("=" * 50)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        main_window = MainWindow(admin_user)
        
        # الانتقال لقسم المستخدمين
        main_window.load_module('users')
        app.processEvents()
        
        # البحث عن واجهة المستخدمين
        users_widget = None
        for i in range(main_window.content_frame.count()):
            widget = main_window.content_frame.widget(i)
            if hasattr(widget, 'users_table'):
                users_widget = widget
                break
        
        if not users_widget:
            print("❌ لم يتم العثور على واجهة المستخدمين")
            return False
        
        print("✅ تم العثور على واجهة المستخدمين")
        
        # فحص التبويبات
        if hasattr(users_widget, 'tab_widget'):
            tab_count = users_widget.tab_widget.count()
            print(f"✅ عدد التبويبات: {tab_count}")
        
        # فحص العناصر الأساسية
        elements_to_check = [
            ('users_table', 'جدول المستخدمين'),
            ('search_edit', 'حقل البحث'),
            ('add_button', 'زر إضافة مستخدم'),
            ('edit_button', 'زر تعديل مستخدم'),
            ('delete_button', 'زر حذف مستخدم'),
            ('refresh_button', 'زر التحديث')
        ]
        
        missing_elements = []
        for element_name, description in elements_to_check:
            if hasattr(users_widget, element_name):
                print(f"   ✅ {description}: موجود")
            else:
                print(f"   ❌ {description}: مفقود")
                missing_elements.append(description)
        
        # فحص الجدول
        if hasattr(users_widget, 'users_table'):
            table = users_widget.users_table
            if table.columnCount() >= 6:
                print(f"✅ جدول المستخدمين: {table.columnCount()} أعمدة")
            else:
                print(f"❌ جدول المستخدمين: أعمدة ناقصة ({table.columnCount()}/6)")
        
        # اختبار تحميل البيانات
        if hasattr(users_widget, 'load_users'):
            users_widget.load_users()
            app.processEvents()
            print("✅ تحميل بيانات المستخدمين: تم")
        
        return len(missing_elements) == 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المستخدمين: {e}")
        return False

def test_security_translations():
    """اختبار ترجمات الأمان"""
    print("\n🌍 اختبار ترجمات الأمان")
    print("=" * 35)
    
    languages = [
        ('ar', 'العربية'),
        ('en', 'English'),
        ('fr', 'Français')
    ]
    
    security_keys = [
        'users.title',
        'users.username',
        'users.password',
        'users.role',
        'users.permissions',
        'users.add_user',
        'users.edit_user',
        'users.delete_user'
    ]
    
    all_correct = True
    
    try:
        for lang_code, lang_name in languages:
            print(f"\n📝 اختبار اللغة: {lang_name} ({lang_code})")
            
            # تغيير اللغة
            translator.set_language(lang_code)
            
            missing_translations = []
            for key in security_keys:
                translation = tr(key)
                if translation == key:  # لم يتم العثور على ترجمة
                    missing_translations.append(key)
                else:
                    print(f"   ✅ {key}: {translation}")
            
            if missing_translations:
                print(f"   ❌ ترجمات مفقودة: {len(missing_translations)}")
                for key in missing_translations:
                    print(f"      - {key}")
                all_correct = False
            else:
                print(f"   ✅ جميع الترجمات متوفرة")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ترجمات الأمان: {e}")
        return False

def run_security_system_comprehensive_test():
    """تشغيل جميع اختبارات نظام الأمان والصلاحيات"""
    print("🔐 بدء الاختبار الشامل لنظام الأمان والصلاحيات المتقدم")
    print("=" * 80)
    
    results = []
    
    # اختبار 1: نظام الصلاحيات
    results.append(("نظام الصلاحيات", test_permissions_system()))
    
    # اختبار 2: مدير الأدوار
    results.append(("مدير الأدوار", test_role_manager()))
    
    # اختبار 3: نظام المصادقة
    results.append(("نظام المصادقة", test_user_authentication()))
    
    # اختبار 4: فاحص الصلاحيات
    results.append(("فاحص الصلاحيات", test_permission_checker()))
    
    # اختبار 5: تسجيل التدقيق
    results.append(("تسجيل التدقيق", test_audit_logging()))
    
    # اختبار 6: مدير الأمان
    results.append(("مدير الأمان", test_security_manager()))
    
    # اختبار 7: واجهة المستخدمين
    results.append(("واجهة المستخدمين", test_users_ui()))
    
    # اختبار 8: ترجمات الأمان
    results.append(("ترجمات الأمان", test_security_translations()))
    
    # النتائج النهائية
    print("\n" + "=" * 80)
    print("📊 نتائج الاختبار الشامل لنظام الأمان والصلاحيات")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 نظام الأمان والصلاحيات يعمل بشكل مثالي!")
        print("✨ جميع الميزات متوفرة ومكتملة")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_security_system_comprehensive_test()
