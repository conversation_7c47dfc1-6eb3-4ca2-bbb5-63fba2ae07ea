"""
Enhanced reporting system for OnePos POS System
Provides comprehensive sales reports and analytics
"""

from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
from models.database import db


class ReportsManager:
    """Enhanced reports and analytics manager"""
    
    @staticmethod
    def get_daily_sales_summary(target_date: date = None) -> Dict:
        """Get daily sales summary"""
        if target_date is None:
            target_date = date.today()
        
        date_str = target_date.strftime('%Y-%m-%d')
        
        query = """
        SELECT 
            COUNT(*) as total_sales,
            COALESCE(SUM(total_amount), 0) as total_revenue,
            COALESCE(SUM(tax_amount), 0) as total_tax,
            COALESCE(SUM(discount_amount), 0) as total_discount,
            COALESCE(AVG(total_amount), 0) as average_sale,
            COUNT(DISTINCT customer_id) as unique_customers
        FROM sales 
        WHERE DATE(created_at) = ? AND status = 'completed'
        """
        
        result = db.execute_query(query, (date_str,))
        if result:
            return {
                'date': target_date,
                'total_sales': result[0]['total_sales'],
                'total_revenue': float(result[0]['total_revenue']),
                'total_tax': float(result[0]['total_tax']),
                'total_discount': float(result[0]['total_discount']),
                'average_sale': float(result[0]['average_sale']),
                'unique_customers': result[0]['unique_customers']
            }
        return {}
    
    @staticmethod
    def get_weekly_sales_summary(start_date: date = None) -> Dict:
        """Get weekly sales summary"""
        if start_date is None:
            start_date = date.today() - timedelta(days=6)
        
        end_date = start_date + timedelta(days=6)
        
        query = """
        SELECT 
            DATE(created_at) as sale_date,
            COUNT(*) as daily_sales,
            COALESCE(SUM(total_amount), 0) as daily_revenue
        FROM sales 
        WHERE DATE(created_at) BETWEEN ? AND ? AND status = 'completed'
        GROUP BY DATE(created_at)
        ORDER BY sale_date
        """
        
        results = db.execute_query(query, (start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))
        
        daily_data = []
        total_revenue = 0
        total_sales = 0
        
        for row in results:
            daily_data.append({
                'date': row['sale_date'],
                'sales': row['daily_sales'],
                'revenue': float(row['daily_revenue'])
            })
            total_revenue += float(row['daily_revenue'])
            total_sales += row['daily_sales']
        
        return {
            'start_date': start_date,
            'end_date': end_date,
            'daily_data': daily_data,
            'total_revenue': total_revenue,
            'total_sales': total_sales,
            'average_daily_revenue': total_revenue / 7 if total_revenue > 0 else 0
        }
    
    @staticmethod
    def get_monthly_sales_summary(year: int = None, month: int = None) -> Dict:
        """Get monthly sales summary"""
        if year is None:
            year = date.today().year
        if month is None:
            month = date.today().month
        
        start_date = date(year, month, 1)
        if month == 12:
            end_date = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = date(year, month + 1, 1) - timedelta(days=1)
        
        query = """
        SELECT 
            COUNT(*) as total_sales,
            COALESCE(SUM(total_amount), 0) as total_revenue,
            COALESCE(SUM(tax_amount), 0) as total_tax,
            COALESCE(SUM(discount_amount), 0) as total_discount,
            COUNT(DISTINCT customer_id) as unique_customers,
            COUNT(DISTINCT user_id) as active_cashiers
        FROM sales 
        WHERE DATE(created_at) BETWEEN ? AND ? AND status = 'completed'
        """
        
        result = db.execute_query(query, (start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))
        
        if result:
            return {
                'year': year,
                'month': month,
                'start_date': start_date,
                'end_date': end_date,
                'total_sales': result[0]['total_sales'],
                'total_revenue': float(result[0]['total_revenue']),
                'total_tax': float(result[0]['total_tax']),
                'total_discount': float(result[0]['total_discount']),
                'unique_customers': result[0]['unique_customers'],
                'active_cashiers': result[0]['active_cashiers']
            }
        return {}
    
    @staticmethod
    def get_top_selling_products(limit: int = 10, days: int = 30) -> List[Dict]:
        """Get top selling products"""
        start_date = date.today() - timedelta(days=days)
        
        query = """
        SELECT
            p.id,
            p.name,
            p.sku,
            SUM(si.quantity) as total_quantity,
            SUM(si.total_amount) as total_revenue,
            COUNT(DISTINCT s.id) as times_sold,
            AVG(si.unit_price) as average_price
        FROM sale_items si
        JOIN products p ON si.product_id = p.id
        JOIN sales s ON si.sale_id = s.id
        WHERE DATE(s.created_at) >= ? AND s.status = 'completed'
        GROUP BY p.id, p.name, p.sku
        ORDER BY total_quantity DESC
        LIMIT ?
        """
        
        results = db.execute_query(query, (start_date.strftime('%Y-%m-%d'), limit))
        
        products = []
        for row in results:
            products.append({
                'id': row['id'],
                'name': row['name'],
                'sku': row['sku'],
                'total_quantity': row['total_quantity'],
                'total_revenue': float(row['total_revenue']),
                'times_sold': row['times_sold'],
                'average_price': float(row['average_price'])
            })
        
        return products
    
    @staticmethod
    def get_low_stock_products(threshold: int = None) -> List[Dict]:
        """Get products with low stock"""
        query = """
        SELECT
            id, name, sku, stock_quantity, min_stock_level,
            (stock_quantity - min_stock_level) as stock_difference
        FROM products
        WHERE stock_quantity <= min_stock_level AND is_active = 1
        ORDER BY stock_difference ASC
        """
        
        results = db.execute_query(query)
        
        products = []
        for row in results:
            products.append({
                'id': row['id'],
                'name': row['name'],
                'sku': row['sku'],
                'current_stock': row['stock_quantity'],
                'min_stock': row['min_stock_level'],
                'stock_difference': row['stock_difference']
            })
        
        return products
    
    @staticmethod
    def get_customer_analytics(days: int = 30) -> List[Dict]:
        """Get customer purchase analytics"""
        start_date = date.today() - timedelta(days=days)
        
        query = """
        SELECT 
            c.id,
            c.name,
            c.phone,
            COUNT(s.id) as total_purchases,
            SUM(s.total_amount) as total_spent,
            AVG(s.total_amount) as average_purchase,
            MAX(s.created_at) as last_purchase
        FROM customers c
        JOIN sales s ON c.id = s.customer_id
        WHERE DATE(s.created_at) >= ? AND s.status = 'completed'
        GROUP BY c.id, c.name, c.phone
        ORDER BY total_spent DESC
        """
        
        results = db.execute_query(query, (start_date.strftime('%Y-%m-%d'),))
        
        customers = []
        for row in results:
            customers.append({
                'id': row['id'],
                'name': row['name'],
                'phone': row['phone'],
                'total_purchases': row['total_purchases'],
                'total_spent': float(row['total_spent']),
                'average_purchase': float(row['average_purchase']),
                'last_purchase': row['last_purchase']
            })
        
        return customers
    
    @staticmethod
    def get_payment_method_analytics(days: int = 30) -> List[Dict]:
        """Get payment method analytics"""
        start_date = date.today() - timedelta(days=days)
        
        query = """
        SELECT 
            payment_method,
            COUNT(*) as transaction_count,
            SUM(total_amount) as total_amount,
            AVG(total_amount) as average_amount
        FROM sales 
        WHERE DATE(created_at) >= ? AND status = 'completed'
        GROUP BY payment_method
        ORDER BY total_amount DESC
        """
        
        results = db.execute_query(query, (start_date.strftime('%Y-%m-%d'),))
        
        methods = []
        for row in results:
            methods.append({
                'method': row['payment_method'],
                'transaction_count': row['transaction_count'],
                'total_amount': float(row['total_amount']),
                'average_amount': float(row['average_amount'])
            })
        
        return methods
    
    @staticmethod
    def get_hourly_sales_pattern(days: int = 7) -> List[Dict]:
        """Get hourly sales pattern"""
        start_date = date.today() - timedelta(days=days)
        
        query = """
        SELECT 
            CAST(strftime('%H', created_at) AS INTEGER) as hour,
            COUNT(*) as transaction_count,
            SUM(total_amount) as total_revenue
        FROM sales 
        WHERE DATE(created_at) >= ? AND status = 'completed'
        GROUP BY hour
        ORDER BY hour
        """
        
        results = db.execute_query(query, (start_date.strftime('%Y-%m-%d'),))
        
        hourly_data = []
        for row in results:
            hourly_data.append({
                'hour': row['hour'],
                'transaction_count': row['transaction_count'],
                'total_revenue': float(row['total_revenue'])
            })
        
        return hourly_data

    @staticmethod
    def get_stock_alerts() -> List[Dict]:
        """Get stock alerts for low inventory products"""
        query = """
        SELECT
            id,
            name,
            sku,
            stock_quantity as current_stock,
            min_stock_level as min_stock,
            (min_stock_level - stock_quantity) as shortage
        FROM products
        WHERE stock_quantity <= min_stock_level AND min_stock_level > 0 AND is_active = 1
        ORDER BY (min_stock_level - stock_quantity) DESC
        """

        results = db.execute_query(query)

        alerts = []
        for row in results:
            alerts.append({
                'id': row['id'],
                'name': row['name'],
                'sku': row['sku'],
                'current_stock': row['current_stock'],
                'min_stock': row['min_stock'],
                'shortage': row['shortage']
            })

        return alerts
    
    @staticmethod
    def get_profit_analysis(days: int = 30) -> Dict:
        """Get profit analysis"""
        start_date = date.today() - timedelta(days=days)
        
        query = """
        SELECT
            SUM(si.total_amount) as total_revenue,
            SUM(si.quantity * p.cost_price) as total_cost,
            COUNT(DISTINCT s.id) as total_transactions
        FROM sale_items si
        JOIN products p ON si.product_id = p.id
        JOIN sales s ON si.sale_id = s.id
        WHERE DATE(s.created_at) >= ? AND s.status = 'completed'
        """
        
        result = db.execute_query(query, (start_date.strftime('%Y-%m-%d'),))
        
        if result and result[0]['total_revenue']:
            total_revenue = float(result[0]['total_revenue'])
            total_cost = float(result[0]['total_cost'] or 0)
            total_profit = total_revenue - total_cost
            profit_margin = (total_profit / total_revenue) * 100 if total_revenue > 0 else 0
            
            return {
                'period_days': days,
                'total_revenue': total_revenue,
                'total_cost': total_cost,
                'total_profit': total_profit,
                'profit_margin': profit_margin,
                'total_transactions': result[0]['total_transactions']
            }
        
        return {
            'period_days': days,
            'total_revenue': 0,
            'total_cost': 0,
            'total_profit': 0,
            'profit_margin': 0,
            'total_transactions': 0
        }
