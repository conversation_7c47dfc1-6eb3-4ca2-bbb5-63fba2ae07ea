#!/usr/bin/env python3
"""
أداة إدارة المبيعات البسيطة
Simple Sales Management Tool
"""

import csv
import os
from datetime import datetime


class SalesManager:
    """مدير المبيعات"""
    
    def __init__(self):
        self.sales_file = "sales_tracker.csv"
        self.codes_file = "OnePos_License_Codes.txt"
        
    def main_menu(self):
        """القائمة الرئيسية"""
        while True:
            print("\n" + "="*50)
            print("💰 أداة إدارة مبيعات OnePos")
            print("="*50)
            print("1. 📝 تسجيل بيع جديد")
            print("2. 📊 عرض المبيعات")
            print("3. 🔍 البحث عن عميل")
            print("4. 📈 إحصائيات المبيعات")
            print("5. 🔑 عرض كود متاح")
            print("6. ✏️ تحديث حالة تفعيل")
            print("0. 🚪 خروج")
            print("-"*50)
            
            choice = input("اختر رقم العملية: ").strip()
            
            if choice == "1":
                self.add_sale()
            elif choice == "2":
                self.show_sales()
            elif choice == "3":
                self.search_customer()
            elif choice == "4":
                self.show_statistics()
            elif choice == "5":
                self.show_available_code()
            elif choice == "6":
                self.update_activation_status()
            elif choice == "0":
                print("👋 وداعاً!")
                break
            else:
                print("❌ اختيار غير صحيح!")
    
    def add_sale(self):
        """تسجيل بيع جديد"""
        print("\n📝 تسجيل بيع جديد:")
        print("-"*30)
        
        # الحصول على رقم العميل التالي
        customer_number = self.get_next_customer_number()
        
        # جمع بيانات العميل
        name = input("اسم العميل: ").strip()
        phone = input("رقم الهاتف: ").strip()
        email = input("البريد الإلكتروني (اختياري): ").strip()
        
        # عرض الأسعار
        print("\n💰 الأسعار المتاحة:")
        print("1. الحزمة الأساسية - $99 (كود واحد)")
        print("2. حزمة الأعمال - $199 (3 أكواد)")
        print("3. حزمة الشركات - $499 (10 أكواد)")
        
        price_choice = input("اختر الحزمة (1-3): ").strip()
        
        if price_choice == "1":
            price = "$99"
            package = "الحزمة الأساسية"
        elif price_choice == "2":
            price = "$199"
            package = "حزمة الأعمال"
        elif price_choice == "3":
            price = "$499"
            package = "حزمة الشركات"
        else:
            price = "$99"
            package = "الحزمة الأساسية"
        
        payment_method = input("طريقة الدفع (نقداً/بطاقة/تحويل): ").strip()
        
        # الحصول على كود متاح
        code = self.get_available_code()
        if not code:
            print("❌ لا توجد أكواد متاحة!")
            return
        
        notes = input("ملاحظات (اختياري): ").strip()
        
        # حفظ البيع
        sale_data = {
            'customer_number': f"{customer_number:03d}",
            'name': name,
            'phone': phone,
            'email': email,
            'code': code,
            'date': datetime.now().strftime('%Y-%m-%d'),
            'price': price,
            'payment_method': payment_method,
            'activation_status': 'لم يفعل بعد',
            'activation_date': '',
            'customer_ip': '',
            'computer_name': '',
            'notes': f"{package} - {notes}"
        }
        
        self.save_sale(sale_data)
        
        print(f"\n✅ تم تسجيل البيع بنجاح!")
        print(f"📋 رقم العميل: {sale_data['customer_number']}")
        print(f"👤 اسم العميل: {name}")
        print(f"🔑 كود التفعيل: {code}")
        print(f"💰 السعر: {price}")
        print(f"\n📄 اطبع هذا الكود على غلاف CD:")
        print("="*60)
        print(f"كود التفعيل: {code}")
        print("="*60)
    
    def get_next_customer_number(self):
        """الحصول على رقم العميل التالي"""
        try:
            with open(self.sales_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
                if rows:
                    last_number = int(rows[-1]['رقم العميل'])
                    return last_number + 1
                else:
                    return 1
        except:
            return 1
    
    def get_available_code(self):
        """الحصول على كود متاح"""
        try:
            with open(self.codes_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # البحث عن كود غير مستخدم
            used_codes = self.get_used_codes()
            
            for line in lines:
                if line.startswith('Z0FBQ'):  # بداية الأكواد
                    code = line.split(' | ')[0].strip()
                    if code not in used_codes:
                        return code
            
            return None
        except:
            return None
    
    def get_used_codes(self):
        """الحصول على الأكواد المستخدمة"""
        used_codes = set()
        try:
            with open(self.sales_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    used_codes.add(row['كود التفعيل'])
        except:
            pass
        return used_codes
    
    def save_sale(self, sale_data):
        """حفظ بيانات البيع"""
        file_exists = os.path.exists(self.sales_file)
        
        with open(self.sales_file, 'a', newline='', encoding='utf-8') as f:
            fieldnames = ['رقم العميل', 'اسم العميل', 'رقم الهاتف', 'البريد الإلكتروني',
                         'كود التفعيل', 'تاريخ البيع', 'السعر', 'طريقة الدفع',
                         'حالة التفعيل', 'تاريخ التفعيل', 'IP العميل', 'اسم الكمبيوتر', 'ملاحظات']
            
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            
            if not file_exists:
                writer.writeheader()
            
            writer.writerow({
                'رقم العميل': sale_data['customer_number'],
                'اسم العميل': sale_data['name'],
                'رقم الهاتف': sale_data['phone'],
                'البريد الإلكتروني': sale_data['email'],
                'كود التفعيل': sale_data['code'],
                'تاريخ البيع': sale_data['date'],
                'السعر': sale_data['price'],
                'طريقة الدفع': sale_data['payment_method'],
                'حالة التفعيل': sale_data['activation_status'],
                'تاريخ التفعيل': sale_data['activation_date'],
                'IP العميل': sale_data['customer_ip'],
                'اسم الكمبيوتر': sale_data['computer_name'],
                'ملاحظات': sale_data['notes']
            })
    
    def show_sales(self):
        """عرض المبيعات"""
        print("\n📊 قائمة المبيعات:")
        print("-"*80)
        
        try:
            with open(self.sales_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
                
                if not rows:
                    print("لا توجد مبيعات مسجلة")
                    return
                
                for row in rows:
                    print(f"📋 رقم العميل: {row['رقم العميل']}")
                    print(f"👤 الاسم: {row['اسم العميل']}")
                    print(f"📞 الهاتف: {row['رقم الهاتف']}")
                    print(f"🔑 الكود: {row['كود التفعيل'][:20]}...")
                    print(f"📅 التاريخ: {row['تاريخ البيع']}")
                    print(f"💰 السعر: {row['السعر']}")
                    print(f"🔄 الحالة: {row['حالة التفعيل']}")
                    print("-"*40)
                    
        except FileNotFoundError:
            print("لا توجد مبيعات مسجلة")
    
    def search_customer(self):
        """البحث عن عميل"""
        search_term = input("\n🔍 أدخل اسم العميل أو رقم الهاتف للبحث: ").strip()
        
        try:
            with open(self.sales_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                found = False
                
                for row in reader:
                    if (search_term.lower() in row['اسم العميل'].lower() or 
                        search_term in row['رقم الهاتف']):
                        
                        print(f"\n✅ تم العثور على العميل:")
                        print(f"📋 رقم العميل: {row['رقم العميل']}")
                        print(f"👤 الاسم: {row['اسم العميل']}")
                        print(f"📞 الهاتف: {row['رقم الهاتف']}")
                        print(f"📧 البريد: {row['البريد الإلكتروني']}")
                        print(f"🔑 كود التفعيل: {row['كود التفعيل']}")
                        print(f"📅 تاريخ البيع: {row['تاريخ البيع']}")
                        print(f"💰 السعر: {row['السعر']}")
                        print(f"🔄 حالة التفعيل: {row['حالة التفعيل']}")
                        print(f"📝 ملاحظات: {row['ملاحظات']}")
                        found = True
                        print("-"*40)
                
                if not found:
                    print("❌ لم يتم العثور على العميل")
                    
        except FileNotFoundError:
            print("❌ لا توجد مبيعات مسجلة")
    
    def show_statistics(self):
        """عرض إحصائيات المبيعات"""
        print("\n📈 إحصائيات المبيعات:")
        print("-"*40)
        
        try:
            with open(self.sales_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
                
                if not rows:
                    print("لا توجد مبيعات مسجلة")
                    return
                
                total_sales = len(rows)
                activated = sum(1 for row in rows if row['حالة التفعيل'] == 'مفعل')
                not_activated = total_sales - activated
                
                # حساب الإيرادات
                total_revenue = 0
                for row in rows:
                    price_str = row['السعر'].replace('$', '')
                    try:
                        total_revenue += int(price_str)
                    except:
                        pass
                
                print(f"📦 إجمالي المبيعات: {total_sales}")
                print(f"✅ العملاء المفعلين: {activated}")
                print(f"⏳ العملاء غير المفعلين: {not_activated}")
                print(f"💰 إجمالي الإيرادات: ${total_revenue}")
                print(f"📊 معدل التفعيل: {(activated/total_sales*100):.1f}%")
                
        except FileNotFoundError:
            print("لا توجد مبيعات مسجلة")
    
    def show_available_code(self):
        """عرض كود متاح للاستخدام"""
        code = self.get_available_code()
        if code:
            print(f"\n🔑 كود متاح للاستخدام:")
            print("="*60)
            print(f"{code}")
            print("="*60)
            print("📄 انسخ هذا الكود واطبعه على غلاف CD")
        else:
            print("❌ لا توجد أكواد متاحة!")
    
    def update_activation_status(self):
        """تحديث حالة التفعيل"""
        customer_number = input("\n✏️ أدخل رقم العميل لتحديث حالة التفعيل: ").strip()
        
        # قراءة الملف
        try:
            with open(self.sales_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
        except FileNotFoundError:
            print("❌ لا توجد مبيعات مسجلة")
            return
        
        # البحث عن العميل وتحديث حالته
        found = False
        for row in rows:
            if row['رقم العميل'] == customer_number:
                print(f"👤 العميل: {row['اسم العميل']}")
                print(f"🔄 الحالة الحالية: {row['حالة التفعيل']}")
                
                new_status = input("الحالة الجديدة (مفعل/غير مفعل): ").strip()
                row['حالة التفعيل'] = new_status
                
                if new_status == 'مفعل':
                    row['تاريخ التفعيل'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ip = input("IP العميل (اختياري): ").strip()
                    computer = input("اسم الكمبيوتر (اختياري): ").strip()
                    row['IP العميل'] = ip
                    row['اسم الكمبيوتر'] = computer
                
                found = True
                break
        
        if found:
            # إعادة كتابة الملف
            with open(self.sales_file, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['رقم العميل', 'اسم العميل', 'رقم الهاتف', 'البريد الإلكتروني',
                             'كود التفعيل', 'تاريخ البيع', 'السعر', 'طريقة الدفع',
                             'حالة التفعيل', 'تاريخ التفعيل', 'IP العميل', 'اسم الكمبيوتر', 'ملاحظات']
                
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(rows)
            
            print("✅ تم تحديث حالة التفعيل بنجاح!")
        else:
            print("❌ لم يتم العثور على العميل")


if __name__ == "__main__":
    try:
        sales_manager = SalesManager()
        sales_manager.main_menu()
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء البرنامج")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
