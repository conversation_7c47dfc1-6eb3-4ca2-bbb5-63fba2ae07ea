#!/usr/bin/env python3
"""
اختبار شامل لنظام التقارير والإحصائيات المتقدم - OnePos
Comprehensive Reports and Analytics System Test
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.main_window import MainWindow
from models.user import User
from models.reports import ReportsManager
from models.sale import Sale
from models.product import Product
from models.customer import Customer
from utils.translator import translator, tr
from datetime import date, timedelta

def test_reports_manager():
    """اختبار مدير التقارير"""
    print("📊 اختبار مدير التقارير")
    print("=" * 30)
    
    try:
        # اختبار التقرير اليومي
        daily_summary = ReportsManager.get_daily_sales_summary()
        if daily_summary:
            print("✅ التقرير اليومي: نجح")
            print(f"   📊 إجمالي المبيعات: {daily_summary.get('total_sales', 0)}")
            print(f"   💰 إجمالي الإيرادات: {daily_summary.get('total_revenue', 0):.2f}")
            print(f"   🧍 العملاء الفريدين: {daily_summary.get('unique_customers', 0)}")
        else:
            print("⚠️ التقرير اليومي: لا توجد بيانات")
        
        # اختبار التقرير الأسبوعي
        weekly_summary = ReportsManager.get_weekly_sales_summary()
        if weekly_summary:
            print("✅ التقرير الأسبوعي: نجح")
            print(f"   📊 إجمالي المبيعات: {weekly_summary.get('total_sales', 0)}")
            print(f"   💰 إجمالي الإيرادات: {weekly_summary.get('total_revenue', 0):.2f}")
            print(f"   📈 متوسط الإيرادات اليومية: {weekly_summary.get('average_daily_revenue', 0):.2f}")
        else:
            print("⚠️ التقرير الأسبوعي: لا توجد بيانات")
        
        # اختبار التقرير الشهري
        monthly_summary = ReportsManager.get_monthly_sales_summary()
        if monthly_summary:
            print("✅ التقرير الشهري: نجح")
            print(f"   📊 إجمالي المبيعات: {monthly_summary.get('total_sales', 0)}")
            print(f"   💰 إجمالي الإيرادات: {monthly_summary.get('total_revenue', 0):.2f}")
            print(f"   🧍 العملاء الفريدين: {monthly_summary.get('unique_customers', 0)}")
            print(f"   👤 الكاشيرين النشطين: {monthly_summary.get('active_cashiers', 0)}")
        else:
            print("⚠️ التقرير الشهري: لا توجد بيانات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير التقارير: {e}")
        return False

def test_product_analytics():
    """اختبار تحليل المنتجات"""
    print("\n📦 اختبار تحليل المنتجات")
    print("=" * 35)
    
    try:
        # اختبار أفضل المنتجات مبيع<|im_start|>
        top_products = ReportsManager.get_top_selling_products(limit=5)
        if top_products:
            print("✅ أفضل المنتجات مبيع<|im_start|>: نجح")
            print(f"   📊 عدد المنتجات: {len(top_products)}")
            for i, product in enumerate(top_products[:3], 1):
                print(f"   {i}. {product['name']}: {product['total_quantity']} قطعة")
        else:
            print("⚠️ أفضل المنتجات مبيع<|im_start|>: لا توجد بيانات")
        
        # اختبار تحليل الأرباح
        profit_analysis = ReportsManager.get_profit_analysis()
        if profit_analysis:
            print("✅ تحليل الأرباح: نجح")
            print(f"   💰 إجمالي الإيرادات: {profit_analysis.get('total_revenue', 0):.2f}")
            print(f"   💸 إجمالي التكلفة: {profit_analysis.get('total_cost', 0):.2f}")
            print(f"   💵 إجمالي الربح: {profit_analysis.get('total_profit', 0):.2f}")
            print(f"   📈 هامش الربح: {profit_analysis.get('profit_margin', 0):.1f}%")
        else:
            print("⚠️ تحليل الأرباح: لا توجد بيانات")
        
        # اختبار تنبيهات المخزون
        stock_alerts = ReportsManager.get_stock_alerts()
        if stock_alerts:
            print("✅ تنبيهات المخزون: نجح")
            print(f"   ⚠️ منتجات بمخزون منخفض: {len(stock_alerts)}")
            for alert in stock_alerts[:3]:
                print(f"   - {alert['name']}: {alert['current_stock']}/{alert['min_stock']}")
        else:
            print("⚠️ تنبيهات المخزون: لا توجد تنبيهات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحليل المنتجات: {e}")
        return False

def test_customer_analytics():
    """اختبار تحليل العملاء"""
    print("\n🧍 اختبار تحليل العملاء")
    print("=" * 35)
    
    try:
        # اختبار تحليل العملاء
        customer_analytics = ReportsManager.get_customer_analytics()
        if customer_analytics:
            print("✅ تحليل العملاء: نجح")
            print(f"   📊 عدد العملاء النشطين: {len(customer_analytics)}")
            for i, customer in enumerate(customer_analytics[:3], 1):
                print(f"   {i}. {customer['name']}: {customer['total_spent']:.2f} ({customer['total_purchases']} مشتريات)")
        else:
            print("⚠️ تحليل العملاء: لا توجد بيانات")
        
        # اختبار تحليل طرق الدفع
        payment_analytics = ReportsManager.get_payment_method_analytics()
        if payment_analytics:
            print("✅ تحليل طرق الدفع: نجح")
            print(f"   💳 عدد طرق الدفع: {len(payment_analytics)}")
            for method in payment_analytics:
                print(f"   - {method['method']}: {method['transaction_count']} معاملة ({method['total_amount']:.2f})")
        else:
            print("⚠️ تحليل طرق الدفع: لا توجد بيانات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحليل العملاء: {e}")
        return False

def test_reports_ui():
    """اختبار واجهة التقارير"""
    print("\n🖼️ اختبار واجهة التقارير")
    print("=" * 35)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        main_window = MainWindow(admin_user)
        
        # الانتقال لقسم التقارير
        main_window.load_module('reports')
        app.processEvents()
        
        # البحث عن واجهة التقارير
        reports_widget = None
        for i in range(main_window.content_frame.count()):
            widget = main_window.content_frame.widget(i)
            if hasattr(widget, 'tab_widget'):
                reports_widget = widget
                break
        
        if not reports_widget:
            print("❌ لم يتم العثور على واجهة التقارير")
            return False
        
        print("✅ تم العثور على واجهة التقارير")
        
        # فحص التبويبات
        tab_widget = reports_widget.tab_widget
        tab_count = tab_widget.count()
        print(f"✅ عدد التبويبات: {tab_count}")
        
        # فحص أسماء التبويبات
        expected_tabs = ['daily', 'weekly', 'monthly', 'products', 'customers']
        for i in range(tab_count):
            tab_text = tab_widget.tabText(i)
            print(f"   📋 تبويب {i+1}: {tab_text}")
        
        # فحص العناصر الأساسية
        elements_to_check = [
            ('refresh_button', 'زر التحديث'),
            ('tab_widget', 'تبويبات التقارير'),
            ('reports_manager', 'مدير التقارير')
        ]
        
        missing_elements = []
        for element_name, description in elements_to_check:
            if hasattr(reports_widget, element_name):
                print(f"   ✅ {description}: موجود")
            else:
                print(f"   ❌ {description}: مفقود")
                missing_elements.append(description)
        
        # اختبار تحميل التقارير
        if hasattr(reports_widget, 'load_reports'):
            reports_widget.load_reports()
            app.processEvents()
            print("✅ تحميل التقارير: تم")
        
        return len(missing_elements) == 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة التقارير: {e}")
        return False

def test_reports_translations():
    """اختبار ترجمات التقارير"""
    print("\n🌍 اختبار ترجمات التقارير")
    print("=" * 35)
    
    languages = [
        ('ar', 'العربية'),
        ('en', 'English'),
        ('fr', 'Français')
    ]
    
    reports_keys = [
        'reports.title',
        'reports.daily',
        'reports.weekly',
        'reports.monthly',
        'reports.products',
        'reports.customers',
        'reports.revenue',
        'reports.profit'
    ]
    
    all_correct = True
    
    try:
        for lang_code, lang_name in languages:
            print(f"\n📝 اختبار اللغة: {lang_name} ({lang_code})")
            
            # تغيير اللغة
            translator.set_language(lang_code)
            
            missing_translations = []
            for key in reports_keys:
                translation = tr(key)
                if translation == key:  # لم يتم العثور على ترجمة
                    missing_translations.append(key)
                else:
                    print(f"   ✅ {key}: {translation}")
            
            if missing_translations:
                print(f"   ❌ ترجمات مفقودة: {len(missing_translations)}")
                for key in missing_translations:
                    print(f"      - {key}")
                all_correct = False
            else:
                print(f"   ✅ جميع الترجمات متوفرة")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ترجمات التقارير: {e}")
        return False

def test_advanced_analytics():
    """اختبار التحليلات المتقدمة"""
    print("\n📈 اختبار التحليلات المتقدمة")
    print("=" * 40)
    
    try:
        # اختبار نمط المبيعات بالساعة
        hourly_pattern = ReportsManager.get_hourly_sales_pattern()
        if hourly_pattern:
            print("✅ نمط المبيعات بالساعة: نجح")
            print(f"   ⏰ عدد الساعات المسجلة: {len(hourly_pattern)}")
            if hourly_pattern:
                peak_hour = max(hourly_pattern, key=lambda x: x['transaction_count'])
                print(f"   🔥 ساعة الذروة: {peak_hour['hour']}:00 ({peak_hour['transaction_count']} معاملة)")
        else:
            print("⚠️ نمط المبيعات بالساعة: لا توجد بيانات")
        
        # اختبار إحصائيات متقدمة
        try:
            # فحص وجود بيانات للتحليل
            all_sales = Sale.get_all()
            all_products = Product.get_all()
            all_customers = Customer.get_all()
            
            print(f"📊 إحصائيات عامة:")
            print(f"   🛒 إجمالي المبيعات: {len(all_sales)}")
            print(f"   📦 إجمالي المنتجات: {len(all_products)}")
            print(f"   🧍 إجمالي العملاء: {len(all_customers)}")
            
            if len(all_sales) > 0:
                print("✅ توفر بيانات للتحليل: نجح")
            else:
                print("⚠️ توفر بيانات للتحليل: بيانات محدودة")
                
        except Exception as e:
            print(f"⚠️ خطأ في الإحصائيات العامة: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحليلات المتقدمة: {e}")
        return False

def test_reports_performance():
    """اختبار أداء التقارير"""
    print("\n⚡ اختبار أداء التقارير")
    print("=" * 35)
    
    try:
        import time
        
        # اختبار سرعة التقارير
        start_time = time.time()
        
        # تشغيل عدة تقارير
        ReportsManager.get_daily_sales_summary()
        ReportsManager.get_weekly_sales_summary()
        ReportsManager.get_monthly_sales_summary()
        ReportsManager.get_top_selling_products(5)
        ReportsManager.get_customer_analytics()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"✅ زمن تنفيذ التقارير: {execution_time:.3f} ثانية")
        
        if execution_time < 2.0:
            print("✅ أداء التقارير: ممتاز (أقل من 2 ثانية)")
        elif execution_time < 5.0:
            print("✅ أداء التقارير: جيد (أقل من 5 ثواني)")
        else:
            print("⚠️ أداء التقارير: بطيء (أكثر من 5 ثواني)")
        
        return execution_time < 10.0  # قبول حتى 10 ثواني
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أداء التقارير: {e}")
        return False

def run_reports_system_comprehensive_test():
    """تشغيل جميع اختبارات نظام التقارير والإحصائيات"""
    print("📊 بدء الاختبار الشامل لنظام التقارير والإحصائيات المتقدم")
    print("=" * 80)
    
    results = []
    
    # اختبار 1: مدير التقارير
    results.append(("مدير التقارير", test_reports_manager()))
    
    # اختبار 2: تحليل المنتجات
    results.append(("تحليل المنتجات", test_product_analytics()))
    
    # اختبار 3: تحليل العملاء
    results.append(("تحليل العملاء", test_customer_analytics()))
    
    # اختبار 4: واجهة التقارير
    results.append(("واجهة التقارير", test_reports_ui()))
    
    # اختبار 5: ترجمات التقارير
    results.append(("ترجمات التقارير", test_reports_translations()))
    
    # اختبار 6: التحليلات المتقدمة
    results.append(("التحليلات المتقدمة", test_advanced_analytics()))
    
    # اختبار 7: أداء التقارير
    results.append(("أداء التقارير", test_reports_performance()))
    
    # النتائج النهائية
    print("\n" + "=" * 80)
    print("📊 نتائج الاختبار الشامل لنظام التقارير والإحصائيات")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 نظام التقارير والإحصائيات يعمل بشكل مثالي!")
        print("✨ جميع الميزات متوفرة ومكتملة")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_reports_system_comprehensive_test()
