"""
Detailed Re-testing for OnePos Reports System
More comprehensive tests with edge cases and error handling
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QDate, Qt
from models.database import db
from models.sale import Sale, SaleItem
from models.product import Product
from models.customer import Customer
from models.user import User
from views.reports_widget import ReportsWidget
from datetime import datetime, timedelta

def test_reports_manager_functionality():
    """Test reports manager functionality in detail"""
    print("🔍 Testing Reports Manager Functionality...")
    
    try:
        from models.reports import ReportsManager
        reports_manager = ReportsManager()
        
        # Test daily sales summary
        today = datetime.now().date()
        daily_summary = reports_manager.get_daily_sales_summary(today)
        
        if daily_summary:
            print(f"✅ Daily summary for {today}:")
            print(f"   - Total sales: {daily_summary['total_sales']}")
            print(f"   - Total revenue: ${daily_summary['total_revenue']:.2f}")
            print(f"   - Average sale: ${daily_summary['average_sale']:.2f}")
            print(f"   - Unique customers: {daily_summary['unique_customers']}")
        else:
            print("⚠️ No daily summary data")
        
        # Test weekly sales summary
        week_start = today - timedelta(days=7)
        weekly_summary = reports_manager.get_weekly_sales_summary(week_start)
        
        if weekly_summary:
            print(f"✅ Weekly summary from {week_start}:")
            print(f"   - Total sales: {weekly_summary['total_sales']}")
            print(f"   - Total revenue: ${weekly_summary['total_revenue']:.2f}")
            print(f"   - Average daily revenue: ${weekly_summary['average_daily_revenue']:.2f}")
            
            if 'daily_data' in weekly_summary and weekly_summary['daily_data']:
                print(f"   - Daily breakdown: {len(weekly_summary['daily_data'])} days")
        else:
            print("⚠️ No weekly summary data")
        
        # Test monthly sales summary
        current_year = today.year
        current_month = today.month
        monthly_summary = reports_manager.get_monthly_sales_summary(current_year, current_month)
        
        if monthly_summary:
            print(f"✅ Monthly summary for {current_year}-{current_month:02d}:")
            print(f"   - Total sales: {monthly_summary['total_sales']}")
            print(f"   - Total revenue: ${monthly_summary['total_revenue']:.2f}")
            print(f"   - Unique customers: {monthly_summary['unique_customers']}")
            print(f"   - Active cashiers: {monthly_summary['active_cashiers']}")
        else:
            print("⚠️ No monthly summary data")
        
        # Test top selling products
        top_products = reports_manager.get_top_selling_products(5, 30)
        print(f"✅ Top selling products: {len(top_products)} products")
        
        for i, product in enumerate(top_products[:3]):
            print(f"   {i+1}. {product['name']}: {product['total_quantity']} sold, ${product['total_revenue']:.2f}")
        
        # Test low stock products
        low_stock = reports_manager.get_low_stock_products()
        print(f"✅ Low stock products: {len(low_stock)} products")
        
        # Test customer analytics
        customer_analytics = reports_manager.get_customer_analytics(30)
        print(f"✅ Customer analytics: {len(customer_analytics)} customers")
        
        for i, customer in enumerate(customer_analytics[:3]):
            print(f"   {i+1}. {customer['name']}: {customer['total_purchases']} purchases, ${customer['total_spent']:.2f}")
        
        # Test payment method analytics
        payment_analytics = reports_manager.get_payment_method_analytics(30)
        print(f"✅ Payment method analytics: {len(payment_analytics)} methods")
        
        for method in payment_analytics:
            print(f"   - {method['method']}: {method['transaction_count']} transactions, ${method['total_amount']:.2f}")
        
        # Test profit analysis
        profit_analysis = reports_manager.get_profit_analysis(30)
        if profit_analysis:
            print(f"✅ Profit analysis (30 days):")
            print(f"   - Total revenue: ${profit_analysis['total_revenue']:.2f}")
            print(f"   - Total cost: ${profit_analysis['total_cost']:.2f}")
            print(f"   - Total profit: ${profit_analysis['total_profit']:.2f}")
            print(f"   - Profit margin: {profit_analysis['profit_margin']:.1f}%")
        else:
            print("⚠️ No profit analysis data")
        
        return True
        
    except ImportError:
        print("❌ ReportsManager not available")
        return False
    except Exception as e:
        print(f"❌ Reports manager test failed: {e}")
        return False

def test_reports_widget_ui_detailed():
    """Test all UI components in detail"""
    print("\n🖥️ Testing Reports Widget UI Components...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        reports_widget = ReportsWidget()
        
        # Test tab widget
        tab_widget = reports_widget.tab_widget
        print(f"✅ Tab widget: {tab_widget.count()} tabs")
        
        # Test each tab
        for i in range(tab_widget.count()):
            tab_text = tab_widget.tabText(i)
            tab_widget.setCurrentIndex(i)
            current_widget = tab_widget.currentWidget()
            print(f"✅ Tab {i}: {tab_text} - Widget: {type(current_widget).__name__}")
        
        # Test daily tab components
        if hasattr(reports_widget, 'daily_date_edit'):
            date_value = reports_widget.daily_date_edit.date()
            print(f"✅ Daily date edit: {date_value.toString()}")
        
        if hasattr(reports_widget, 'daily_table'):
            table = reports_widget.daily_table
            print(f"✅ Daily table: {table.rowCount()} rows, {table.columnCount()} columns")
            
            # Test table headers
            headers = []
            for col in range(table.columnCount()):
                header = table.horizontalHeaderItem(col)
                headers.append(header.text() if header else f"Column {col}")
            print(f"   Headers: {headers}")
        
        # Test weekly tab components
        if hasattr(reports_widget, 'weekly_date_edit'):
            weekly_date = reports_widget.weekly_date_edit.date()
            print(f"✅ Weekly date edit: {weekly_date.toString()}")
        
        if hasattr(reports_widget, 'weekly_chart_area'):
            chart_area = reports_widget.weekly_chart_area
            print(f"✅ Weekly chart area: {len(chart_area.toPlainText())} characters")
        
        # Test monthly tab components
        if hasattr(reports_widget, 'month_combo'):
            month_combo = reports_widget.month_combo
            print(f"✅ Month combo: {month_combo.count()} months, current: {month_combo.currentText()}")
        
        if hasattr(reports_widget, 'year_spin'):
            year_spin = reports_widget.year_spin
            print(f"✅ Year spin: {year_spin.value()}")
        
        # Test products tab components
        if hasattr(reports_widget, 'top_products_table'):
            products_table = reports_widget.top_products_table
            print(f"✅ Top products table: {products_table.rowCount()} rows, {products_table.columnCount()} columns")
        
        if hasattr(reports_widget, 'low_stock_table'):
            stock_table = reports_widget.low_stock_table
            print(f"✅ Low stock table: {stock_table.rowCount()} rows, {stock_table.columnCount()} columns")
        
        # Test customers tab components
        if hasattr(reports_widget, 'top_customers_table'):
            customers_table = reports_widget.top_customers_table
            print(f"✅ Top customers table: {customers_table.rowCount()} rows, {customers_table.columnCount()} columns")
        
        if hasattr(reports_widget, 'payment_table'):
            payment_table = reports_widget.payment_table
            print(f"✅ Payment table: {payment_table.rowCount()} rows, {payment_table.columnCount()} columns")
        
        return reports_widget
        
    except Exception as e:
        print(f"❌ UI components test failed: {e}")
        return False

def test_reports_data_loading_detailed(reports_widget):
    """Test detailed data loading for all report types"""
    print("\n📊 Testing Detailed Data Loading...")
    
    try:
        # Test daily report loading
        print("Testing daily report loading...")
        reports_widget.load_daily_report()
        
        # Check if daily summary cards were created
        daily_layout = reports_widget.daily_summary_layout
        card_count = daily_layout.count()
        print(f"✅ Daily summary cards: {card_count}")
        
        # Test weekly report loading
        print("Testing weekly report loading...")
        reports_widget.load_weekly_report()
        
        # Check weekly summary cards
        weekly_layout = reports_widget.weekly_summary_layout
        weekly_card_count = weekly_layout.count()
        print(f"✅ Weekly summary cards: {weekly_card_count}")
        
        # Check weekly chart content
        chart_content = reports_widget.weekly_chart_area.toPlainText()
        print(f"✅ Weekly chart content: {len(chart_content)} characters")
        
        # Test monthly report loading
        print("Testing monthly report loading...")
        reports_widget.load_monthly_report()
        
        # Check monthly summary cards
        monthly_layout = reports_widget.monthly_summary_layout
        monthly_card_count = monthly_layout.count()
        print(f"✅ Monthly summary cards: {monthly_card_count}")
        
        # Check profit analysis labels
        if hasattr(reports_widget, 'profit_revenue_label'):
            revenue_text = reports_widget.profit_revenue_label.text()
            print(f"✅ Profit revenue label: {revenue_text}")
        
        # Test products analytics loading
        print("Testing products analytics loading...")
        reports_widget.load_products_analytics()
        
        top_products_rows = reports_widget.top_products_table.rowCount()
        low_stock_rows = reports_widget.low_stock_table.rowCount()
        print(f"✅ Top products: {top_products_rows} rows")
        print(f"✅ Low stock: {low_stock_rows} rows")
        
        # Test customers analytics loading
        print("Testing customers analytics loading...")
        reports_widget.load_customers_analytics()
        
        top_customers_rows = reports_widget.top_customers_table.rowCount()
        payment_methods_rows = reports_widget.payment_table.rowCount()
        print(f"✅ Top customers: {top_customers_rows} rows")
        print(f"✅ Payment methods: {payment_methods_rows} rows")
        
        return True
        
    except Exception as e:
        print(f"❌ Detailed data loading test failed: {e}")
        return False

def test_reports_date_filtering():
    """Test date filtering functionality"""
    print("\n📅 Testing Date Filtering...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        reports_widget = ReportsWidget()
        
        # Test different date ranges
        today = QDate.currentDate()
        yesterday = today.addDays(-1)
        last_week = today.addDays(-7)
        last_month = today.addDays(-30)
        
        # Test daily date changes
        print("Testing daily date changes...")
        reports_widget.daily_date_edit.setDate(yesterday)
        reports_widget.load_daily_report()
        print(f"✅ Daily report for yesterday: {yesterday.toString()}")
        
        reports_widget.daily_date_edit.setDate(today)
        reports_widget.load_daily_report()
        print(f"✅ Daily report for today: {today.toString()}")
        
        # Test weekly date changes
        print("Testing weekly date changes...")
        reports_widget.weekly_date_edit.setDate(last_week)
        reports_widget.load_weekly_report()
        print(f"✅ Weekly report from: {last_week.toString()}")
        
        # Test monthly changes
        print("Testing monthly changes...")
        current_month = reports_widget.month_combo.currentIndex()
        current_year = reports_widget.year_spin.value()
        
        # Change to previous month
        if current_month > 0:
            reports_widget.month_combo.setCurrentIndex(current_month - 1)
        else:
            reports_widget.month_combo.setCurrentIndex(11)
            reports_widget.year_spin.setValue(current_year - 1)
        
        reports_widget.load_monthly_report()
        print(f"✅ Monthly report for: {reports_widget.month_combo.currentText()} {reports_widget.year_spin.value()}")
        
        # Reset to current month
        reports_widget.month_combo.setCurrentIndex(current_month)
        reports_widget.year_spin.setValue(current_year)
        reports_widget.load_monthly_report()
        
        return True
        
    except Exception as e:
        print(f"❌ Date filtering test failed: {e}")
        return False

def test_reports_edge_cases():
    """Test edge cases in reports"""
    print("\n🔍 Testing Reports Edge Cases...")
    
    try:
        from models.reports import ReportsManager
        reports_manager = ReportsManager()
        
        # Test with future dates
        future_date = datetime.now().date() + timedelta(days=30)
        future_summary = reports_manager.get_daily_sales_summary(future_date)
        
        if future_summary:
            print(f"✅ Future date summary: {future_summary['total_sales']} sales")
        else:
            print("✅ Future date returns no data (expected)")
        
        # Test with very old dates
        old_date = datetime.now().date() - timedelta(days=365)
        old_summary = reports_manager.get_daily_sales_summary(old_date)
        
        if old_summary:
            print(f"✅ Old date summary: {old_summary['total_sales']} sales")
        else:
            print("✅ Old date returns no data (expected)")
        
        # Test with invalid parameters
        try:
            invalid_products = reports_manager.get_top_selling_products(-1, 30)
            print(f"⚠️ Negative limit accepted: {len(invalid_products)} products")
        except Exception as e:
            print(f"✅ Negative limit rejected: {type(e).__name__}")
        
        try:
            invalid_days = reports_manager.get_customer_analytics(-10)
            print(f"⚠️ Negative days accepted: {len(invalid_days)} customers")
        except Exception as e:
            print(f"✅ Negative days rejected: {type(e).__name__}")
        
        # Test with zero parameters
        zero_products = reports_manager.get_top_selling_products(0, 30)
        print(f"✅ Zero limit: {len(zero_products)} products")
        
        zero_days = reports_manager.get_customer_analytics(0)
        print(f"✅ Zero days: {len(zero_days)} customers")
        
        return True
        
    except ImportError:
        print("❌ ReportsManager not available for edge case testing")
        return False
    except Exception as e:
        print(f"❌ Edge cases test failed: {e}")
        return False

def test_reports_performance_detailed():
    """Test detailed performance of reports system"""
    print("\n⚡ Testing Detailed Reports Performance...")
    
    try:
        import time
        from models.reports import ReportsManager
        
        reports_manager = ReportsManager()
        
        # Test multiple date ranges
        test_dates = [
            datetime.now().date(),
            datetime.now().date() - timedelta(days=1),
            datetime.now().date() - timedelta(days=7),
            datetime.now().date() - timedelta(days=30)
        ]
        
        for test_date in test_dates:
            start_time = time.time()
            summary = reports_manager.get_daily_sales_summary(test_date)
            end_time = time.time()
            
            print(f"✅ Daily summary for {test_date}: {end_time - start_time:.4f}s")
        
        # Test different limit sizes for top products
        limits = [1, 5, 10, 20, 50]
        
        for limit in limits:
            start_time = time.time()
            products = reports_manager.get_top_selling_products(limit, 30)
            end_time = time.time()
            
            print(f"✅ Top {limit} products: {end_time - start_time:.4f}s ({len(products)} results)")
        
        # Test UI loading performance
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        start_time = time.time()
        reports_widget = ReportsWidget()
        creation_time = time.time() - start_time
        print(f"✅ Widget creation: {creation_time:.4f}s")
        
        start_time = time.time()
        reports_widget.load_reports()
        loading_time = time.time() - start_time
        print(f"✅ All reports loading: {loading_time:.4f}s")
        
        # Performance evaluation
        if creation_time < 1.0 and loading_time < 2.0:
            print("✅ Reports performance excellent")
        elif creation_time < 2.0 and loading_time < 5.0:
            print("✅ Reports performance good")
        else:
            print("⚠️ Reports performance could be improved")
        
        return True
        
    except ImportError:
        print("❌ ReportsManager not available for performance testing")
        return False
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def test_reports_error_handling():
    """Test error handling in reports system"""
    print("\n⚠️ Testing Reports Error Handling...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        reports_widget = ReportsWidget()
        
        # Test with corrupted date
        try:
            # This should not cause crashes
            reports_widget.daily_date_edit.setDate(QDate(1900, 1, 1))
            reports_widget.load_daily_report()
            print("✅ Very old date handled gracefully")
        except Exception as e:
            print(f"⚠️ Old date caused error: {e}")
        
        # Test with future date
        try:
            reports_widget.daily_date_edit.setDate(QDate(2030, 12, 31))
            reports_widget.load_daily_report()
            print("✅ Future date handled gracefully")
        except Exception as e:
            print(f"⚠️ Future date caused error: {e}")
        
        # Test invalid month/year combinations
        try:
            reports_widget.month_combo.setCurrentIndex(1)  # February
            reports_widget.year_spin.setValue(2021)  # Non-leap year
            reports_widget.load_monthly_report()
            print("✅ February 2021 handled correctly")
        except Exception as e:
            print(f"⚠️ February 2021 caused error: {e}")
        
        # Test rapid date changes
        try:
            for i in range(10):
                date = QDate.currentDate().addDays(-i)
                reports_widget.daily_date_edit.setDate(date)
                reports_widget.load_daily_report()
            print("✅ Rapid date changes handled well")
        except Exception as e:
            print(f"⚠️ Rapid changes caused error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def run_detailed_reports_test():
    """Run all detailed reports tests"""
    print("🔍 STARTING DETAILED REPORTS SYSTEM TEST")
    print("=" * 60)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # Test 1: Reports Manager Functionality
    test_results.append(("Reports Manager", test_reports_manager_functionality()))
    
    # Test 2: UI Components
    reports_widget = test_reports_widget_ui_detailed()
    test_results.append(("UI Components", reports_widget is not False))
    
    if reports_widget:
        # Test 3: Data Loading
        test_results.append(("Data Loading", test_reports_data_loading_detailed(reports_widget)))
    
    # Test 4: Date Filtering
    test_results.append(("Date Filtering", test_reports_date_filtering()))
    
    # Test 5: Edge Cases
    test_results.append(("Edge Cases", test_reports_edge_cases()))
    
    # Test 6: Performance
    test_results.append(("Performance", test_reports_performance_detailed()))
    
    # Test 7: Error Handling
    test_results.append(("Error Handling", test_reports_error_handling()))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 DETAILED REPORTS TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL DETAILED TESTS PASSED! Reports system is robust and reliable!")
    else:
        print("⚠️  Some detailed tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_detailed_reports_test()
