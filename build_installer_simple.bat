@echo off
echo Building OnePos Installer...

if not exist "dist\OnePos\OnePos.exe" (
    echo Application not found! Run build_simple.bat first.
    pause
    exit /b 1
)

echo Application found: dist\OnePos\OnePos.exe

if not exist "installer_output" mkdir "installer_output"

echo Looking for Inno Setup...

set "INNO_PATH="
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
) else (
    echo Inno Setup not found!
    echo Download from: https://jrsoftware.org/isdl.php
    pause
    exit /b 1
)

echo Inno Setup found: %INNO_PATH%

echo Building installer with simple script...
"%INNO_PATH%" "onepos_simple.iss"

if %errorlevel% neq 0 (
    echo Installer build failed!
    pause
    exit /b 1
)

echo Installer build completed successfully!
for %%f in (installer_output\*.exe) do (
    echo Installer created: %%f
    echo File size:
    dir "%%f" | find "OnePos_Setup"
)

echo.
echo Installer is ready for distribution!
pause
