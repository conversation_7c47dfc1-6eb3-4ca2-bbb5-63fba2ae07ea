"""
Sale model for OnePos POS System
"""

from datetime import datetime
from .database import db
from .product import Product
from .customer import Customer


class Sale:
    """Sale model"""
    
    def __init__(self, sale_id=None, invoice_number=None, customer_id=None, user_id=None,
                 subtotal=0, tax_amount=0, discount_amount=0, total_amount=0, 
                 paid_amount=0, payment_method='cash', status='completed', notes=None):
        self.id = sale_id
        self.invoice_number = invoice_number
        self.customer_id = customer_id
        self.user_id = user_id
        self.subtotal = subtotal
        self.tax_amount = tax_amount
        self.discount_amount = discount_amount
        self.total_amount = total_amount
        self.paid_amount = paid_amount
        self.payment_method = payment_method
        self.status = status
        self.notes = notes
        self.created_at = None
        self.items = []
    
    @classmethod
    def create(cls, user_id, items, **kwargs):
        """Create a new sale"""
        # Generate invoice number
        invoice_number = cls._generate_invoice_number()
        
        # Calculate totals
        subtotal = sum(item['quantity'] * item['unit_price'] - item.get('discount_amount', 0) for item in items)
        tax_rate = kwargs.get('tax_rate', 0)
        tax_amount = subtotal * (tax_rate / 100) if tax_rate > 0 else 0
        discount_amount = kwargs.get('discount_amount', 0)
        total_amount = subtotal + tax_amount - discount_amount
        
        # Create sale record
        sale_id = db.execute_update("""
            INSERT INTO sales (invoice_number, customer_id, user_id, subtotal, tax_amount, 
                             discount_amount, total_amount, paid_amount, payment_method, status, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            invoice_number,
            kwargs.get('customer_id'),
            user_id,
            subtotal,
            tax_amount,
            discount_amount,
            total_amount,
            kwargs.get('paid_amount', total_amount),
            kwargs.get('payment_method', 'cash'),
            kwargs.get('status', 'completed'),
            kwargs.get('notes')
        ))
        
        # Create sale items
        for item in items:
            SaleItem.create(sale_id, item)
        
        # Update product stock
        for item in items:
            product = Product.get_by_id(item['product_id'])
            if product:
                product.adjust_stock(
                    -item['quantity'],
                    'sale',
                    sale_id,
                    'sale',
                    f"Sale #{invoice_number}",
                    user_id
                )
        
        return cls.get_by_id(sale_id)
    
    @classmethod
    def get_by_id(cls, sale_id):
        """Get sale by ID"""
        result = db.execute_query("SELECT * FROM sales WHERE id = ?", (sale_id,))
        if result:
            sale = cls._from_db_row(result[0])
            sale.load_items()
            return sale
        return None
    
    @classmethod
    def get_by_invoice_number(cls, invoice_number):
        """Get sale by invoice number"""
        result = db.execute_query("SELECT * FROM sales WHERE invoice_number = ?", (invoice_number,))
        if result:
            sale = cls._from_db_row(result[0])
            sale.load_items()
            return sale
        return None
    
    @classmethod
    def get_all(cls, limit=None, offset=None, status=None, user_id=None, customer_id=None, 
                date_from=None, date_to=None):
        """Get all sales with filters"""
        query = "SELECT * FROM sales WHERE 1=1"
        params = []
        
        if status:
            query += " AND status = ?"
            params.append(status)
        
        if user_id:
            query += " AND user_id = ?"
            params.append(user_id)
        
        if customer_id:
            query += " AND customer_id = ?"
            params.append(customer_id)
        
        if date_from:
            query += " AND DATE(created_at) >= ?"
            params.append(date_from)
        
        if date_to:
            query += " AND DATE(created_at) <= ?"
            params.append(date_to)
        
        query += " ORDER BY created_at DESC"
        
        if limit:
            query += " LIMIT ?"
            params.append(limit)
            
            if offset:
                query += " OFFSET ?"
                params.append(offset)
        
        results = db.execute_query(query, params if params else None)
        sales = [cls._from_db_row(row) for row in results]
        
        # Load items for each sale
        for sale in sales:
            sale.load_items()
        
        return sales

    @classmethod
    def get_by_customer_id(cls, customer_id, status=None, limit=None):
        """Get sales by customer ID"""
        return cls.get_all(customer_id=customer_id, status=status, limit=limit)

    @classmethod
    def get_daily_sales(cls, date=None):
        """Get sales for a specific date"""
        if date is None:
            date = datetime.now().date()
        
        return cls.get_all(date_from=date, date_to=date, status='completed')
    
    @classmethod
    def get_sales_summary(cls, date_from=None, date_to=None):
        """Get sales summary"""
        query = """
            SELECT 
                COUNT(*) as total_sales,
                COALESCE(SUM(total_amount), 0) as total_revenue,
                COALESCE(SUM(tax_amount), 0) as total_tax,
                COALESCE(SUM(discount_amount), 0) as total_discount,
                COALESCE(AVG(total_amount), 0) as average_sale
            FROM sales 
            WHERE status = 'completed'
        """
        params = []
        
        if date_from:
            query += " AND DATE(created_at) >= ?"
            params.append(date_from)
        
        if date_to:
            query += " AND DATE(created_at) <= ?"
            params.append(date_to)
        
        result = db.execute_query(query, params if params else None)
        return result[0] if result else {}
    
    def load_items(self):
        """Load sale items"""
        self.items = SaleItem.get_by_sale_id(self.id)

    def get_items(self):
        """Get sale items"""
        if not hasattr(self, 'items') or not self.items:
            self.load_items()
        return self.items
    
    def update(self, **kwargs):
        """Update sale information"""
        allowed_fields = ['customer_id', 'subtotal', 'tax_amount', 'discount_amount',
                         'total_amount', 'paid_amount', 'payment_method', 'status', 'notes']
        update_fields = []
        params = []
        
        for field, value in kwargs.items():
            if field in allowed_fields:
                update_fields.append(f"{field} = ?")
                params.append(value)
        
        if update_fields:
            params.append(self.id)
            query = f"UPDATE sales SET {', '.join(update_fields)} WHERE id = ?"
            db.execute_update(query, params)
            
            # Refresh object
            updated_sale = self.get_by_id(self.id)
            if updated_sale:
                self.__dict__.update(updated_sale.__dict__)
    
    def cancel(self, user_id, reason=None):
        """Cancel sale and restore stock"""
        if self.status == 'cancelled':
            return False
        
        # Restore stock for each item
        for item in self.items:
            product = Product.get_by_id(item.product_id)
            if product:
                product.adjust_stock(
                    item.quantity,
                    'return',
                    self.id,
                    'sale_cancellation',
                    f"Sale #{self.invoice_number} cancelled: {reason}",
                    user_id
                )
        
        # Update sale status
        self.update(status='cancelled', notes=f"Cancelled: {reason}")
        return True
    
    def get_customer(self):
        """Get sale customer"""
        if self.customer_id:
            return Customer.get_by_id(self.customer_id)
        return None
    
    def get_change_amount(self):
        """Calculate change amount"""
        return max(0, self.paid_amount - self.total_amount)
    
    def is_paid_in_full(self):
        """Check if sale is paid in full"""
        return self.paid_amount >= self.total_amount
    
    def to_dict(self):
        """Convert sale to dictionary"""
        return {
            'id': self.id,
            'invoice_number': self.invoice_number,
            'customer_id': self.customer_id,
            'user_id': self.user_id,
            'subtotal': self.subtotal,
            'tax_amount': self.tax_amount,
            'discount_amount': self.discount_amount,
            'total_amount': self.total_amount,
            'paid_amount': self.paid_amount,
            'payment_method': self.payment_method,
            'status': self.status,
            'notes': self.notes,
            'created_at': self.created_at,
            'items': [item.to_dict() for item in self.items]
        }
    
    @classmethod
    def _generate_invoice_number(cls):
        """Generate unique invoice number"""
        from datetime import datetime
        
        # Get current date
        today = datetime.now()
        date_prefix = today.strftime("%Y%m%d")
        
        # Get next sequence number for today
        result = db.execute_query("""
            SELECT COUNT(*) as count 
            FROM sales 
            WHERE DATE(created_at) = DATE('now')
        """)
        
        sequence = (result[0]['count'] if result else 0) + 1
        
        return f"INV{date_prefix}{sequence:04d}"
    
    @classmethod
    def _from_db_row(cls, row):
        """Create Sale object from database row"""
        sale = cls(
            sale_id=row['id'],
            invoice_number=row['invoice_number'],
            customer_id=row['customer_id'],
            user_id=row['user_id'],
            subtotal=row['subtotal'],
            tax_amount=row['tax_amount'],
            discount_amount=row['discount_amount'],
            total_amount=row['total_amount'],
            paid_amount=row['paid_amount'],
            payment_method=row['payment_method'],
            status=row['status'],
            notes=row['notes']
        )
        sale.created_at = row['created_at']
        return sale


class SaleItem:
    """Sale item model"""
    
    def __init__(self, item_id=None, sale_id=None, product_id=None, quantity=0,
                 unit_price=0, discount_amount=0, total_amount=0):
        self.id = item_id
        self.sale_id = sale_id
        self.product_id = product_id
        self.quantity = quantity
        self.unit_price = unit_price
        self.discount_amount = discount_amount
        self.total_amount = total_amount
    
    @classmethod
    def create(cls, sale_id, item_data):
        """Create a new sale item"""
        total_amount = (item_data['quantity'] * item_data['unit_price']) - item_data.get('discount_amount', 0)
        
        item_id = db.execute_update("""
            INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, discount_amount, total_amount)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            sale_id,
            item_data['product_id'],
            item_data['quantity'],
            item_data['unit_price'],
            item_data.get('discount_amount', 0),
            total_amount
        ))
        
        return cls.get_by_id(item_id)
    
    @classmethod
    def get_by_id(cls, item_id):
        """Get sale item by ID"""
        result = db.execute_query("SELECT * FROM sale_items WHERE id = ?", (item_id,))
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def get_by_sale_id(cls, sale_id):
        """Get all items for a sale"""
        results = db.execute_query("SELECT * FROM sale_items WHERE sale_id = ?", (sale_id,))
        return [cls._from_db_row(row) for row in results]
    
    def get_product(self):
        """Get sale item product"""
        return Product.get_by_id(self.product_id)
    
    def to_dict(self):
        """Convert sale item to dictionary"""
        return {
            'id': self.id,
            'sale_id': self.sale_id,
            'product_id': self.product_id,
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'discount_amount': self.discount_amount,
            'total_amount': self.total_amount
        }
    
    @classmethod
    def _from_db_row(cls, row):
        """Create SaleItem object from database row"""
        return cls(
            item_id=row['id'],
            sale_id=row['sale_id'],
            product_id=row['product_id'],
            quantity=row['quantity'],
            unit_price=row['unit_price'],
            discount_amount=row['discount_amount'],
            total_amount=row['total_amount']
        )
