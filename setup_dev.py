#!/usr/bin/env python3
"""
Development setup script for OnePos POS System
"""

import os
import sys
import subprocess
import json


def install_dependencies():
    """Install required dependencies"""
    print("Installing Python dependencies...")
    
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✓ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing dependencies: {e}")
        return False


def create_sample_data():
    """Create sample data for development"""
    print("Creating sample data...")
    
    try:
        # Import after dependencies are installed
        from models.database import db
        from models.product import Product, Category
        from models.customer import Customer
        
        # Create sample categories
        categories = [
            ("Electronics", "Electronic devices and accessories"),
            ("Food & Beverages", "Food and drink items"),
            ("Clothing", "Clothing and fashion items"),
            ("Books", "Books and magazines"),
            ("Home & Garden", "Home and garden products")
        ]
        
        for name, description in categories:
            existing = Category.get_all()
            category_names = [c.name for c in existing]
            if name not in category_names:
                Category.create(name, description)
        
        # Create sample products
        sample_products = [
            {
                "name": "Laptop Computer",
                "description": "High-performance laptop for business use",
                "selling_price": 15000.00,
                "cost_price": 12000.00,
                "stock_quantity": 10,
                "min_stock_level": 2,
                "category_id": 1
            },
            {
                "name": "Coffee - Premium Blend",
                "description": "Premium coffee blend 250g",
                "selling_price": 45.00,
                "cost_price": 30.00,
                "stock_quantity": 50,
                "min_stock_level": 10,
                "category_id": 2
            },
            {
                "name": "T-Shirt - Cotton",
                "description": "100% cotton t-shirt, various sizes",
                "selling_price": 120.00,
                "cost_price": 80.00,
                "stock_quantity": 25,
                "min_stock_level": 5,
                "category_id": 3
            },
            {
                "name": "Programming Book",
                "description": "Learn Python programming",
                "selling_price": 250.00,
                "cost_price": 180.00,
                "stock_quantity": 15,
                "min_stock_level": 3,
                "category_id": 4
            },
            {
                "name": "Plant Pot",
                "description": "Ceramic plant pot, medium size",
                "selling_price": 85.00,
                "cost_price": 50.00,
                "stock_quantity": 20,
                "min_stock_level": 5,
                "category_id": 5
            }
        ]
        
        for product_data in sample_products:
            # Check if product already exists
            existing_products = Product.search(product_data["name"])
            if not existing_products:
                Product.create(**product_data)
        
        # Create sample customers
        sample_customers = [
            {
                "name": "Ahmed Hassan",
                "phone": "+212 600 123 456",
                "email": "<EMAIL>",
                "address": "123 Main Street, Casablanca"
            },
            {
                "name": "Fatima Zahra",
                "phone": "+212 661 234 567",
                "email": "<EMAIL>",
                "address": "456 Oak Avenue, Rabat"
            },
            {
                "name": "Mohamed Ali",
                "phone": "+212 662 345 678",
                "email": "<EMAIL>",
                "address": "789 Pine Road, Marrakech"
            }
        ]
        
        for customer_data in sample_customers:
            # Check if customer already exists
            existing = Customer.search(customer_data["name"])
            if not existing:
                Customer.create(**customer_data)
        
        print("✓ Sample data created successfully")
        return True
        
    except Exception as e:
        print(f"✗ Error creating sample data: {e}")
        return False


def setup_development_config():
    """Setup development configuration"""
    print("Setting up development configuration...")
    
    try:
        # Load current config
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Update for development
        config['app']['language'] = 'en'  # Default to English for development
        config['security']['require_login'] = False  # Skip login for development
        config['ui']['fullscreen'] = False
        config['ui']['window_width'] = 1400
        config['ui']['window_height'] = 900
        
        # Save updated config
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        
        print("✓ Development configuration updated")
        return True
        
    except Exception as e:
        print(f"✗ Error setting up development config: {e}")
        return False


def run_tests():
    """Run setup tests"""
    print("Running setup tests...")
    
    try:
        result = subprocess.run([sys.executable, 'test_setup.py'], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        
        if result.returncode == 0:
            print("✓ All tests passed")
            return True
        else:
            print("✗ Some tests failed")
            if result.stderr:
                print("Errors:", result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Error running tests: {e}")
        return False


def main():
    """Main setup function"""
    print("OnePos Development Setup")
    print("=" * 50)
    
    success = True
    
    # Install dependencies
    if not install_dependencies():
        success = False
    
    # Setup development config
    if not setup_development_config():
        success = False
    
    # Create sample data
    if not create_sample_data():
        success = False
    
    # Run tests
    if not run_tests():
        success = False
    
    print("\n" + "=" * 50)
    
    if success:
        print("✓ Development setup completed successfully!")
        print("\nNext steps:")
        print("1. Run 'python main.py' to start the application")
        print("2. Login with username: admin, password: admin123")
        print("3. Explore the sample data and features")
        print("4. Start developing!")
        
        # Ask if user wants to start the application
        try:
            start_app = input("\nWould you like to start OnePos now? (y/n): ").lower().strip()
            if start_app in ['y', 'yes']:
                print("Starting OnePos...")
                subprocess.run([sys.executable, 'main.py'])
        except KeyboardInterrupt:
            print("\nSetup completed.")
        
        return 0
    else:
        print("✗ Development setup failed!")
        print("Please check the errors above and try again.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
