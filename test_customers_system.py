"""
Comprehensive Testing for OnePos Customers System
Tests all customer management functionality including UI components and business logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from models.database import db
from models.customer import Customer
from models.sale import Sale
from models.user import User
from views.customers_widget import CustomersWidget
from datetime import datetime, timedel<PERSON>

def test_customers_database_setup():
    """Test customers database setup and required data"""
    print("🔗 Testing Customers Database Setup...")
    
    try:
        # Check required tables
        result = db.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row['name'] for row in result]
        
        required_tables = ['customers', 'sales', 'users']
        missing_tables = [table for table in required_tables if table not in tables]
        
        if missing_tables:
            print(f"❌ Missing tables: {missing_tables}")
            return False
        else:
            print(f"✅ All required tables exist: {required_tables}")
        
        # Check if we have customers data
        customers = Customer.get_all()
        print(f"✅ Found {len(customers)} customers in database")
        
        # Display some customers
        for customer in customers[:5]:
            print(f"   - {customer.name}: {customer.email or 'No email'} ({customer.phone or 'No phone'})")
        
        # Check customer types/categories
        customer_types = set()
        for customer in customers:
            if hasattr(customer, 'customer_type') and customer.customer_type:
                customer_types.add(customer.customer_type)
        
        if customer_types:
            print(f"✅ Customer types found: {list(customer_types)}")
        else:
            print("⚠️ No customer types defined")
        
        return True
        
    except Exception as e:
        print(f"❌ Database setup test failed: {e}")
        return False

def test_customers_widget_creation():
    """Test customers widget creation and initialization"""
    print("\n🖥️ Testing Customers Widget Creation...")
    
    try:
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create customers widget
        customers_widget = CustomersWidget()
        
        if customers_widget:
            print("✅ CustomersWidget created successfully")
            
            # Check main components
            if hasattr(customers_widget, 'customers_table'):
                print("✅ Customers table exists")
                columns = customers_widget.customers_table.columnCount()
                print(f"   - Columns: {columns}")
            
            if hasattr(customers_widget, 'search_edit'):
                print("✅ Search field exists")
            
            if hasattr(customers_widget, 'customer_type_filter'):
                print("✅ Customer type filter exists")
            
            # Check buttons
            buttons_to_check = [
                'add_button',
                'edit_button',
                'delete_button',
                'refresh_button',
                'import_button',
                'export_button'
            ]
            
            for button_name in buttons_to_check:
                if hasattr(customers_widget, button_name):
                    print(f"✅ {button_name} exists")
                else:
                    print(f"❌ {button_name} missing")
            
            # Check summary components
            summary_components = [
                'total_customers_label',
                'active_customers_label',
                'recent_customers_label'
            ]
            
            for component_name in summary_components:
                if hasattr(customers_widget, component_name):
                    print(f"✅ {component_name} exists")
                else:
                    print(f"⚠️ {component_name} missing")
            
            return customers_widget
        else:
            print("❌ Failed to create CustomersWidget")
            return False
            
    except Exception as e:
        print(f"❌ Customers widget creation test failed: {e}")
        return False

def test_customers_data_loading(customers_widget):
    """Test customers data loading functionality"""
    print("\n📊 Testing Customers Data Loading...")
    
    try:
        # Test loading customers
        customers_widget.load_customers()
        customers_count = customers_widget.customers_table.rowCount()
        print(f"✅ Loaded {customers_count} customers into table")
        
        # Test loading customer types
        if hasattr(customers_widget, 'load_customer_types'):
            customers_widget.load_customer_types()
            types_count = customers_widget.customer_type_filter.count()
            print(f"✅ Loaded {types_count} customer types into filter")
        
        # Test table data
        if customers_count > 0:
            for row in range(min(3, customers_count)):
                row_data = []
                for col in range(customers_widget.customers_table.columnCount()):
                    item = customers_widget.customers_table.item(row, col)
                    row_data.append(item.text() if item else "")
                print(f"   Row {row}: {row_data[:4]}...")  # Show first 4 columns
        
        return True
        
    except Exception as e:
        print(f"❌ Customers data loading test failed: {e}")
        return False

def test_customers_filtering(customers_widget):
    """Test customers filtering functionality"""
    print("\n🔍 Testing Customers Filtering...")
    
    try:
        # Test search by name
        if hasattr(customers_widget, 'search_edit') and hasattr(customers_widget, 'search_customers'):
            customers_widget.search_edit.setText("Customer")
            customers_widget.search_customers()
            search_results = customers_widget.customers_table.rowCount()
            print(f"✅ Search for 'Customer' returned {search_results} results")
            
            # Clear search
            customers_widget.search_edit.setText("")
            customers_widget.search_customers()
            all_results = customers_widget.customers_table.rowCount()
            print(f"✅ Cleared search, showing {all_results} customers")
        
        # Test customer type filtering
        if hasattr(customers_widget, 'customer_type_filter') and hasattr(customers_widget, 'filter_by_type'):
            # Test different filter options
            for i in range(customers_widget.customer_type_filter.count()):
                filter_text = customers_widget.customer_type_filter.itemText(i)
                customers_widget.customer_type_filter.setCurrentIndex(i)
                customers_widget.filter_by_type()
                filtered_results = customers_widget.customers_table.rowCount()
                print(f"✅ Filter '{filter_text}': {filtered_results} results")
        
        return True
        
    except Exception as e:
        print(f"❌ Customers filtering test failed: {e}")
        return False

def test_customer_crud_operations(customers_widget):
    """Test customer CRUD operations"""
    print("\n📝 Testing Customer CRUD Operations...")
    
    try:
        # Get initial customer count
        initial_count = len(Customer.get_all())
        print(f"✅ Initial customers count: {initial_count}")
        
        # Test adding a new customer
        import time
        timestamp = str(int(time.time()))[-6:]
        
        test_customer_data = {
            'name': f'Test Customer {timestamp}',
            'email': f'test{timestamp}@example.com',
            'phone': f'123-456-{timestamp}',
            'address': f'{timestamp} Test Street',
            'city': 'Test City',
            'customer_type': 'regular',
            'notes': 'Test customer for CRUD operations'
        }
        
        new_customer = Customer.create(**test_customer_data)
        if new_customer:
            print(f"✅ Customer created: {new_customer.name}")
            
            # Refresh the widget
            customers_widget.load_customers()
            new_count = customers_widget.customers_table.rowCount()
            print(f"✅ Customers count after add: {new_count}")
            
            # Test editing the customer
            updated_data = {
                'name': f'Updated Test Customer {timestamp}',
                'phone': f'987-654-{timestamp}'
            }
            new_customer.update(**updated_data)
            print(f"✅ Customer updated: {new_customer.name}")
            
            # Test customer lookup
            found_customer = Customer.get_by_email(new_customer.email)
            if found_customer and found_customer.id == new_customer.id:
                print("✅ Customer lookup by email successful")
            else:
                print("❌ Customer lookup by email failed")
                return False
            
            # Test soft delete
            new_customer.delete()
            deleted_customer = Customer.get_by_id(new_customer.id)
            if deleted_customer and not deleted_customer.is_active:
                print("✅ Customer soft deleted successfully")
                customers_widget.load_customers()
                final_count = customers_widget.customers_table.rowCount()
                print(f"✅ Customers count after delete: {final_count}")
            else:
                print("❌ Customer soft delete failed")
                return False
                
        else:
            print("❌ Failed to create customer")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Customer CRUD operations test failed: {e}")
        return False

def test_customer_sales_history():
    """Test customer sales history functionality"""
    print("\n📈 Testing Customer Sales History...")
    
    try:
        # Get customers with sales
        customers = Customer.get_all()
        customers_with_sales = []
        
        for customer in customers:
            sales = Sale.get_by_customer_id(customer.id)
            if sales:
                customers_with_sales.append((customer, sales))
        
        print(f"✅ Found {len(customers_with_sales)} customers with sales history")
        
        # Test sales history for each customer
        for customer, sales in customers_with_sales[:3]:  # Test first 3
            print(f"   Customer: {customer.name}")
            print(f"   - Total sales: {len(sales)}")
            
            total_amount = sum(sale.total_amount for sale in sales)
            print(f"   - Total amount: ${total_amount:.2f}")
            
            # Test getting customer statistics
            if hasattr(customer, 'get_sales_stats'):
                stats = customer.get_sales_stats()
                print(f"   - Sales stats: {stats}")
            
            # Test recent sales
            recent_sales = [sale for sale in sales if sale.created_at]
            print(f"   - Recent sales: {len(recent_sales)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Customer sales history test failed: {e}")
        return False

def test_customer_validation():
    """Test customer data validation"""
    print("\n✅ Testing Customer Validation...")
    
    try:
        # Test creating customer with invalid data
        invalid_data_tests = [
            {'name': '', 'email': '<EMAIL>'},  # Empty name
            {'name': 'Test', 'email': 'invalid-email'},  # Invalid email
            {'name': 'Test', 'phone': '123'},  # Invalid phone (too short)
        ]
        
        for i, invalid_data in enumerate(invalid_data_tests):
            try:
                invalid_customer = Customer.create(**invalid_data)
                if invalid_customer:
                    print(f"⚠️ Test {i+1}: Invalid data was accepted (should be rejected)")
                    # Clean up
                    invalid_customer.delete()
                else:
                    print(f"✅ Test {i+1}: Invalid data properly rejected")
            except Exception as e:
                print(f"✅ Test {i+1}: Invalid data caused expected error: {type(e).__name__}")
        
        # Test duplicate email
        customers = Customer.get_all()
        if customers:
            existing_customer = customers[0]
            
            duplicate_data = {
                'name': 'Duplicate Test',
                'email': existing_customer.email
            }
            
            try:
                duplicate_customer = Customer.create(**duplicate_data)
                if duplicate_customer:
                    print(f"⚠️ Duplicate email was accepted (should be rejected)")
                    duplicate_customer.delete()
                else:
                    print(f"✅ Duplicate email properly rejected")
            except Exception as e:
                print(f"✅ Duplicate email caused expected error: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Customer validation test failed: {e}")
        return False

def test_customer_search_functionality():
    """Test customer search functionality"""
    print("\n🔍 Testing Customer Search Functionality...")
    
    try:
        # Test search by different criteria
        customers = Customer.get_all()
        if customers:
            test_customer = customers[0]
            
            # Test search by name
            name_results = Customer.search(test_customer.name[:4])
            print(f"✅ Search by name '{test_customer.name[:4]}': {len(name_results)} results")
            
            # Test search by email
            if test_customer.email:
                email_results = Customer.search(test_customer.email[:5])
                print(f"✅ Search by email '{test_customer.email[:5]}': {len(email_results)} results")
            
            # Test search by phone
            if test_customer.phone:
                phone_results = Customer.search(test_customer.phone[:3])
                print(f"✅ Search by phone '{test_customer.phone[:3]}': {len(phone_results)} results")
            
            # Test case-insensitive search
            case_results = Customer.search(test_customer.name.upper())
            print(f"✅ Case-insensitive search: {len(case_results)} results")
            
            # Test partial search
            if len(test_customer.name) > 3:
                partial_results = Customer.search(test_customer.name[1:-1])
                print(f"✅ Partial search: {len(partial_results)} results")
            
            return True
        else:
            print("⚠️ No customers available for search testing")
            return True
            
    except Exception as e:
        print(f"❌ Customer search functionality test failed: {e}")
        return False

def test_customer_export_import():
    """Test customer export and import functionality"""
    print("\n📤 Testing Customer Export/Import...")
    
    try:
        customers_widget = CustomersWidget()
        
        # Test export functionality if available
        if hasattr(customers_widget, 'export_customers'):
            try:
                # Mock file dialog to return a test path
                import unittest.mock
                with unittest.mock.patch('PyQt5.QtWidgets.QFileDialog.getSaveFileName', 
                                       return_value=('test_customers_export.csv', 'CSV Files (*.csv)')):
                    result = customers_widget.export_customers()
                    
                    if result is not False:
                        print("✅ Customer export executed successfully")
                    else:
                        print("⚠️ Customer export returned False")
            except Exception as e:
                print(f"⚠️ Customer export failed: {e}")
        else:
            print("⚠️ Export functionality not available")
        
        # Test import functionality if available
        if hasattr(customers_widget, 'import_customers'):
            try:
                # Mock file dialog to return a test path
                import unittest.mock
                with unittest.mock.patch('PyQt5.QtWidgets.QFileDialog.getOpenFileName', 
                                       return_value=('test_customers_import.csv', 'CSV Files (*.csv)')):
                    # This would normally fail because file doesn't exist, but we're testing the function exists
                    print("✅ Import functionality available")
            except Exception as e:
                print(f"⚠️ Customer import test: {e}")
        else:
            print("⚠️ Import functionality not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Customer export/import test failed: {e}")
        return False

def run_comprehensive_customers_test():
    """Run all customers tests"""
    print("🧪 STARTING COMPREHENSIVE CUSTOMERS SYSTEM TEST")
    print("=" * 60)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    customers_widget = None
    
    # Test 1: Database Setup
    test_results.append(("Database Setup", test_customers_database_setup()))
    
    # Test 2: Widget Creation
    customers_widget = test_customers_widget_creation()
    test_results.append(("Widget Creation", customers_widget is not False))
    
    if customers_widget:
        # Test 3: Data Loading
        test_results.append(("Data Loading", test_customers_data_loading(customers_widget)))
        
        # Test 4: Customers Filtering
        test_results.append(("Customers Filtering", test_customers_filtering(customers_widget)))
        
        # Test 5: CRUD Operations
        test_results.append(("CRUD Operations", test_customer_crud_operations(customers_widget)))
    
    # Test 6: Sales History
    test_results.append(("Sales History", test_customer_sales_history()))
    
    # Test 7: Validation
    test_results.append(("Validation", test_customer_validation()))
    
    # Test 8: Search Functionality
    test_results.append(("Search Functionality", test_customer_search_functionality()))
    
    # Test 9: Export/Import
    test_results.append(("Export/Import", test_customer_export_import()))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 CUSTOMERS TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL CUSTOMERS TESTS PASSED! Customers system is working perfectly!")
    else:
        print("⚠️  Some customers tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_comprehensive_customers_test()
