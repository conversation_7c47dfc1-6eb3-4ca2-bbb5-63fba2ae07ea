"""
Enhanced Users Management Widget for OnePos POS System
Advanced user management with roles and permissions
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem, 
                            QLineEdit, QComboBox, QCheckBox, QGroupBox,
                            QFormLayout, QMessageBox, QDialog, QDialogButtonBox,
                            QTextEdit, QTabWidget, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from models.user import User
from models.permissions import role_manager, Permission, AuditLogger
from utils.translator import tr

def create_dark_label(text):
    """Create a dark label with proper styling"""
    label = QLabel(text)
    label.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
    return label


class UserDialog(QDialog):
    """Dialog for creating/editing users"""
    
    def __init__(self, user=None, parent=None):
        super().__init__(parent)
        self.user = user
        self.setWindowTitle(tr("users.edit_user") if user else tr("users.add_user"))
        self.setModal(True)
        self.resize(500, 600)

        # Apply dialog styling for better contrast
        self.setStyleSheet("""
            QDialog {
                background-color: #2c3e50;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff !important;
                font-weight: 700;
                font-size: 13px;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                background-color: #ffffff;
                color: #000000;
                border: 2px solid #34495e;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
            QCheckBox {
                color: #ffffff;
                font-weight: 600;
            }
            QGroupBox {
                color: #ffffff;
                font-weight: 700;
                border: 2px solid #34495e;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                color: #ffffff;
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #3498db;
                color: #ffffff;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        self.init_ui()

        if user:
            self.load_user_data()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # User information
        info_group = QGroupBox(tr("users.user_info"))
        info_layout = QFormLayout(info_group)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText(tr("users.username"))
        info_layout.addRow(create_dark_label(tr("users.username") + ":"), self.username_edit)
        
        self.full_name_edit = QLineEdit()
        self.full_name_edit.setPlaceholderText(tr("users.full_name"))
        info_layout.addRow(create_dark_label(tr("users.full_name") + ":"), self.full_name_edit)
        
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText(tr("users.email"))
        info_layout.addRow(create_dark_label(tr("users.email") + ":"), self.email_edit)
        
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText(tr("users.phone"))
        info_layout.addRow(create_dark_label(tr("users.phone") + ":"), self.phone_edit)
        
        # Role selection
        self.role_combo = QComboBox()
        roles = role_manager.get_all_roles()
        for role_name, role in roles.items():
            self.role_combo.addItem(f"{role.name} - {role.description}", role_name)
        info_layout.addRow(create_dark_label(tr("users.role") + ":"), self.role_combo)
        
        # Password fields (only for new users or password change)
        if not self.user:
            self.password_edit = QLineEdit()
            self.password_edit.setEchoMode(QLineEdit.Password)
            self.password_edit.setPlaceholderText(tr("users.password"))
            info_layout.addRow(create_dark_label(tr("users.password") + ":"), self.password_edit)
            
            self.confirm_password_edit = QLineEdit()
            self.confirm_password_edit.setEchoMode(QLineEdit.Password)
            self.confirm_password_edit.setPlaceholderText(tr("users.confirm_password"))
            info_layout.addRow(create_dark_label(tr("users.confirm_password") + ":"), self.confirm_password_edit)
        
        # Active status
        self.active_check = QCheckBox(tr("users.active"))
        self.active_check.setChecked(True)
        info_layout.addRow("", self.active_check)
        
        layout.addWidget(info_group)
        
        # Permissions preview
        permissions_group = QGroupBox(tr("users.permissions_preview"))
        permissions_layout = QVBoxLayout(permissions_group)
        
        self.permissions_text = QTextEdit()
        self.permissions_text.setReadOnly(True)
        self.permissions_text.setMaximumHeight(150)
        permissions_layout.addWidget(self.permissions_text)
        
        # Update permissions when role changes
        self.role_combo.currentTextChanged.connect(self.update_permissions_preview)
        
        layout.addWidget(permissions_group)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Initial permissions preview
        self.update_permissions_preview()
    
    def load_user_data(self):
        """Load user data into form"""
        if self.user:
            self.username_edit.setText(self.user.username)
            self.full_name_edit.setText(self.user.full_name or "")
            self.email_edit.setText(self.user.email or "")
            self.phone_edit.setText(self.user.phone or "")
            self.active_check.setChecked(self.user.is_active)
            
            # Set role
            for i in range(self.role_combo.count()):
                if self.role_combo.itemData(i) == self.user.role:
                    self.role_combo.setCurrentIndex(i)
                    break
    
    def update_permissions_preview(self):
        """Update permissions preview based on selected role"""
        role_name = self.role_combo.currentData()
        if role_name:
            role = role_manager.get_role(role_name)
            if role:
                permissions_text = f"Role: {role.name}\n\n"
                permissions_text += f"Description: {role.description}\n\n"
                permissions_text += "Permissions:\n"
                
                for permission in sorted(role.permissions, key=lambda p: p.value):
                    permissions_text += f"• {permission.value.replace('_', ' ').title()}\n"
                
                self.permissions_text.setText(permissions_text)
    
    def get_user_data(self):
        """Get user data from form"""
        data = {
            'username': self.username_edit.text().strip(),
            'full_name': self.full_name_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'role': self.role_combo.currentData(),
            'is_active': self.active_check.isChecked()
        }
        
        if not self.user:  # New user
            data['password'] = self.password_edit.text()
            data['confirm_password'] = self.confirm_password_edit.text()
        
        return data
    
    def validate_data(self, data):
        """Validate user data"""
        if not data['username']:
            QMessageBox.warning(self, tr("common.warning"), tr("users.username_required"))
            return False
        
        if not data['full_name']:
            QMessageBox.warning(self, tr("common.warning"), tr("users.full_name_required"))
            return False
        
        if not self.user:  # New user
            if not data['password']:
                QMessageBox.warning(self, tr("common.warning"), tr("users.password_required"))
                return False
            
            if data['password'] != data['confirm_password']:
                QMessageBox.warning(self, tr("common.warning"), tr("users.passwords_not_match"))
                return False
            
            # Check password strength
            from models.permissions import security_manager
            strength = security_manager.check_password_strength(data['password'])
            if not strength['strong']:
                QMessageBox.warning(self, tr("common.warning"), tr("users.weak_password"))
                return False
        
        return True


class UsersWidget(QWidget):
    """Enhanced users management widget"""
    
    user_created = pyqtSignal(object)
    user_updated = pyqtSignal(object)
    user_deleted = pyqtSignal(int)
    
    def __init__(self, current_user=None):
        super().__init__()
        self.current_user = current_user
        self.init_ui()
        self.load_users()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("👥 " + tr("users.title"))
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title_label.setStyleSheet("color: #000000; font-weight: 800; font-size: 18px;")
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Create tabs
        self.tab_widget = QTabWidget()
        
        # Users management tab
        self.users_tab = self.create_users_tab()
        self.tab_widget.addTab(self.users_tab, "👥 " + tr("users.manage"))
        
        # Audit logs tab
        self.audit_tab = self.create_audit_tab()
        self.tab_widget.addTab(self.audit_tab, "📋 " + tr("users.audit_logs"))
        
        # Security settings tab
        self.security_tab = self.create_security_tab()
        self.tab_widget.addTab(self.security_tab, "🔒 " + tr("users.security"))
        
        layout.addWidget(self.tab_widget)
    
    def create_users_tab(self):
        """Create users management tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Toolbar
        toolbar_layout = QHBoxLayout()
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(tr("users.search_users"))
        self.search_edit.textChanged.connect(self.search_users)
        toolbar_layout.addWidget(self.search_edit)
        
        # Add user button
        self.add_button = QPushButton("➕ " + tr("users.add_user"))
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.add_button.clicked.connect(self.add_user)
        toolbar_layout.addWidget(self.add_button)

        # Refresh button
        self.refresh_button = QPushButton("🔄 " + tr("common.refresh"))
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.refresh_button.clicked.connect(self.load_users)
        toolbar_layout.addWidget(self.refresh_button)
        
        # Edit user button
        self.edit_button = QPushButton("✏️ " + tr("users.edit_user"))
        self.edit_button.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #000000;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.edit_button.clicked.connect(self.edit_user)
        toolbar_layout.addWidget(self.edit_button)
        
        # Delete user button
        self.delete_button = QPushButton("🗑️ " + tr("users.delete_user"))
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.delete_button.clicked.connect(self.delete_user)
        toolbar_layout.addWidget(self.delete_button)

        # Change password button
        self.change_password_button = QPushButton("🔐 " + tr("users.change_password"))
        self.change_password_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.change_password_button.clicked.connect(self.change_user_password)
        toolbar_layout.addWidget(self.change_password_button)
        
        toolbar_layout.addStretch()
        layout.addLayout(toolbar_layout)
        
        # Users table
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(7)
        self.users_table.setHorizontalHeaderLabels([
            tr("users.username"),
            tr("users.full_name"),
            tr("users.email"),
            tr("users.role"),
            tr("users.last_login"),
            tr("users.status"),
            tr("users.actions")
        ])
        
        # Set column widths
        header = self.users_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        # Set selection behavior
        self.users_table.setSelectionBehavior(self.users_table.SelectRows)
        self.users_table.setSelectionMode(self.users_table.SingleSelection)
        self.users_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.users_table)
        
        return widget
    
    def create_audit_tab(self):
        """Create audit logs tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Audit logs table
        self.audit_table = QTableWidget()
        self.audit_table.setColumnCount(5)
        self.audit_table.setHorizontalHeaderLabels([
            tr("users.user"),
            tr("users.action"),
            tr("users.details"),
            tr("users.ip_address"),
            tr("users.timestamp")
        ])
        
        header = self.audit_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.audit_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.audit_table)
        
        # Load audit logs
        self.load_audit_logs()
        
        return widget
    
    def create_security_tab(self):
        """Create security settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Security metrics
        metrics_group = QGroupBox("🔒 " + tr("users.security_metrics"))
        metrics_layout = QVBoxLayout(metrics_group)
        
        self.security_text = QTextEdit()
        self.security_text.setReadOnly(True)
        self.security_text.setMaximumHeight(200)
        metrics_layout.addWidget(self.security_text)
        
        layout.addWidget(metrics_group)
        
        # Security actions
        actions_group = QGroupBox("🛡️ " + tr("users.security_actions"))
        actions_layout = QHBoxLayout(actions_group)
        
        self.unlock_accounts_button = QPushButton("🔓 " + tr("users.unlock_accounts"))
        self.unlock_accounts_button.clicked.connect(self.unlock_all_accounts)
        actions_layout.addWidget(self.unlock_accounts_button)
        
        self.clear_sessions_button = QPushButton("🚪 " + tr("users.clear_sessions"))
        self.clear_sessions_button.clicked.connect(self.clear_all_sessions)
        actions_layout.addWidget(self.clear_sessions_button)
        
        actions_layout.addStretch()
        layout.addWidget(actions_group)
        
        # Load security metrics
        self.load_security_metrics()
        
        return widget
    
    def load_users(self):
        """Load users into table"""
        users = User.get_all()
        self.populate_users_table(users)
    
    def populate_users_table(self, users):
        """Populate users table"""
        self.users_table.setRowCount(len(users))
        
        for row, user in enumerate(users):
            # Username
            self.users_table.setItem(row, 0, QTableWidgetItem(user.username))
            
            # Full name
            self.users_table.setItem(row, 1, QTableWidgetItem(user.full_name or ""))
            
            # Email
            self.users_table.setItem(row, 2, QTableWidgetItem(user.email or ""))
            
            # Role
            role = role_manager.get_role(user.role)
            role_name = role.name if role else user.role
            self.users_table.setItem(row, 3, QTableWidgetItem(role_name))
            
            # Last login
            last_login = user.last_login or "Never"
            self.users_table.setItem(row, 4, QTableWidgetItem(last_login))
            
            # Status
            status = "Active" if user.is_active else "Inactive"
            if user.is_account_locked():
                status = "Locked"
            self.users_table.setItem(row, 5, QTableWidgetItem(status))
            
            # Store user ID
            self.users_table.item(row, 0).setData(Qt.UserRole, user.id)
    
    def search_users(self):
        """Search users"""
        query = self.search_edit.text().strip()
        if query:
            users = User.search(query)
        else:
            users = User.get_all()
        
        self.populate_users_table(users)
    
    def add_user(self):
        """Add new user"""
        dialog = UserDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_user_data()
            if dialog.validate_data(data):
                try:
                    user = User.create(
                        username=data['username'],
                        password=data['password'],
                        full_name=data['full_name'],
                        email=data['email'],
                        phone=data['phone'],
                        role=data['role'],
                        is_active=data['is_active']
                    )
                    
                    if user:
                        # Log action
                        if self.current_user:
                            AuditLogger.log_action(
                                self.current_user.id,
                                "USER_CREATED",
                                f"Created user: {data['username']}"
                            )
                        
                        QMessageBox.information(self, tr("common.success"), tr("users.user_created"))
                        self.load_users()
                        self.user_created.emit(user)
                    
                except Exception as e:
                    QMessageBox.critical(self, tr("common.error"), f"Error creating user: {e}")
    
    def edit_user(self):
        """Edit selected user"""
        user = self.get_selected_user()
        if not user:
            QMessageBox.warning(self, tr("common.warning"), tr("users.select_user"))
            return
        
        dialog = UserDialog(user, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_user_data()
            if dialog.validate_data(data):
                try:
                    user.username = data['username']
                    user.full_name = data['full_name']
                    user.email = data['email']
                    user.phone = data['phone']
                    user.role = data['role']
                    user.is_active = data['is_active']
                    user.save()
                    
                    # Log action
                    if self.current_user:
                        AuditLogger.log_action(
                            self.current_user.id,
                            "USER_UPDATED",
                            f"Updated user: {data['username']}"
                        )
                    
                    QMessageBox.information(self, tr("common.success"), tr("users.user_updated"))
                    self.load_users()
                    self.user_updated.emit(user)
                    
                except Exception as e:
                    QMessageBox.critical(self, tr("common.error"), f"Error updating user: {e}")
    
    def delete_user(self):
        """Delete selected user"""
        user = self.get_selected_user()
        if not user:
            QMessageBox.warning(self, tr("common.warning"), tr("users.select_user"))
            return
        
        if user.username == 'admin':
            QMessageBox.warning(self, tr("common.warning"), tr("users.cannot_delete_admin"))
            return
        
        reply = QMessageBox.question(
            self, tr("common.confirm"),
            tr("users.confirm_delete").format(user.username),
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                user_id = user.id
                username = user.username
                user.delete()
                
                # Log action
                if self.current_user:
                    AuditLogger.log_action(
                        self.current_user.id,
                        "USER_DELETED",
                        f"Deleted user: {username}"
                    )
                
                QMessageBox.information(self, tr("common.success"), tr("users.user_deleted"))
                self.load_users()
                self.user_deleted.emit(user_id)
                
            except Exception as e:
                QMessageBox.critical(self, tr("common.error"), f"Error deleting user: {e}")

    def change_user_password(self):
        """Change password for selected user"""
        user = self.get_selected_user()
        if not user:
            QMessageBox.warning(self, tr("common.warning"), tr("users.select_user"))
            return

        try:
            from views.change_password_dialog import ChangePasswordDialog

            dialog = ChangePasswordDialog(user, self)
            if dialog.exec_() == QDialog.Accepted:
                # Log action
                if self.current_user:
                    AuditLogger.log_action(
                        self.current_user.id,
                        "PASSWORD_CHANGED_ADMIN",
                        f"Admin changed password for user: {user.username}"
                    )

                QMessageBox.information(self, tr("common.success"), tr("users.password_changed"))

        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"Error changing password: {e}")

    def get_selected_user(self):
        """Get selected user"""
        current_row = self.users_table.currentRow()
        if current_row >= 0:
            user_id = self.users_table.item(current_row, 0).data(Qt.UserRole)
            return User.get_by_id(user_id)
        return None
    
    def load_audit_logs(self):
        """Load audit logs"""
        try:
            logs = AuditLogger.get_audit_logs(100)
            self.audit_table.setRowCount(len(logs))
            
            for row, log in enumerate(logs):
                # Handle both dict and sqlite3.Row objects
                username = log.get('username') if hasattr(log, 'get') else getattr(log, 'username', 'Unknown')
                action = log.get('action') if hasattr(log, 'get') else getattr(log, 'action', '')
                details = log.get('details') if hasattr(log, 'get') else getattr(log, 'details', '')
                ip_address = log.get('ip_address') if hasattr(log, 'get') else getattr(log, 'ip_address', '')
                created_at = log.get('created_at') if hasattr(log, 'get') else getattr(log, 'created_at', '')

                self.audit_table.setItem(row, 0, QTableWidgetItem(username))
                self.audit_table.setItem(row, 1, QTableWidgetItem(action))
                self.audit_table.setItem(row, 2, QTableWidgetItem(details))
                self.audit_table.setItem(row, 3, QTableWidgetItem(ip_address))
                self.audit_table.setItem(row, 4, QTableWidgetItem(created_at))
                
        except Exception as e:
            print(f"Error loading audit logs: {e}")
    
    def load_security_metrics(self):
        """Load security metrics"""
        try:
            from models.permissions import security_manager
            
            metrics_text = "Security Status:\n\n"
            
            # Count locked accounts
            users = User.get_all()
            locked_count = sum(1 for user in users if user.is_account_locked())
            active_count = sum(1 for user in users if user.is_active)
            
            metrics_text += f"Total Users: {len(users)}\n"
            metrics_text += f"Active Users: {active_count}\n"
            metrics_text += f"Locked Accounts: {locked_count}\n"
            metrics_text += f"Failed Attempts Tracked: {len(security_manager.failed_attempts)}\n\n"
            
            metrics_text += "Recent Security Events:\n"
            logs = AuditLogger.get_audit_logs(10)
            for log in logs:
                if 'LOGIN' in log['action'] or 'PERMISSION' in log['action']:
                    metrics_text += f"• {log['action']}: {log.get('username', 'Unknown')} at {log['created_at']}\n"
            
            self.security_text.setText(metrics_text)
            
        except Exception as e:
            self.security_text.setText(f"Error loading security metrics: {e}")
    
    def unlock_all_accounts(self):
        """Unlock all locked accounts"""
        try:
            users = User.get_all()
            unlocked_count = 0
            
            for user in users:
                if user.is_account_locked():
                    user.unlock_account()
                    unlocked_count += 1
            
            # Log action
            if self.current_user:
                AuditLogger.log_action(
                    self.current_user.id,
                    "ACCOUNTS_UNLOCKED",
                    f"Unlocked {unlocked_count} accounts"
                )
            
            QMessageBox.information(self, tr("common.success"), f"Unlocked {unlocked_count} accounts")
            self.load_users()
            self.load_security_metrics()
            
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"Error unlocking accounts: {e}")
    
    def clear_all_sessions(self):
        """Clear all user sessions"""
        try:
            # This would clear all active sessions
            # Implementation depends on session management
            
            # Log action
            if self.current_user:
                AuditLogger.log_action(
                    self.current_user.id,
                    "SESSIONS_CLEARED",
                    "Cleared all user sessions"
                )
            
            QMessageBox.information(self, tr("common.success"), "All sessions cleared")
            
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"Error clearing sessions: {e}")
