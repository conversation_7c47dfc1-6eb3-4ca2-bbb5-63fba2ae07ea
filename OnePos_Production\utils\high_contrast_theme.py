"""
High contrast theme for OnePos POS System
Maximum readability with black text on white background
"""

def get_high_contrast_stylesheet():
    """Get high contrast stylesheet for maximum readability"""
    return """
    /* High Contrast Theme - Maximum Readability */
    
    /* Global Settings */
    * {
        font-family: "Segoe UI", "Arial", "Helvetica", sans-serif;
        font-size: 12px;
        font-weight: 500;
        color: #000000 !important;
    }
    
    /* Main Window */
    QMainWindow {
        background-color: #e6f3ff;
        color: #000000;
    }

    /* All Widgets */
    QWidget {
        background-color: #e6f3ff;
        color: #000000;
        font-weight: 500;
    }

    /* Labels - Dark Black for maximum visibility */
    QLabel {
        color: #000000 !important;
        font-weight: 700 !important;
        font-size: 13px !important;
        background-color: transparent;
    }

    /* Form Labels - Extra dark for input field labels */
    QFormLayout QLabel {
        color: #000000 !important;
        font-weight: 800 !important;
        font-size: 13px !important;
    }
    
    /* Buttons */
    QPushButton {
        background-color: #cce7ff;
        border: 2px solid #000000;
        border-radius: 4px;
        padding: 10px 18px;
        color: #000000 !important;
        font-weight: 600 !important;
        font-size: 12px !important;
        min-height: 25px;
    }
    
    QPushButton:hover {
        background-color: #b3d9ff;
        color: #000000 !important;
        border: 2px solid #000000;
    }

    QPushButton:pressed {
        background-color: #99ccff;
        color: #000000 !important;
    }
    
    /* Input Fields */
    QLineEdit, QTextEdit, QPlainTextEdit {
        background-color: #f0f8ff;
        border: 2px solid #000000;
        border-radius: 4px;
        padding: 8px;
        color: #000000 !important;
        font-weight: 500 !important;
        font-size: 12px !important;
    }

    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
        border: 2px solid #0066cc;
        background-color: #ffffff;
        color: #000000 !important;
    }
    
    /* ComboBox */
    QComboBox {
        background-color: #f0f8ff;
        border: 2px solid #000000;
        border-radius: 4px;
        padding: 8px;
        color: #000000 !important;
        font-weight: 500 !important;
        font-size: 12px !important;
        min-height: 25px;
    }
    
    QComboBox:hover {
        border: 2px solid #0066cc;
    }
    
    QComboBox::drop-down {
        border: none;
        width: 25px;
    }
    
    QComboBox::down-arrow {
        image: none;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #000000;
        margin-right: 8px;
    }
    
    QComboBox QAbstractItemView {
        background-color: #f0f8ff;
        border: 3px solid #000000;
        color: #000000 !important;
        font-weight: 700 !important;
        selection-background-color: #b3d9ff;
        selection-color: #000000 !important;
    }
    
    /* SpinBox */
    QSpinBox, QDoubleSpinBox {
        background-color: #f0f8ff;
        border: 3px solid #000000;
        border-radius: 6px;
        padding: 10px;
        color: #000000 !important;
        font-weight: 700 !important;
        font-size: 13px !important;
        min-height: 25px;
    }
    
    QSpinBox:focus, QDoubleSpinBox:focus {
        border: 3px solid #0066cc;
    }
    
    /* Tables */
    QTableWidget {
        background-color: #f0f8ff;
        alternate-background-color: #e6f3ff;
        gridline-color: #000000;
        color: #000000 !important;
        font-weight: 700 !important;
        font-size: 13px !important;
        border: 3px solid #000000;
        border-radius: 6px;
    }
    
    QTableWidget::item {
        padding: 10px;
        border-bottom: 1px solid #000000;
        color: #000000 !important;
        font-weight: 700 !important;
    }
    
    QTableWidget::item:selected {
        background-color: #b3d9ff;
        color: #000000 !important;
        font-weight: 800 !important;
    }

    QTableWidget::item:hover {
        background-color: #cce7ff;
        color: #000000 !important;
    }
    
    QHeaderView::section {
        background-color: #cce7ff;
        color: #000000 !important;
        padding: 10px;
        border: 2px solid #000000;
        font-weight: 800 !important;
        font-size: 14px !important;
    }
    
    /* GroupBox */
    QGroupBox {
        color: #000000 !important;
        font-weight: 800 !important;
        font-size: 15px !important;
        border: 3px solid #000000;
        border-radius: 8px;
        margin-top: 15px;
        padding-top: 15px;
        background-color: #e6f3ff;
    }

    QGroupBox::title {
        subcontrol-origin: margin;
        left: 15px;
        padding: 0 10px 0 10px;
        color: #000000 !important;
        font-weight: 800 !important;
        font-size: 15px !important;
        background-color: #e6f3ff;
    }
    
    /* CheckBox */
    QCheckBox {
        color: #000000 !important;
        font-weight: 700 !important;
        font-size: 13px !important;
        spacing: 10px;
    }
    
    QCheckBox::indicator {
        width: 20px;
        height: 20px;
        border: 3px solid #000000;
        border-radius: 4px;
        background-color: #f0f8ff;
    }
    
    QCheckBox::indicator:checked {
        background-color: #000000;
        border-color: #000000;
    }
    
    /* RadioButton */
    QRadioButton {
        color: #000000 !important;
        font-weight: 700 !important;
        font-size: 13px !important;
        spacing: 10px;
    }
    
    QRadioButton::indicator {
        width: 20px;
        height: 20px;
        border: 3px solid #000000;
        border-radius: 10px;
        background-color: #f0f8ff;
    }
    
    QRadioButton::indicator:checked {
        background-color: #000000;
        border-color: #000000;
    }
    
    /* Tabs */
    QTabWidget::pane {
        border: 3px solid #000000;
        border-radius: 6px;
        background-color: #e6f3ff;
        margin-top: -1px;
    }

    QTabBar::tab {
        background-color: #cce7ff;
        color: #000000 !important;
        padding: 12px 20px;
        margin-right: 2px;
        font-weight: 800 !important;
        font-size: 13px !important;
        border: 3px solid #000000;
        border-bottom: none;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
    }

    QTabBar::tab:selected {
        background-color: #e6f3ff;
        color: #000000 !important;
        border-color: #000000;
        font-weight: 800 !important;
    }

    QTabBar::tab:hover {
        background-color: #b3d9ff;
        color: #000000 !important;
    }
    
    /* Scrollbars */
    QScrollBar:vertical {
        background-color: #cce7ff;
        width: 15px;
        border: 2px solid #000000;
        border-radius: 6px;
    }

    QScrollBar::handle:vertical {
        background-color: #000000;
        border-radius: 4px;
        min-height: 25px;
    }

    QScrollBar:horizontal {
        background-color: #cce7ff;
        height: 15px;
        border: 2px solid #000000;
        border-radius: 6px;
    }

    QScrollBar::handle:horizontal {
        background-color: #000000;
        border-radius: 4px;
        min-width: 25px;
    }
    
    /* Status Bar */
    QStatusBar {
        background-color: #cce7ff;
        color: #000000 !important;
        border-top: 2px solid #000000;
        font-weight: 700 !important;
        font-size: 12px !important;
    }

    /* Menu Bar */
    QMenuBar {
        background-color: #cce7ff;
        color: #000000 !important;
        border-bottom: 2px solid #000000;
        font-weight: 700 !important;
    }
    
    QMenuBar::item {
        background-color: transparent;
        padding: 10px 15px;
        color: #000000 !important;
    }
    
    QMenuBar::item:selected {
        background-color: #b3d9ff;
        color: #000000 !important;
    }

    QMenu {
        background-color: #e6f3ff;
        color: #000000 !important;
        border: 3px solid #000000;
        border-radius: 6px;
        font-weight: 700 !important;
    }

    QMenu::item {
        padding: 10px 20px;
        color: #000000 !important;
    }

    QMenu::item:selected {
        background-color: #b3d9ff;
        color: #000000 !important;
    }
    """
