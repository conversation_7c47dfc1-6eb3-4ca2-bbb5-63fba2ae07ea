"""
Comprehensive Testing for OnePos Settings System
Tests all settings functionality including UI components and business logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QTabWidget, QComboBox, QLineEdit, QPushButton, QCheckBox
from PyQt5.QtCore import Qt
from utils.config_manager import config
from utils.translator import tr, change_language, get_current_language
from views.settings_widget import SettingsWidget, GeneralSettingsTab, CompanySettingsTab, PrinterSettingsTab
from views.main_window import MainWindow
from models.user import User
from datetime import datetime

def test_settings_database_setup():
    """Test settings database setup and configuration"""
    print("🔗 Testing Settings Database Setup...")
    
    try:
        # Test config manager
        app_name = config.get_app_name()
        print(f"✅ App name: {app_name}")
        
        # Test language settings
        current_language = config.get_language()
        print(f"✅ Current language: {current_language}")
        
        # Test theme settings
        current_theme = config.get_theme()
        print(f"✅ Current theme: {current_theme}")
        
        # Test UI config
        ui_config = config.get_ui_config()
        print(f"✅ UI config loaded: {len(ui_config)} settings")
        
        # Test company settings
        company_config = config.get_company_config()
        print(f"✅ Company config loaded: {len(company_config)} settings")
        
        # Test printer settings
        printer_config = config.get_printer_config()
        print(f"✅ Printer config loaded: {len(printer_config)} settings")
        
        return True
        
    except Exception as e:
        print(f"❌ Database setup test failed: {e}")
        return False

def test_settings_widget_creation():
    """Test settings widget creation and initialization"""
    print("\n🖥️ Testing Settings Widget Creation...")
    
    try:
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create settings widget
        settings_widget = SettingsWidget()
        
        if settings_widget:
            print("✅ SettingsWidget created successfully")
            
            # Check main components
            if hasattr(settings_widget, 'tabs'):
                print("✅ Tabs widget exists")
                tabs_count = settings_widget.tabs.count()
                print(f"   - Tabs count: {tabs_count}")
                
                # Check each tab
                for i in range(tabs_count):
                    tab_text = settings_widget.tabs.tabText(i)
                    tab_widget = settings_widget.tabs.widget(i)
                    print(f"   - Tab {i}: {tab_text} ({type(tab_widget).__name__})")
            
            # Check if specific tabs exist
            tab_types = [GeneralSettingsTab, CompanySettingsTab, PrinterSettingsTab]
            found_tabs = []
            
            for i in range(settings_widget.tabs.count()):
                tab_widget = settings_widget.tabs.widget(i)
                for tab_type in tab_types:
                    if isinstance(tab_widget, tab_type):
                        found_tabs.append(tab_type.__name__)
                        break
            
            print(f"✅ Found tab types: {found_tabs}")
            
            return settings_widget
        else:
            print("❌ Failed to create SettingsWidget")
            return False
            
    except Exception as e:
        print(f"❌ Settings widget creation test failed: {e}")
        return False

def test_general_settings_tab(settings_widget):
    """Test general settings tab functionality"""
    print("\n⚙️ Testing General Settings Tab...")
    
    try:
        # Find general settings tab
        general_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, GeneralSettingsTab):
                general_tab = tab_widget
                settings_widget.tabs.setCurrentIndex(i)
                break
        
        if not general_tab:
            print("❌ General settings tab not found")
            return False
        
        print("✅ General settings tab found")
        
        # Test language combo
        if hasattr(general_tab, 'language_combo'):
            language_combo = general_tab.language_combo
            print(f"✅ Language combo: {language_combo.count()} options")
            
            # Test language options
            for i in range(language_combo.count()):
                lang_code = language_combo.itemData(i)
                lang_name = language_combo.itemText(i)
                print(f"   - {lang_name}: {lang_code}")
            
            # Test language change
            original_language = get_current_language()
            
            # Find a different language to test
            test_language = None
            for i in range(language_combo.count()):
                lang_code = language_combo.itemData(i)
                if lang_code and lang_code != original_language:
                    test_language = lang_code
                    language_combo.setCurrentIndex(i)
                    break
            
            if test_language:
                print(f"   Testing language change to {test_language}...")
                general_tab.on_language_changed()
                
                # Check if language actually changed
                new_language = get_current_language()
                if new_language == test_language:
                    print(f"   ✅ Language changed successfully to {test_language}")
                    
                    # Change back
                    for i in range(language_combo.count()):
                        if language_combo.itemData(i) == original_language:
                            language_combo.setCurrentIndex(i)
                            general_tab.on_language_changed()
                            break
                else:
                    print(f"   ❌ Language change failed")
                    return False
        else:
            print("❌ Language combo not found")
            return False
        
        # Test theme combo
        if hasattr(general_tab, 'theme_combo'):
            theme_combo = general_tab.theme_combo
            print(f"✅ Theme combo: {theme_combo.count()} options")
            
            for i in range(theme_combo.count()):
                theme_name = theme_combo.itemText(i)
                print(f"   - Theme: {theme_name}")
        else:
            print("⚠️ Theme combo not found")
        
        # Test auto backup checkbox
        if hasattr(general_tab, 'auto_backup_check'):
            auto_backup = general_tab.auto_backup_check
            print(f"✅ Auto backup checkbox: {auto_backup.isChecked()}")
        else:
            print("⚠️ Auto backup checkbox not found")
        
        # Test save button
        if hasattr(general_tab, 'save_button'):
            save_button = general_tab.save_button
            print(f"✅ Save button: {save_button.text()}")
        else:
            print("⚠️ Save button not found")
        
        return True
        
    except Exception as e:
        print(f"❌ General settings tab test failed: {e}")
        return False

def test_company_settings_tab(settings_widget):
    """Test company settings tab functionality"""
    print("\n🏢 Testing Company Settings Tab...")
    
    try:
        # Find company settings tab
        company_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, CompanySettingsTab):
                company_tab = tab_widget
                settings_widget.tabs.setCurrentIndex(i)
                break
        
        if not company_tab:
            print("❌ Company settings tab not found")
            return False
        
        print("✅ Company settings tab found")
        
        # Test company fields
        company_fields = [
            ('company_name_edit', 'Company name'),
            ('company_address_edit', 'Company address'),
            ('company_phone_edit', 'Company phone'),
            ('company_email_edit', 'Company email'),
            ('tax_number_edit', 'Tax number'),
            ('currency_combo', 'Currency')
        ]
        
        for field_name, description in company_fields:
            if hasattr(company_tab, field_name):
                field = getattr(company_tab, field_name)
                
                if isinstance(field, QLineEdit):
                    value = field.text()
                    print(f"✅ {description}: '{value}'")
                elif isinstance(field, QComboBox):
                    value = field.currentText()
                    count = field.count()
                    print(f"✅ {description}: '{value}' ({count} options)")
                else:
                    print(f"✅ {description}: Available")
            else:
                print(f"⚠️ {description}: Not found")
        
        # Test logo upload
        if hasattr(company_tab, 'logo_button'):
            logo_button = company_tab.logo_button
            print(f"✅ Logo upload button: {logo_button.text()}")
        else:
            print("⚠️ Logo upload button not found")
        
        # Test save functionality
        if hasattr(company_tab, 'save_company_settings'):
            print("✅ Save company settings function available")
        else:
            print("⚠️ Save company settings function not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Company settings tab test failed: {e}")
        return False

def test_printer_settings_tab(settings_widget):
    """Test printer settings tab functionality"""
    print("\n🖨️ Testing Printer Settings Tab...")
    
    try:
        # Find printer settings tab
        printer_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, PrinterSettingsTab):
                printer_tab = tab_widget
                settings_widget.tabs.setCurrentIndex(i)
                break
        
        if not printer_tab:
            print("❌ Printer settings tab not found")
            return False
        
        print("✅ Printer settings tab found")
        
        # Test printer fields
        printer_fields = [
            ('receipt_printer_combo', 'Receipt printer'),
            ('invoice_printer_combo', 'Invoice printer'),
            ('label_printer_combo', 'Label printer'),
            ('paper_size_combo', 'Paper size'),
            ('print_logo_check', 'Print logo'),
            ('print_header_check', 'Print header'),
            ('print_footer_check', 'Print footer')
        ]
        
        for field_name, description in printer_fields:
            if hasattr(printer_tab, field_name):
                field = getattr(printer_tab, field_name)
                
                if isinstance(field, QComboBox):
                    value = field.currentText()
                    count = field.count()
                    print(f"✅ {description}: '{value}' ({count} options)")
                elif isinstance(field, QCheckBox):
                    value = field.isChecked()
                    print(f"✅ {description}: {value}")
                else:
                    print(f"✅ {description}: Available")
            else:
                print(f"⚠️ {description}: Not found")
        
        # Test printer detection
        if hasattr(printer_tab, 'detect_printers_button'):
            detect_button = printer_tab.detect_printers_button
            print(f"✅ Detect printers button: {detect_button.text()}")
        else:
            print("⚠️ Detect printers button not found")
        
        # Test print test
        if hasattr(printer_tab, 'test_print_button'):
            test_button = printer_tab.test_print_button
            print(f"✅ Test print button: {test_button.text()}")
        else:
            print("⚠️ Test print button not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Printer settings tab test failed: {e}")
        return False

def test_settings_save_load():
    """Test settings save and load functionality"""
    print("\n💾 Testing Settings Save/Load...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create settings widget
        settings_widget = SettingsWidget()
        
        # Find general settings tab
        general_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, GeneralSettingsTab):
                general_tab = tab_widget
                break
        
        if general_tab:
            # Test loading settings
            if hasattr(general_tab, 'load_settings'):
                general_tab.load_settings()
                print("✅ Settings loaded successfully")
            else:
                print("⚠️ Load settings function not found")
            
            # Test saving settings
            if hasattr(general_tab, 'save_settings'):
                try:
                    general_tab.save_settings()
                    print("✅ Settings saved successfully")
                except Exception as e:
                    print(f"⚠️ Settings save failed: {e}")
            else:
                print("⚠️ Save settings function not found")
        
        # Test config manager operations
        original_language = config.get_language()
        
        # Test setting language
        test_language = 'fr' if original_language != 'fr' else 'en'
        config.set_language(test_language)
        
        # Verify change
        new_language = config.get_language()
        if new_language == test_language:
            print(f"✅ Config language change: {original_language} → {test_language}")
            
            # Change back
            config.set_language(original_language)
            final_language = config.get_language()
            
            if final_language == original_language:
                print(f"✅ Config language restored: {test_language} → {original_language}")
            else:
                print(f"⚠️ Config language restore failed")
        else:
            print(f"❌ Config language change failed")
            return False
        
        # Test theme setting
        original_theme = config.get_theme()
        test_theme = 'light' if original_theme != 'light' else 'dark'
        
        config.set_theme(test_theme)
        new_theme = config.get_theme()
        
        if new_theme == test_theme:
            print(f"✅ Config theme change: {original_theme} → {test_theme}")
            
            # Change back
            config.set_theme(original_theme)
        else:
            print(f"⚠️ Config theme change failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings save/load test failed: {e}")
        return False

def test_settings_integration():
    """Test settings integration with main application"""
    print("\n🔗 Testing Settings Integration...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Navigate to settings
        if 'settings' in main_window.nav_buttons:
            settings_button = main_window.nav_buttons['settings']
            settings_button.click()
            app.processEvents()
            
            # Check if settings widget is loaded
            current_widget = main_window.stacked_widget.currentWidget()
            if isinstance(current_widget, SettingsWidget):
                print("✅ Settings widget loaded in main window")
                
                # Test tab switching
                settings_widget = current_widget
                
                for i in range(settings_widget.tabs.count()):
                    tab_text = settings_widget.tabs.tabText(i)
                    settings_widget.tabs.setCurrentIndex(i)
                    app.processEvents()
                    
                    current_tab = settings_widget.tabs.currentWidget()
                    print(f"✅ Tab {i} ({tab_text}): {type(current_tab).__name__}")
                
                # Test language change through settings
                for i in range(settings_widget.tabs.count()):
                    tab_widget = settings_widget.tabs.widget(i)
                    if isinstance(tab_widget, GeneralSettingsTab):
                        general_tab = tab_widget
                        settings_widget.tabs.setCurrentIndex(i)
                        app.processEvents()
                        
                        # Test language change
                        if hasattr(general_tab, 'language_combo'):
                            original_language = get_current_language()
                            
                            # Find different language
                            for j in range(general_tab.language_combo.count()):
                                lang_code = general_tab.language_combo.itemData(j)
                                if lang_code and lang_code != original_language:
                                    general_tab.language_combo.setCurrentIndex(j)
                                    general_tab.on_language_changed()
                                    app.processEvents()
                                    
                                    # Check if main window updated
                                    new_language = get_current_language()
                                    if new_language == lang_code:
                                        print(f"✅ Language change integrated: {original_language} → {lang_code}")
                                        
                                        # Check navigation button texts
                                        nav_button_text = main_window.nav_buttons['settings'].text()
                                        print(f"   - Settings button text: '{nav_button_text}'")
                                        
                                        # Change back
                                        for k in range(general_tab.language_combo.count()):
                                            if general_tab.language_combo.itemData(k) == original_language:
                                                general_tab.language_combo.setCurrentIndex(k)
                                                general_tab.on_language_changed()
                                                app.processEvents()
                                                break
                                    else:
                                        print(f"❌ Language change integration failed")
                                        return False
                                    break
                        break
            else:
                print("❌ Settings widget not loaded properly")
                return False
        else:
            print("❌ Settings button not found")
            return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Settings integration test failed: {e}")
        return False

def test_settings_validation():
    """Test settings validation"""
    print("\n✅ Testing Settings Validation...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create settings widget
        settings_widget = SettingsWidget()
        
        # Find company settings tab
        company_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, CompanySettingsTab):
                company_tab = tab_widget
                break
        
        if company_tab:
            print("✅ Testing company settings validation...")
            
            # Test email validation
            if hasattr(company_tab, 'company_email_edit'):
                email_field = company_tab.company_email_edit
                
                # Test valid email
                email_field.setText("<EMAIL>")
                if hasattr(company_tab, 'validate_email'):
                    is_valid = company_tab.validate_email("<EMAIL>")
                    print(f"   ✅ Valid email test: {is_valid}")
                
                # Test invalid email
                email_field.setText("invalid-email")
                if hasattr(company_tab, 'validate_email'):
                    is_valid = company_tab.validate_email("invalid-email")
                    print(f"   ✅ Invalid email test: {not is_valid}")
            
            # Test phone validation
            if hasattr(company_tab, 'company_phone_edit'):
                phone_field = company_tab.company_phone_edit
                
                # Test valid phone
                phone_field.setText("******-123-4567")
                print("   ✅ Phone field accepts formatted number")
                
                # Test empty phone (should be allowed)
                phone_field.setText("")
                print("   ✅ Phone field accepts empty value")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings validation test failed: {e}")
        return False

def run_comprehensive_settings_test():
    """Run all settings tests"""
    print("🧪 STARTING COMPREHENSIVE SETTINGS SYSTEM TEST")
    print("=" * 60)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    settings_widget = None
    
    # Test 1: Database Setup
    test_results.append(("Database Setup", test_settings_database_setup()))
    
    # Test 2: Widget Creation
    settings_widget = test_settings_widget_creation()
    test_results.append(("Widget Creation", settings_widget is not False))
    
    if settings_widget:
        # Test 3: General Settings Tab
        test_results.append(("General Settings Tab", test_general_settings_tab(settings_widget)))
        
        # Test 4: Company Settings Tab
        test_results.append(("Company Settings Tab", test_company_settings_tab(settings_widget)))
        
        # Test 5: Printer Settings Tab
        test_results.append(("Printer Settings Tab", test_printer_settings_tab(settings_widget)))
    
    # Test 6: Settings Save/Load
    test_results.append(("Settings Save/Load", test_settings_save_load()))
    
    # Test 7: Settings Integration
    test_results.append(("Settings Integration", test_settings_integration()))
    
    # Test 8: Settings Validation
    test_results.append(("Settings Validation", test_settings_validation()))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 SETTINGS TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL SETTINGS TESTS PASSED! Settings system is working perfectly!")
    else:
        print("⚠️  Some settings tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_comprehensive_settings_test()
