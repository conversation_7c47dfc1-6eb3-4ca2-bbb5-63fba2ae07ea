#!/usr/bin/env python3
"""
اختبار بسيط لخط القائمة الجانبية
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.main_window import SidebarButton

def test_sidebar_button():
    """اختبار بسيط لزر القائمة الجانبية"""
    print("🔤 اختبار حجم خط القائمة الجانبية")
    print("=" * 40)
    
    try:
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إنشاء زر اختبار
        button = SidebarButton("🧾 نقطة البيع")
        
        # فحص الخصائص
        font = button.font()
        font_size = font.pointSize()
        height = button.height()
        
        print(f"📏 خصائص الزر:")
        print(f"   - النص: {button.text()}")
        print(f"   - حجم الخط: {font_size}px")
        print(f"   - ارتفاع الزر: {height}px")
        
        # التحقق من القيم
        if font_size == 12:
            print("✅ حجم الخط مثالي (12px)")
        else:
            print(f"❌ حجم الخط غير مثالي! متوقع: 12px، الحالي: {font_size}px")
        
        if height == 45:
            print("✅ ارتفاع الزر مثالي (45px)")
        else:
            print(f"❌ ارتفاع الزر غير مثالي! متوقع: 45px، الحالي: {height}px")
        
        # إظهار الزر للمراجعة البصرية
        button.show()
        print("\n🖼️ تم عرض الزر للمراجعة البصرية")
        print("   يمكنك رؤية الزر الآن للتأكد من الحجم المثالي")
        
        # انتظار قصير ثم إغلاق
        app.processEvents()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    test_sidebar_button()
