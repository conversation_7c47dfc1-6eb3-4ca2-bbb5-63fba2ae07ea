#!/usr/bin/env python3
"""
إنشاء نسخة الإنتاج للبيع التجاري
Create Production Version for Commercial Sale
"""

import os
import shutil
import sys
from datetime import datetime


def create_production_version():
    """إنشاء نسخة إنتاج نظيفة للبيع"""
    print("🏭 إنشاء نسخة الإنتاج OnePos")
    print("=" * 50)
    
    # مجلد الإنتاج
    production_dir = "OnePos_Production"
    
    # إزالة المجلد إذا كان موجود
    if os.path.exists(production_dir):
        shutil.rmtree(production_dir)
        print(f"🗑️ تم حذف المجلد القديم: {production_dir}")
    
    # إنشاء مجلد جديد
    os.makedirs(production_dir)
    print(f"📁 تم إنشاء مجلد الإنتاج: {production_dir}")
    
    # قائمة الملفات والمجلدات المطلوبة
    items_to_copy = [
        "main.py",
        "models/",
        "views/",
        "utils/",
        "translations/",
        "data/licenses.db",  # قاعدة بيانات الأكواد
        "requirements.txt"
    ]
    
    # نسخ الملفات
    for item in items_to_copy:
        src = item
        dst = os.path.join(production_dir, item)
        
        if os.path.isfile(src):
            # نسخ ملف
            os.makedirs(os.path.dirname(dst), exist_ok=True)
            shutil.copy2(src, dst)
            print(f"📄 تم نسخ الملف: {item}")
        elif os.path.isdir(src):
            # نسخ مجلد
            shutil.copytree(src, dst)
            print(f"📁 تم نسخ المجلد: {item}")
        else:
            print(f"⚠️ لم يتم العثور على: {item}")
    
    # إنشاء ملف README للعميل
    create_customer_readme(production_dir)
    
    # إنشاء ملف batch للتشغيل
    create_run_script(production_dir)
    
    # إنشاء ملف التثبيت
    create_installer_script(production_dir)
    
    # إحصائيات
    print("\n" + "=" * 50)
    print("✅ تم إنشاء نسخة الإنتاج بنجاح!")
    print(f"📁 المجلد: {production_dir}")
    print(f"📊 الملفات:")
    
    total_files = 0
    for root, dirs, files in os.walk(production_dir):
        total_files += len(files)
    
    print(f"   • إجمالي الملفات: {total_files}")
    print(f"   • حجم المجلد: {get_folder_size(production_dir):.2f} MB")
    
    print("\n🎯 الخطوات التالية:")
    print("1. اختبر النسخة في مجلد الإنتاج")
    print("2. انسخ المجلد إلى CD/فلاشة")
    print("3. أضف كود تفعيل لكل عميل")
    print("4. وزع النسخة تجارياً")


def create_customer_readme(production_dir):
    """إنشاء ملف README للعميل"""
    readme_content = """
# OnePos - نظام نقاط البيع المتقدم

## 🚀 مرحباً بك في OnePos

OnePos هو نظام نقاط بيع متقدم ومتكامل يوفر جميع الأدوات اللازمة لإدارة أعمالك التجارية بكفاءة عالية.

## 📋 المتطلبات

- Windows 10 أو أحدث
- Python 3.8 أو أحدث (سيتم تثبيته تلقائياً)
- 4 GB RAM كحد أدنى
- 1 GB مساحة فارغة على القرص الصلب

## 🔧 التثبيت

1. **انسخ جميع الملفات** إلى مجلد على جهازك
2. **شغل ملف install.bat** كمدير (Run as Administrator)
3. **انتظر انتهاء التثبيت** (قد يستغرق بضع دقائق)
4. **شغل التطبيق** من ملف run.bat

## 🔐 التفعيل

1. **شغل التطبيق** لأول مرة
2. **ستظهر نافذة التفعيل** تلقائياً
3. **أدخل كود التفعيل** المرفق مع النسخة
4. **اضغط تفعيل** وانتظر التأكيد
5. **استمتع بالاستخدام الكامل**

## 🎯 المميزات

- ✅ إدارة المنتجات والمخزون
- ✅ نظام نقاط البيع السريع
- ✅ إدارة العملاء والموردين
- ✅ التقارير والإحصائيات
- ✅ طباعة الفواتير والإيصالات
- ✅ نظام الباركود
- ✅ دعم متعدد اللغات (عربي، إنجليزي، فرنسي)
- ✅ واجهة مستخدم جميلة وسهلة

## 📞 الدعم الفني

للحصول على الدعم الفني أو كود تفعيل إضافي:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +1234567890
- الموقع: www.onepos.com

## ⚠️ ملاحظات مهمة

- كود التفعيل صالح لجهاز واحد فقط
- احتفظ بكود التفعيل في مكان آمن
- لا تشارك كود التفعيل مع الآخرين
- النسخ الاحتياطي مهم لحماية بياناتك

---
© 2024 OnePos. جميع الحقوق محفوظة.
"""
    
    with open(os.path.join(production_dir, "README.txt"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("📄 تم إنشاء ملف README للعميل")


def create_run_script(production_dir):
    """إنشاء ملف تشغيل للعميل"""
    run_script = """@echo off
echo ========================================
echo       OnePos - نظام نقاط البيع
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تشغيل install.bat أولاً
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo خطأ: البيئة الافتراضية غير موجودة
    echo يرجى تشغيل install.bat أولاً
    pause
    exit /b 1
)

echo تشغيل OnePos...
echo.

REM Activate virtual environment and run
call venv\\Scripts\\activate.bat
python main.py

if errorlevel 1 (
    echo.
    echo خطأ في تشغيل التطبيق
    pause
)

deactivate
"""
    
    with open(os.path.join(production_dir, "run.bat"), 'w', encoding='utf-8') as f:
        f.write(run_script)
    
    print("📄 تم إنشاء ملف التشغيل")


def create_installer_script(production_dir):
    """إنشاء ملف التثبيت"""
    install_script = """@echo off
echo ========================================
echo    OnePos Installation - تثبيت OnePos
echo ========================================
echo.

echo جاري التحقق من متطلبات النظام...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo تحذير: Python غير مثبت
    echo يرجى تحميل وتثبيت Python من python.org
    echo ثم إعادة تشغيل هذا الملف
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
echo.

echo إنشاء البيئة الافتراضية...
python -m venv venv
if errorlevel 1 (
    echo خطأ في إنشاء البيئة الافتراضية
    pause
    exit /b 1
)

echo ✅ تم إنشاء البيئة الافتراضية
echo.

echo تفعيل البيئة الافتراضية...
call venv\\Scripts\\activate.bat

echo تثبيت المتطلبات...
pip install --upgrade pip
pip install -r requirements.txt

if errorlevel 1 (
    echo خطأ في تثبيت المتطلبات
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ تم تثبيت OnePos بنجاح!
echo ========================================
echo.
echo يمكنك الآن تشغيل التطبيق من ملف run.bat
echo.
pause

deactivate
"""
    
    with open(os.path.join(production_dir, "install.bat"), 'w', encoding='utf-8') as f:
        f.write(install_script)
    
    print("📄 تم إنشاء ملف التثبيت")


def get_folder_size(folder_path):
    """حساب حجم المجلد بالميجابايت"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(folder_path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            if os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
    return total_size / (1024 * 1024)  # Convert to MB


if __name__ == "__main__":
    try:
        create_production_version()
    except Exception as e:
        print(f"\n❌ خطأ في إنشاء نسخة الإنتاج: {e}")
        import traceback
        traceback.print_exc()
    
    input("\nاضغط Enter للخروج...")
