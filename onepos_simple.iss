[Setup]
AppName=OnePos
AppVersion=1.0.0
AppVerName=OnePos 1.0.0
AppPublisher=ASSANAJE_APP
AppPublisherURL=https://www.assanaje.com
AppSupportURL=https://www.assanaje.com
AppUpdatesURL=https://www.assanaje.com
DefaultDirName={autopf}\OnePos
DefaultGroupName=OnePos
AllowNoIcons=yes
OutputDir=installer_output
OutputBaseFilename=OnePos_Setup_v1.0.0
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "french"; MessagesFile: "compiler:Languages\French.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "dist\OnePos\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\OnePos"; Filename: "{app}\OnePos.exe"
Name: "{group}\{cm:UninstallProgram,OnePos}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\OnePos"; Filename: "{app}\OnePos.exe"; Tasks: desktopicon

[Run]
Filename: "{app}\OnePos.exe"; Description: "{cm:LaunchProgram,OnePos}"; Flags: nowait postinstall skipifsilent
