"""
Change Password Dialog for OnePos POS System
Secure password change functionality with validation
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QFormLayout, QMessageBox,
                            QProgressBar, QGroupBox, QCheckBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from utils.translator import tr


class PasswordStrengthIndicator(QProgressBar):
    """Password strength indicator widget"""
    
    def __init__(self):
        super().__init__()
        self.setMaximum(100)
        self.setTextVisible(True)
        self.setFixedHeight(20)
        self.update_strength("")
    
    def update_strength(self, password):
        """Update password strength indicator"""
        if not password:
            self.setValue(0)
            self.setFormat("Password Strength")
            self.setStyleSheet("QProgressBar::chunk { background-color: #6c757d; }")
            return
        
        try:
            from models.permissions import security_manager
            strength = security_manager.check_password_strength(password)
            
            score = 0
            if strength['length']: score += 20
            if strength['uppercase']: score += 20
            if strength['lowercase']: score += 20
            if strength['digit']: score += 20
            if strength['special']: score += 20
            
            self.setValue(score)
            
            if score < 40:
                self.setFormat("Weak")
                self.setStyleSheet("QProgressBar::chunk { background-color: #dc3545; }")
            elif score < 80:
                self.setFormat("Medium")
                self.setStyleSheet("QProgressBar::chunk { background-color: #ffc107; }")
            else:
                self.setFormat("Strong")
                self.setStyleSheet("QProgressBar::chunk { background-color: #28a745; }")
                
        except ImportError:
            # Basic strength check
            score = min(len(password) * 10, 100)
            self.setValue(score)
            
            if score < 60:
                self.setFormat("Weak")
                self.setStyleSheet("QProgressBar::chunk { background-color: #dc3545; }")
            else:
                self.setFormat("Good")
                self.setStyleSheet("QProgressBar::chunk { background-color: #28a745; }")


class ChangePasswordDialog(QDialog):
    """Dialog for changing user password"""
    
    def __init__(self, user, parent=None):
        super().__init__(parent)
        self.user = user
        self.setWindowTitle(tr("password.change_title"))
        self.setModal(True)
        self.setFixedSize(450, 500)
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Header
        self.create_header(layout)
        
        # Password form
        self.create_password_form(layout)
        
        # Password requirements
        self.create_requirements(layout)
        
        # Buttons
        self.create_buttons(layout)
        
        # Apply styling
        self.apply_styling()
    
    def create_header(self, layout):
        """Create header section"""
        header_layout = QVBoxLayout()
        
        # Title
        title_label = QLabel("🔐 " + tr("password.change_title"))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setStyleSheet("color: #000000; font-weight: 800; margin-bottom: 10px;")
        header_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel(tr("password.change_subtitle"))
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("color: #6c757d; font-size: 12px; margin-bottom: 20px;")
        subtitle_label.setWordWrap(True)
        header_layout.addWidget(subtitle_label)
        
        layout.addLayout(header_layout)
    
    def create_password_form(self, layout):
        """Create password form"""
        form_group = QGroupBox(tr("password.form_title"))
        form_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 20px;
                background-color: #ffffff;
                font-weight: bold;
                color: #000000;
            }
            QGroupBox::title {
                color: #000000;
                font-weight: 700;
                font-size: 14px;
            }
        """)
        
        form_layout = QFormLayout(form_group)
        form_layout.setSpacing(15)
        
        # Current password
        current_label = QLabel(tr("password.current"))
        current_label.setStyleSheet("color: #000000; font-weight: 700; font-size: 13px;")
        
        self.current_password_edit = QLineEdit()
        self.current_password_edit.setEchoMode(QLineEdit.Password)
        self.current_password_edit.setPlaceholderText(tr("password.enter_current"))
        self.current_password_edit.setFixedHeight(40)
        
        form_layout.addRow(current_label, self.current_password_edit)
        
        # New password
        new_label = QLabel(tr("password.new"))
        new_label.setStyleSheet("color: #000000; font-weight: 700; font-size: 13px;")
        
        self.new_password_edit = QLineEdit()
        self.new_password_edit.setEchoMode(QLineEdit.Password)
        self.new_password_edit.setPlaceholderText(tr("password.enter_new"))
        self.new_password_edit.setFixedHeight(40)
        self.new_password_edit.textChanged.connect(self.update_password_strength)
        
        form_layout.addRow(new_label, self.new_password_edit)
        
        # Password strength indicator
        strength_label = QLabel(tr("password.strength"))
        strength_label.setStyleSheet("color: #000000; font-weight: 700; font-size: 13px;")
        
        self.strength_indicator = PasswordStrengthIndicator()
        form_layout.addRow(strength_label, self.strength_indicator)
        
        # Confirm password
        confirm_label = QLabel(tr("password.confirm"))
        confirm_label.setStyleSheet("color: #000000; font-weight: 700; font-size: 13px;")
        
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.confirm_password_edit.setPlaceholderText(tr("password.enter_confirm"))
        self.confirm_password_edit.setFixedHeight(40)
        self.confirm_password_edit.textChanged.connect(self.check_password_match)
        
        form_layout.addRow(confirm_label, self.confirm_password_edit)
        
        # Show passwords checkbox
        self.show_passwords_check = QCheckBox(tr("password.show_passwords"))
        self.show_passwords_check.setStyleSheet("color: #000000; font-weight: 600; font-size: 12px;")
        self.show_passwords_check.toggled.connect(self.toggle_password_visibility)
        form_layout.addRow("", self.show_passwords_check)
        
        layout.addWidget(form_group)
    
    def create_requirements(self, layout):
        """Create password requirements section"""
        req_group = QGroupBox(tr("password.requirements"))
        req_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                background-color: #f8f9fa;
                font-weight: bold;
                color: #000000;
            }
            QGroupBox::title {
                color: #000000;
                font-weight: 700;
                font-size: 12px;
            }
        """)
        
        req_layout = QVBoxLayout(req_group)
        
        requirements = [
            tr("password.req_length"),
            tr("password.req_uppercase"),
            tr("password.req_lowercase"),
            tr("password.req_digit"),
            tr("password.req_special")
        ]
        
        for req in requirements:
            req_label = QLabel(f"• {req}")
            req_label.setStyleSheet("color: #000000; font-size: 11px; font-weight: 600;")
            req_layout.addWidget(req_label)
        
        layout.addWidget(req_group)
    
    def create_buttons(self, layout):
        """Create buttons section"""
        button_layout = QHBoxLayout()
        
        # Cancel button
        self.cancel_button = QPushButton(tr("common.cancel"))
        self.cancel_button.setFixedHeight(45)
        self.cancel_button.clicked.connect(self.reject)
        
        # Change password button
        self.change_button = QPushButton(tr("password.change_button"))
        self.change_button.setFixedHeight(45)
        self.change_button.clicked.connect(self.change_password)
        self.change_button.setEnabled(False)
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.change_button)
        
        layout.addLayout(button_layout)
    
    def apply_styling(self):
        """Apply widget styling"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QLineEdit {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: #ffffff;
                color: #000000;
            }
            
            QLineEdit:focus {
                border-color: #0d6efd;
                outline: none;
            }
            
            QPushButton {
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                padding: 15px;
            }
            
            QPushButton#cancel_button {
                background-color: #6c757d;
                color: #ffffff;
            }
            
            QPushButton#cancel_button:hover {
                background-color: #5a6268;
            }
            
            QPushButton#change_button {
                background-color: #0d6efd;
                color: #ffffff;
            }
            
            QPushButton#change_button:hover {
                background-color: #0b5ed7;
            }
            
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
            }
            
            QCheckBox {
                spacing: 8px;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #dee2e6;
                border-radius: 4px;
                background-color: #ffffff;
            }
            
            QCheckBox::indicator:checked {
                background-color: #0d6efd;
                border-color: #0d6efd;
            }
        """)
        
        # Set object names for styling
        self.cancel_button.setObjectName("cancel_button")
        self.change_button.setObjectName("change_button")
    
    def update_password_strength(self):
        """Update password strength indicator"""
        password = self.new_password_edit.text()
        self.strength_indicator.update_strength(password)
        self.check_form_validity()
    
    def check_password_match(self):
        """Check if passwords match"""
        new_password = self.new_password_edit.text()
        confirm_password = self.confirm_password_edit.text()
        
        if confirm_password and new_password != confirm_password:
            self.confirm_password_edit.setStyleSheet("""
                QLineEdit {
                    border: 2px solid #dc3545;
                    border-radius: 8px;
                    padding: 12px 15px;
                    font-size: 14px;
                    background-color: #ffffff;
                    color: #000000;
                }
            """)
        else:
            self.confirm_password_edit.setStyleSheet("""
                QLineEdit {
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    padding: 12px 15px;
                    font-size: 14px;
                    background-color: #ffffff;
                    color: #000000;
                }
            """)
        
        self.check_form_validity()
    
    def check_form_validity(self):
        """Check if form is valid"""
        current = self.current_password_edit.text()
        new = self.new_password_edit.text()
        confirm = self.confirm_password_edit.text()
        
        valid = (
            len(current) > 0 and
            len(new) >= 6 and
            new == confirm and
            new != current
        )
        
        self.change_button.setEnabled(valid)
    
    def toggle_password_visibility(self, checked):
        """Toggle password visibility"""
        mode = QLineEdit.Normal if checked else QLineEdit.Password
        self.current_password_edit.setEchoMode(mode)
        self.new_password_edit.setEchoMode(mode)
        self.confirm_password_edit.setEchoMode(mode)
    
    def change_password(self):
        """Change user password"""
        try:
            current_password = self.current_password_edit.text()
            new_password = self.new_password_edit.text()
            
            # Change password
            self.user.change_password(current_password, new_password)
            
            QMessageBox.information(
                self,
                tr("common.success"),
                tr("password.changed_successfully")
            )
            
            self.accept()
            
        except ValueError as e:
            QMessageBox.warning(
                self,
                tr("common.warning"),
                str(e)
            )
            
        except Exception as e:
            QMessageBox.critical(
                self,
                tr("common.error"),
                tr("password.change_error").format(str(e))
            )
