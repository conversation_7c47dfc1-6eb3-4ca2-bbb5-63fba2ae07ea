#!/usr/bin/env python3
"""
إصلاح شامل لجميع مشاكل الترجمة في التطبيق
Comprehensive translation fix for all application issues
"""

import os
import json
import re
from datetime import datetime

class ComprehensiveTranslationFixer:
    def __init__(self):
        self.fixes_applied = []
        
    def log_fix(self, description):
        """تسجيل إصلاح"""
        self.fixes_applied.append(description)
        print(f"   ✅ {description}")

    def fix_pos_widget(self):
        """إصلاح واجهة نقطة البيع"""
        print("\n🔧 إصلاح واجهة نقطة البيع...")
        
        file_path = "views/pos_widget.py"
        if not os.path.exists(file_path):
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إصلاحات محددة
            fixes = [
                ('"0.00 DZD"', 'f"0.00 {tr(\'common.currency\')}"'),
                ('"DZD"', 'tr("common.currency")')
            ]
            
            modified = False
            for old, new in fixes:
                if old in content:
                    content = content.replace(old, new)
                    modified = True
            
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.log_fix("إصلاح ترجمات واجهة نقطة البيع")
                
        except Exception as e:
            print(f"   ❌ خطأ في إصلاح POS: {e}")

    def fix_products_widget(self):
        """إصلاح واجهة المنتجات"""
        print("\n🔧 إصلاح واجهة المنتجات...")
        
        file_path = "views/products_widget.py"
        if not os.path.exists(file_path):
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إصلاحات محددة
            fixes = [
                ('QMessageBox.critical(self, "Error"', 'QMessageBox.critical(self, tr("common.error")'),
                ('QMessageBox.information(self, "Success"', 'QMessageBox.information(self, tr("common.success")'),
                ('"piece"', 'tr("common.piece")'),
                ('"Generate Barcode"', 'tr("products.generate_barcode")'),
                ('f"Error creating product: {str(e)}"', 'f"{tr(\'common.error\')}: {str(e)}"'),
                ('f"Error updating product: {str(e)}"', 'f"{tr(\'common.error\')}: {str(e)}"'),
                ('f"Error deleting product: {str(e)}"', 'f"{tr(\'common.error\')}: {str(e)}"')
            ]
            
            modified = False
            for old, new in fixes:
                if old in content:
                    content = content.replace(old, new)
                    modified = True
            
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.log_fix("إصلاح ترجمات واجهة المنتجات")
                
        except Exception as e:
            print(f"   ❌ خطأ في إصلاح المنتجات: {e}")

    def fix_sales_widget(self):
        """إصلاح واجهة المبيعات"""
        print("\n🔧 إصلاح واجهة المبيعات...")
        
        file_path = "views/sales_widget.py"
        if not os.path.exists(file_path):
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إصلاحات محددة
            fixes = [
                ('"No sales data"', 'tr("sales.no_data")'),
                ('QMessageBox.critical(self, "Error"', 'QMessageBox.critical(self, tr("common.error")'),
                ('QMessageBox.warning(self, "Warning"', 'QMessageBox.warning(self, tr("common.warning")'),
                ('QMessageBox.warning(self, "تحذير"', 'QMessageBox.warning(self, tr("common.warning")'),
                ('"Search by invoice number or customer"', 'tr("sales.search_placeholder")'),
                ('"خطأ"', 'tr("common.error")')
            ]
            
            modified = False
            for old, new in fixes:
                if old in content:
                    content = content.replace(old, new)
                    modified = True
            
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.log_fix("إصلاح ترجمات واجهة المبيعات")
                
        except Exception as e:
            print(f"   ❌ خطأ في إصلاح المبيعات: {e}")

    def fix_customers_widget(self):
        """إصلاح واجهة العملاء"""
        print("\n🔧 إصلاح واجهة العملاء...")
        
        file_path = "views/customers_widget.py"
        if not os.path.exists(file_path):
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إصلاحات محددة
            fixes = [
                ('"Select a customer to view details"', 'tr("customers.select_to_view")')
            ]
            
            modified = False
            for old, new in fixes:
                if old in content:
                    content = content.replace(old, new)
                    modified = True
            
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.log_fix("إصلاح ترجمات واجهة العملاء")
                
        except Exception as e:
            print(f"   ❌ خطأ في إصلاح العملاء: {e}")

    def fix_performance_widget(self):
        """إصلاح واجهة الأداء"""
        print("\n🔧 إصلاح واجهة الأداء...")
        
        file_path = "views/performance_widget.py"
        if not os.path.exists(file_path):
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إصلاحات محددة
            fixes = [
                ('"Slow Queries: N/A"', 'f"{tr(\'performance.slow_queries\')}: {tr(\'common.not_available\')}"'),
                ('"N/A"', 'tr("common.not_available")'),
                ('"Avg Time: N/A"', 'f"{tr(\'performance.avg_time\')}: {tr(\'common.not_available\')}"'),
                ('"Total: N/A"', 'f"{tr(\'common.total\')}: {tr(\'common.not_available\')}"'),
                ('"psutil not available"', 'tr("performance.psutil_not_available")')
            ]
            
            modified = False
            for old, new in fixes:
                if old in content:
                    content = content.replace(old, new)
                    modified = True
            
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.log_fix("إصلاح ترجمات واجهة الأداء")
                
        except Exception as e:
            print(f"   ❌ خطأ في إصلاح الأداء: {e}")

    def fix_settings_widget(self):
        """إصلاح واجهة الإعدادات"""
        print("\n🔧 إصلاح واجهة الإعدادات...")
        
        file_path = "views/settings_widget.py"
        if not os.path.exists(file_path):
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إصلاحات محددة
            fixes = [
                ('"✅ مفعل"', 'f"✅ {tr(\'license.activated\')}"'),
                ('"Settings saved"', 'tr("settings.saved")'),
                ('"Reset Settings"', 'tr("settings.reset")'),
                ('"✅ التطبيق مفعل بنجاح"', 'f"✅ {tr(\'license.activation_success\')}"'),
                ('QMessageBox.critical(self, "فشل التفعيل"', 'QMessageBox.critical(self, tr("license.activation_failed")'),
                ('QMessageBox.warning(self, "تحذير"', 'QMessageBox.warning(self, tr("common.warning")'),
                ('QMessageBox.information(self, "نجح التفعيل"', 'QMessageBox.information(self, tr("license.activation_success")'),
                ('QMessageBox.information(self, "Backup"', 'QMessageBox.information(self, tr("settings.backup")'),
                ('QMessageBox.information(self, "Restore"', 'QMessageBox.information(self, tr("settings.restore")')
            ]
            
            modified = False
            for old, new in fixes:
                if old in content:
                    content = content.replace(old, new)
                    modified = True
            
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.log_fix("إصلاح ترجمات واجهة الإعدادات")
                
        except Exception as e:
            print(f"   ❌ خطأ في إصلاح الإعدادات: {e}")

    def add_missing_translations_to_files(self):
        """إضافة الترجمات المفقودة لملفات الترجمة"""
        print("\n🔧 إضافة الترجمات المفقودة...")
        
        new_translations = {
            "ar.json": {
                "common.currency": "دج",
                "common.piece": "قطعة",
                "common.not_available": "غير متاح",
                "sales.no_data": "لا توجد بيانات مبيعات",
                "sales.search_placeholder": "البحث برقم الفاتورة أو العميل",
                "customers.select_to_view": "اختر عميل لعرض التفاصيل",
                "performance.slow_queries": "الاستعلامات البطيئة",
                "performance.avg_time": "متوسط الوقت",
                "performance.psutil_not_available": "psutil غير متاح",
                "settings.saved": "تم حفظ الإعدادات",
                "settings.reset": "إعادة تعيين الإعدادات",
                "settings.backup": "نسخ احتياطي",
                "settings.restore": "استعادة",
                "license.activated": "مفعل",
                "license.activation_success": "تم التفعيل بنجاح",
                "license.activation_failed": "فشل التفعيل"
            },
            "en.json": {
                "common.currency": "DZD",
                "common.piece": "piece",
                "common.not_available": "N/A",
                "sales.no_data": "No sales data",
                "sales.search_placeholder": "Search by invoice number or customer",
                "customers.select_to_view": "Select a customer to view details",
                "performance.slow_queries": "Slow Queries",
                "performance.avg_time": "Avg Time",
                "performance.psutil_not_available": "psutil not available",
                "settings.saved": "Settings saved",
                "settings.reset": "Reset Settings",
                "settings.backup": "Backup",
                "settings.restore": "Restore",
                "license.activated": "Activated",
                "license.activation_success": "Activation successful",
                "license.activation_failed": "Activation failed"
            },
            "fr.json": {
                "common.currency": "DZD",
                "common.piece": "pièce",
                "common.not_available": "N/D",
                "sales.no_data": "Aucune donnée de vente",
                "sales.search_placeholder": "Rechercher par numéro de facture ou client",
                "customers.select_to_view": "Sélectionnez un client pour voir les détails",
                "performance.slow_queries": "Requêtes Lentes",
                "performance.avg_time": "Temps Moyen",
                "performance.psutil_not_available": "psutil non disponible",
                "settings.saved": "Paramètres sauvegardés",
                "settings.reset": "Réinitialiser les Paramètres",
                "settings.backup": "Sauvegarde",
                "settings.restore": "Restaurer",
                "license.activated": "Activé",
                "license.activation_success": "Activation réussie",
                "license.activation_failed": "Échec de l'activation"
            }
        }
        
        for filename, translations in new_translations.items():
            file_path = f"translations/{filename}"
            
            try:
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # إضافة الترجمات الجديدة
                    for key, value in translations.items():
                        if '.' in key:
                            # مفتاح مركب مثل common.currency
                            parts = key.split('.')
                            if parts[0] not in data:
                                data[parts[0]] = {}
                            data[parts[0]][parts[1]] = value
                        else:
                            # مفتاح بسيط
                            data[key] = value
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=4)
                    
                    self.log_fix(f"إضافة ترجمات مفقودة في {filename}")
                
            except Exception as e:
                print(f"   ❌ خطأ في تحديث {filename}: {e}")

    def fix_hold_order_implementation(self):
        """إصلاح تطبيق Hold Order بشكل صحيح"""
        print("\n🔧 إصلاح تطبيق Hold Order...")
        
        file_path = "views/pos_widget.py"
        if not os.path.exists(file_path):
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن دالة hold_order الحالية
            if 'def hold_order(self):' in content:
                # استبدال التطبيق المؤقت بتطبيق حقيقي
                old_implementation = '''def hold_order(self):
        """Hold current order"""
        QMessageBox.information(self, tr("pos.hold_order"),
                              "Hold order functionality will be implemented")'''
                
                new_implementation = '''def hold_order(self):
        """Hold current order"""
        if not hasattr(self, 'cart_items') or not self.cart_items:
            QMessageBox.warning(self, tr("common.warning"), tr("pos.cart_empty"))
            return
        
        # طلب اسم للطلب المعلق
        from PyQt5.QtWidgets import QInputDialog
        order_name, ok = QInputDialog.getText(
            self, tr("pos.hold_order"), 
            tr("pos.enter_order_name"),
            text=f"Order {datetime.now().strftime('%H:%M')}"
        )
        
        if not ok or not order_name.strip():
            return
        
        try:
            # حفظ الطلب المعلق
            if not hasattr(self, 'held_orders'):
                self.held_orders = []
            
            held_order = {
                'name': order_name.strip(),
                'items': self.cart_items.copy(),
                'customer': getattr(self.current_customer, 'id', None) if hasattr(self, 'current_customer') else None,
                'tax_rate': getattr(self, 'tax_rate', 0.0),
                'timestamp': datetime.now().isoformat()
            }
            
            self.held_orders.append(held_order)
            
            # مسح السلة الحالية
            if hasattr(self, 'clear_cart'):
                self.clear_cart()
            
            QMessageBox.information(self, tr("common.success"), 
                                  tr("pos.order_held_successfully").format(order_name))
            
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"{tr('common.error')}: {e}")'''
                
                if old_implementation in content:
                    content = content.replace(old_implementation, new_implementation)
                    
                    # إضافة import datetime إذا لم يكن موجود
                    if "from datetime import datetime" not in content:
                        # البحث عن مكان مناسب للإضافة
                        lines = content.split('\n')
                        insert_index = 0
                        for i, line in enumerate(lines):
                            if line.startswith('from ') or line.startswith('import '):
                                insert_index = i + 1
                        
                        lines.insert(insert_index, "from datetime import datetime")
                        content = '\n'.join(lines)
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.log_fix("إصلاح تطبيق Hold Order بشكل صحيح")
                
        except Exception as e:
            print(f"   ❌ خطأ في إصلاح Hold Order: {e}")

    def run_comprehensive_fix(self):
        """تشغيل الإصلاح الشامل"""
        print("🚀 بدء الإصلاح الشامل للترجمات")
        print("="*80)
        
        # إضافة الترجمات المفقودة أولاً
        self.add_missing_translations_to_files()
        
        # إصلاح جميع الواجهات
        self.fix_pos_widget()
        self.fix_products_widget()
        self.fix_sales_widget()
        self.fix_customers_widget()
        self.fix_performance_widget()
        self.fix_settings_widget()
        
        # إصلاح Hold Order
        self.fix_hold_order_implementation()
        
        # تقرير النتائج
        print("\n" + "="*80)
        print("📋 تقرير الإصلاح الشامل")
        print("="*80)
        
        print(f"\n🔧 الإصلاحات المطبقة: {len(self.fixes_applied)}")
        for fix in self.fixes_applied:
            print(f"   ✅ {fix}")
        
        print("\n🎯 الخطوات التالية:")
        print("1. بناء التطبيق: pyinstaller --onedir --windowed --icon=app_icon.ico --name=OnePos main.py --clean")
        print("2. إنشاء المثبت: build_installer_simple.bat")
        print("3. اختبار جميع الوظائف والترجمات")
        
        print("\n" + "="*80)

def main():
    """الدالة الرئيسية"""
    fixer = ComprehensiveTranslationFixer()
    fixer.run_comprehensive_fix()

if __name__ == "__main__":
    main()
