#!/usr/bin/env python3
"""
اختبار نظام الطباعة المحسن - OnePos
Test Enhanced Printing System
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.main_window import MainWindow
from models.user import User
from utils.printer_manager import printer_manager
from utils.print_manager import print_manager

def test_printer_detection():
    """اختبار كشف الطابعات"""
    print("🔍 اختبار كشف الطابعات")
    print("=" * 35)
    
    try:
        # تحديث قائمة الطابعات
        success = printer_manager.refresh_printers()
        
        if not success:
            print("❌ فشل في تحديث قائمة الطابعات")
            return False
        
        # عرض الطابعات المتاحة
        all_printers = printer_manager.get_available_printers()
        thermal_printers = printer_manager.get_thermal_printers()
        standard_printers = printer_manager.get_standard_printers()
        
        print(f"📊 إجمالي الطابعات: {len(all_printers)}")
        print(f"🔥 الطابعات الحرارية: {len(thermal_printers)}")
        print(f"📄 الطابعات العادية: {len(standard_printers)}")
        
        if all_printers:
            print("\n📋 قائمة الطابعات المتاحة:")
            for name, data in all_printers.items():
                printer_type = "🔥 حرارية" if data['is_thermal'] else "📄 عادية"
                status = printer_manager.get_printer_status(name)
                print(f"   {printer_type} {name} - {status}")
                
                if data['is_default']:
                    print(f"      ⭐ طابعة افتراضية")
        else:
            print("⚠️ لم يتم العثور على طابعات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في كشف الطابعات: {e}")
        return False

def test_printer_assignment():
    """اختبار تعيين الطابعات"""
    print("\n⚙️ اختبار تعيين الطابعات")
    print("=" * 35)
    
    try:
        # التعيين التلقائي
        printer_manager.auto_assign_printers()
        
        # فحص التعيينات
        receipt_printer = printer_manager.get_receipt_printer()
        invoice_printer = printer_manager.get_invoice_printer()
        label_printer = printer_manager.get_label_printer()
        
        print("📋 التعيينات الحالية:")
        print(f"   🧾 طابعة الإيصالات: {receipt_printer or 'غير محددة'}")
        print(f"   📄 طابعة الفواتير: {invoice_printer or 'غير محددة'}")
        print(f"   🏷️ طابعة الملصقات: {label_printer or 'غير محددة'}")
        
        # حفظ الإعدادات
        save_success = printer_manager.save_settings()
        if save_success:
            print("✅ تم حفظ إعدادات الطابعات")
        else:
            print("❌ فشل في حفظ إعدادات الطابعات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تعيين الطابعات: {e}")
        return False

def test_pos_buttons():
    """اختبار أزرار POS الجديدة"""
    print("\n🧾 اختبار أزرار POS الجديدة")
    print("=" * 35)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        main_window = MainWindow(admin_user)
        
        # الانتقال لقسم POS
        main_window.load_module('pos')
        app.processEvents()
        
        # البحث عن أزرار الطباعة
        pos_widget = None
        for i in range(main_window.content_frame.count()):
            widget = main_window.content_frame.widget(i)
            if hasattr(widget, 'complete_sale_button'):
                pos_widget = widget
                break
        
        if not pos_widget:
            print("❌ لم يتم العثور على واجهة POS")
            return False
        
        # فحص النصوص الجديدة للأزرار
        buttons_to_check = [
            ('complete_sale_button', '💳 دفع وطباعة الفاتورة'),
            ('print_receipt_button', '🧾 طباعة مبسطة'),
            ('print_invoice_button', '📄 طباعة احترافية')
        ]
        
        all_correct = True
        
        for button_attr, expected_text in buttons_to_check:
            if hasattr(pos_widget, button_attr):
                button = getattr(pos_widget, button_attr)
                actual_text = button.text()
                
                if actual_text == expected_text:
                    print(f"   ✅ {button_attr}: {actual_text}")
                else:
                    print(f"   ❌ {button_attr}: متوقع '{expected_text}', الحالي '{actual_text}'")
                    all_correct = False
            else:
                print(f"   ❌ {button_attr}: الزر غير موجود")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أزرار POS: {e}")
        return False

def test_print_functionality():
    """اختبار وظائف الطباعة"""
    print("\n🖨️ اختبار وظائف الطباعة")
    print("=" * 35)
    
    try:
        # بيانات تجريبية للطباعة
        test_sale_data = {
            'id': 'TEST-001',
            'date': '2024-01-15',
            'time': '14:30:25',
            'cashier': 'Admin',
            'customer': 'عميل تجريبي',
            'items': [
                {'name': 'منتج تجريبي 1', 'quantity': 2, 'price': 10.00, 'total': 20.00},
                {'name': 'منتج تجريبي 2', 'quantity': 1, 'price': 15.00, 'total': 15.00}
            ],
            'subtotal': 35.00,
            'tax': 3.50,
            'discount': 0.00,
            'total': 38.50,
            'payment_method': 'نقدي',
            'amount_paid': 40.00,
            'change': 1.50
        }
        
        print("📄 اختبار طباعة الإيصال المبسط:")
        try:
            # لا نطبع فعلياً، فقط نختبر إنشاء المحتوى
            receipt_html = print_manager.generate_receipt_html(test_sale_data)
            if receipt_html and len(receipt_html) > 100:
                print("   ✅ تم إنشاء محتوى الإيصال المبسط")
            else:
                print("   ❌ فشل في إنشاء محتوى الإيصال المبسط")
                return False
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الإيصال المبسط: {e}")
            return False
        
        print("📋 اختبار طباعة الفاتورة الاحترافية:")
        try:
            invoice_html = print_manager.generate_invoice_html(test_sale_data)
            if invoice_html and len(invoice_html) > 200:
                print("   ✅ تم إنشاء محتوى الفاتورة الاحترافية")
            else:
                print("   ❌ فشل في إنشاء محتوى الفاتورة الاحترافية")
                return False
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء الفاتورة الاحترافية: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظائف الطباعة: {e}")
        return False

def test_printer_settings_ui():
    """اختبار واجهة إعدادات الطابعات"""
    print("\n⚙️ اختبار واجهة إعدادات الطابعات")
    print("=" * 40)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        main_window = MainWindow(admin_user)
        
        # الانتقال لقسم الإعدادات
        main_window.load_module('settings')
        app.processEvents()
        
        # البحث عن واجهة الإعدادات
        settings_widget = None
        for i in range(main_window.content_frame.count()):
            widget = main_window.content_frame.widget(i)
            if hasattr(widget, 'tabs'):
                settings_widget = widget
                break
        
        if not settings_widget:
            print("❌ لم يتم العثور على واجهة الإعدادات")
            return False
        
        # البحث عن تبويب الطابعات
        printer_tab_found = False
        for i in range(settings_widget.tabs.count()):
            tab_text = settings_widget.tabs.tabText(i)
            if 'طابعات' in tab_text or 'Printer' in tab_text:
                printer_tab_found = True
                print(f"   ✅ تم العثور على تبويب الطابعات: {tab_text}")
                
                # التبديل للتبويب
                settings_widget.tabs.setCurrentIndex(i)
                app.processEvents()
                
                # فحص محتوى التبويب
                current_widget = settings_widget.tabs.currentWidget()
                if hasattr(current_widget, 'printers_table'):
                    print("   ✅ جدول الطابعات موجود")
                if hasattr(current_widget, 'receipt_printer_combo'):
                    print("   ✅ قائمة طابعة الإيصالات موجودة")
                if hasattr(current_widget, 'invoice_printer_combo'):
                    print("   ✅ قائمة طابعة الفواتير موجودة")
                if hasattr(current_widget, 'label_printer_combo'):
                    print("   ✅ قائمة طابعة الملصقات موجودة")
                
                break
        
        if not printer_tab_found:
            print("   ❌ لم يتم العثور على تبويب الطابعات")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة إعدادات الطابعات: {e}")
        return False

def run_enhanced_printing_test():
    """تشغيل جميع اختبارات نظام الطباعة المحسن"""
    print("🖨️ بدء اختبار نظام الطباعة المحسن")
    print("=" * 60)
    
    results = []
    
    # اختبار 1: كشف الطابعات
    results.append(("كشف الطابعات", test_printer_detection()))
    
    # اختبار 2: تعيين الطابعات
    results.append(("تعيين الطابعات", test_printer_assignment()))
    
    # اختبار 3: أزرار POS الجديدة
    results.append(("أزرار POS الجديدة", test_pos_buttons()))
    
    # اختبار 4: وظائف الطباعة
    results.append(("وظائف الطباعة", test_print_functionality()))
    
    # اختبار 5: واجهة إعدادات الطابعات
    results.append(("واجهة إعدادات الطابعات", test_printer_settings_ui()))
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار نظام الطباعة المحسن")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 نظام الطباعة المحسن يعمل بشكل مثالي!")
        print("✨ جميع الميزات الجديدة تعمل بنجاح")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_enhanced_printing_test()
