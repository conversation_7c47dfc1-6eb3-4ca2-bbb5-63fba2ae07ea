"""
Main window for OnePos POS System
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
                            QStackedWidget, QPushButton, QLabel, QFrame,
                            QStatusBar, QMenuBar, QAction, QMessageBox,
                            QSizePolicy, QSpacerItem, QToolBar, QApplication)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QSize
from PyQt5.QtGui import QFont, QIcon, QPixmap

from models.user import User, UserSession
from utils.config_manager import config
from utils.translator import tr, register_for_translation, unregister_from_translation
from utils.license_manager import license_manager





class MainWindow(QMainWindow):
    """Main application window"""
    
    # Signals
    user_logged_out = pyqtSignal()
    
    def __init__(self, user, parent=None):
        super().__init__(parent)
        self.current_user = user
        self.current_session = None

        # Initialize permission checker
        self.permission_checker = None
        if user:
            try:
                from models.permissions import Per<PERSON><PERSON>he<PERSON>
                self.permission_checker = PermissionChecker(user)
            except ImportError:
                pass

        # Register for translation updates
        register_for_translation(self)

        # Initialize UI
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_license_system()
        self.complete_initialization()

    def check_permission(self, permission_name):
        """Check if current user has permission"""
        # For now, allow all permissions to ensure all sections are visible
        # This can be enhanced later with proper role-based access control
        return True

        # Future implementation:
        # if not self.permission_checker:
        #     return True  # Default allow if no permission system
        #
        # try:
        #     from models.permissions import Permission
        #     permission = getattr(Permission, permission_name, None)
        #     if permission:
        #         return self.permission_checker.has_permission(permission)
        # except (ImportError, AttributeError):
        #     pass
        #
        # return True  # Default allow

    def complete_initialization(self):
        """Complete the initialization process"""
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_connections()

        # Start user session
        self.start_user_session()

        # Setup auto-logout timer
        self.setup_auto_logout()

        # Load initial module
        self.load_pos_module()

    def setup_ui(self):
        """Setup user interface"""
        self.setWindowTitle(f"{config.get_app_name()} - {self.current_user.full_name}")
        self.setMinimumSize(1200, 800)

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout (vertical now)
        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        # Create top navigation and content area
        self.create_layout_components()

        central_widget.setLayout(self.main_layout)

    def create_layout_components(self):
        """Create top navigation and content area"""
        # Create top navigation bar
        self.top_nav_frame = self.create_top_navigation()
        self.main_layout.addWidget(self.top_nav_frame)

        # Create content area
        self.content_frame = self.create_content_area_frame()
        self.main_layout.addWidget(self.content_frame)




    def create_top_navigation(self):
        """Create top navigation bar with section names"""
        nav_frame = QFrame()
        nav_frame.setObjectName("top_navigation_frame")
        nav_frame.setFixedHeight(60)

        layout = QHBoxLayout()
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(5)

        # App title/logo
        title_label = QLabel(f"🏪 {config.get_app_name()}")
        title_label.setObjectName("app_title")
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                padding: 10px 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 8px;
            }
        """)
        layout.addWidget(title_label)

        # Add stretch to center the navigation buttons
        layout.addStretch()

        # Navigation buttons
        self.nav_buttons = {}
        nav_buttons = [
            ("🧾 " + tr("navigation.pos"), "pos", True),
            ("📦 " + tr("navigation.products"), "products", True),
            ("💰 " + tr("navigation.sales"), "sales", True),
            ("🧍 " + tr("navigation.customers"), "customers", True),
            ("📥 " + tr("navigation.purchases"), "purchases", True),
            ("👤 " + tr("navigation.users"), "users", True),
            ("📊 " + tr("navigation.reports"), "reports", True),
            ("⚡ " + tr("navigation.performance"), "performance", True),
            ("🛠️ " + tr("navigation.settings"), "settings", True)
        ]

        for text, module_name, has_permission in nav_buttons:
            if has_permission:
                button = self.create_nav_button(text, module_name)
                self.nav_buttons[module_name] = button
                layout.addWidget(button)

        # Add stretch to balance
        layout.addStretch()

        # User info and logout
        user_info = QLabel(f"👤 {self.current_user.full_name}")
        user_info.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: 600;
                padding: 5px 10px;
            }
        """)
        layout.addWidget(user_info)

        logout_button = QPushButton("🚪 " + tr("navigation.logout"))
        logout_button.setObjectName("danger_logout")
        logout_button.clicked.connect(self.handle_logout)
        layout.addWidget(logout_button)

        nav_frame.setLayout(layout)
        return nav_frame

    def create_nav_button(self, text, module_name):
        """Create a navigation button for the top bar"""
        button = QPushButton(text)
        button.setObjectName(f"nav_{module_name}")
        button.setCheckable(True)
        button.setFixedHeight(40)
        button.setMinimumWidth(120)
        button.clicked.connect(lambda: self.load_module(module_name))

        # Set initial style
        button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #2c3e50;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 600;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #74b9ff, stop:1 #0984e3);
                color: #ffffff;
                border: 2px solid #0984e3;
            }
            QPushButton:checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: #ffffff;
                border: 2px solid #667eea;
                font-weight: bold;
            }
        """)

        return button

    def create_content_area_frame(self):
        """Create main content area frame"""
        content_frame = QStackedWidget()
        content_frame.setObjectName("content_stack")
        return content_frame

    

    


    

    
    def setup_menu_bar(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("File")
        
        # New sale action
        new_sale_action = QAction("New Sale", self)
        new_sale_action.setShortcut("Ctrl+N")
        new_sale_action.triggered.connect(lambda: self.load_module("pos"))
        file_menu.addAction(new_sale_action)
        
        file_menu.addSeparator()
        
        # Backup action
        backup_action = QAction("Backup Database", self)
        backup_action.triggered.connect(self.backup_database)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu("View")
        
        # Fullscreen action
        fullscreen_action = QAction("Toggle Fullscreen", self)
        fullscreen_action.setShortcut("F11")
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        # User menu
        user_menu = menubar.addMenu(f"👤 {self.current_user.username}")

        # Change password action
        change_password_action = QAction("🔐 Change Password", self)
        change_password_action.triggered.connect(self.change_password)
        user_menu.addAction(change_password_action)

        user_menu.addSeparator()

        # Logout action
        logout_action = QAction("🚪 Logout", self)
        logout_action.triggered.connect(self.logout)
        user_menu.addAction(logout_action)

        # Help menu
        help_menu = menubar.addMenu("Help")

        # About action
        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """Setup toolbar (removed duplicate sections)"""
        # Toolbar removed to avoid duplication with navigation bar
        pass
    
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # Restore original status bar styling
        self.status_bar.setFixedHeight(30)  # Back to reasonable size
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #ecf0f1;
                border-top: 1px solid #bdc3c7;
                font-size: 11px;
                font-weight: normal;
                padding: 2px;
            }
            QStatusBar::item {
                border: none;
                padding: 0px 3px;
            }
        """)

        # User info (will be empty, we'll use left side widgets)
        self.status_bar.showMessage("")

        # Left side widgets (database and license status)
        self.connection_label = QLabel("Database: Connected")
        self.connection_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                font-weight: 600;
                font-size: 11px;
                padding: 3px 8px;
                border: 1px solid #27ae60;
                border-radius: 3px;
                background-color: #d5f4e6;
                margin: 2px;
            }
        """)
        self.status_bar.addWidget(self.connection_label)  # Left side

        # License status label (left side)
        self.license_label = QLabel()
        self.license_label.setStyleSheet("""
            QLabel {
                font-weight: 600;
                font-size: 11px;
                padding: 3px 8px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: #f8f9fa;
                margin: 2px;
            }
        """)
        self.license_label.setToolTip(tr("license.status_tooltip"))
        self.update_license_status()
        self.status_bar.addWidget(self.license_label)  # Left side

        # Center copyright text
        self.copyright_label = QLabel()
        self.copyright_label.setAlignment(Qt.AlignCenter)
        self.copyright_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 10px;
                font-weight: normal;
                padding: 2px 10px;
                margin: 0px;
            }
        """)
        self.update_copyright_text()
        self.status_bar.addPermanentWidget(self.copyright_label)

        # Digital clock and date (right side)
        self.datetime_label = QLabel()
        self.datetime_label.setStyleSheet("""
            QLabel {
                font-weight: 600;
                font-size: 11px;
                color: #2c3e50;
                padding: 3px 10px;
                border: 1px solid #3498db;
                border-radius: 3px;
                background-color: #ecf0f1;
                margin: 2px;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        self.datetime_label.setToolTip(tr("common.current_datetime"))
        self.update_datetime()
        self.status_bar.addPermanentWidget(self.datetime_label)  # Right side

        # Setup timer for updating clock every second
        self.datetime_timer = QTimer()
        self.datetime_timer.timeout.connect(self.update_datetime)
        self.datetime_timer.start(1000)  # Update every 1 second

        # Note: Quick change password button removed - available in user menu only
    
    def setup_license_system(self):
        """Setup license management system"""
        # Connect license manager signals
        license_manager.license_expired.connect(self.handle_license_expired)
        license_manager.trial_warning.connect(self.handle_trial_warning)

        # Check license status on startup
        self.check_license_on_startup()

    def check_license_on_startup(self):
        """Check license status when application starts"""
        status = license_manager.get_license_status()

        if status['status'] == 'expired':
            self.show_activation_dialog(force=True)
        elif status['status'] == 'trial':
            if status['days_remaining'] <= 1:
                self.show_activation_dialog(force=False)

    def show_activation_dialog(self, force=False):
        """Show activation dialog"""
        try:
            from views.activation_dialog import ActivationDialog
            dialog = ActivationDialog(self, force_activation=force)
            dialog.activation_successful.connect(self.update_license_status)

            if force:
                # Force activation - cannot continue without it
                result = dialog.exec_()
                if result != dialog.Accepted:
                    # User cancelled or closed - exit application
                    QMessageBox.critical(self, "تفعيل مطلوب",
                                       "يجب تفعيل التطبيق للمتابعة")
                    QApplication.quit()
            else:
                # Optional activation during trial
                dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في نظام التفعيل: {str(e)}")

    def handle_license_expired(self):
        """Handle license expiration"""
        self.show_activation_dialog(force=True)

    def handle_trial_warning(self, days_remaining):
        """Handle trial period warning"""
        if days_remaining <= 0:
            self.show_activation_dialog(force=True)
        else:
            QMessageBox.warning(self, "تحذير فترة التجربة",
                              f"متبقي {days_remaining} أيام من فترة التجربة.\n"
                              "يرجى تفعيل التطبيق لمواصلة الاستخدام.")

    def show_activation_dialog(self):
        """Show activation dialog manually"""
        try:
            from views.activation_dialog import ActivationDialog
            dialog = ActivationDialog(self, force_activation=False)
            dialog.activation_successful.connect(self.update_license_status)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة التفعيل: {str(e)}")

    def update_license_status(self):
        """Update license status display"""
        if hasattr(self, 'license_label'):
            status = license_manager.get_license_status()

            if status['status'] == 'activated':
                self.license_label.setText(tr("license.activated"))
                self.license_label.setStyleSheet("""
                    QLabel {
                        color: #27ae60;
                        font-weight: 600;
                        font-size: 11px;
                        padding: 3px 8px;
                        border: 1px solid #27ae60;
                        border-radius: 3px;
                        background-color: #d5f4e6;
                        margin: 2px;
                    }
                """)

            elif status['status'] == 'trial':
                days = status['days_remaining']
                trial_text = tr("license.trial_days").format(days=days)
                self.license_label.setText(trial_text)
                self.license_label.setStyleSheet("""
                    QLabel {
                        color: #f39c12;
                        font-weight: 600;
                        font-size: 11px;
                        padding: 3px 8px;
                        border: 1px solid #f39c12;
                        border-radius: 3px;
                        background-color: #fef9e7;
                        margin: 2px;
                    }
                """)

            elif status['status'] == 'expired':
                self.license_label.setText(tr("license.expired"))
                self.license_label.setStyleSheet("""
                    QLabel {
                        color: #e74c3c;
                        font-weight: 600;
                        font-size: 11px;
                        padding: 3px 8px;
                        border: 1px solid #e74c3c;
                        border-radius: 3px;
                        background-color: #fadbd8;
                        margin: 2px;
                    }
                """)

    def update_copyright_text(self):
        """Update copyright text with current language"""
        if hasattr(self, 'copyright_label'):
            copyright_text = tr("common.copyright_text")
            self.copyright_label.setText(copyright_text)

    def update_datetime(self):
        """Update digital clock and date display"""
        if hasattr(self, 'datetime_label'):
            from datetime import datetime
            from utils.translator import translator

            # Get current date and time
            now = datetime.now()

            # Format based on current language
            current_lang = translator.current_language

            if current_lang == 'ar':
                # Arabic format
                date_str = now.strftime("%Y/%m/%d")
                time_str = now.strftime("%H:%M:%S")
                weekday = self.get_arabic_weekday(now.weekday())
                display_text = f"📅 {weekday} {date_str} | 🕒 {time_str}"
            elif current_lang == 'fr':
                # French format
                date_str = now.strftime("%d/%m/%Y")
                time_str = now.strftime("%H:%M:%S")
                weekday = self.get_french_weekday(now.weekday())
                display_text = f"📅 {weekday} {date_str} | 🕒 {time_str}"
            else:
                # English format (default)
                date_str = now.strftime("%m/%d/%Y")
                time_str = now.strftime("%H:%M:%S")
                weekday = self.get_english_weekday(now.weekday())
                display_text = f"📅 {weekday} {date_str} | 🕒 {time_str}"

            self.datetime_label.setText(display_text)

    def get_arabic_weekday(self, weekday):
        """Get Arabic weekday name"""
        weekdays = [
            "الاثنين", "الثلاثاء", "الأربعاء", "الخميس",
            "الجمعة", "السبت", "الأحد"
        ]
        return weekdays[weekday]

    def get_english_weekday(self, weekday):
        """Get English weekday name"""
        weekdays = [
            "Monday", "Tuesday", "Wednesday", "Thursday",
            "Friday", "Saturday", "Sunday"
        ]
        return weekdays[weekday]

    def get_french_weekday(self, weekday):
        """Get French weekday name"""
        weekdays = [
            "Lundi", "Mardi", "Mercredi", "Jeudi",
            "Vendredi", "Samedi", "Dimanche"
        ]
        return weekdays[weekday]

    def setup_connections(self):
        """Setup signal connections"""
        pass
    
    def setup_auto_logout(self):
        """Setup auto-logout timer"""
        timeout = config.get_security_config().get('session_timeout', 3600)
        if timeout > 0 and config.get_security_config().get('auto_logout', True):
            self.auto_logout_timer = QTimer()
            self.auto_logout_timer.timeout.connect(self.handle_auto_logout)
            self.auto_logout_timer.start(timeout * 1000)  # Convert to milliseconds
    
    def start_user_session(self):
        """Start user session"""
        try:
            self.current_session = UserSession.create_session(self.current_user.id)
        except Exception as e:
            print(f"Error starting user session: {e}")
    
    def load_module(self, module_name):
        """Load a specific module"""
        # Update navigation buttons
        for name, button in self.nav_buttons.items():
            button.setChecked(name == module_name)

        # Update window title with current module
        module_titles = {
            "pos": "Point of Sale",
            "products": "Product Management",
            "sales": "Sales Management",
            "customers": "Customer Management",
            "purchases": "Purchase Management",
            "users": "User Management",
            "reports": "Reports & Analytics",
            "settings": "Settings"
        }

        current_title = module_titles.get(module_name, module_name.title())
        self.setWindowTitle(f"{config.get_app_name()} - {current_title} - {self.current_user.full_name}")

        # Load module widget
        self.load_module_widget(module_name)
    
    def load_module_widget(self, module_name):
        """Load module widget into content frame"""
        # Clear existing widgets
        while self.content_frame.count():
            widget = self.content_frame.widget(0)
            self.content_frame.removeWidget(widget)
            widget.deleteLater()

        # Load appropriate widget based on module
        widget = None

        if module_name == "pos":
            from views.pos_widget import POSWidget
            widget = POSWidget(self.current_user)
            widget.sale_completed.connect(self.on_sale_completed)

        elif module_name == "products":
            from views.products_widget import ProductsWidget
            widget = ProductsWidget()

        elif module_name == "customers":
            from views.customers_widget import CustomersWidget
            widget = CustomersWidget()

        elif module_name == "purchases":
            from views.purchases_widget import PurchasesWidget
            widget = PurchasesWidget()

        elif module_name == "sales":
            from views.sales_widget import SalesWidget
            widget = SalesWidget()

        elif module_name == "reports":
            from views.reports_widget import ReportsWidget
            widget = ReportsWidget()

        elif module_name == "performance":
            from views.performance_widget import PerformanceWidget
            widget = PerformanceWidget()

        elif module_name == "users":
            from views.users_widget import UsersWidget
            widget = UsersWidget(self.current_user)

        elif module_name == "settings":
            from views.settings_widget import SettingsWidget
            widget = SettingsWidget()

        else:
            # Create placeholder for other modules
            widget = QWidget()
            layout = QVBoxLayout()

            label = QLabel(f"{module_name.title()} Module")
            label.setAlignment(Qt.AlignCenter)
            label.setFont(QFont("Segoe UI", 24))
            label.setStyleSheet("color: #7f8c8d; margin: 50px;")

            layout.addWidget(label)
            widget.setLayout(layout)

        if widget:
            self.content_frame.addWidget(widget)

    def on_sale_completed(self, sale):
        """Handle sale completion"""
        # Update status bar
        self.status_bar.showMessage(f"Sale completed: {sale.invoice_number}", 3000)
    
    def load_pos_module(self):
        """Load POS module by default"""
        self.load_module("pos")
    
    def update_time(self):
        """Update time display"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def handle_logout(self):
        """Handle user logout"""
        reply = QMessageBox.question(
            self,
            "Logout",
            "Are you sure you want to logout?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.logout()
    
    def handle_auto_logout(self):
        """Handle automatic logout"""
        QMessageBox.information(
            self,
            "Session Expired",
            "Your session has expired. You will be logged out automatically."
        )
        self.logout()
    
    def logout(self):
        """Logout user"""
        try:
            # End user session
            if self.current_session:
                self.current_session.end_session()
            
            # Emit logout signal
            self.user_logged_out.emit()
            
            # Close window
            self.close()
            
        except Exception as e:
            print(f"Error during logout: {e}")
            self.close()
    
    def backup_database(self):
        """Backup database"""
        try:
            # This would implement database backup functionality
            QMessageBox.information(self, "Backup", "Database backup functionality will be implemented.")
        except Exception as e:
            QMessageBox.critical(self, "Backup Error", f"Error creating backup: {str(e)}")
    
    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About OnePos",
            f"""
            <h3>{config.get_app_name()}</h3>
            <p>Version: {config.get_app_version()}</p>
            <p>Professional Point of Sale System</p>
            <p>Built with Python and PyQt5</p>
            """
        )
    
    def center_on_screen(self):
        """Center window on screen"""
        screen = self.screen().availableGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def change_password(self):
        """Show change password dialog"""
        try:
            from views.change_password_dialog import ChangePasswordDialog

            dialog = ChangePasswordDialog(self.current_user, self)
            if dialog.exec_() == dialog.Accepted:
                QMessageBox.information(
                    self,
                    tr("common.success"),
                    tr("password.changed_successfully")
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                tr("common.error"),
                f"Error opening change password dialog: {e}"
            )

    def logout(self):
        """Logout current user"""
        reply = QMessageBox.question(
            self,
            tr("common.confirm"),
            tr("login.confirm_logout"),
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Log logout action
                from models.permissions import AuditLogger
                AuditLogger.log_logout(self.current_user.id)
            except ImportError:
                pass

            # Close main window and restart application
            self.close()

            # Restart application with login
            import sys
            from main import OnePos
            app = OnePos(sys.argv)
            app.run()

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About OnePos",
            "OnePos POS System\nVersion 1.0\n\nA comprehensive point of sale solution."
        )

    def closeEvent(self, event):
        """Handle window close event"""
        # Unregister from translation updates
        unregister_from_translation(self)

        reply = QMessageBox.question(
            self,
            "Exit Application",
            "Are you sure you want to exit?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # End user session
            if self.current_session:
                self.current_session.end_session()

            event.accept()
        else:
            event.ignore()

    def on_language_changed(self, old_language: str, new_language: str):
        """Handle language change"""
        self.retranslate_ui()
        # Update datetime display with new language
        self.update_datetime()
        # Update copyright text with new language
        self.update_copyright_text()

    def retranslate_ui(self):
        """Update UI text when language changes"""
        try:
            # Update navigation buttons
            nav_button_keys = ['pos', 'products', 'sales', 'customers', 'purchases', 'users', 'reports', 'performance', 'settings']
            nav_button_icons = ['🧾', '📦', '💰', '🧍', '📥', '👤', '📊', '⚡', '🛠️']

            if hasattr(self, 'nav_buttons'):
                for i, (key, icon) in enumerate(zip(nav_button_keys, nav_button_icons)):
                    if key in self.nav_buttons:
                        button = self.nav_buttons[key]
                        button.setText(f"{icon} {tr(f'navigation.{key}')}")

            # Update logout button
            logout_buttons = self.findChildren(QPushButton)
            for button in logout_buttons:
                if button.objectName() == "danger_logout":
                    button.setText(f"🚪 {tr('navigation.logout')}")
                    break

            # Update license status with new language
            self.update_license_status()

            # تحديث ترجمات الطابعات إذا كانت مفتوحة
            if hasattr(self, 'content_frame'):
                for i in range(self.content_frame.count()):
                    widget = self.content_frame.widget(i)
                    if hasattr(widget, 'tabs'):  # إعدادات
                        for j in range(widget.tabs.count()):
                            tab_widget = widget.tabs.widget(j)
                            if hasattr(tab_widget, 'retranslate_ui'):
                                tab_widget.retranslate_ui()

        except Exception as e:
            print(f"Error in retranslate_ui: {e}")

    def retranslate_menu_bar(self):
        """Retranslate menu bar"""
        # Clear and recreate menu bar
        self.menuBar().clear()
        self.setup_menu_bar()

    def retranslate_status_bar(self):
        """Retranslate status bar"""
        # Update user info
        user_info = f"{tr('common.user')}: {self.current_user.full_name} ({self.current_user.role})"
        self.status_bar.showMessage(user_info)

        # Update connection label
        self.connection_label.setText(f"{tr('common.database')}: {tr('common.connected')}")

        # Note: Change password button removed from status bar

    def update_children_layout_direction(self, is_rtl: bool):
        """Update layout direction for all child widgets"""
        direction = Qt.RightToLeft if is_rtl else Qt.LeftToRight

        # Update all child widgets recursively
        def update_widget_direction(widget):
            try:
                widget.setLayoutDirection(direction)
                for child in widget.children():
                    if hasattr(child, 'setLayoutDirection'):
                        update_widget_direction(child)
            except:
                pass

        update_widget_direction(self)

    def update_sidebar_styles(self, is_rtl: bool):
        """Update sidebar styles based on language direction"""
        if hasattr(self, 'sidebar_frame'):
            if is_rtl:
                # Arabic: Border on left side
                self.sidebar_frame.setStyleSheet("""
                    QFrame {
                        background-color: #2c3e50;
                        border-left: 1px solid #34495e;
                        border-right: none;
                    }
                """)
            else:
                # English/French: Border on right side
                self.sidebar_frame.setStyleSheet("""
                    QFrame {
                        background-color: #2c3e50;
                        border-right: 1px solid #34495e;
                        border-left: none;
                    }
                """)
