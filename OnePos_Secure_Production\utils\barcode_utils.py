"""
Barcode utilities for OnePos POS System
"""

import os
from io import BytesIO

# Optional imports
try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL not available - image processing disabled")

try:
    import barcode
    from barcode.writer import ImageWriter
    BARCODE_AVAILABLE = True
except ImportError:
    BARCODE_AVAILABLE = False
    print("python-barcode not available - barcode generation disabled")

# Optional imports for camera scanning
try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("OpenCV not available - camera scanning disabled")

try:
    from pyzbar import pyzbar
    PYZBAR_AVAILABLE = True
except (ImportError, OSError, FileNotFoundError) as e:
    PYZBAR_AVAILABLE = False
    print(f"Pyzbar not available - barcode scanning disabled: {e}")


class BarcodeGenerator:
    """Barcode generation utilities"""
    
    @staticmethod
    def generate_barcode(code, barcode_type='code128', save_path=None):
        """Generate barcode image"""
        if not BARCODE_AVAILABLE:
            print("python-barcode not available")
            return None

        try:
            # Create barcode
            barcode_class = barcode.get_barcode_class(barcode_type)
            barcode_instance = barcode_class(code, writer=ImageWriter())

            if save_path:
                # Save to file
                barcode_instance.save(save_path)
                return save_path
            else:
                # Return as PIL Image
                if PIL_AVAILABLE:
                    buffer = BytesIO()
                    barcode_instance.write(buffer)
                    buffer.seek(0)
                    return Image.open(buffer)
                else:
                    print("PIL not available for image processing")
                    return None

        except Exception as e:
            print(f"Error generating barcode: {e}")
            return None
    
    @staticmethod
    def generate_product_label(product_name, barcode_value, price, save_path=None):
        """Generate product label with barcode"""
        if not PIL_AVAILABLE or not BARCODE_AVAILABLE:
            print("PIL or python-barcode not available")
            return None

        try:
            # Create barcode image
            barcode_img = BarcodeGenerator.generate_barcode(barcode_value)
            if not barcode_img:
                return None

            # Create label image
            label_width = 400
            label_height = 200
            label = Image.new('RGB', (label_width, label_height), 'white')
            draw = ImageDraw.Draw(label)
            
            # Try to load font, fallback to default if not available
            try:
                font_large = ImageFont.truetype("arial.ttf", 16)
                font_medium = ImageFont.truetype("arial.ttf", 14)
                font_small = ImageFont.truetype("arial.ttf", 12)
            except:
                font_large = ImageFont.load_default()
                font_medium = ImageFont.load_default()
                font_small = ImageFont.load_default()
            
            # Add product name
            name_text = product_name[:30] + "..." if len(product_name) > 30 else product_name
            draw.text((10, 10), name_text, fill='black', font=font_large)
            
            # Add price
            price_text = f"Price: {price}"
            draw.text((10, 35), price_text, fill='black', font=font_medium)
            
            # Resize and add barcode
            barcode_resized = barcode_img.resize((300, 80))
            label.paste(barcode_resized, (50, 70))
            
            # Add barcode number
            draw.text((50, 155), barcode_value, fill='black', font=font_small)
            
            if save_path:
                label.save(save_path)
                return save_path
            else:
                return label
                
        except Exception as e:
            print(f"Error generating product label: {e}")
            return None


class BarcodeScanner:
    """Barcode scanning utilities"""
    
    def __init__(self, camera_index=0):
        self.camera_index = camera_index
        self.cap = None
        self.is_scanning = False
    
    def start_camera(self):
        """Start camera for barcode scanning"""
        if not CV2_AVAILABLE:
            print("OpenCV not available - camera scanning disabled")
            return False

        try:
            self.cap = cv2.VideoCapture(self.camera_index)
            if not self.cap.isOpened():
                raise Exception("Could not open camera")

            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.is_scanning = True
            return True

        except Exception as e:
            print(f"Error starting camera: {e}")
            return False
    
    def stop_camera(self):
        """Stop camera"""
        self.is_scanning = False
        if self.cap:
            self.cap.release()
            if CV2_AVAILABLE:
                cv2.destroyAllWindows()
    
    def scan_frame(self):
        """Scan single frame for barcodes"""
        if not CV2_AVAILABLE or not PYZBAR_AVAILABLE:
            return None, None

        if not self.cap or not self.is_scanning:
            return None, None

        try:
            ret, frame = self.cap.read()
            if not ret:
                return None, None

            # Decode barcodes
            barcodes = pyzbar.decode(frame)

            # Draw rectangles around detected barcodes
            for barcode_obj in barcodes:
                # Extract barcode data
                barcode_data = barcode_obj.data.decode('utf-8')
                barcode_type = barcode_obj.type

                # Draw rectangle around barcode
                (x, y, w, h) = barcode_obj.rect
                cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)

                # Add text
                text = f"{barcode_type}: {barcode_data}"
                cv2.putText(frame, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                return frame, barcode_data

            return frame, None

        except Exception as e:
            print(f"Error scanning frame: {e}")
            return None, None
    
    def scan_image(self, image_path):
        """Scan barcode from image file"""
        if not CV2_AVAILABLE or not PYZBAR_AVAILABLE:
            print("OpenCV or Pyzbar not available - image scanning disabled")
            return None

        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                return None

            # Decode barcodes
            barcodes = pyzbar.decode(image)

            if barcodes:
                # Return first barcode found
                return barcodes[0].data.decode('utf-8')

            return None

        except Exception as e:
            print(f"Error scanning image: {e}")
            return None
    
    @staticmethod
    def validate_barcode(barcode_value):
        """Validate barcode format"""
        if not barcode_value:
            return False
        
        # Remove any whitespace
        barcode_value = barcode_value.strip()
        
        # Check if it's numeric and has valid length
        if barcode_value.isdigit():
            length = len(barcode_value)
            # Common barcode lengths: 8 (EAN-8), 12 (UPC-A), 13 (EAN-13)
            return length in [8, 12, 13] or (6 <= length <= 18)
        
        # For non-numeric barcodes (Code 128, Code 39, etc.)
        return 3 <= len(barcode_value) <= 50
    
    @staticmethod
    def format_barcode(barcode_value):
        """Format barcode value"""
        if not barcode_value:
            return ""
        
        # Remove whitespace and convert to uppercase
        return barcode_value.strip().upper()


class QRCodeGenerator:
    """QR Code generation utilities"""
    
    @staticmethod
    def generate_qr_code(data, save_path=None, size=10, border=4):
        """Generate QR code"""
        try:
            import qrcode

            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=size,
                border=border,
            )
            qr.add_data(data)
            qr.make(fit=True)

            img = qr.make_image(fill_color="black", back_color="white")

            if save_path:
                img.save(save_path)
                return save_path
            else:
                return img

        except ImportError:
            print("qrcode library not available")
            return None
        except Exception as e:
            print(f"Error generating QR code: {e}")
            return None
    
    @staticmethod
    def generate_product_qr(product_id, product_name, price):
        """Generate QR code for product"""
        data = {
            "type": "product",
            "id": product_id,
            "name": product_name,
            "price": price
        }
        
        import json
        qr_data = json.dumps(data)
        return QRCodeGenerator.generate_qr_code(qr_data)


# Utility functions
def ensure_barcode_directory():
    """Ensure barcode directory exists"""
    barcode_dir = "assets/barcodes"
    os.makedirs(barcode_dir, exist_ok=True)
    return barcode_dir


def get_barcode_path(barcode_value, extension=".png"):
    """Get file path for barcode image"""
    barcode_dir = ensure_barcode_directory()
    filename = f"{barcode_value}{extension}"
    return os.path.join(barcode_dir, filename)


def get_label_path(product_id, extension=".png"):
    """Get file path for product label"""
    barcode_dir = ensure_barcode_directory()
    filename = f"label_{product_id}{extension}"
    return os.path.join(barcode_dir, filename)
