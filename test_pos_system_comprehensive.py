#!/usr/bin/env python3
"""
اختبار شامل لنظام نقطة البيع (POS System)
Comprehensive POS System Test
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QTableWidgetItem
from PyQt5.QtCore import QTimer
from views.pos_widget import POSWidget
from models.user import User
from models.product import Product
from models.customer import Customer
from models.sale import Sale
import time

def test_pos_widget_creation():
    """اختبار إنشاء واجهة نقطة البيع"""
    print("🏪 اختبار إنشاء واجهة نقطة البيع")
    print("=" * 40)
    
    try:
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # الحصول على مستخدم للاختبار
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        # إنشاء واجهة نقطة البيع
        pos_widget = POSWidget(admin_user)
        
        print("✅ تم إنشاء واجهة نقطة البيع بنجاح")
        
        # فحص العناصر الأساسية
        components_to_check = [
            ('barcode_input', 'حقل البحث/الباركود', 'QLineEdit'),
            ('search_button', 'زر البحث', 'QPushButton'),
            ('scan_button', 'زر المسح', 'QPushButton'),
            ('quantity_spin', 'حقل الكمية', 'QSpinBox'),
            ('add_to_cart_button', 'زر إضافة للسلة', 'QPushButton'),
            ('cart_table', 'جدول السلة', 'QTableWidget'),
            ('clear_cart_button', 'زر مسح السلة', 'QPushButton'),
            ('hold_order_button', 'زر تعليق الطلب', 'QPushButton'),
            ('customer_combo', 'قائمة العملاء', 'QComboBox'),
            ('subtotal_label', 'تسمية المجموع الفرعي', 'QLabel'),
            ('tax_label', 'تسمية الضريبة', 'QLabel'),
            ('total_label', 'تسمية المجموع الكلي', 'QLabel'),
            ('discount_spin', 'حقل الخصم', 'QDoubleSpinBox'),
            ('payment_method_combo', 'قائمة طرق الدفع', 'QComboBox'),
            ('amount_paid_spin', 'حقل المبلغ المدفوع', 'QDoubleSpinBox'),
            ('change_label', 'تسمية الباقي', 'QLabel'),
            ('complete_sale_button', 'زر إتمام البيع', 'QPushButton'),
            ('print_receipt_button', 'زر طباعة الإيصال', 'QPushButton'),
            ('print_invoice_button', 'زر طباعة الفاتورة', 'QPushButton')
        ]
        
        all_components_found = True
        
        for attr_name, description, expected_type in components_to_check:
            if hasattr(pos_widget, attr_name):
                component = getattr(pos_widget, attr_name)
                actual_type = type(component).__name__
                
                if expected_type in actual_type:
                    print(f"   ✅ {description}: موجود ({actual_type})")
                else:
                    print(f"   ⚠️ {description}: نوع غير متوقع - متوقع: {expected_type}, الحالي: {actual_type}")
            else:
                print(f"   ❌ {description}: غير موجود")
                all_components_found = False
        
        # فحص الخصائص الأساسية
        print(f"\n📋 الخصائص الأساسية:")
        print(f"   - المستخدم الحالي: {pos_widget.current_user.username}")
        print(f"   - عناصر السلة: {len(pos_widget.cart_items)}")
        print(f"   - معدل الضريبة: {pos_widget.tax_rate}%")
        print(f"   - العميل الحالي: {pos_widget.current_customer.name if pos_widget.current_customer else 'غير محدد'}")
        
        # فحص جدول السلة
        cart_table = pos_widget.cart_table
        print(f"\n🛒 جدول السلة:")
        print(f"   - عدد الأعمدة: {cart_table.columnCount()}")
        print(f"   - عدد الصفوف: {cart_table.rowCount()}")
        
        expected_headers = ["Product", "Quantity", "Price", "Discount", "Total", ""]
        actual_headers = []
        for i in range(cart_table.columnCount()):
            header_item = cart_table.horizontalHeaderItem(i)
            actual_headers.append(header_item.text() if header_item else "")
        
        print(f"   - رؤوس الأعمدة: {actual_headers}")
        
        return all_components_found
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء واجهة نقطة البيع: {e}")
        return False

def test_product_search():
    """اختبار البحث عن المنتجات"""
    print("\n🔍 اختبار البحث عن المنتجات")
    print("=" * 35)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        pos_widget = POSWidget(admin_user)
        
        # إنشاء منتج للاختبار
        test_products = Product.get_all()
        if not test_products:
            print("⚠️ لا توجد منتجات في قاعدة البيانات للاختبار")
            return True  # لا نعتبر هذا فشل
        
        test_product = test_products[0]
        print(f"📦 منتج الاختبار: {test_product.name}")
        
        # اختبار البحث بالاسم
        pos_widget.barcode_input.setText(test_product.name)
        pos_widget.search_product()
        
        if len(pos_widget.cart_items) > 0:
            print("✅ البحث بالاسم يعمل - تم إضافة المنتج للسلة")
        else:
            print("❌ البحث بالاسم لا يعمل")
            return False
        
        # مسح السلة للاختبار التالي
        pos_widget.cart_items.clear()
        
        # اختبار البحث بالباركود (إذا كان متوفر)
        if hasattr(test_product, 'barcode') and test_product.barcode:
            pos_widget.barcode_input.setText(test_product.barcode)
            pos_widget.search_product()
            
            if len(pos_widget.cart_items) > 0:
                print("✅ البحث بالباركود يعمل")
            else:
                print("⚠️ البحث بالباركود لا يعمل أو الباركود غير صحيح")
        else:
            print("⚠️ المنتج لا يحتوي على باركود للاختبار")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البحث: {e}")
        return False

def test_cart_operations():
    """اختبار عمليات السلة"""
    print("\n🛒 اختبار عمليات السلة")
    print("=" * 25)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        pos_widget = POSWidget(admin_user)
        
        # الحصول على منتجات للاختبار
        products = Product.get_all()
        if len(products) < 2:
            print("⚠️ نحتاج منتجين على الأقل للاختبار")
            return True
        
        # إضافة منتجات للسلة
        print("📦 إضافة منتجات للسلة...")
        
        for i, product in enumerate(products[:2]):
            pos_widget.quantity_spin.setValue(i + 1)  # كميات مختلفة
            pos_widget.add_product_to_cart(product)
            print(f"   ✅ تم إضافة {product.name} (الكمية: {i + 1})")
        
        print(f"🛒 عدد عناصر السلة: {len(pos_widget.cart_items)}")
        
        # فحص عرض السلة
        pos_widget.update_cart_display()
        cart_rows = pos_widget.cart_table.rowCount()
        
        if cart_rows == len(pos_widget.cart_items):
            print("✅ عرض السلة يعمل بشكل صحيح")
        else:
            print(f"❌ مشكلة في عرض السلة - متوقع: {len(pos_widget.cart_items)}, الحالي: {cart_rows}")
            return False
        
        # اختبار حساب المجاميع
        pos_widget.update_totals()
        
        subtotal = pos_widget.subtotal_label.text()
        tax = pos_widget.tax_label.text()
        total = pos_widget.total_label.text()
        
        print(f"💰 المجاميع:")
        print(f"   - المجموع الفرعي: {subtotal}")
        print(f"   - الضريبة: {tax}")
        print(f"   - المجموع الكلي: {total}")
        
        # اختبار حذف عنصر
        if len(pos_widget.cart_items) > 0:
            initial_count = len(pos_widget.cart_items)
            pos_widget.remove_cart_item(0)
            
            if len(pos_widget.cart_items) == initial_count - 1:
                print("✅ حذف عنصر من السلة يعمل")
            else:
                print("❌ مشكلة في حذف عنصر من السلة")
                return False
        
        # اختبار مسح السلة
        pos_widget.cart_items.clear()
        pos_widget.update_cart_display()
        pos_widget.update_totals()
        
        if len(pos_widget.cart_items) == 0 and pos_widget.cart_table.rowCount() == 0:
            print("✅ مسح السلة يعمل")
        else:
            print("❌ مشكلة في مسح السلة")
            return False
        
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات السلة: {e}")
        return False

def test_payment_calculations():
    """اختبار حسابات الدفع"""
    print("\n💳 اختبار حسابات الدفع")
    print("=" * 25)

    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        admin_user = User.authenticate('admin', 'admin123')
        pos_widget = POSWidget(admin_user)

        # إضافة منتج للاختبار
        products = Product.get_all()
        if not products:
            print("⚠️ لا توجد منتجات للاختبار")
            return True

        # إضافة منتج بسعر معروف
        test_product = products[0]
        pos_widget.add_product_to_cart(test_product)
        pos_widget.update_totals()

        # اختبار حساب الخصم
        print("🏷️ اختبار الخصم...")

        # خصم ثابت
        pos_widget.discount_spin.setValue(10.0)
        pos_widget.discount_percent_check.setChecked(False)
        pos_widget.update_totals()
        print("   ✅ خصم ثابت (10.00)")

        # خصم نسبي
        pos_widget.discount_spin.setValue(10.0)
        pos_widget.discount_percent_check.setChecked(True)
        pos_widget.update_totals()
        print("   ✅ خصم نسبي (10%)")

        # اختبار حساب الباقي
        print("💰 اختبار حساب الباقي...")

        total = float(pos_widget.total_label.text())

        # دفع مبلغ أكبر
        pos_widget.amount_paid_spin.setValue(total + 20)
        pos_widget.calculate_change()
        change = float(pos_widget.change_label.text())

        if change == 20.0:
            print("   ✅ حساب الباقي صحيح")
        else:
            print(f"   ⚠️ حساب الباقي غير دقيق - متوقع: 20.00, الحالي: {change}")

        # دفع مبلغ أقل
        pos_widget.amount_paid_spin.setValue(total - 10)
        pos_widget.calculate_change()
        change = float(pos_widget.change_label.text())

        if change == -10.0:
            print("   ✅ حساب النقص صحيح")
        else:
            print(f"   ⚠️ حساب النقص غير دقيق - متوقع: -10.00, الحالي: {change}")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار حسابات الدفع: {e}")
        return False

def test_customer_selection():
    """اختبار اختيار العملاء"""
    print("\n👤 اختبار اختيار العملاء")
    print("=" * 25)

    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        admin_user = User.authenticate('admin', 'admin123')
        pos_widget = POSWidget(admin_user)

        # فحص تحميل العملاء
        pos_widget.load_customers()
        customer_count = pos_widget.customer_combo.count()

        print(f"👥 عدد العملاء المحملين: {customer_count}")

        if customer_count > 0:
            print("✅ تم تحميل العملاء بنجاح")

            # اختبار اختيار عميل
            pos_widget.customer_combo.setCurrentIndex(0)
            pos_widget.select_customer()

            if pos_widget.current_customer:
                print(f"✅ تم اختيار العميل: {pos_widget.current_customer.name}")
            else:
                print("❌ فشل في اختيار العميل")
                return False
        else:
            print("⚠️ لا توجد عملاء في قاعدة البيانات")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار اختيار العملاء: {e}")
        return False

def test_keyboard_shortcuts():
    """اختبار اختصارات لوحة المفاتيح"""
    print("\n⌨️ اختبار اختصارات لوحة المفاتيح")
    print("=" * 35)

    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        admin_user = User.authenticate('admin', 'admin123')
        pos_widget = POSWidget(admin_user)

        # فحص وجود الاختصارات
        shortcuts_found = 0

        # البحث عن الاختصارات في الكائن
        for child in pos_widget.findChildren(object):
            if hasattr(child, 'key') and hasattr(child, 'activated'):
                shortcuts_found += 1

        print(f"🔑 عدد الاختصارات الموجودة: {shortcuts_found}")

        if shortcuts_found >= 4:  # F1, F2, F3, F4
            print("✅ اختصارات لوحة المفاتيح موجودة")
        else:
            print("⚠️ بعض اختصارات لوحة المفاتيح قد تكون مفقودة")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار اختصارات لوحة المفاتيح: {e}")
        return False

def run_comprehensive_pos_test():
    """تشغيل جميع اختبارات نظام نقطة البيع"""
    print("🧾 بدء الاختبار الشامل لنظام نقطة البيع")
    print("=" * 50)

    test_results = []

    # الاختبارات
    test_results.append(("إنشاء واجهة نقطة البيع", test_pos_widget_creation()))
    test_results.append(("البحث عن المنتجات", test_product_search()))
    test_results.append(("عمليات السلة", test_cart_operations()))
    test_results.append(("حسابات الدفع", test_payment_calculations()))
    test_results.append(("اختيار العملاء", test_customer_selection()))
    test_results.append(("اختصارات لوحة المفاتيح", test_keyboard_shortcuts()))

    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 نتائج اختبار نظام نقطة البيع")
    print("=" * 50)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1

    print("=" * 50)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 جميع اختبارات نظام نقطة البيع نجحت!")
        print("✨ النظام جاهز للاستخدام")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")

    return passed == total

if __name__ == "__main__":
    run_comprehensive_pos_test()
