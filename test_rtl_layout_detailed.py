"""
Detailed Re-testing for OnePos RTL Layout System
More comprehensive tests with real user interaction and edge cases
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QHBoxLayout, Q<PERSON>rame
from PyQt5.QtCore import Qt
from utils.translator import change_language, get_current_language
from views.main_window import MainWindow
from views.settings_widget import SettingsWidget, GeneralSettingsTab
from models.user import User
import time

def test_real_user_language_switching():
    """Test language switching through real user interaction"""
    print("🌐 Testing Real User Language Switching with Layout...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        print(f"✅ Main window created and shown")
        
        # Navigate to settings
        if 'settings' in main_window.nav_buttons:
            settings_button = main_window.nav_buttons['settings']
            settings_button.click()
            app.processEvents()
            print("✅ Navigated to settings")
            
            # Get settings widget
            current_widget = main_window.stacked_widget.currentWidget()
            if isinstance(current_widget, SettingsWidget):
                settings_widget = current_widget
                
                # Find general settings tab
                general_tab = None
                for i in range(settings_widget.tabs.count()):
                    widget = settings_widget.tabs.widget(i)
                    if isinstance(widget, GeneralSettingsTab):
                        general_tab = widget
                        settings_widget.tabs.setCurrentIndex(i)
                        app.processEvents()
                        break
                
                if general_tab:
                    print("✅ Found general settings tab")
                    
                    # Test language switching and layout changes
                    language_combo = general_tab.language_combo
                    
                    # Test each language
                    for i in range(language_combo.count()):
                        lang_code = language_combo.itemData(i)
                        lang_name = language_combo.itemText(i)
                        
                        if lang_code:
                            print(f"\n   Testing {lang_name} ({lang_code})...")
                            
                            # Change language through UI
                            language_combo.setCurrentIndex(i)
                            general_tab.on_language_changed()
                            app.processEvents()
                            time.sleep(0.2)  # Give time for layout changes
                            
                            # Verify language changed
                            current_lang = get_current_language()
                            if current_lang == lang_code:
                                print(f"     ✅ Language changed to {lang_code}")
                                
                                # Check layout arrangement
                                main_layout = main_window.main_layout
                                if main_layout.count() >= 2:
                                    first_widget = main_layout.itemAt(0).widget()
                                    second_widget = main_layout.itemAt(1).widget()
                                    
                                    sidebar_frame = main_window.sidebar_frame
                                    content_frame = main_window.content_frame
                                    
                                    if lang_code == 'ar':
                                        # Arabic: Content left, Sidebar right
                                        if first_widget == content_frame and second_widget == sidebar_frame:
                                            print(f"     ✅ Arabic layout: Content left, Sidebar right")
                                        else:
                                            print(f"     ❌ Arabic layout incorrect")
                                            return False
                                    else:
                                        # Other languages: Sidebar left, Content right
                                        if first_widget == sidebar_frame and second_widget == content_frame:
                                            print(f"     ✅ {lang_name} layout: Sidebar left, Content right")
                                        else:
                                            print(f"     ❌ {lang_name} layout incorrect")
                                            return False
                                    
                                    # Check layout direction
                                    layout_direction = main_window.layoutDirection()
                                    expected_direction = Qt.RightToLeft if lang_code == 'ar' else Qt.LeftToRight
                                    
                                    if layout_direction == expected_direction:
                                        print(f"     ✅ Layout direction: {layout_direction}")
                                    else:
                                        print(f"     ❌ Layout direction incorrect: expected {expected_direction}, got {layout_direction}")
                                        return False
                                    
                                    # Check sidebar styles
                                    sidebar_style = sidebar_frame.styleSheet()
                                    if lang_code == 'ar':
                                        if 'border-left' in sidebar_style:
                                            print(f"     ✅ Arabic sidebar: Border on left")
                                        else:
                                            print(f"     ⚠️ Arabic sidebar style may be incorrect")
                                    else:
                                        if 'border-right' in sidebar_style:
                                            print(f"     ✅ {lang_name} sidebar: Border on right")
                                        else:
                                            print(f"     ⚠️ {lang_name} sidebar style may be incorrect")
                                    
                                    # Test navigation button texts
                                    nav_buttons = main_window.nav_buttons
                                    sample_buttons = ['pos', 'products', 'customers']
                                    
                                    for btn_name in sample_buttons:
                                        if btn_name in nav_buttons:
                                            btn_text = nav_buttons[btn_name].text()
                                            print(f"     - {btn_name}: '{btn_text}'")
                                
                                else:
                                    print(f"     ❌ Layout doesn't have enough components")
                                    return False
                            else:
                                print(f"     ❌ Failed to change language to {lang_code}")
                                return False
                else:
                    print("❌ General settings tab not found")
                    return False
            else:
                print("❌ Settings widget not found")
                return False
        else:
            print("❌ Settings button not found")
            return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Real user language switching test failed: {e}")
        return False

def test_layout_visual_consistency():
    """Test visual consistency of layout across different states"""
    print("\n🎨 Testing Layout Visual Consistency...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Test different window states with different languages
        languages = [('ar', 'Arabic'), ('en', 'English'), ('fr', 'French')]
        
        for lang_code, lang_name in languages:
            print(f"   Testing {lang_name} layout consistency...")
            
            # Change language
            change_language(lang_code)
            app.processEvents()
            time.sleep(0.1)
            
            # Test different window sizes
            window_sizes = [
                (800, 600, "Small"),
                (1200, 800, "Medium"),
                (1600, 1000, "Large")
            ]
            
            for width, height, size_name in window_sizes:
                main_window.resize(width, height)
                app.processEvents()
                time.sleep(0.1)
                
                # Check if layout is maintained
                main_layout = main_window.main_layout
                if main_layout.count() >= 2:
                    first_widget = main_layout.itemAt(0).widget()
                    sidebar_frame = main_window.sidebar_frame
                    content_frame = main_window.content_frame
                    
                    if lang_code == 'ar':
                        expected_first = content_frame
                    else:
                        expected_first = sidebar_frame
                    
                    if first_widget == expected_first:
                        print(f"     ✅ {size_name} ({width}x{height}): Layout maintained")
                    else:
                        print(f"     ❌ {size_name} ({width}x{height}): Layout broken")
                        return False
            
            # Test minimizing and restoring
            main_window.showMinimized()
            app.processEvents()
            time.sleep(0.1)
            
            main_window.showNormal()
            app.processEvents()
            time.sleep(0.1)
            
            # Check layout after restore
            main_layout = main_window.main_layout
            if main_layout.count() >= 2:
                first_widget = main_layout.itemAt(0).widget()
                sidebar_frame = main_window.sidebar_frame
                content_frame = main_window.content_frame
                
                if lang_code == 'ar':
                    expected_first = content_frame
                else:
                    expected_first = sidebar_frame
                
                if first_widget == expected_first:
                    print(f"     ✅ After minimize/restore: Layout maintained")
                else:
                    print(f"     ❌ After minimize/restore: Layout broken")
                    return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Layout visual consistency test failed: {e}")
        return False

def test_layout_edge_cases():
    """Test layout behavior in edge cases"""
    print("\n🔍 Testing Layout Edge Cases...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Test 1: Multiple rapid language changes
        print("   Testing multiple rapid language changes...")
        
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Rapid changes
        languages = ['ar', 'en', 'fr', 'ar', 'en', 'fr', 'ar']
        
        for i, lang in enumerate(languages):
            change_language(lang)
            app.processEvents()
            
            # Check layout immediately
            main_layout = main_window.main_layout
            if main_layout.count() >= 2:
                first_widget = main_layout.itemAt(0).widget()
                sidebar_frame = main_window.sidebar_frame
                content_frame = main_window.content_frame
                
                if lang == 'ar':
                    expected_first = content_frame
                else:
                    expected_first = sidebar_frame
                
                if first_widget == expected_first:
                    print(f"     ✅ Rapid change {i+1} ({lang}): Layout correct")
                else:
                    print(f"     ❌ Rapid change {i+1} ({lang}): Layout incorrect")
                    return False
        
        main_window.close()
        
        # Test 2: Language change during module navigation
        print("   Testing language change during module navigation...")
        
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Start with English
        change_language('en')
        app.processEvents()
        
        # Navigate to different modules while changing language
        modules = ['pos', 'products', 'customers']
        languages = ['ar', 'fr', 'en']
        
        for i, (module, lang) in enumerate(zip(modules, languages)):
            if module in main_window.nav_buttons:
                # Navigate to module
                main_window.nav_buttons[module].click()
                app.processEvents()
                
                # Change language
                change_language(lang)
                app.processEvents()
                time.sleep(0.1)
                
                # Check layout
                main_layout = main_window.main_layout
                if main_layout.count() >= 2:
                    first_widget = main_layout.itemAt(0).widget()
                    sidebar_frame = main_window.sidebar_frame
                    content_frame = main_window.content_frame
                    
                    if lang == 'ar':
                        expected_first = content_frame
                    else:
                        expected_first = sidebar_frame
                    
                    if first_widget == expected_first:
                        print(f"     ✅ Module {module} + {lang}: Layout correct")
                    else:
                        print(f"     ❌ Module {module} + {lang}: Layout incorrect")
                        return False
        
        main_window.close()
        
        # Test 3: Layout with very narrow window
        print("   Testing layout with very narrow window...")
        
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Make window very narrow
        main_window.resize(400, 600)
        app.processEvents()
        
        # Test both languages
        for lang in ['ar', 'en']:
            change_language(lang)
            app.processEvents()
            time.sleep(0.1)
            
            # Check if layout still works
            main_layout = main_window.main_layout
            if main_layout.count() >= 2:
                first_widget = main_layout.itemAt(0).widget()
                sidebar_frame = main_window.sidebar_frame
                content_frame = main_window.content_frame
                
                if lang == 'ar':
                    expected_first = content_frame
                else:
                    expected_first = sidebar_frame
                
                if first_widget == expected_first:
                    print(f"     ✅ Narrow window + {lang}: Layout correct")
                else:
                    print(f"     ❌ Narrow window + {lang}: Layout incorrect")
                    return False
        
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Layout edge cases test failed: {e}")
        return False

def test_layout_memory_management():
    """Test layout memory management"""
    print("\n🧠 Testing Layout Memory Management...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create and destroy multiple windows with different languages
        for iteration in range(5):
            print(f"   Iteration {iteration + 1}/5...")
            
            # Create test user
            test_user = User()
            test_user.id = 1
            test_user.username = "test"
            test_user.full_name = "Test User"
            test_user.role = "admin"
            
            # Create main window
            main_window = MainWindow(test_user)
            main_window.show()
            app.processEvents()
            
            # Test different languages
            for lang in ['ar', 'en', 'fr']:
                change_language(lang)
                app.processEvents()
                
                # Check layout
                main_layout = main_window.main_layout
                if main_layout.count() >= 2:
                    first_widget = main_layout.itemAt(0).widget()
                    sidebar_frame = main_window.sidebar_frame
                    content_frame = main_window.content_frame
                    
                    if lang == 'ar':
                        expected_first = content_frame
                    else:
                        expected_first = sidebar_frame
                    
                    if first_widget != expected_first:
                        print(f"     ❌ Iteration {iteration + 1}, {lang}: Layout incorrect")
                        return False
            
            # Close window
            main_window.close()
            app.processEvents()
            
            # Force garbage collection
            import gc
            gc.collect()
        
        print("   ✅ All iterations completed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Layout memory management test failed: {e}")
        return False

def test_layout_component_integrity():
    """Test layout component integrity"""
    print("\n🔧 Testing Layout Component Integrity...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Test component references
        print("   Testing component references...")
        
        # Check if all required components exist
        required_components = [
            ('main_layout', 'Main layout'),
            ('sidebar_frame', 'Sidebar frame'),
            ('content_frame', 'Content frame'),
            ('stacked_widget', 'Stacked widget'),
            ('nav_buttons', 'Navigation buttons')
        ]
        
        for attr_name, description in required_components:
            if hasattr(main_window, attr_name):
                component = getattr(main_window, attr_name)
                if component is not None:
                    print(f"     ✅ {description}: Available")
                else:
                    print(f"     ❌ {description}: None")
                    return False
            else:
                print(f"     ❌ {description}: Missing attribute")
                return False
        
        # Test component types
        print("   Testing component types...")
        
        if isinstance(main_window.main_layout, QHBoxLayout):
            print("     ✅ Main layout: QHBoxLayout")
        else:
            print(f"     ❌ Main layout: Wrong type {type(main_window.main_layout)}")
            return False
        
        if isinstance(main_window.sidebar_frame, QFrame):
            print("     ✅ Sidebar frame: QFrame")
        else:
            print(f"     ❌ Sidebar frame: Wrong type {type(main_window.sidebar_frame)}")
            return False
        
        if isinstance(main_window.content_frame, QFrame):
            print("     ✅ Content frame: QFrame")
        else:
            print(f"     ❌ Content frame: Wrong type {type(main_window.content_frame)}")
            return False
        
        # Test layout count
        layout_count = main_window.main_layout.count()
        if layout_count == 2:
            print(f"     ✅ Layout count: {layout_count}")
        else:
            print(f"     ❌ Layout count: Expected 2, got {layout_count}")
            return False
        
        # Test component arrangement for different languages
        print("   Testing component arrangement...")
        
        for lang in ['ar', 'en']:
            change_language(lang)
            app.processEvents()
            time.sleep(0.1)
            
            # Get widgets in layout
            widgets_in_layout = []
            for i in range(main_window.main_layout.count()):
                widget = main_window.main_layout.itemAt(i).widget()
                if widget == main_window.sidebar_frame:
                    widgets_in_layout.append('sidebar')
                elif widget == main_window.content_frame:
                    widgets_in_layout.append('content')
                else:
                    widgets_in_layout.append('unknown')
            
            if lang == 'ar':
                expected_order = ['content', 'sidebar']
            else:
                expected_order = ['sidebar', 'content']
            
            if widgets_in_layout == expected_order:
                print(f"     ✅ {lang}: Component order correct {widgets_in_layout}")
            else:
                print(f"     ❌ {lang}: Component order incorrect. Expected {expected_order}, got {widgets_in_layout}")
                return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Layout component integrity test failed: {e}")
        return False

def run_detailed_rtl_layout_test():
    """Run all detailed RTL layout tests"""
    print("🔄 STARTING DETAILED RTL LAYOUT SYSTEM TEST")
    print("=" * 70)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # Test 1: Real User Language Switching
    test_results.append(("Real User Language Switching", test_real_user_language_switching()))
    
    # Test 2: Layout Visual Consistency
    test_results.append(("Layout Visual Consistency", test_layout_visual_consistency()))
    
    # Test 3: Layout Edge Cases
    test_results.append(("Layout Edge Cases", test_layout_edge_cases()))
    
    # Test 4: Layout Memory Management
    test_results.append(("Layout Memory Management", test_layout_memory_management()))
    
    # Test 5: Layout Component Integrity
    test_results.append(("Layout Component Integrity", test_layout_component_integrity()))
    
    # Print results summary
    print("\n" + "=" * 70)
    print("🏁 DETAILED RTL LAYOUT TEST RESULTS")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("=" * 70)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL DETAILED RTL LAYOUT TESTS PASSED! System is robust and reliable!")
    else:
        print("⚠️  Some detailed tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_detailed_rtl_layout_test()
