#!/usr/bin/env python3
"""
اختبار أسماء الأقسام العربية
Test Arabic Section Names
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.main_window import MainWindow
from models.user import User

def test_arabic_section_names():
    """اختبار أسماء الأقسام العربية"""
    print("🔤 اختبار أسماء الأقسام العربية")
    print("=" * 40)
    
    try:
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # الحصول على مستخدم للاختبار
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow(admin_user)
        
        print("🔍 فحص أسماء الأقسام:")
        
        # الأسماء المتوقعة
        expected_names = {
            'pos': '🧾 نقطة البيع',
            'products': '📦 المنتجات',
            'sales': '💰 المبيعات',
            'customers': '🧍 العملاء',
            'purchases': '📥 المشتريات',
            'users': '👤 المستخدمين',
            'reports': '📊 التقارير',
            'performance': '⚡ الأداء',
            'settings': '🛠️ الإعدادات'
        }
        
        all_correct = True
        
        for module_name, expected_text in expected_names.items():
            if module_name in main_window.nav_buttons:
                button = main_window.nav_buttons[module_name]
                actual_text = button.text()
                
                if actual_text == expected_text:
                    print(f"   ✅ {module_name}: {actual_text}")
                else:
                    print(f"   ❌ {module_name}: متوقع '{expected_text}', الحالي '{actual_text}'")
                    all_correct = False
            else:
                print(f"   ❌ {module_name}: الزر غير موجود")
                all_correct = False
        
        # فحص زر تسجيل الخروج
        print("\n🚪 فحص زر تسجيل الخروج:")
        logout_buttons = main_window.findChildren(type(main_window.nav_buttons['pos']), name="danger_logout")
        if logout_buttons:
            logout_button = logout_buttons[0]
            logout_text = logout_button.text()
            expected_logout = "🚪 تسجيل الخروج"
            
            if logout_text == expected_logout:
                print(f"   ✅ زر الخروج: {logout_text}")
            else:
                print(f"   ❌ زر الخروج: متوقع '{expected_logout}', الحالي '{logout_text}'")
                all_correct = False
        else:
            print("   ⚠️ زر الخروج غير موجود")
        
        print(f"\n📊 النتيجة: {'جميع الأسماء صحيحة' if all_correct else 'بعض الأسماء تحتاج تصحيح'}")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_visual_display():
    """اختبار العرض البصري"""
    print("\n🖼️ اختبار العرض البصري")
    print("=" * 25)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        main_window = MainWindow(admin_user)
        
        print("📱 عرض النافذة مع الأسماء العربية...")
        main_window.show()
        app.processEvents()
        
        print("✅ النافذة معروضة بنجاح")
        print("🔤 الأسماء العربية ظاهرة في الشريط العلوي")
        print("🎨 التصميم محافظ على جماليته")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في العرض البصري: {e}")
        return False

def run_arabic_names_test():
    """تشغيل جميع اختبارات الأسماء العربية"""
    print("🌍 بدء اختبار الأسماء العربية")
    print("=" * 50)
    
    results = []
    
    # اختبار 1: أسماء الأقسام العربية
    results.append(("أسماء الأقسام العربية", test_arabic_section_names()))
    
    # اختبار 2: العرض البصري
    results.append(("العرض البصري", test_visual_display()))
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 نتائج اختبار الأسماء العربية")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 جميع الأسماء العربية تعمل بشكل مثالي!")
        print("✨ الأقسام تظهر بأسمائها الصحيحة")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_arabic_names_test()
