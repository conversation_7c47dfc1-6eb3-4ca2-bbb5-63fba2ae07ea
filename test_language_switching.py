"""
Test Language Switching System for OnePos POS System
Tests the instant language switching functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from utils.translator import translator, tr, change_language, get_current_language
from utils.config_manager import config
from views.main_window import MainWindow
from views.settings_widget import GeneralSettingsTab
from models.user import User
import time

def test_translator_basic_functionality():
    """Test basic translator functionality"""
    print("🌐 Testing Basic Translator Functionality...")
    
    try:
        # Test getting available languages
        languages = translator.get_available_languages()
        print(f"✅ Available languages: {list(languages.keys())}")
        
        # Test current language
        current_lang = get_current_language()
        print(f"✅ Current language: {current_lang}")
        
        # Test basic translation
        test_key = "common.ok"
        translation = tr(test_key)
        print(f"✅ Translation of '{test_key}': '{translation}'")
        
        # Test translation with missing key
        missing_key = "nonexistent.key"
        missing_translation = tr(missing_key)
        print(f"✅ Missing key '{missing_key}': '{missing_translation}'")
        
        # Test RTL detection
        is_rtl = translator.is_rtl()
        print(f"✅ Is RTL: {is_rtl}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic translator test failed: {e}")
        return False

def test_language_switching():
    """Test language switching functionality"""
    print("\n🔄 Testing Language Switching...")
    
    try:
        original_language = get_current_language()
        print(f"✅ Original language: {original_language}")
        
        # Test switching to different languages
        test_languages = ['ar', 'fr', 'en']
        
        for lang in test_languages:
            if lang != original_language:
                print(f"   Switching to {lang}...")
                change_language(lang)
                
                # Verify language changed
                current_lang = get_current_language()
                if current_lang == lang:
                    print(f"   ✅ Successfully switched to {lang}")
                    
                    # Test translation in new language
                    test_translations = [
                        "common.ok",
                        "navigation.pos",
                        "navigation.products",
                        "navigation.customers"
                    ]
                    
                    for key in test_translations:
                        translation = tr(key)
                        print(f"     {key}: '{translation}'")
                    
                    # Test RTL detection
                    is_rtl = translator.is_rtl()
                    print(f"     RTL: {is_rtl}")
                    
                else:
                    print(f"   ❌ Failed to switch to {lang}")
                    return False
        
        # Switch back to original language
        change_language(original_language)
        final_lang = get_current_language()
        
        if final_lang == original_language:
            print(f"✅ Successfully switched back to {original_language}")
        else:
            print(f"❌ Failed to switch back to {original_language}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Language switching test failed: {e}")
        return False

def test_widget_registration():
    """Test widget registration for translation updates"""
    print("\n📝 Testing Widget Registration...")
    
    try:
        # Create test widget
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create a simple user for testing
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window (should auto-register)
        main_window = MainWindow(test_user)
        
        # Check if widget is registered
        registered_count = len(translator.registered_widgets)
        print(f"✅ Registered widgets: {registered_count}")
        
        if registered_count > 0:
            print("✅ Main window registered for translation updates")
        else:
            print("⚠️ No widgets registered")
        
        # Test language change notification
        original_language = get_current_language()
        
        # Switch language and check if widget is notified
        test_language = 'fr' if original_language != 'fr' else 'en'
        
        print(f"   Testing notification with language change to {test_language}...")
        change_language(test_language)
        
        # Give time for UI updates
        app.processEvents()
        
        # Check if window title updated
        window_title = main_window.windowTitle()
        print(f"   Window title: {window_title}")
        
        # Switch back
        change_language(original_language)
        app.processEvents()
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Widget registration test failed: {e}")
        return False

def test_settings_widget_language_change():
    """Test settings widget language change functionality"""
    print("\n⚙️ Testing Settings Widget Language Change...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create settings widget (main settings tab)
        settings_widget = GeneralSettingsTab()

        # Load settings to initialize components
        if hasattr(settings_widget, 'load_settings'):
            settings_widget.load_settings()

        # Get original language
        original_language = get_current_language()
        print(f"✅ Original language: {original_language}")

        # Test language combo box
        if hasattr(settings_widget, 'language_combo'):
            language_combo = settings_widget.language_combo
            print(f"✅ Language combo has {language_combo.count()} options")
        else:
            print("❌ Language combo not found")
            return False
        
        # Test changing language through combo box
        for i in range(language_combo.count()):
            lang_code = language_combo.itemData(i)
            lang_name = language_combo.itemText(i)
            
            if lang_code and lang_code != original_language:
                print(f"   Testing change to {lang_name} ({lang_code})...")
                
                # Set combo box selection
                language_combo.setCurrentIndex(i)
                
                # Trigger the change
                settings_widget.on_language_changed()
                
                # Process events
                app.processEvents()
                
                # Check if language actually changed
                current_lang = get_current_language()
                if current_lang == lang_code:
                    print(f"   ✅ Successfully changed to {lang_code}")
                    
                    # Test some translations
                    test_keys = [
                        "settings.title",
                        "settings.language",
                        "common.save"
                    ]
                    
                    for key in test_keys:
                        translation = tr(key)
                        print(f"     {key}: '{translation}'")
                    
                    break
                else:
                    print(f"   ❌ Failed to change to {lang_code}")
        
        # Switch back to original
        for i in range(language_combo.count()):
            if language_combo.itemData(i) == original_language:
                language_combo.setCurrentIndex(i)
                settings_widget.on_language_changed()
                app.processEvents()
                break
        
        # Clean up
        settings_widget.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Settings widget test failed: {e}")
        return False

def test_translation_completeness():
    """Test translation completeness across languages"""
    print("\n📚 Testing Translation Completeness...")
    
    try:
        languages = ['ar', 'fr', 'en']
        
        # Test keys that should exist in all languages
        test_keys = [
            "common.ok",
            "common.cancel",
            "common.save",
            "navigation.pos",
            "navigation.products",
            "navigation.customers",
            "navigation.sales",
            "navigation.reports",
            "navigation.settings",
            "settings.language",
            "settings.language_changed",
            "settings.language_changed_message"
        ]
        
        original_language = get_current_language()
        
        for lang in languages:
            print(f"   Testing {lang} translations...")
            change_language(lang)
            
            missing_keys = []
            for key in test_keys:
                translation = tr(key)
                if translation == key:  # Translation not found
                    missing_keys.append(key)
            
            if missing_keys:
                print(f"   ⚠️ Missing translations in {lang}: {missing_keys}")
            else:
                print(f"   ✅ All test keys translated in {lang}")
        
        # Switch back to original
        change_language(original_language)
        
        return True
        
    except Exception as e:
        print(f"❌ Translation completeness test failed: {e}")
        return False

def test_performance():
    """Test translation performance"""
    print("\n⚡ Testing Translation Performance...")
    
    try:
        # Test translation speed
        test_keys = [
            "common.ok", "common.cancel", "common.save",
            "navigation.pos", "navigation.products", "navigation.customers"
        ]
        
        # Test current language performance
        start_time = time.time()
        for _ in range(1000):
            for key in test_keys:
                tr(key)
        end_time = time.time()
        
        current_lang_time = end_time - start_time
        print(f"✅ 1000 translations in current language: {current_lang_time:.4f}s")
        
        # Test language switching performance
        languages = ['ar', 'fr', 'en']
        original_language = get_current_language()
        
        start_time = time.time()
        for lang in languages:
            change_language(lang)
            for key in test_keys:
                tr(key)
        end_time = time.time()
        
        switching_time = end_time - start_time
        print(f"✅ Language switching + translations: {switching_time:.4f}s")
        
        # Switch back
        change_language(original_language)
        
        # Performance evaluation
        if current_lang_time < 0.1 and switching_time < 1.0:
            print("✅ Translation performance excellent")
        elif current_lang_time < 0.5 and switching_time < 2.0:
            print("✅ Translation performance good")
        else:
            print("⚠️ Translation performance could be improved")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def run_language_switching_test():
    """Run all language switching tests"""
    print("🌐 STARTING LANGUAGE SWITCHING SYSTEM TEST")
    print("=" * 60)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # Test 1: Basic Functionality
    test_results.append(("Basic Functionality", test_translator_basic_functionality()))
    
    # Test 2: Language Switching
    test_results.append(("Language Switching", test_language_switching()))
    
    # Test 3: Widget Registration
    test_results.append(("Widget Registration", test_widget_registration()))
    
    # Test 4: Settings Widget
    test_results.append(("Settings Widget", test_settings_widget_language_change()))
    
    # Test 5: Translation Completeness
    test_results.append(("Translation Completeness", test_translation_completeness()))
    
    # Test 6: Performance
    test_results.append(("Performance", test_performance()))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 LANGUAGE SWITCHING TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL LANGUAGE SWITCHING TESTS PASSED! System is working perfectly!")
    else:
        print("⚠️  Some language switching tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_language_switching_test()
