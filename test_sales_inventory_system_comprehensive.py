#!/usr/bin/env python3
"""
اختبار شامل لنظام المبيعات والمخزون المتقدم - OnePos
Comprehensive Sales and Inventory Management System Test
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.main_window import MainWindow
from models.user import User
from models.sale import Sale, SaleItem
from models.product import Product
from models.stock import StockMovement, StockAlert
from models.purchase import Purchase, Supplier, PurchaseItem
from utils.translator import translator, tr

def test_sales_model():
    """اختبار موديل المبيعات"""
    print("💰 اختبار موديل المبيعات")
    print("=" * 30)
    
    try:
        # الحصول على منتجات للاختبار
        products = Product.get_all()
        if len(products) < 2:
            print("❌ لا توجد منتجات كافية للاختبار")
            return False
        
        # إنشاء عناصر البيع
        sale_items = [
            {
                'product_id': products[0].id,
                'quantity': 2,
                'unit_price': products[0].selling_price,
                'discount_amount': 0
            },
            {
                'product_id': products[1].id,
                'quantity': 1,
                'unit_price': products[1].selling_price,
                'discount_amount': 5.0
            }
        ]
        
        # اختبار إنشاء بيع
        sale = Sale.create(
            user_id=1,  # Admin user
            items=sale_items,
            payment_method='cash',
            tax_rate=20.0,
            discount_amount=10.0
        )
        
        if sale:
            print("✅ إنشاء بيع: نجح")
            print(f"   🧾 رقم الفاتورة: {sale.invoice_number}")
            print(f"   💰 المبلغ الإجمالي: {sale.total_amount}")
            print(f"   📊 عدد العناصر: {len(sale.items)}")
        else:
            print("❌ إنشاء بيع: فشل")
            return False
        
        # اختبار البحث بالفاتورة
        found_sale = Sale.get_by_invoice_number(sale.invoice_number)
        if found_sale:
            print("✅ البحث بالفاتورة: نجح")
        else:
            print("❌ البحث بالفاتورة: فشل")
        
        # اختبار الحصول على جميع المبيعات
        all_sales = Sale.get_all()
        print(f"📊 إجمالي المبيعات: {len(all_sales)}")
        
        # اختبار تحديث البيع
        sale.update(status='completed', notes='Test sale completed')
        updated_sale = Sale.get_by_id(sale.id)
        if updated_sale and updated_sale.status == 'completed':
            print("✅ تحديث البيع: نجح")
        else:
            print("❌ تحديث البيع: فشل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار موديل المبيعات: {e}")
        return False

def test_inventory_management():
    """اختبار إدارة المخزون"""
    print("\n📦 اختبار إدارة المخزون")
    print("=" * 35)
    
    try:
        # الحصول على منتج للاختبار
        products = Product.get_all()
        if not products:
            print("❌ لا توجد منتجات للاختبار")
            return False
        
        product = products[0]
        initial_stock = product.stock_quantity
        
        # اختبار إضافة مخزون (شراء)
        product.adjust_stock(20, 'purchase', notes='Test purchase')
        updated_product = Product.get_by_id(product.id)
        
        if updated_product.stock_quantity == initial_stock + 20:
            print("✅ إضافة مخزون (شراء): نجح")
            print(f"   📊 المخزون السابق: {initial_stock}")
            print(f"   📊 المخزون الحالي: {updated_product.stock_quantity}")
        else:
            print("❌ إضافة مخزون (شراء): فشل")
        
        # اختبار تقليل مخزون (بيع)
        product.adjust_stock(-5, 'sale', notes='Test sale')
        updated_product = Product.get_by_id(product.id)
        
        if updated_product.stock_quantity == initial_stock + 15:
            print("✅ تقليل مخزون (بيع): نجح")
        else:
            print("❌ تقليل مخزون (بيع): فشل")
        
        # اختبار حركات المخزون
        movements = StockMovement.get_by_product_id(product.id)
        if movements and len(movements) >= 2:
            print("✅ تسجيل حركات المخزون: نجح")
            print(f"   📊 عدد الحركات: {len(movements)}")
        else:
            print("❌ تسجيل حركات المخزون: فشل")
        
        # اختبار تنبيهات المخزون
        alerts = StockAlert.check_stock_alerts()
        print(f"📊 تنبيهات المخزون:")
        print(f"   ⚠️ مخزون منخفض: {len(alerts['low_stock'])}")
        print(f"   🔴 نفد المخزون: {len(alerts['out_of_stock'])}")
        print(f"   📈 مخزون زائد: {len(alerts['overstock'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة المخزون: {e}")
        return False

def test_purchase_system():
    """اختبار نظام المشتريات"""
    print("\n📥 اختبار نظام المشتريات")
    print("=" * 35)
    
    try:
        # اختبار إنشاء مورد
        supplier = Supplier.create(
            name="Test Supplier",
            contact_person="John Doe",
            phone="******-123-4567",
            email="<EMAIL>",
            address="123 Supplier Street"
        )
        
        if supplier:
            print("✅ إنشاء مورد: نجح")
            print(f"   🏢 اسم المورد: {supplier.name}")
            print(f"   📞 الهاتف: {supplier.phone}")
        else:
            print("❌ إنشاء مورد: فشل")
            return False
        
        # اختبار إنشاء مشتريات
        products = Product.get_all()
        if len(products) < 2:
            print("❌ لا توجد منتجات كافية للاختبار")
            return False
        
        purchase_items = [
            {
                'product_id': products[0].id,
                'quantity': 10,
                'unit_cost': 25.0
            },
            {
                'product_id': products[1].id,
                'quantity': 5,
                'unit_cost': 15.0
            }
        ]
        
        purchase = Purchase.create(
            supplier_id=supplier.id,
            user_id=1,
            notes="Test purchase order"
        )

        # إضافة عناصر المشتريات
        if purchase:
            for item in purchase_items:
                PurchaseItem.create(
                    purchase_id=purchase.id,
                    product_id=item['product_id'],
                    quantity=item['quantity'],
                    unit_cost=item['unit_cost']
                )

            # إعادة حساب الإجمالي
            purchase.calculate_totals()
        
        if purchase:
            print("✅ إنشاء مشتريات: نجح")
            print(f"   📋 رقم المشتريات: {purchase.purchase_number}")
            print(f"   💰 المبلغ الإجمالي: {purchase.total_amount}")
        else:
            print("❌ إنشاء مشتريات: فشل")
        
        # اختبار الحصول على المشتريات
        if purchase:
            retrieved_purchase = Purchase.get_by_id(purchase.id)
            if retrieved_purchase and retrieved_purchase.purchase_number == purchase.purchase_number:
                print("✅ استرجاع المشتريات: نجح")
            else:
                print("❌ استرجاع المشتريات: فشل")
        
        # اختبار البحث في الموردين
        found_suppliers = Supplier.search("Test")
        if found_suppliers and len(found_suppliers) > 0:
            print("✅ البحث في الموردين: نجح")
        else:
            print("❌ البحث في الموردين: فشل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام المشتريات: {e}")
        return False

def test_sales_ui():
    """اختبار واجهة المبيعات"""
    print("\n🖼️ اختبار واجهة المبيعات")
    print("=" * 35)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        main_window = MainWindow(admin_user)
        
        # الانتقال لقسم المبيعات
        main_window.load_module('sales')
        app.processEvents()
        
        # البحث عن واجهة المبيعات
        sales_widget = None
        for i in range(main_window.content_frame.count()):
            widget = main_window.content_frame.widget(i)
            if hasattr(widget, 'sales_table'):
                sales_widget = widget
                break
        
        if not sales_widget:
            print("❌ لم يتم العثور على واجهة المبيعات")
            return False
        
        print("✅ تم العثور على واجهة المبيعات")
        
        # فحص العناصر الأساسية
        elements_to_check = [
            ('sales_table', 'جدول المبيعات'),
            ('from_date', 'تاريخ البداية'),
            ('to_date', 'تاريخ النهاية'),
            ('status_filter', 'فلتر الحالة'),
            ('search_edit', 'حقل البحث'),
            ('summary_label', 'ملخص المبيعات')
        ]
        
        missing_elements = []
        for element_name, description in elements_to_check:
            if hasattr(sales_widget, element_name):
                print(f"   ✅ {description}: موجود")
            else:
                print(f"   ❌ {description}: مفقود")
                missing_elements.append(description)
        
        # فحص الجدول
        table = sales_widget.sales_table
        if table.columnCount() >= 8:
            print(f"✅ جدول المبيعات: {table.columnCount()} أعمدة")
        else:
            print(f"❌ جدول المبيعات: أعمدة ناقصة ({table.columnCount()}/8)")
        
        # اختبار تحميل البيانات
        sales_widget.load_sales()
        app.processEvents()
        
        if table.rowCount() >= 0:
            print(f"✅ تحميل البيانات: {table.rowCount()} بيع")
        else:
            print("❌ تحميل البيانات: فشل")
        
        return len(missing_elements) == 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المبيعات: {e}")
        return False

def test_sales_translations():
    """اختبار ترجمات المبيعات"""
    print("\n🌍 اختبار ترجمات المبيعات")
    print("=" * 35)
    
    languages = [
        ('ar', 'العربية'),
        ('en', 'English'),
        ('fr', 'Français')
    ]
    
    sales_keys = [
        'sales.title',
        'sales.invoice_number',
        'sales.date',
        'sales.customer',
        'sales.amount',
        'sales.payment_method',
        'sales.status'
    ]
    
    all_correct = True
    
    try:
        for lang_code, lang_name in languages:
            print(f"\n📝 اختبار اللغة: {lang_name} ({lang_code})")
            
            # تغيير اللغة
            translator.set_language(lang_code)
            
            missing_translations = []
            for key in sales_keys:
                translation = tr(key)
                if translation == key:  # لم يتم العثور على ترجمة
                    missing_translations.append(key)
                else:
                    print(f"   ✅ {key}: {translation}")
            
            if missing_translations:
                print(f"   ❌ ترجمات مفقودة: {len(missing_translations)}")
                for key in missing_translations:
                    print(f"      - {key}")
                all_correct = False
            else:
                print(f"   ✅ جميع الترجمات متوفرة")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ترجمات المبيعات: {e}")
        return False

def test_stock_alerts():
    """اختبار تنبيهات المخزون"""
    print("\n⚠️ اختبار تنبيهات المخزون")
    print("=" * 35)
    
    try:
        # إنشاء منتج بمخزون منخفض للاختبار
        low_stock_product = Product.create(
            name="Low Stock Test Product",
            selling_price=50.0,
            stock_quantity=2,
            min_stock_level=10
        )
        
        if low_stock_product:
            print("✅ إنشاء منتج بمخزون منخفض: نجح")
        
        # إنشاء منتج نفد مخزونه للاختبار
        out_of_stock_product = Product.create(
            name="Out of Stock Test Product",
            selling_price=30.0,
            stock_quantity=0,
            min_stock_level=5
        )
        
        if out_of_stock_product:
            print("✅ إنشاء منتج نفد مخزونه: نجح")
        
        # فحص تنبيهات المخزون
        alerts = StockAlert.check_stock_alerts()
        
        print(f"📊 نتائج تنبيهات المخزون:")
        print(f"   ⚠️ منتجات بمخزون منخفض: {len(alerts['low_stock'])}")
        print(f"   🔴 منتجات نفد مخزونها: {len(alerts['out_of_stock'])}")
        print(f"   📈 منتجات بمخزون زائد: {len(alerts['overstock'])}")
        
        # التحقق من وجود المنتجات في التنبيهات
        low_stock_found = any(p.id == low_stock_product.id for p in alerts['low_stock'])
        out_of_stock_found = any(p.id == out_of_stock_product.id for p in alerts['out_of_stock'])
        
        if low_stock_found:
            print("✅ تنبيه المخزون المنخفض: يعمل")
        else:
            print("❌ تنبيه المخزون المنخفض: لا يعمل")
        
        if out_of_stock_found:
            print("✅ تنبيه نفاد المخزون: يعمل")
        else:
            print("❌ تنبيه نفاد المخزون: لا يعمل")
        
        return low_stock_found and out_of_stock_found
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تنبيهات المخزون: {e}")
        return False

def run_sales_inventory_comprehensive_test():
    """تشغيل جميع اختبارات نظام المبيعات والمخزون"""
    print("💰 بدء الاختبار الشامل لنظام المبيعات والمخزون المتقدم")
    print("=" * 80)
    
    results = []
    
    # اختبار 1: موديل المبيعات
    results.append(("موديل المبيعات", test_sales_model()))
    
    # اختبار 2: إدارة المخزون
    results.append(("إدارة المخزون", test_inventory_management()))
    
    # اختبار 3: نظام المشتريات
    results.append(("نظام المشتريات", test_purchase_system()))
    
    # اختبار 4: واجهة المبيعات
    results.append(("واجهة المبيعات", test_sales_ui()))
    
    # اختبار 5: ترجمات المبيعات
    results.append(("ترجمات المبيعات", test_sales_translations()))
    
    # اختبار 6: تنبيهات المخزون
    results.append(("تنبيهات المخزون", test_stock_alerts()))
    
    # النتائج النهائية
    print("\n" + "=" * 80)
    print("📊 نتائج الاختبار الشامل لنظام المبيعات والمخزون")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 نظام المبيعات والمخزون يعمل بشكل مثالي!")
        print("✨ جميع الميزات متوفرة ومكتملة")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_sales_inventory_comprehensive_test()
