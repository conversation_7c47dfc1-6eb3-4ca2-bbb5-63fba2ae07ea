#!/usr/bin/env python3
"""
إنشاء نسخة إنتاج آمنة بدون ملفات الأكواد
Create Secure Production Version without License Files
"""

import os
import shutil
import sys
from datetime import datetime


def create_secure_production():
    """إنشاء نسخة إنتاج آمنة للبيع"""
    print("🔒 إنشاء نسخة الإنتاج الآمنة OnePos")
    print("=" * 60)
    
    # مجلد الإنتاج الآمن
    production_dir = "OnePos_Secure_Production"
    
    # إزالة المجلد إذا كان موجود
    if os.path.exists(production_dir):
        shutil.rmtree(production_dir)
        print(f"🗑️ تم حذف المجلد القديم: {production_dir}")
    
    # إنشاء مجلد جديد
    os.makedirs(production_dir)
    print(f"📁 تم إنشاء مجلد الإنتاج الآمن: {production_dir}")
    
    # قائمة الملفات والمجلدات المطلوبة (بدون ملفات الأكواد)
    items_to_copy = [
        "main.py",
        "models/",
        "views/",
        "utils/",
        "translations/",
        "requirements.txt"
    ]
    
    # الملفات المحظورة (لن يتم نسخها)
    forbidden_files = [
        "OnePos_License_Codes.txt",
        "OnePos_License_Codes.csv", 
        "OnePos_License_Codes.json",
        "data/licenses.db",  # قاعدة بيانات الأكواد
        "generate_license_codes.py",
        "license_manager_tool.py",
        "test_license.py",
        "create_production_version.py",
        "create_secure_production.py"
    ]
    
    print("\n🚫 الملفات المحظورة (لن يتم نسخها):")
    for file in forbidden_files:
        print(f"   ❌ {file}")
    
    print("\n📋 نسخ الملفات المسموحة:")
    
    # نسخ الملفات
    for item in items_to_copy:
        src = item
        dst = os.path.join(production_dir, item)
        
        if os.path.isfile(src):
            # نسخ ملف
            os.makedirs(os.path.dirname(dst), exist_ok=True)
            shutil.copy2(src, dst)
            print(f"   ✅ تم نسخ الملف: {item}")
        elif os.path.isdir(src):
            # نسخ مجلد (مع تصفية الملفات المحظورة)
            copy_directory_filtered(src, dst, forbidden_files)
            print(f"   ✅ تم نسخ المجلد: {item}")
        else:
            print(f"   ⚠️ لم يتم العثور على: {item}")
    
    # إنشاء قاعدة بيانات فارغة (بدون أكواد)
    create_empty_license_db(production_dir)
    
    # إنشاء ملفات التثبيت والتشغيل
    create_installation_files(production_dir)
    
    # إنشاء ملف README للعميل
    create_customer_readme(production_dir)
    
    # فحص الأمان
    security_check(production_dir)
    
    # إحصائيات
    print("\n" + "=" * 60)
    print("✅ تم إنشاء نسخة الإنتاج الآمنة بنجاح!")
    print(f"📁 المجلد: {production_dir}")
    
    total_files = 0
    for root, dirs, files in os.walk(production_dir):
        total_files += len(files)
    
    print(f"📊 إجمالي الملفات: {total_files}")
    print(f"💾 حجم المجلد: {get_folder_size(production_dir):.2f} MB")
    
    print("\n🔒 ضمانات الأمان:")
    print("   ✅ لا توجد ملفات أكواد")
    print("   ✅ لا توجد أدوات توليد أكواد")
    print("   ✅ قاعدة بيانات فارغة")
    print("   ✅ آمن للتوزيع التجاري")
    
    print("\n🎯 الخطوات التالية:")
    print("1. اختبر النسخة الآمنة")
    print("2. انسخها إلى CD/فلاشة")
    print("3. أضف كود تفعيل منفصل لكل عميل")
    print("4. وزع بأمان تام!")


def copy_directory_filtered(src, dst, forbidden_files):
    """نسخ مجلد مع تصفية الملفات المحظورة"""
    if not os.path.exists(dst):
        os.makedirs(dst)
    
    for item in os.listdir(src):
        src_path = os.path.join(src, item)
        dst_path = os.path.join(dst, item)
        
        # تحقق من الملفات المحظورة
        relative_path = os.path.relpath(src_path)
        if any(forbidden in relative_path for forbidden in forbidden_files):
            print(f"      🚫 تم تجاهل: {relative_path}")
            continue
        
        if os.path.isfile(src_path):
            shutil.copy2(src_path, dst_path)
        elif os.path.isdir(src_path):
            copy_directory_filtered(src_path, dst_path, forbidden_files)


def create_empty_license_db(production_dir):
    """إنشاء قاعدة بيانات ترخيص فارغة"""
    import sqlite3
    
    # إنشاء مجلد البيانات
    data_dir = os.path.join(production_dir, "data")
    os.makedirs(data_dir, exist_ok=True)
    
    # إنشاء قاعدة بيانات فارغة
    db_path = os.path.join(data_dir, "licenses.db")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # إنشاء الجداول فقط (بدون بيانات)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS license_codes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            serial_code TEXT UNIQUE NOT NULL,
            license_type TEXT DEFAULT 'standard',
            validity_days INTEGER DEFAULT 365,
            max_activations INTEGER DEFAULT 1,
            created_date TEXT NOT NULL,
            expiry_date TEXT NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            notes TEXT
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS activations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            serial_code TEXT NOT NULL,
            machine_id TEXT NOT NULL,
            machine_fingerprint TEXT NOT NULL,
            ip_address TEXT,
            computer_name TEXT,
            os_info TEXT,
            activation_date TEXT NOT NULL,
            last_check_date TEXT,
            is_active BOOLEAN DEFAULT 1,
            activation_count INTEGER DEFAULT 1,
            FOREIGN KEY (serial_code) REFERENCES license_codes (serial_code),
            UNIQUE(serial_code, machine_id)
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS activity_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            serial_code TEXT,
            machine_id TEXT,
            action TEXT NOT NULL,
            details TEXT,
            ip_address TEXT,
            timestamp TEXT NOT NULL
        )
    ''')
    
    conn.commit()
    conn.close()
    
    print(f"   ✅ تم إنشاء قاعدة بيانات فارغة: data/licenses.db")


def create_installation_files(production_dir):
    """إنشاء ملفات التثبيت والتشغيل"""
    
    # ملف التثبيت
    install_script = """@echo off
echo ========================================
echo    OnePos Installation - تثبيت OnePos
echo ========================================
echo.

echo جاري التحقق من متطلبات النظام...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo تحذير: Python غير مثبت
    echo يرجى تحميل وتثبيت Python من python.org
    echo ثم إعادة تشغيل هذا الملف
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
echo.

echo إنشاء البيئة الافتراضية...
python -m venv venv
if errorlevel 1 (
    echo خطأ في إنشاء البيئة الافتراضية
    pause
    exit /b 1
)

echo ✅ تم إنشاء البيئة الافتراضية
echo.

echo تفعيل البيئة الافتراضية...
call venv\\Scripts\\activate.bat

echo تثبيت المتطلبات...
pip install --upgrade pip
pip install -r requirements.txt

if errorlevel 1 (
    echo خطأ في تثبيت المتطلبات
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ تم تثبيت OnePos بنجاح!
echo ========================================
echo.
echo يمكنك الآن تشغيل التطبيق من ملف run.bat
echo.
pause

deactivate
"""
    
    with open(os.path.join(production_dir, "install.bat"), 'w', encoding='utf-8') as f:
        f.write(install_script)
    
    # ملف التشغيل
    run_script = """@echo off
echo ========================================
echo       OnePos - نظام نقاط البيع
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تشغيل install.bat أولاً
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo خطأ: البيئة الافتراضية غير موجودة
    echo يرجى تشغيل install.bat أولاً
    pause
    exit /b 1
)

echo تشغيل OnePos...
echo.

REM Activate virtual environment and run
call venv\\Scripts\\activate.bat
python main.py

if errorlevel 1 (
    echo.
    echo خطأ في تشغيل التطبيق
    pause
)

deactivate
"""
    
    with open(os.path.join(production_dir, "run.bat"), 'w', encoding='utf-8') as f:
        f.write(run_script)
    
    print(f"   ✅ تم إنشاء ملفات التثبيت والتشغيل")


def create_customer_readme(production_dir):
    """إنشاء ملف README للعميل"""
    readme_content = """
# OnePos - نظام نقاط البيع المتقدم

## 🚀 مرحباً بك في OnePos

OnePos هو نظام نقاط بيع متقدم ومتكامل يوفر جميع الأدوات اللازمة لإدارة أعمالك التجارية بكفاءة عالية.

## 📋 المتطلبات

- Windows 10 أو أحدث
- Python 3.8 أو أحدث (سيتم تثبيته تلقائياً)
- 4 GB RAM كحد أدنى
- 1 GB مساحة فارغة على القرص الصلب

## 🔧 التثبيت

1. **انسخ جميع الملفات** إلى مجلد على جهازك
2. **شغل ملف install.bat** كمدير (Run as Administrator)
3. **انتظر انتهاء التثبيت** (قد يستغرق بضع دقائق)
4. **شغل التطبيق** من ملف run.bat

## 🔐 التفعيل

1. **شغل التطبيق** لأول مرة
2. **ستظهر نافذة التفعيل** تلقائياً
3. **أدخل كود التفعيل** المرفق مع النسخة
4. **اضغط تفعيل** وانتظر التأكيد
5. **استمتع بالاستخدام الكامل**

⚠️ **مهم**: كود التفعيل صالح لجهاز واحد فقط ولا يمكن استخدامه مرة أخرى

## 🎯 المميزات

- ✅ إدارة المنتجات والمخزون
- ✅ نظام نقاط البيع السريع
- ✅ إدارة العملاء والموردين
- ✅ التقارير والإحصائيات
- ✅ طباعة الفواتير والإيصالات
- ✅ نظام الباركود
- ✅ دعم متعدد اللغات (عربي، إنجليزي، فرنسي)
- ✅ واجهة مستخدم جميلة وسهلة

## 📞 الدعم الفني

للحصول على الدعم الفني أو كود تفعيل إضافي:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +1234567890
- الموقع: www.onepos.com

## ⚠️ ملاحظات مهمة

- كود التفعيل صالح لجهاز واحد فقط
- احتفظ بكود التفعيل في مكان آمن
- لا تشارك كود التفعيل مع الآخرين
- النسخ الاحتياطي مهم لحماية بياناتك

---
© 2024 OnePos. جميع الحقوق محفوظة.
"""
    
    with open(os.path.join(production_dir, "README.txt"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"   ✅ تم إنشاء ملف README للعميل")


def security_check(production_dir):
    """فحص أمني للتأكد من عدم وجود ملفات حساسة"""
    print(f"\n🔍 فحص أمني للمجلد: {production_dir}")
    
    dangerous_files = [
        "license_codes",
        "generate_license",
        "license_manager_tool",
        "test_license"
    ]
    
    security_issues = []
    
    for root, dirs, files in os.walk(production_dir):
        for file in files:
            file_path = os.path.join(root, file)
            file_lower = file.lower()
            
            # تحقق من الملفات الخطيرة
            for dangerous in dangerous_files:
                if dangerous in file_lower:
                    security_issues.append(file_path)
    
    if security_issues:
        print("   ❌ تم العثور على ملفات خطيرة:")
        for issue in security_issues:
            print(f"      🚨 {issue}")
        print("   ⚠️ يرجى حذف هذه الملفات قبل التوزيع!")
    else:
        print("   ✅ الفحص الأمني نجح - لا توجد ملفات خطيرة")


def get_folder_size(folder_path):
    """حساب حجم المجلد بالميجابايت"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(folder_path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            if os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
    return total_size / (1024 * 1024)  # Convert to MB


if __name__ == "__main__":
    try:
        create_secure_production()
    except Exception as e:
        print(f"\n❌ خطأ في إنشاء نسخة الإنتاج الآمنة: {e}")
        import traceback
        traceback.print_exc()
    
    input("\nاضغط Enter للخروج...")
