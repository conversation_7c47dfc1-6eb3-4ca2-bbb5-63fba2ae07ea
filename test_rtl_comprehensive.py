"""
Comprehensive RTL Layout Test for OnePos POS System
Complete test covering all aspects of RTL layout functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from utils.translator import change_language, get_current_language
from views.main_window import MainWindow
from views.settings_widget import SettingsWidget, GeneralSettingsTab
from models.user import User
import time

def test_settings_language_change_with_layout():
    """Test language change through settings with layout verification"""
    print("⚙️ Testing Settings Language Change with Layout...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Navigate to settings
        if 'settings' in main_window.nav_buttons:
            settings_button = main_window.nav_buttons['settings']
            settings_button.click()
            app.processEvents()
            
            # Get settings widget
            current_widget = main_window.stacked_widget.currentWidget()
            if isinstance(current_widget, SettingsWidget):
                settings_widget = current_widget
                
                # Find general settings tab
                general_tab = None
                for i in range(settings_widget.tabs.count()):
                    widget = settings_widget.tabs.widget(i)
                    if isinstance(widget, GeneralSettingsTab):
                        general_tab = widget
                        settings_widget.tabs.setCurrentIndex(i)
                        app.processEvents()
                        break
                
                if general_tab:
                    language_combo = general_tab.language_combo
                    
                    # Test each language
                    languages_tested = []
                    for i in range(language_combo.count()):
                        lang_code = language_combo.itemData(i)
                        lang_name = language_combo.itemText(i)
                        
                        if lang_code:
                            print(f"   Testing {lang_name} ({lang_code})...")
                            
                            # Change language through UI
                            language_combo.setCurrentIndex(i)
                            general_tab.on_language_changed()
                            app.processEvents()
                            time.sleep(0.1)
                            
                            # Verify layout
                            layout = main_window.main_layout
                            if layout.count() >= 2:
                                first_widget = layout.itemAt(0).widget()
                                
                                if lang_code == 'ar':
                                    expected_first = main_window.content_frame
                                    expected_direction = Qt.RightToLeft
                                else:
                                    expected_first = main_window.sidebar_frame
                                    expected_direction = Qt.LeftToRight
                                
                                # Check layout arrangement
                                if first_widget == expected_first:
                                    print(f"     ✅ Layout arrangement correct")
                                else:
                                    print(f"     ❌ Layout arrangement incorrect")
                                    return False
                                
                                # Check layout direction
                                actual_direction = main_window.layoutDirection()
                                if actual_direction == expected_direction:
                                    print(f"     ✅ Layout direction correct: {actual_direction}")
                                else:
                                    print(f"     ❌ Layout direction incorrect: expected {expected_direction}, got {actual_direction}")
                                    return False
                                
                                languages_tested.append(lang_code)
                    
                    print(f"   ✅ Tested languages: {languages_tested}")
                    
                    if len(languages_tested) >= 3:
                        print("   ✅ All languages tested successfully")
                    else:
                        print(f"   ⚠️ Only {len(languages_tested)} languages tested")
                else:
                    print("   ❌ General settings tab not found")
                    return False
            else:
                print("   ❌ Settings widget not found")
                return False
        else:
            print("   ❌ Settings button not found")
            return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Settings language change test failed: {e}")
        return False

def test_layout_with_navigation():
    """Test layout consistency during navigation"""
    print("\n🧭 Testing Layout with Navigation...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Test with different languages
        languages = [('ar', 'Arabic'), ('en', 'English'), ('fr', 'French')]
        modules = ['pos', 'products', 'customers', 'reports', 'settings']
        
        for lang_code, lang_name in languages:
            print(f"   Testing {lang_name} navigation...")
            
            # Change language
            change_language(lang_code)
            app.processEvents()
            time.sleep(0.1)
            
            # Navigate through modules
            for module in modules:
                if module in main_window.nav_buttons:
                    # Click module button
                    main_window.nav_buttons[module].click()
                    app.processEvents()
                    time.sleep(0.05)
                    
                    # Check layout consistency
                    layout = main_window.main_layout
                    if layout.count() >= 2:
                        first_widget = layout.itemAt(0).widget()
                        
                        if lang_code == 'ar':
                            expected_first = main_window.content_frame
                        else:
                            expected_first = main_window.sidebar_frame
                        
                        if first_widget == expected_first:
                            print(f"     ✅ {module}: Layout consistent")
                        else:
                            print(f"     ❌ {module}: Layout inconsistent")
                            return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Layout with navigation test failed: {e}")
        return False

def test_layout_stress():
    """Test layout under stress conditions"""
    print("\n💪 Testing Layout Stress...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Stress test: rapid language and navigation changes
        languages = ['ar', 'en', 'fr']
        modules = ['pos', 'products', 'customers']
        
        print("   Performing stress test...")
        
        for iteration in range(10):  # 10 iterations
            for lang in languages:
                # Change language
                change_language(lang)
                app.processEvents()
                
                # Navigate to random module
                module = modules[iteration % len(modules)]
                if module in main_window.nav_buttons:
                    main_window.nav_buttons[module].click()
                    app.processEvents()
                
                # Quick layout check
                layout = main_window.main_layout
                if layout.count() >= 2:
                    first_widget = layout.itemAt(0).widget()
                    
                    if lang == 'ar':
                        expected_first = main_window.content_frame
                    else:
                        expected_first = main_window.sidebar_frame
                    
                    if first_widget != expected_first:
                        print(f"     ❌ Stress test failed at iteration {iteration+1}, lang {lang}, module {module}")
                        return False
        
        print("   ✅ Stress test completed successfully")
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Layout stress test failed: {e}")
        return False

def test_layout_window_operations():
    """Test layout during window operations"""
    print("\n🪟 Testing Layout Window Operations...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Set to Arabic for testing
        change_language('ar')
        app.processEvents()
        time.sleep(0.1)
        
        # Test resize operations
        print("   Testing resize operations...")
        sizes = [(800, 600), (1200, 800), (600, 400), (1000, 700)]
        
        for width, height in sizes:
            main_window.resize(width, height)
            app.processEvents()
            time.sleep(0.05)
            
            # Check layout
            layout = main_window.main_layout
            if layout.count() >= 2:
                first_widget = layout.itemAt(0).widget()
                if first_widget == main_window.content_frame:
                    print(f"     ✅ Resize {width}x{height}: Layout maintained")
                else:
                    print(f"     ❌ Resize {width}x{height}: Layout broken")
                    return False
        
        # Test minimize/restore
        print("   Testing minimize/restore...")
        main_window.showMinimized()
        app.processEvents()
        time.sleep(0.1)
        
        main_window.showNormal()
        app.processEvents()
        time.sleep(0.1)
        
        # Check layout after restore
        layout = main_window.main_layout
        if layout.count() >= 2:
            first_widget = layout.itemAt(0).widget()
            if first_widget == main_window.content_frame:
                print("     ✅ Minimize/restore: Layout maintained")
            else:
                print("     ❌ Minimize/restore: Layout broken")
                return False
        
        # Test maximize/restore
        print("   Testing maximize/restore...")
        main_window.showMaximized()
        app.processEvents()
        time.sleep(0.1)
        
        # Check layout when maximized
        layout = main_window.main_layout
        if layout.count() >= 2:
            first_widget = layout.itemAt(0).widget()
            if first_widget == main_window.content_frame:
                print("     ✅ Maximize: Layout maintained")
            else:
                print("     ❌ Maximize: Layout broken")
                return False
        
        main_window.showNormal()
        app.processEvents()
        time.sleep(0.1)
        
        # Final check
        layout = main_window.main_layout
        if layout.count() >= 2:
            first_widget = layout.itemAt(0).widget()
            if first_widget == main_window.content_frame:
                print("     ✅ Restore from maximize: Layout maintained")
            else:
                print("     ❌ Restore from maximize: Layout broken")
                return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Layout window operations test failed: {e}")
        return False

def test_layout_memory_stability():
    """Test layout memory stability"""
    print("\n🧠 Testing Layout Memory Stability...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create and destroy multiple windows
        for i in range(3):
            print(f"   Iteration {i+1}/3...")
            
            # Create test user
            test_user = User()
            test_user.id = 1
            test_user.username = "test"
            test_user.full_name = "Test User"
            test_user.role = "admin"
            
            # Create main window
            main_window = MainWindow(test_user)
            main_window.show()
            app.processEvents()
            
            # Test language changes
            for lang in ['ar', 'en', 'fr']:
                change_language(lang)
                app.processEvents()
                
                # Check layout
                layout = main_window.main_layout
                if layout.count() >= 2:
                    first_widget = layout.itemAt(0).widget()
                    
                    if lang == 'ar':
                        expected_first = main_window.content_frame
                    else:
                        expected_first = main_window.sidebar_frame
                    
                    if first_widget != expected_first:
                        print(f"     ❌ Memory test failed at iteration {i+1}, lang {lang}")
                        return False
            
            # Close window
            main_window.close()
            app.processEvents()
            
            # Force garbage collection
            import gc
            gc.collect()
        
        print("   ✅ Memory stability test completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Layout memory stability test failed: {e}")
        return False

def run_comprehensive_rtl_test():
    """Run comprehensive RTL tests"""
    print("🔄 STARTING COMPREHENSIVE RTL LAYOUT TEST")
    print("=" * 60)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # Test 1: Settings Language Change with Layout
    test_results.append(("Settings Language Change", test_settings_language_change_with_layout()))
    
    # Test 2: Layout with Navigation
    test_results.append(("Layout with Navigation", test_layout_with_navigation()))
    
    # Test 3: Layout Stress
    test_results.append(("Layout Stress", test_layout_stress()))
    
    # Test 4: Layout Window Operations
    test_results.append(("Layout Window Operations", test_layout_window_operations()))
    
    # Test 5: Layout Memory Stability
    test_results.append(("Layout Memory Stability", test_layout_memory_stability()))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 COMPREHENSIVE RTL TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL COMPREHENSIVE RTL TESTS PASSED!")
    else:
        print("⚠️  Some tests failed.")
    
    return passed == total

if __name__ == "__main__":
    run_comprehensive_rtl_test()
