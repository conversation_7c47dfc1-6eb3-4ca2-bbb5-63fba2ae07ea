"""
Comprehensive Testing for OnePos Products System
Tests all product management functionality including UI components and business logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from models.database import db
from models.product import Product, Category
from models.user import User
from views.products_widget import ProductsWidget
from datetime import datetime

def test_products_database_setup():
    """Test products database setup and required data"""
    print("🔗 Testing Products Database Setup...")
    
    try:
        # Check required tables
        result = db.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row['name'] for row in result]
        
        required_tables = ['products', 'categories', 'stock_movements', 'users']
        missing_tables = [table for table in required_tables if table not in tables]
        
        if missing_tables:
            print(f"❌ Missing tables: {missing_tables}")
            return False
        else:
            print(f"✅ All required tables exist: {required_tables}")
        
        # Check if we have products data
        products = Product.get_all()
        print(f"✅ Found {len(products)} products in database")
        
        # Display some products
        for product in products[:5]:
            print(f"   - {product.name}: ${product.selling_price:.2f} (Stock: {product.stock_quantity})")
        
        # Check categories
        categories = Category.get_all()
        print(f"✅ Found {len(categories)} categories in database")
        
        # Display categories
        for category in categories[:5]:
            print(f"   - {category.name}: {category.description}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database setup test failed: {e}")
        return False

def test_products_widget_creation():
    """Test products widget creation and initialization"""
    print("\n🖥️ Testing Products Widget Creation...")
    
    try:
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create products widget
        products_widget = ProductsWidget()
        
        if products_widget:
            print("✅ ProductsWidget created successfully")
            
            # Check main components
            if hasattr(products_widget, 'products_table'):
                print("✅ Products table exists")
                columns = products_widget.products_table.columnCount()
                print(f"   - Columns: {columns}")
            
            if hasattr(products_widget, 'search_edit'):
                print("✅ Search field exists")
            
            if hasattr(products_widget, 'category_filter'):
                print("✅ Category filter exists")
            
            if hasattr(products_widget, 'stock_filter'):
                print("✅ Stock filter exists")
            
            # Check buttons
            buttons_to_check = [
                'add_button',
                'edit_button',
                'delete_button',
                'refresh_button',
                'import_button',
                'export_button'
            ]
            
            for button_name in buttons_to_check:
                if hasattr(products_widget, button_name):
                    print(f"✅ {button_name} exists")
                else:
                    print(f"❌ {button_name} missing")
            
            # Check summary components
            summary_components = [
                'total_products_label',
                'low_stock_label',
                'total_value_label'
            ]
            
            for component_name in summary_components:
                if hasattr(products_widget, component_name):
                    print(f"✅ {component_name} exists")
                else:
                    print(f"⚠️ {component_name} missing")
            
            return products_widget
        else:
            print("❌ Failed to create ProductsWidget")
            return False
            
    except Exception as e:
        print(f"❌ Products widget creation test failed: {e}")
        return False

def test_products_data_loading(products_widget):
    """Test products data loading functionality"""
    print("\n📊 Testing Products Data Loading...")
    
    try:
        # Test loading products
        products_widget.load_products()
        products_count = products_widget.products_table.rowCount()
        print(f"✅ Loaded {products_count} products into table")
        
        # Test loading categories
        if hasattr(products_widget, 'load_categories'):
            products_widget.load_categories()
            categories_count = products_widget.category_filter.count()
            print(f"✅ Loaded {categories_count} categories into filter")
        
        # Test stock filter options
        if hasattr(products_widget, 'stock_filter'):
            stock_options = products_widget.stock_filter.count()
            print(f"✅ Stock filter has {stock_options} options")
        
        return True
        
    except Exception as e:
        print(f"❌ Products data loading test failed: {e}")
        return False

def test_products_filtering(products_widget):
    """Test products filtering functionality"""
    print("\n🔍 Testing Products Filtering...")
    
    try:
        # Test search by name
        if hasattr(products_widget, 'search_edit') and hasattr(products_widget, 'search_products'):
            products_widget.search_edit.setText("Test")
            products_widget.search_products()
            search_results = products_widget.products_table.rowCount()
            print(f"✅ Search for 'Test' returned {search_results} results")
            
            # Clear search
            products_widget.search_edit.setText("")
            products_widget.search_products()
            all_results = products_widget.products_table.rowCount()
            print(f"✅ Cleared search, showing {all_results} products")
        
        # Test category filtering
        if hasattr(products_widget, 'category_filter') and hasattr(products_widget, 'filter_by_category'):
            # Set to first category (not "All")
            if products_widget.category_filter.count() > 1:
                products_widget.category_filter.setCurrentIndex(1)
                products_widget.filter_by_category()
                filtered_results = products_widget.products_table.rowCount()
                print(f"✅ Category filter returned {filtered_results} results")
        
        # Test stock filtering
        if hasattr(products_widget, 'stock_filter') and hasattr(products_widget, 'filter_by_stock'):
            # Test low stock filter
            for i in range(products_widget.stock_filter.count()):
                if 'low' in products_widget.stock_filter.itemText(i).lower():
                    products_widget.stock_filter.setCurrentIndex(i)
                    break
            
            products_widget.filter_by_stock()
            stock_results = products_widget.products_table.rowCount()
            print(f"✅ Stock filter for 'low stock' returned {stock_results} results")
        
        return True
        
    except Exception as e:
        print(f"❌ Products filtering test failed: {e}")
        return False

def test_product_crud_operations(products_widget):
    """Test product CRUD operations"""
    print("\n📝 Testing Product CRUD Operations...")
    
    try:
        # Get initial product count
        initial_count = len(Product.get_all())
        print(f"✅ Initial products count: {initial_count}")
        
        # Test adding a new product
        if hasattr(products_widget, 'add_product'):
            try:
                # Mock the add product dialog
                # Generate unique SKU and barcode
                import time
                timestamp = str(int(time.time()))[-6:]

                test_product_data = {
                    'name': 'Test Product CRUD',
                    'sku': f'TEST-CRUD-{timestamp}',
                    'barcode': f'123456789{timestamp}',
                    'selling_price': 25.99,
                    'cost_price': 15.00,
                    'stock_quantity': 100,
                    'min_stock_level': 10,
                    'category_id': 1,
                    'description': 'Test product for CRUD operations'
                }
                
                # Create product directly for testing
                new_product = Product.create(**test_product_data)
                if new_product:
                    print(f"✅ Product created: {new_product.name}")
                    
                    # Refresh the widget
                    products_widget.load_products()
                    new_count = products_widget.products_table.rowCount()
                    print(f"✅ Products count after add: {new_count}")
                    
                    # Test editing the product
                    updated_data = {
                        'name': 'Test Product CRUD Updated',
                        'selling_price': 29.99
                    }
                    new_product.update(**updated_data)
                    print(f"✅ Product updated: {new_product.name}")
                    
                    # Test deleting the product (soft delete)
                    new_product.delete()
                    # Check if product is marked as inactive
                    updated_product = Product.get_by_id(new_product.id)
                    if updated_product and not updated_product.is_active:
                        print(f"✅ Product soft deleted successfully")
                        products_widget.load_products()
                        final_count = products_widget.products_table.rowCount()
                        print(f"✅ Products count after delete: {final_count}")
                    else:
                        print(f"❌ Failed to soft delete product")
                        return False
                else:
                    print(f"❌ Failed to create product")
                    return False
                    
            except Exception as e:
                print(f"⚠️ CRUD operations test failed: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Product CRUD operations test failed: {e}")
        return False

def test_stock_management(products_widget):
    """Test stock management functionality"""
    print("\n📦 Testing Stock Management...")
    
    try:
        # Get a product to test stock operations
        products = Product.get_all()
        if products:
            test_product = products[0]
            original_stock = test_product.stock_quantity
            print(f"✅ Testing stock with product: {test_product.name}")
            print(f"   Original stock: {original_stock}")
            
            # Test stock adjustment
            adjustment_qty = 10
            test_product.adjust_stock(
                adjustment_qty,
                'adjustment',
                None,
                'manual',
                'Test stock adjustment',
                1  # user_id
            )
            
            # Reload product to get updated stock
            updated_product = Product.get_by_id(test_product.id)
            new_stock = updated_product.stock_quantity
            print(f"   Stock after +{adjustment_qty}: {new_stock}")
            
            if new_stock == original_stock + adjustment_qty:
                print("✅ Stock adjustment successful")
            else:
                print(f"❌ Stock adjustment failed: expected {original_stock + adjustment_qty}, got {new_stock}")
                return False
            
            # Test negative adjustment
            negative_adjustment = -5
            updated_product.adjust_stock(
                negative_adjustment,
                'adjustment',
                None,
                'manual',
                'Test negative stock adjustment',
                1  # user_id
            )
            
            # Reload product again
            final_product = Product.get_by_id(test_product.id)
            final_stock = final_product.stock_quantity
            print(f"   Stock after {negative_adjustment}: {final_stock}")
            
            expected_final = new_stock + negative_adjustment
            if final_stock == expected_final:
                print("✅ Negative stock adjustment successful")
            else:
                print(f"❌ Negative stock adjustment failed: expected {expected_final}, got {final_stock}")
                return False
            
            # Test low stock detection
            low_stock_products = Product.get_low_stock()
            print(f"✅ Found {len(low_stock_products)} low stock products")
            
            return True
        else:
            print("⚠️ No products available for stock testing")
            return True
            
    except Exception as e:
        print(f"❌ Stock management test failed: {e}")
        return False

def test_product_categories(products_widget):
    """Test product categories functionality"""
    print("\n📂 Testing Product Categories...")
    
    try:
        # Test category operations
        categories = Category.get_all()
        print(f"✅ Found {len(categories)} categories")
        
        # Test creating a new category
        test_category_data = {
            'name': 'Test Category',
            'description': 'Test category for testing purposes'
        }
        
        new_category = Category.create(**test_category_data)
        if new_category:
            print(f"✅ Category created: {new_category.name}")
            
            # Test updating category
            new_category.update(name='Test Category Updated')
            print(f"✅ Category updated: {new_category.name}")
            
            # Test getting products by category
            category_products = Product.get_all(category_id=new_category.id)
            print(f"✅ Found {len(category_products)} products in category")
            
            # Test deleting category (soft delete)
            new_category.delete()
            # Check if category is marked as inactive
            updated_category = Category.get_by_id(new_category.id)
            if updated_category and not updated_category.is_active:
                print(f"✅ Category soft deleted successfully")
            else:
                print(f"❌ Failed to soft delete category")
                return False
        else:
            print(f"❌ Failed to create category")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Product categories test failed: {e}")
        return False

def test_product_search_and_barcode(products_widget):
    """Test product search and barcode functionality"""
    print("\n🔍 Testing Product Search and Barcode...")
    
    try:
        # Test search by different criteria
        products = Product.get_all()
        if products:
            test_product = products[0]
            
            # Test search by name
            name_results = Product.search(test_product.name[:4])
            print(f"✅ Search by name '{test_product.name[:4]}': {len(name_results)} results")
            
            # Test search by SKU
            if test_product.sku:
                sku_results = Product.search(test_product.sku)
                print(f"✅ Search by SKU '{test_product.sku}': {len(sku_results)} results")
            
            # Test search by barcode
            if test_product.barcode:
                barcode_results = Product.get_by_barcode(test_product.barcode)
                if barcode_results:
                    print(f"✅ Search by barcode '{test_product.barcode}': Found product")
                else:
                    print(f"⚠️ Search by barcode '{test_product.barcode}': No results")
            
            # Test case-insensitive search
            case_results = Product.search(test_product.name.upper())
            print(f"✅ Case-insensitive search: {len(case_results)} results")
            
            return True
        else:
            print("⚠️ No products available for search testing")
            return True
            
    except Exception as e:
        print(f"❌ Product search and barcode test failed: {e}")
        return False

def test_product_validation(products_widget):
    """Test product data validation"""
    print("\n✅ Testing Product Validation...")
    
    try:
        # Test creating product with invalid data
        invalid_data_tests = [
            {'name': '', 'selling_price': 10.0},  # Empty name
            {'name': 'Test', 'selling_price': -5.0},  # Negative price
            {'name': 'Test', 'selling_price': 10.0, 'stock_quantity': -1},  # Negative stock
        ]
        
        for i, invalid_data in enumerate(invalid_data_tests):
            try:
                invalid_product = Product.create(**invalid_data)
                if invalid_product:
                    print(f"⚠️ Test {i+1}: Invalid data was accepted (should be rejected)")
                    # Clean up
                    invalid_product.delete()
                else:
                    print(f"✅ Test {i+1}: Invalid data properly rejected")
            except Exception as e:
                print(f"✅ Test {i+1}: Invalid data caused expected error: {type(e).__name__}")
        
        # Test duplicate SKU/barcode
        products = Product.get_all()
        if products:
            existing_product = products[0]
            
            duplicate_data = {
                'name': 'Duplicate Test',
                'sku': existing_product.sku,
                'selling_price': 10.0
            }
            
            try:
                duplicate_product = Product.create(**duplicate_data)
                if duplicate_product:
                    print(f"⚠️ Duplicate SKU was accepted (should be rejected)")
                    duplicate_product.delete()
                else:
                    print(f"✅ Duplicate SKU properly rejected")
            except Exception as e:
                print(f"✅ Duplicate SKU caused expected error: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Product validation test failed: {e}")
        return False

def run_comprehensive_products_test():
    """Run all products tests"""
    print("🧪 STARTING COMPREHENSIVE PRODUCTS SYSTEM TEST")
    print("=" * 60)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    products_widget = None
    
    # Test 1: Database Setup
    test_results.append(("Database Setup", test_products_database_setup()))
    
    # Test 2: Widget Creation
    products_widget = test_products_widget_creation()
    test_results.append(("Widget Creation", products_widget is not False))
    
    if products_widget:
        # Test 3: Data Loading
        test_results.append(("Data Loading", test_products_data_loading(products_widget)))
        
        # Test 4: Products Filtering
        test_results.append(("Products Filtering", test_products_filtering(products_widget)))
        
        # Test 5: CRUD Operations
        test_results.append(("CRUD Operations", test_product_crud_operations(products_widget)))
        
        # Test 6: Stock Management
        test_results.append(("Stock Management", test_stock_management(products_widget)))
        
        # Test 7: Categories
        test_results.append(("Categories", test_product_categories(products_widget)))
        
        # Test 8: Search and Barcode
        test_results.append(("Search and Barcode", test_product_search_and_barcode(products_widget)))
        
        # Test 9: Validation
        test_results.append(("Validation", test_product_validation(products_widget)))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 PRODUCTS TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL PRODUCTS TESTS PASSED! Products system is working perfectly!")
    else:
        print("⚠️  Some products tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_comprehensive_products_test()
