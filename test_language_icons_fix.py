#!/usr/bin/env python3
"""
اختبار إصلاح مشكلة الأيقونات المكررة عند تغيير اللغة
Test Language Icons Duplication Fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.main_window import MainWindow
from models.user import User
from utils.translator import translator

def test_language_change_icons():
    """اختبار الأيقونات عند تغيير اللغة"""
    print("🔄 اختبار إصلاح الأيقونات المكررة")
    print("=" * 45)
    
    try:
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # الحصول على مستخدم للاختبار
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow(admin_user)
        
        print("🔍 فحص الأيقونات في اللغات المختلفة:")
        
        # اختبار اللغات المختلفة
        languages = [
            ('ar', 'العربية'),
            ('en', 'English'),
            ('fr', 'Français')
        ]
        
        all_correct = True
        
        for lang_code, lang_name in languages:
            print(f"\n📝 اختبار اللغة: {lang_name} ({lang_code})")
            
            # تغيير اللغة
            translator.set_language(lang_code)
            main_window.retranslate_ui()
            app.processEvents()
            
            # فحص أزرار التنقل
            expected_single_icons = {
                'pos': '🧾',
                'products': '📦',
                'sales': '💰',
                'customers': '🧍',
                'purchases': '📥',
                'users': '👤',
                'reports': '📊',
                'performance': '⚡',
                'settings': '🛠️'
            }
            
            for module_name, expected_icon in expected_single_icons.items():
                if module_name in main_window.nav_buttons:
                    button = main_window.nav_buttons[module_name]
                    button_text = button.text()
                    
                    # عد الأيقونات في النص
                    icon_count = button_text.count(expected_icon)
                    
                    if icon_count == 1:
                        print(f"   ✅ {module_name}: أيقونة واحدة فقط - {button_text}")
                    elif icon_count > 1:
                        print(f"   ❌ {module_name}: أيقونات مكررة ({icon_count}) - {button_text}")
                        all_correct = False
                    else:
                        print(f"   ⚠️ {module_name}: لا توجد أيقونة - {button_text}")
                        all_correct = False
            
            # فحص زر تسجيل الخروج
            logout_buttons = main_window.findChildren(type(main_window.nav_buttons['pos']))
            logout_found = False
            for button in logout_buttons:
                if button.objectName() == "danger_logout":
                    logout_text = button.text()
                    logout_icon_count = logout_text.count('🚪')
                    
                    if logout_icon_count == 1:
                        print(f"   ✅ تسجيل الخروج: أيقونة واحدة فقط - {logout_text}")
                    elif logout_icon_count > 1:
                        print(f"   ❌ تسجيل الخروج: أيقونات مكررة ({logout_icon_count}) - {logout_text}")
                        all_correct = False
                    else:
                        print(f"   ⚠️ تسجيل الخروج: لا توجد أيقونة - {logout_text}")
                        all_correct = False
                    
                    logout_found = True
                    break
            
            if not logout_found:
                print("   ⚠️ زر تسجيل الخروج غير موجود")
        
        print(f"\n📊 النتيجة النهائية: {'جميع الأيقونات صحيحة' if all_correct else 'توجد مشاكل في الأيقونات'}")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_translation_files():
    """اختبار ملفات الترجمة"""
    print("\n📁 اختبار ملفات الترجمة")
    print("=" * 30)
    
    try:
        import json
        
        translation_files = [
            ('translations/ar.json', 'العربية'),
            ('translations/en.json', 'English'),
            ('translations/fr.json', 'Français')
        ]
        
        all_clean = True
        
        for file_path, lang_name in translation_files:
            print(f"\n📄 فحص ملف: {lang_name}")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if 'navigation' in data:
                    nav_data = data['navigation']
                    
                    # فحص عدم وجود أيقونات في ملفات الترجمة
                    icons_found = []
                    for key, value in nav_data.items():
                        if any(icon in value for icon in ['🧾', '📦', '💰', '🧍', '📥', '👤', '📊', '⚡', '🛠️', '🚪']):
                            icons_found.append(f"{key}: {value}")
                    
                    if not icons_found:
                        print(f"   ✅ لا توجد أيقونات في ملف الترجمة")
                    else:
                        print(f"   ❌ توجد أيقونات في ملف الترجمة:")
                        for item in icons_found:
                            print(f"      - {item}")
                        all_clean = False
                else:
                    print(f"   ⚠️ قسم navigation غير موجود")
                    all_clean = False
                    
            except Exception as e:
                print(f"   ❌ خطأ في قراءة الملف: {e}")
                all_clean = False
        
        print(f"\n📊 نتيجة ملفات الترجمة: {'نظيفة من الأيقونات' if all_clean else 'تحتوي على أيقونات'}")
        
        return all_clean
        
    except Exception as e:
        print(f"❌ خطأ في فحص ملفات الترجمة: {e}")
        return False

def run_language_icons_test():
    """تشغيل جميع اختبارات الأيقونات واللغات"""
    print("🌍 بدء اختبار إصلاح الأيقونات المكررة")
    print("=" * 60)
    
    results = []
    
    # اختبار 1: ملفات الترجمة
    results.append(("ملفات الترجمة نظيفة", test_translation_files()))
    
    # اختبار 2: الأيقونات عند تغيير اللغة
    results.append(("الأيقونات عند تغيير اللغة", test_language_change_icons()))
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار إصلاح الأيقونات المكررة")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 تم إصلاح مشكلة الأيقونات المكررة بنجاح!")
        print("✨ الآن عند تغيير اللغة تظهر أيقونة واحدة فقط")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_language_icons_test()
