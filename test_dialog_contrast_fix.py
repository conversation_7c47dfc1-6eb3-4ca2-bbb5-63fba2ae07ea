#!/usr/bin/env python3
"""
اختبار إصلاح التباين في نوافذ الحوار - OnePos
Test Dialog Contrast Fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.products_widget import ProductDialog
from views.customers_widget import CustomerDialog
from views.purchases_widget import SupplierDialog
from views.users_widget import UserDialog
from models.user import User

def test_dialog_contrast():
    """اختبار إصلاح التباين في النوافذ"""
    print("🎨 اختبار إصلاح التباين في نوافذ الحوار")
    print("=" * 50)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # اختبار نافذة المنتجات
        print("✅ اختبار نافذة إضافة المنتج...")
        product_dialog = ProductDialog()
        
        # فحص التنسيق
        stylesheet = product_dialog.styleSheet()
        if "background-color: #2c3e50" in stylesheet:
            print("   ✅ خلفية النافذة: داكنة (#2c3e50)")
        else:
            print("   ❌ خلفية النافذة: غير محددة")
            return False
        
        if "color: #ffffff !important" in stylesheet:
            print("   ✅ لون النصوص: أبيض (#ffffff)")
        else:
            print("   ❌ لون النصوص: غير محدد")
            return False
        
        if "QLineEdit" in stylesheet and "background-color: #ffffff" in stylesheet:
            print("   ✅ حقول الإدخال: خلفية بيضاء")
        else:
            print("   ❌ حقول الإدخال: غير منسقة")
            return False
        
        product_dialog.close()
        
        # اختبار نافذة العملاء
        print("\n✅ اختبار نافذة إضافة العميل...")
        customer_dialog = CustomerDialog()
        
        stylesheet = customer_dialog.styleSheet()
        if "background-color: #2c3e50" in stylesheet and "color: #ffffff" in stylesheet:
            print("   ✅ تنسيق نافذة العملاء: صحيح")
        else:
            print("   ❌ تنسيق نافذة العملاء: خاطئ")
            return False
        
        customer_dialog.close()
        
        # اختبار نافذة الموردين
        print("\n✅ اختبار نافذة إضافة المورد...")
        supplier_dialog = SupplierDialog()
        
        stylesheet = supplier_dialog.styleSheet()
        if "background-color: #2c3e50" in stylesheet and "QGroupBox" in stylesheet:
            print("   ✅ تنسيق نافذة الموردين: صحيح")
        else:
            print("   ❌ تنسيق نافذة الموردين: خاطئ")
            return False
        
        supplier_dialog.close()
        
        # اختبار نافذة المستخدمين
        print("\n✅ اختبار نافذة إضافة المستخدم...")
        user_dialog = UserDialog()
        
        stylesheet = user_dialog.styleSheet()
        if "background-color: #2c3e50" in stylesheet and "QCheckBox" in stylesheet:
            print("   ✅ تنسيق نافذة المستخدمين: صحيح")
        else:
            print("   ❌ تنسيق نافذة المستخدمين: خاطئ")
            return False
        
        user_dialog.close()
        
        print("\n🎉 جميع النوافذ تم إصلاحها بنجاح!")
        print("   📝 الخلفية: داكنة (#2c3e50)")
        print("   📝 النصوص: بيضاء (#ffffff)")
        print("   📝 حقول الإدخال: خلفية بيضاء مع نص أسود")
        print("   📝 الأزرار: زرقاء مع نص أبيض")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التباين: {e}")
        return False

def test_label_contrast():
    """اختبار تباين العناوين التوضيحية"""
    print("\n🏷️ اختبار تباين العناوين التوضيحية")
    print("=" * 45)
    
    try:
        from views.products_widget import create_dark_label
        
        # إنشاء عنوان تجريبي
        label = create_dark_label("اسم المنتج:")
        
        # فحص التنسيق
        stylesheet = label.styleSheet()
        if "color: #ffffff !important" in stylesheet:
            print("✅ لون العناوين: أبيض (#ffffff)")
        else:
            print("❌ لون العناوين: غير صحيح")
            return False
        
        if "font-weight: 700" in stylesheet:
            print("✅ سماكة الخط: عريض (700)")
        else:
            print("❌ سماكة الخط: غير محددة")
            return False
        
        if "font-size: 13px" in stylesheet:
            print("✅ حجم الخط: 13px")
        else:
            print("❌ حجم الخط: غير محدد")
            return False
        
        print("🎉 العناوين التوضيحية محسنة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العناوين: {e}")
        return False

def test_contrast_comprehensive():
    """اختبار شامل لإصلاح التباين"""
    print("🎨 بدء الاختبار الشامل لإصلاح التباين في النوافذ")
    print("=" * 60)
    
    results = []
    
    # اختبار 1: نوافذ الحوار
    results.append(("نوافذ الحوار", test_dialog_contrast()))
    
    # اختبار 2: العناوين التوضيحية
    results.append(("العناوين التوضيحية", test_label_contrast()))
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار إصلاح التباين")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 إصلاح التباين مكتمل بنجاح!")
        print("✨ جميع النوافذ أصبحت واضحة ومقروءة")
        print("\n📋 ملخص الإصلاحات:")
        print("   🎨 خلفية النوافذ: داكنة (#2c3e50)")
        print("   📝 العناوين: بيضاء (#ffffff) عريضة")
        print("   📄 حقول الإدخال: بيضاء مع نص أسود")
        print("   🔘 الأزرار: زرقاء مع نص أبيض")
        print("   ✅ تباين عالي للقراءة المثلى")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    test_contrast_comprehensive()
