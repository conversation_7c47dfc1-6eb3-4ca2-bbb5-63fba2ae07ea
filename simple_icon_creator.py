#!/usr/bin/env python3
"""
إنشاء أيقونة بسيطة لـ OnePos
Simple Icon Creator for OnePos
"""

def create_simple_ico():
    """إنشاء ملف ICO بسيط"""
    try:
        from PIL import Image, ImageDraw

        # إنشاء صورة 64x64
        size = 64
        img = Image.new('RGBA', (size, size), (52, 152, 219, 255))  # خلفية زرقاء
        draw = ImageDraw.Draw(img)

        # رسم دائرة بيضاء في المنتصف
        center = size // 2
        radius = size // 4
        draw.ellipse([
            center - radius, center - radius,
            center + radius, center + radius
        ], fill=(255, 255, 255, 255))

        # رسم نقطة حمراء صغيرة
        dot_size = 4
        draw.ellipse([
            center + radius//2 - dot_size, center - radius//2 - dot_size,
            center + radius//2 + dot_size, center - radius//2 + dot_size
        ], fill=(231, 76, 60, 255))

        # حفظ كـ ICO
        img.save('app_icon.ico', format='ICO', sizes=[(64, 64)])
        print("✅ تم إنشاء app_icon.ico بنجاح")
        return True

    except ImportError:
        print("⚠️ PIL غير متاح، سأنشئ ملف ICO افتراضي")
        return create_default_ico()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return create_default_ico()

def create_default_ico():
    """إنشاء ملف ICO افتراضي بسيط"""
    try:
        # بيانات ICO بسيطة (16x16 أزرق)
        ico_data = bytes([
            0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x10, 0x10, 0x00, 0x00, 0x01, 0x00, 0x20, 0x00, 0x68, 0x04,
            0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00,
            0x00, 0x00, 0x01, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
        ])

        # إضافة بيانات الصورة (أزرق بسيط)
        pixel_data = []
        for y in range(16):
            for x in range(16):
                # لون أزرق (BGRA format)
                pixel_data.extend([219, 152, 52, 255])  # أزرق

        ico_data += bytes(pixel_data)

        # إضافة mask (شفافية)
        mask_data = [0x00] * (16 * 16 // 8)  # كل البكسلات مرئية
        ico_data += bytes(mask_data)

        # كتابة الملف
        with open('app_icon.ico', 'wb') as f:
            f.write(ico_data)

        print("✅ تم إنشاء ملف ICO افتراضي")
        return True

    except Exception as e:
        print(f"❌ فشل في إنشاء ICO افتراضي: {e}")
        return False

def create_text_ico():
    """إنشاء ملف نصي يحتوي على بيانات ICO"""
    try:
        # إنشاء ملف ICO كنص base64
        ico_base64 = """
AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
"""

        import base64
        ico_data = base64.b64decode(ico_base64.strip())

        with open('app_icon.ico', 'wb') as f:
            f.write(ico_data)

        print("✅ تم إنشاء أيقونة من البيانات المحفوظة")
        return True

    except Exception as e:
        print(f"❌ فشل في إنشاء الأيقونة من النص: {e}")
        return False

if __name__ == "__main__":
    print("🎨 إنشاء أيقونة OnePos")
    print("=" * 30)

    # محاولة الطرق المختلفة
    success = False

    # الطريقة الأولى: PIL
    print("🔄 محاولة إنشاء أيقونة بـ PIL...")
    success = create_simple_ico()

    if not success:
        print("🔄 محاولة إنشاء أيقونة افتراضية...")
        success = create_text_ico()

    if not success:
        print("🔄 محاولة إنشاء ملف ICO بسيط...")
        success = create_default_ico()

    if success:
        print("\n🎉 تم إنشاء app_icon.ico بنجاح!")
        print("📁 الملف موجود في المجلد الرئيسي")
        print("🚀 يمكنك الآن تشغيل build_complete.bat")
    else:
        print("\n❌ فشل في إنشاء الأيقونة")
        print("💡 يمكنك تحميل أي ملف .ico من الإنترنت")
        print("📝 أو استخدام أي أيقونة موجودة")

    print("=" * 30)