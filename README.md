# OnePos - Professional Point of Sale System

A comprehensive, offline-capable Point of Sale (POS) system built with Python and PyQt5 for retail and commercial store management.

## 🌟 Features

### Core POS Features
- **Point of Sale Interface**: Fast and intuitive sales processing
- **Barcode Support**: USB scanner and camera-based barcode scanning
- **Multi-language**: Arabic (RTL), French, and English support
- **Offline Operation**: Works completely offline with local SQLite database
- **Thermal Printing**: Receipt, kitchen, and label printing support
- **Multi-payment**: Cash, card, and split payment options

### Inventory Management
- **Product Management**: Add, edit, delete products with categories
- **Stock Tracking**: Real-time inventory with low stock alerts
- **Barcode Generation**: Automatic barcode generation and label printing
- **Stock Movements**: Track all inventory changes with detailed logs
- **Import/Export**: Excel integration for bulk operations

### Customer & Sales
- **Customer Management**: Detailed customer profiles with purchase history
- **Sales Tracking**: Complete sales history with filtering and search
- **Invoice Management**: Professional invoice generation and printing
- **Returns & Refunds**: Handle returns and refund processing
- **Credit Management**: Customer credit limits and balance tracking

### Reports & Analytics
- **Sales Reports**: Daily, weekly, monthly, and yearly reports
- **Inventory Reports**: Stock levels, movements, and valuations
- **Customer Reports**: Purchase history and loyalty analytics
- **Export Options**: PDF and Excel export capabilities

### Security & Users
- **User Management**: Role-based access control (Admin, Cashier, etc.)
- **Secure Authentication**: Encrypted passwords and session management
- **Activity Logging**: Track all user activities and changes
- **Backup & Restore**: Automatic and manual database backups

## 🛠️ Technology Stack

- **Python 3.11+**: Core programming language
- **PyQt5**: GUI framework for desktop application
- **SQLite3**: Local embedded database
- **QDarkStyle**: Modern dark/light theme support
- **PyBarcode**: Barcode generation
- **Pyzbar + OpenCV**: Barcode scanning via camera
- **python-escpos**: ESC/POS thermal printer support
- **ReportLab**: PDF generation
- **OpenPyXL**: Excel file handling
- **BCrypt**: Password encryption

## 📁 Project Structure

```
OnePos/
├── main.py                 # Application entry point
├── config.json            # Configuration file
├── requirements.txt       # Python dependencies
├── test_setup.py          # Setup verification script
├── README.md              # This file
│
├── models/                 # Database models
│   ├── __init__.py
│   ├── database.py        # Database connection and setup
│   ├── user.py           # User and authentication models
│   ├── product.py        # Product and category models
│   ├── customer.py       # Customer management
│   ├── sale.py           # Sales and sale items
│   └── stock.py          # Stock movements and adjustments
│
├── views/                  # UI components
│   ├── __init__.py
│   ├── main_window.py    # Main application window
│   ├── login_dialog.py   # Login dialog
│   └── [other views]     # Additional UI modules
│
├── utils/                  # Utility modules
│   ├── __init__.py
│   ├── config_manager.py # Configuration management
│   ├── translator.py     # Multi-language support
│   └── barcode_utils.py  # Barcode generation and scanning
│
├── ui/                     # Qt Designer UI files
├── assets/                # Images, icons, sounds
│   ├── icons/
│   ├── images/
│   └── sounds/
│
├── translations/          # Language files
│   ├── ar.json           # Arabic translations
│   ├── en.json           # English translations
│   └── fr.json           # French translations
│
├── data/                  # Database files
└── backups/              # Database backups
```

## 🚀 Installation & Setup

### Prerequisites
- Python 3.11 or higher
- Windows 10/11 (primary target)
- Visual Studio Code (recommended)

### Quick Start (Recommended)
```bash
# Clone or download the project
git clone <repository-url>
cd OnePos

# Run the development setup script
python setup_dev.py
```

The setup script will:
- Install all required dependencies
- Create sample data for testing
- Configure development settings
- Run verification tests
- Optionally start the application

### Manual Setup

#### Step 1: Clone or Download
```bash
git clone <repository-url>
cd OnePos
```

#### Step 2: Install Dependencies
```bash
pip install -r requirements.txt
```

#### Step 3: Verify Setup
```bash
python test_setup.py
```

#### Step 4: Run Application
```bash
python main.py
# Or use the batch file on Windows
run.bat
# Or use the shell script on Linux/Mac
./run.sh
```

## 🔐 Default Login

- **Username**: `admin`
- **Password**: `admin123`

⚠️ **Important**: Change the default password after first login!

## 📖 Usage Guide

### First Time Setup
1. Run the application and login with default credentials
2. Go to Settings → Company to configure your business details
3. Set up printers in Settings → Printers
4. Configure tax rates and currency in Settings → General
5. Add product categories in Products module
6. Import or add your products
7. Start selling!

### Daily Operations
1. **Making Sales**: Use the POS module to process sales
2. **Adding Products**: Use barcode scanner or manual entry
3. **Customer Selection**: Choose existing customer or walk-in
4. **Payment Processing**: Accept cash, card, or split payments
5. **Receipt Printing**: Automatic receipt printing after sale

### Inventory Management
1. **Add Products**: Use Products module to add new items
2. **Stock Adjustments**: Adjust quantities when needed
3. **Low Stock Alerts**: Monitor products running low
4. **Barcode Labels**: Print labels for new products

### Reports
1. **Daily Reports**: Check daily sales performance
2. **Inventory Reports**: Monitor stock levels and movements
3. **Customer Reports**: Analyze customer purchase patterns
4. **Export Data**: Export reports to PDF or Excel

## 🖨️ Printer Setup

### Thermal Receipt Printers
1. Connect USB thermal printer
2. Go to Settings → Printers → Receipt Printer
3. Configure printer settings (width, interface)
4. Test print to verify setup

### Kitchen Printers
1. Set up separate thermal printer for kitchen orders
2. Configure in Settings → Printers → Kitchen Printer
3. Enable kitchen printing for relevant products

### Label Printers
1. Connect label printer for barcode labels
2. Configure in Settings → Printers → Label Printer
3. Print product labels as needed

## 📱 Barcode Scanning

### USB Barcode Scanner
1. Connect USB barcode scanner
2. Scanner should work in keyboard emulation mode
3. Scan products directly in POS interface

### Camera Scanning
1. Enable camera scanning in Settings → Barcode
2. Use camera icon in POS to scan barcodes
3. Requires good lighting for best results

## 🌐 Multi-Language Support

### Switching Languages
1. Go to Settings → General → Language
2. Select Arabic, French, or English
3. Restart application for full effect

### Arabic Support
- Right-to-left (RTL) layout
- Arabic number formatting
- Proper text alignment

## 💾 Backup & Restore

### Automatic Backups
- Configured in Settings → Backup
- Daily automatic backups (configurable)
- Stored in `backups/` directory

### Manual Backup
1. Go to Settings → Backup
2. Click "Create Backup"
3. Save backup file to safe location

### Restore
1. Go to Settings → Backup
2. Click "Restore from Backup"
3. Select backup file to restore

## 🔧 Configuration

### Main Configuration (config.json)
- Application settings
- Company information
- Printer configurations
- UI preferences
- Security settings

### Database Configuration
- SQLite database location
- Backup settings
- Connection parameters

## 🚀 Building Executable

### Automated Build (Recommended)
```bash
# Run the build script
python build_exe.py
```

The build script will:
- Install PyInstaller if needed
- Create a spec file with proper configuration
- Build the executable with all dependencies
- Copy necessary files and create installer
- Generate installation script

### Manual Build with PyInstaller
```bash
# Install PyInstaller
pip install pyinstaller

# Create executable (basic)
pyinstaller --onefile --windowed --name OnePos main.py

# Or use the generated spec file for advanced options
pyinstaller onepos.spec
```

### Distribution
After building:
1. The executable will be in `dist/OnePos.exe`
2. Run `dist/install.bat` as administrator to install
3. Or copy the entire `dist/` folder to target computer
4. All necessary files are included automatically

## 🐛 Troubleshooting

### Common Issues

**Database Errors**
- Check if `data/` directory exists
- Verify database file permissions
- Run `test_setup.py` to diagnose

**Printer Issues**
- Verify USB connection
- Check printer drivers
- Test with printer manufacturer software

**Barcode Scanner Issues**
- Ensure scanner is in keyboard mode
- Check USB connection
- Test scanner in text editor

**Language Issues**
- Verify translation files exist
- Check file encoding (UTF-8)
- Restart application after language change

### Getting Help
1. Check the troubleshooting section
2. Review log files in application directory
3. Run diagnostic tests with `test_setup.py`

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the troubleshooting guide

---

**OnePos** - Professional Point of Sale System
Built with ❤️ using Python and PyQt5
