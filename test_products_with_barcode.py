"""
Comprehensive Re-testing for OnePos Products System with Barcode Printing
Tests all product functionality including the new barcode printing feature
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QPoint, Qt
from models.database import db
from models.product import Product, Category
from models.user import User
from views.products_widget import ProductsWidget
from utils.barcode_generator import BarcodeGenerator, BarcodePrintDialog
from datetime import datetime

def test_products_widget_with_barcode():
    """Test products widget with barcode functionality"""
    print("🖥️ Testing Products Widget with Barcode Features...")
    
    try:
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create products widget
        products_widget = ProductsWidget()
        
        if products_widget:
            print("✅ ProductsWidget created successfully")
            
            # Check main components
            if hasattr(products_widget, 'products_table'):
                print("✅ Products table exists")
                columns = products_widget.products_table.columnCount()
                print(f"   - Columns: {columns}")
                
                # Check context menu
                if hasattr(products_widget, 'show_context_menu'):
                    print("✅ Context menu function exists")
                else:
                    print("❌ Context menu function missing")
            
            # Check barcode-related buttons
            barcode_buttons = [
                'print_labels_button'
            ]
            
            for button_name in barcode_buttons:
                if hasattr(products_widget, button_name):
                    print(f"✅ {button_name} exists")
                else:
                    print(f"❌ {button_name} missing")
            
            # Check barcode functions
            barcode_functions = [
                'print_labels',
                'show_context_menu'
            ]
            
            for func_name in barcode_functions:
                if hasattr(products_widget, func_name):
                    print(f"✅ {func_name} function exists")
                else:
                    print(f"❌ {func_name} function missing")
            
            return products_widget
        else:
            print("❌ Failed to create ProductsWidget")
            return False
            
    except Exception as e:
        print(f"❌ Products widget with barcode test failed: {e}")
        return False

def test_barcode_integration_in_products(products_widget):
    """Test barcode integration in products widget"""
    print("\n🏷️ Testing Barcode Integration in Products Widget...")
    
    try:
        # Load products
        products_widget.load_products()
        products_count = products_widget.products_table.rowCount()
        print(f"✅ Loaded {products_count} products")
        
        # Test selecting a product
        if products_count > 0:
            products_widget.products_table.selectRow(0)
            selected_product = products_widget.get_selected_product()
            
            if selected_product:
                print(f"✅ Selected product: {selected_product.name}")
                print(f"   - Barcode: {selected_product.barcode or 'None'}")
                
                # Test print labels function
                try:
                    # This should work if product has barcode
                    if selected_product.barcode:
                        print("✅ Product has barcode - print function should work")
                        # Don't actually call print_labels to avoid opening dialog
                        print("✅ Print labels function available")
                    else:
                        print("⚠️ Product has no barcode - print function should show warning")
                        # Test the function to see if it handles missing barcode
                        try:
                            products_widget.print_labels()
                            print("✅ Print labels handles missing barcode gracefully")
                        except Exception as e:
                            print(f"⚠️ Print labels error with missing barcode: {e}")
                
                except Exception as e:
                    print(f"❌ Print labels function error: {e}")
                    return False
            else:
                print("❌ Failed to get selected product")
                return False
        
        # Test context menu simulation
        try:
            # Simulate right-click on first row
            if products_count > 0:
                # Get position of first item
                item = products_widget.products_table.item(0, 0)
                if item:
                    rect = products_widget.products_table.visualItemRect(item)
                    position = rect.center()
                    
                    # Test context menu function (don't actually show menu)
                    print("✅ Context menu position calculated")
                    print("✅ Context menu function ready")
                else:
                    print("⚠️ Could not get table item for context menu test")
        
        except Exception as e:
            print(f"⚠️ Context menu test error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Barcode integration test failed: {e}")
        return False

def test_barcode_dialog_integration():
    """Test barcode dialog integration with products"""
    print("\n📱 Testing Barcode Dialog Integration...")
    
    try:
        # Get a product with barcode
        products = Product.get_all()
        test_product = None
        
        for product in products:
            if product.barcode:
                test_product = product
                break
        
        if not test_product:
            # Create a product with barcode for testing
            test_product_data = {
                'name': 'Barcode Test Product',
                'sku': 'BTP001',
                'barcode': '1234567890123',
                'selling_price': 19.99,
                'cost_price': 12.00,
                'stock_quantity': 50,
                'min_stock_level': 5,
                'category_id': 1,
                'description': 'Product for barcode testing'
            }
            
            test_product = Product.create(**test_product_data)
            if test_product:
                print(f"✅ Created test product with barcode: {test_product.name}")
            else:
                print("❌ Failed to create test product")
                return False
        
        print(f"✅ Testing with product: {test_product.name}")
        print(f"   - Barcode: {test_product.barcode}")
        
        # Test dialog creation
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            dialog = BarcodePrintDialog(test_product)
            print("✅ Barcode dialog created successfully")
            
            # Test dialog components
            components = [
                'copies_spin',
                'format_combo', 
                'include_price_combo',
                'preview_label'
            ]
            
            for component in components:
                if hasattr(dialog, component):
                    print(f"✅ Dialog component {component} exists")
                else:
                    print(f"❌ Dialog component {component} missing")
            
            # Test preview generation
            try:
                dialog.generate_preview()
                print("✅ Preview generation successful")
            except Exception as e:
                print(f"⚠️ Preview generation error: {e}")
            
            # Test dialog methods
            methods = ['generate_preview', 'print_labels', 'save_image']
            for method in methods:
                if hasattr(dialog, method):
                    print(f"✅ Dialog method {method} exists")
                else:
                    print(f"❌ Dialog method {method} missing")
            
            return True
            
        except Exception as e:
            print(f"❌ Dialog creation error: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Barcode dialog integration test failed: {e}")
        return False

def test_products_crud_with_barcode():
    """Test product CRUD operations with barcode functionality"""
    print("\n📝 Testing Product CRUD with Barcode...")
    
    try:
        # Test creating product with barcode
        import time
        import random
        timestamp = str(int(time.time()))[-6:]
        random_suffix = str(random.randint(1000, 9999))

        # Ensure unique barcode and SKU
        unique_barcode = f'987654{timestamp}{random_suffix}'[:13]
        unique_sku = f'CTP-{timestamp}-{random_suffix}'

        test_product_data = {
            'name': f'CRUD Test Product {timestamp}',
            'sku': unique_sku,
            'barcode': unique_barcode,
            'selling_price': 29.99,
            'cost_price': 18.00,
            'stock_quantity': 75,
            'min_stock_level': 8,
            'category_id': 1,
            'description': 'Product for CRUD testing with barcode'
        }
        
        new_product = Product.create(**test_product_data)
        if new_product:
            print(f"✅ Product created with barcode: {new_product.name}")
            print(f"   - Barcode: {new_product.barcode}")
            
            # Test barcode lookup
            found_product = Product.get_by_barcode(new_product.barcode)
            if found_product and found_product.id == new_product.id:
                print("✅ Barcode lookup successful")
            else:
                print("❌ Barcode lookup failed")
                return False
            
            # Test updating product (keeping barcode)
            updated_data = {
                'name': f'Updated CRUD Test Product {timestamp}',
                'selling_price': 34.99
            }
            new_product.update(**updated_data)
            print(f"✅ Product updated: {new_product.name}")
            
            # Verify barcode still exists after update
            updated_product = Product.get_by_id(new_product.id)
            if updated_product.barcode == new_product.barcode:
                print("✅ Barcode preserved after update")
            else:
                print("❌ Barcode lost after update")
                return False
            
            # Test barcode generation for product
            try:
                barcode_image = BarcodeGenerator.generate_barcode_image(
                    new_product.barcode,
                    new_product.name,
                    f"{new_product.selling_price:.2f}"
                )
                
                if barcode_image:
                    print("✅ Barcode image generated for product")
                else:
                    print("❌ Failed to generate barcode image")
                    return False
                    
            except ImportError:
                print("⚠️ Barcode library not available")
            except Exception as e:
                print(f"❌ Barcode generation error: {e}")
                return False
            
            # Test soft delete (barcode should be preserved)
            new_product.delete()
            deleted_product = Product.get_by_id(new_product.id)
            if deleted_product and not deleted_product.is_active:
                print("✅ Product soft deleted successfully")
                if deleted_product.barcode == new_product.barcode:
                    print("✅ Barcode preserved after soft delete")
                else:
                    print("❌ Barcode lost after soft delete")
                    return False
            else:
                print("❌ Product soft delete failed")
                return False
            
        else:
            print("❌ Failed to create product with barcode")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Product CRUD with barcode test failed: {e}")
        return False

def test_barcode_error_handling():
    """Test error handling in barcode functionality"""
    print("\n⚠️ Testing Barcode Error Handling...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        products_widget = ProductsWidget()
        
        # Test print labels with no selection
        products_widget.products_table.clearSelection()
        try:
            products_widget.print_labels()
            print("✅ Print labels handles no selection gracefully")
        except Exception as e:
            print(f"⚠️ Print labels error with no selection: {e}")
        
        # Test with product without barcode
        products = Product.get_all()
        product_without_barcode = None
        
        for product in products:
            if not product.barcode:
                product_without_barcode = product
                break
        
        if not product_without_barcode:
            # Create product without barcode
            import random
            random_id = str(random.randint(10000, 99999))
            test_data = {
                'name': f'No Barcode Product {random_id}',
                'sku': f'NBP-{random_id}',
                'selling_price': 15.99,
                'stock_quantity': 25
            }
            product_without_barcode = Product.create(**test_data)
        
        if product_without_barcode:
            print(f"✅ Testing with product without barcode: {product_without_barcode.name}")
            
            # Test dialog creation with product without barcode
            try:
                dialog = BarcodePrintDialog(product_without_barcode)
                dialog.generate_preview()
                print("✅ Dialog handles missing barcode gracefully")
            except Exception as e:
                print(f"⚠️ Dialog error with missing barcode: {e}")
        
        # Test invalid barcode data
        try:
            invalid_barcode = ""
            barcode_image = BarcodeGenerator.generate_barcode_image(invalid_barcode, "Test", "10.00")
            print("⚠️ Empty barcode was accepted (should be rejected)")
        except Exception as e:
            print(f"✅ Empty barcode properly rejected: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Barcode error handling test failed: {e}")
        return False

def test_products_performance_with_barcode():
    """Test products performance with barcode features"""
    print("\n⚡ Testing Products Performance with Barcode...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        products_widget = ProductsWidget()
        
        # Test loading performance
        import time
        start_time = time.time()
        products_widget.load_products()
        load_time = time.time() - start_time
        
        products_count = products_widget.products_table.rowCount()
        print(f"✅ Loaded {products_count} products in {load_time:.3f} seconds")
        
        if load_time < 2.0:  # Should load quickly
            print("✅ Loading performance acceptable")
        else:
            print("⚠️ Loading performance slow")
        
        # Test barcode generation performance
        products = Product.get_all()
        barcode_products = [p for p in products if p.barcode]
        
        if barcode_products:
            test_product = barcode_products[0]
            
            start_time = time.time()
            try:
                barcode_image = BarcodeGenerator.generate_barcode_image(
                    test_product.barcode,
                    test_product.name,
                    f"{test_product.selling_price:.2f}"
                )
                generation_time = time.time() - start_time
                
                print(f"✅ Barcode generated in {generation_time:.3f} seconds")
                
                if generation_time < 1.0:  # Should be fast
                    print("✅ Barcode generation performance good")
                else:
                    print("⚠️ Barcode generation performance slow")
                    
            except ImportError:
                print("⚠️ Barcode library not available for performance test")
            except Exception as e:
                print(f"❌ Barcode generation performance test failed: {e}")
                return False
        
        # Test search performance with barcodes
        start_time = time.time()
        products_widget.search_edit.setText("123")
        products_widget.search_products()
        search_time = time.time() - start_time
        
        search_results = products_widget.products_table.rowCount()
        print(f"✅ Search completed in {search_time:.3f} seconds ({search_results} results)")
        
        if search_time < 0.5:  # Should be very fast
            print("✅ Search performance excellent")
        else:
            print("⚠️ Search performance could be improved")
        
        return True
        
    except Exception as e:
        print(f"❌ Products performance test failed: {e}")
        return False

def run_comprehensive_products_retest():
    """Run comprehensive products re-test with barcode features"""
    print("🔍 STARTING COMPREHENSIVE PRODUCTS RE-TEST WITH BARCODE")
    print("=" * 65)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    products_widget = None
    
    # Test 1: Widget with Barcode Features
    products_widget = test_products_widget_with_barcode()
    test_results.append(("Widget with Barcode", products_widget is not False))
    
    if products_widget:
        # Test 2: Barcode Integration
        test_results.append(("Barcode Integration", test_barcode_integration_in_products(products_widget)))
    
    # Test 3: Barcode Dialog Integration
    test_results.append(("Dialog Integration", test_barcode_dialog_integration()))
    
    # Test 4: CRUD with Barcode
    test_results.append(("CRUD with Barcode", test_products_crud_with_barcode()))
    
    # Test 5: Error Handling
    test_results.append(("Error Handling", test_barcode_error_handling()))
    
    # Test 6: Performance
    test_results.append(("Performance", test_products_performance_with_barcode()))
    
    # Print results summary
    print("\n" + "=" * 65)
    print("🏁 PRODUCTS RE-TEST RESULTS WITH BARCODE")
    print("=" * 65)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 65)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL PRODUCTS RE-TESTS PASSED! System is stable with barcode features!")
    else:
        print("⚠️  Some re-tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_comprehensive_products_retest()
