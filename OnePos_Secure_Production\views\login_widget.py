"""
Enhanced Login Widget for OnePos POS System
Secure authentication with advanced security features
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QCheckBox, QFrame,
                            QMessageBox, QProgressBar, QGroupBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPixmap
from models.user import User
from utils.translator import tr
import time


class LoginAttemptThread(QThread):
    """Thread for handling login attempts with security checks"""
    
    login_result = pyqtSignal(object, str)  # user, error_message
    
    def __init__(self, username, password, ip_address=""):
        super().__init__()
        self.username = username
        self.password = password
        self.ip_address = ip_address
    
    def run(self):
        """Perform login attempt"""
        try:
            # Add small delay to prevent brute force attacks
            time.sleep(0.5)
            
            user = User.authenticate(self.username, self.password, self.ip_address)
            
            if user:
                self.login_result.emit(user, "")
            else:
                self.login_result.emit(None, tr("login.invalid_credentials"))
                
        except Exception as e:
            self.login_result.emit(None, str(e))


class LoginWidget(QWidget):
    """Enhanced login widget with security features"""
    
    login_successful = pyqtSignal(object)  # user
    
    def __init__(self):
        super().__init__()
        self.login_thread = None
        self.failed_attempts = 0
        self.max_attempts = 5
        self.lockout_time = 300  # 5 minutes
        self.last_attempt_time = 0
        
        self.init_ui()
        self.setup_security_timer()
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setFixedSize(450, 600)
        self.setWindowTitle(tr("login.title"))
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # Logo and title
        self.create_header(main_layout)
        
        # Login form
        self.create_login_form(main_layout)
        
        # Security info
        self.create_security_info(main_layout)
        
        # Footer
        self.create_footer(main_layout)
        
        # Apply styling
        self.apply_styling()
    
    def create_header(self, layout):
        """Create header with logo and title"""
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignCenter)
        
        # Logo placeholder
        logo_label = QLabel("🏪")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setFont(QFont("Segoe UI", 48))
        logo_label.setStyleSheet("color: #0d6efd; margin-bottom: 10px;")
        header_layout.addWidget(logo_label)
        
        # Title
        title_label = QLabel("OnePos")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Segoe UI", 24, QFont.Bold))
        title_label.setStyleSheet("color: #000000; font-weight: 800; margin-bottom: 5px;")
        header_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel(tr("login.subtitle"))
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setFont(QFont("Segoe UI", 12))
        subtitle_label.setStyleSheet("color: #6c757d; margin-bottom: 20px;")
        header_layout.addWidget(subtitle_label)
        
        layout.addLayout(header_layout)
    
    def create_login_form(self, layout):
        """Create login form"""
        form_group = QGroupBox()
        form_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 20px;
                background-color: #ffffff;
            }
        """)
        
        form_layout = QVBoxLayout(form_group)
        form_layout.setSpacing(15)
        
        # Username field
        username_layout = QVBoxLayout()
        username_label = QLabel(tr("login.username"))
        username_label.setStyleSheet("color: #000000; font-weight: 700; font-size: 13px; margin-bottom: 5px;")
        username_layout.addWidget(username_label)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText(tr("login.enter_username"))
        self.username_edit.setFont(QFont("Segoe UI", 12))
        self.username_edit.setFixedHeight(45)
        self.username_edit.returnPressed.connect(self.login)
        username_layout.addWidget(self.username_edit)
        form_layout.addLayout(username_layout)
        
        # Password field
        password_layout = QVBoxLayout()
        password_label = QLabel(tr("login.password"))
        password_label.setStyleSheet("color: #000000; font-weight: 700; font-size: 13px; margin-bottom: 5px;")
        password_layout.addWidget(password_label)
        
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText(tr("login.enter_password"))
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setFont(QFont("Segoe UI", 12))
        self.password_edit.setFixedHeight(45)
        self.password_edit.returnPressed.connect(self.login)
        password_layout.addWidget(self.password_edit)
        form_layout.addLayout(password_layout)
        
        # Remember me checkbox
        self.remember_check = QCheckBox(tr("login.remember_me"))
        self.remember_check.setStyleSheet("color: #000000; font-weight: 600; font-size: 12px;")
        form_layout.addWidget(self.remember_check)
        
        # Login button
        self.login_button = QPushButton(tr("login.sign_in"))
        self.login_button.setFont(QFont("Segoe UI", 14, QFont.Bold))
        self.login_button.setFixedHeight(50)
        self.login_button.clicked.connect(self.login)
        form_layout.addWidget(self.login_button)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setFixedHeight(6)
        form_layout.addWidget(self.progress_bar)
        
        layout.addWidget(form_group)
    
    def create_security_info(self, layout):
        """Create security information panel"""
        security_group = QGroupBox(tr("login.security_info"))
        security_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                background-color: #f8f9fa;
                font-weight: bold;
                color: #000000;
            }
            QGroupBox::title {
                color: #000000;
                font-weight: 700;
                font-size: 12px;
            }
        """)
        
        security_layout = QVBoxLayout(security_group)
        
        self.security_label = QLabel(tr("login.security_status"))
        self.security_label.setStyleSheet("color: #000000; font-size: 11px; font-weight: 600;")
        self.security_label.setWordWrap(True)
        security_layout.addWidget(self.security_label)
        
        # Lockout timer
        self.lockout_label = QLabel()
        self.lockout_label.setStyleSheet("color: #dc3545; font-weight: 700; font-size: 12px;")
        self.lockout_label.setVisible(False)
        security_layout.addWidget(self.lockout_label)
        
        layout.addWidget(security_group)
    
    def create_footer(self, layout):
        """Create footer"""
        footer_layout = QVBoxLayout()
        footer_layout.setAlignment(Qt.AlignCenter)
        
        # Version info
        version_label = QLabel("OnePos v1.0 - Secure POS System")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("color: #6c757d; font-size: 10px; margin-top: 20px;")
        footer_layout.addWidget(version_label)
        
        # Security notice
        security_notice = QLabel(tr("login.security_notice"))
        security_notice.setAlignment(Qt.AlignCenter)
        security_notice.setStyleSheet("color: #6c757d; font-size: 9px; margin-top: 5px;")
        security_notice.setWordWrap(True)
        footer_layout.addWidget(security_notice)
        
        layout.addLayout(footer_layout)
    
    def apply_styling(self):
        """Apply widget styling"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QLineEdit {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                background-color: #ffffff;
                color: #000000;
            }
            
            QLineEdit:focus {
                border-color: #0d6efd;
                outline: none;
            }
            
            QPushButton {
                background-color: #0d6efd;
                color: #ffffff;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                padding: 15px;
            }
            
            QPushButton:hover {
                background-color: #0b5ed7;
            }
            
            QPushButton:pressed {
                background-color: #0a58ca;
            }
            
            QPushButton:disabled {
                background-color: #6c757d;
                color: #ffffff;
            }
            
            QCheckBox {
                spacing: 8px;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #dee2e6;
                border-radius: 4px;
                background-color: #ffffff;
            }
            
            QCheckBox::indicator:checked {
                background-color: #0d6efd;
                border-color: #0d6efd;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjIgNy44TDEuNCA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            
            QProgressBar {
                border: none;
                border-radius: 3px;
                background-color: #e9ecef;
            }
            
            QProgressBar::chunk {
                background-color: #0d6efd;
                border-radius: 3px;
            }
        """)
    
    def setup_security_timer(self):
        """Setup security monitoring timer"""
        self.security_timer = QTimer()
        self.security_timer.timeout.connect(self.update_security_status)
        self.security_timer.start(1000)  # Update every second
    
    def update_security_status(self):
        """Update security status display"""
        current_time = time.time()
        
        # Check if in lockout period
        if self.failed_attempts >= self.max_attempts:
            time_since_last = current_time - self.last_attempt_time
            remaining_lockout = self.lockout_time - time_since_last
            
            if remaining_lockout > 0:
                minutes = int(remaining_lockout // 60)
                seconds = int(remaining_lockout % 60)
                self.lockout_label.setText(
                    tr("login.account_locked").format(minutes, seconds)
                )
                self.lockout_label.setVisible(True)
                self.login_button.setEnabled(False)
                return
            else:
                # Lockout period expired
                self.failed_attempts = 0
                self.lockout_label.setVisible(False)
                self.login_button.setEnabled(True)
        
        # Update security status
        if self.failed_attempts == 0:
            self.security_label.setText(tr("login.security_normal"))
        elif self.failed_attempts < 3:
            self.security_label.setText(tr("login.security_warning").format(self.failed_attempts))
        else:
            self.security_label.setText(tr("login.security_critical").format(self.failed_attempts))
    
    def login(self):
        """Perform login"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        if not username or not password:
            QMessageBox.warning(self, tr("common.warning"), tr("login.fill_all_fields"))
            return
        
        # Check if locked out
        if self.failed_attempts >= self.max_attempts:
            current_time = time.time()
            if current_time - self.last_attempt_time < self.lockout_time:
                QMessageBox.warning(self, tr("common.warning"), tr("login.account_temporarily_locked"))
                return
        
        # Disable login button and show progress
        self.login_button.setEnabled(False)
        self.login_button.setText(tr("login.signing_in"))
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        
        # Start login thread
        self.login_thread = LoginAttemptThread(username, password)
        self.login_thread.login_result.connect(self.handle_login_result)
        self.login_thread.start()
    
    def handle_login_result(self, user, error_message):
        """Handle login result"""
        # Re-enable login button and hide progress
        self.login_button.setEnabled(True)
        self.login_button.setText(tr("login.sign_in"))
        self.progress_bar.setVisible(False)
        
        if user:
            # Successful login
            self.failed_attempts = 0
            self.login_successful.emit(user)
        else:
            # Failed login
            self.failed_attempts += 1
            self.last_attempt_time = time.time()
            
            if error_message:
                QMessageBox.critical(self, tr("common.error"), error_message)
            
            # Clear password field
            self.password_edit.clear()
            self.password_edit.setFocus()
    
    def reset_form(self):
        """Reset login form"""
        self.username_edit.clear()
        self.password_edit.clear()
        self.remember_check.setChecked(False)
        self.failed_attempts = 0
        self.lockout_label.setVisible(False)
        self.login_button.setEnabled(True)
        self.username_edit.setFocus()
