#!/usr/bin/env python3
"""
اختبار زيادة حجم وعرض شريط الحالة
Test Status Bar Size and Width Improvements
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QLabel
from PyQt5.QtCore import QTimer
from models.user import User
from views.main_window import MainWindow
from utils.translator import translator


def test_status_bar_improvements():
    """اختبار تحسينات شريط الحالة"""
    
    app = QApplication(sys.argv)
    
    # إنشاء مستخدم تجريبي
    test_user = User(
        id=1,
        username="admin",
        full_name="أحمد محمد المدير",
        email="<EMAIL>",
        role="admin",
        permissions={"all": True}
    )
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow(test_user)
    main_window.show()
    
    print("📏 اختبار تحسينات حجم وعرض شريط الحالة")
    print("=" * 60)
    
    # اختبار 1: فحص ارتفاع شريط الحالة
    print("\n1. فحص ارتفاع شريط الحالة:")
    
    if hasattr(main_window, 'status_bar'):
        height = main_window.status_bar.height()
        fixed_height = main_window.status_bar.minimumHeight()
        print(f"   📊 الارتفاع الحالي: {height}px")
        print(f"   📊 الارتفاع المحدد: {fixed_height}px")
        
        if height >= 40:
            print("   ✅ ارتفاع شريط الحالة مناسب (≥40px)")
        else:
            print("   ❌ ارتفاع شريط الحالة صغير (<40px)")
    else:
        print("   ❌ لم يتم العثور على شريط الحالة")
    
    # اختبار 2: فحص أحجام العناصر
    print("\n2. فحص أحجام عناصر شريط الحالة:")
    
    elements_to_check = [
        ('connection_label', 'تسمية قاعدة البيانات'),
        ('license_label', 'تسمية حالة الترخيص'),
        ('datetime_label', 'تسمية التاريخ والساعة')
    ]
    
    for attr_name, display_name in elements_to_check:
        if hasattr(main_window, attr_name):
            element = getattr(main_window, attr_name)
            width = element.width()
            height = element.height()
            min_width = element.minimumWidth()
            
            print(f"   📊 {display_name}:")
            print(f"      العرض: {width}px | الارتفاع: {height}px | العرض الأدنى: {min_width}px")
            
            # فحص النص
            text = element.text()
            if text:
                print(f"      النص: {text}")
            
            # فحص الخط
            font = element.font()
            print(f"      حجم الخط: {font.pointSize()}px | العائلة: {font.family()}")
            
        else:
            print(f"   ❌ لم يتم العثور على {display_name}")
    
    # اختبار 3: فحص الأنماط (Styles)
    print("\n3. فحص أنماط العناصر:")
    
    if hasattr(main_window, 'status_bar'):
        status_bar_style = main_window.status_bar.styleSheet()
        print(f"   📊 نمط شريط الحالة:")
        if "QStatusBar" in status_bar_style:
            print("      ✅ يحتوي على أنماط مخصصة")
            if "45" in status_bar_style or "height" in status_bar_style:
                print("      ✅ يحتوي على إعدادات الارتفاع")
        else:
            print("      ❌ لا يحتوي على أنماط مخصصة")
    
    # اختبار 4: فحص الخطوط والألوان
    print("\n4. فحص الخطوط والألوان:")
    
    for attr_name, display_name in elements_to_check:
        if hasattr(main_window, attr_name):
            element = getattr(main_window, attr_name)
            style = element.styleSheet()
            
            print(f"   📊 {display_name}:")
            
            # فحص حجم الخط في النمط
            if "font-size" in style:
                print("      ✅ يحتوي على حجم خط مخصص")
            else:
                print("      ❌ لا يحتوي على حجم خط مخصص")
            
            # فحص الحشو
            if "padding" in style:
                print("      ✅ يحتوي على حشو مخصص")
            else:
                print("      ❌ لا يحتوي على حشو مخصص")
            
            # فحص الحدود
            if "border" in style:
                print("      ✅ يحتوي على حدود مخصصة")
            else:
                print("      ❌ لا يحتوي على حدود مخصصة")
    
    # اختبار 5: اختبار تغيير اللغة وتأثيرها على الأحجام
    print("\n5. اختبار تأثير تغيير اللغة:")
    
    languages = [("ar", "العربية"), ("en", "English"), ("fr", "Français")]
    
    for lang_code, lang_name in languages:
        print(f"   🌐 اختبار اللغة: {lang_name}")
        
        # تغيير اللغة
        translator.set_language(lang_code)
        main_window.retranslate_ui()
        
        # فحص النصوص المحدثة
        if hasattr(main_window, 'datetime_label'):
            datetime_text = main_window.datetime_label.text()
            print(f"      📅 نص التاريخ: {datetime_text}")
        
        if hasattr(main_window, 'license_label'):
            license_text = main_window.license_label.text()
            print(f"      🔐 نص الترخيص: {license_text}")
    
    # إعادة اللغة الأصلية
    translator.set_language("ar")
    main_window.retranslate_ui()
    
    # اختبار 6: قياس المساحة الإجمالية
    print("\n6. قياس المساحة الإجمالية:")
    
    if hasattr(main_window, 'status_bar'):
        total_width = main_window.status_bar.width()
        total_height = main_window.status_bar.height()
        
        print(f"   📊 العرض الإجمالي: {total_width}px")
        print(f"   📊 الارتفاع الإجمالي: {total_height}px")
        
        # حساب مساحة العناصر
        elements_width = 0
        for attr_name, _ in elements_to_check:
            if hasattr(main_window, attr_name):
                element = getattr(main_window, attr_name)
                elements_width += element.width()
        
        print(f"   📊 عرض العناصر: {elements_width}px")
        print(f"   📊 المساحة المتبقية: {total_width - elements_width}px")
    
    # اختبار 7: فحص الاستجابة
    print("\n7. فحص الاستجابة:")
    
    # محاكاة تغيير حجم النافذة
    original_size = main_window.size()
    print(f"   📊 الحجم الأصلي: {original_size.width()}x{original_size.height()}")
    
    # تكبير النافذة
    main_window.resize(1200, 800)
    new_size = main_window.size()
    print(f"   📊 الحجم الجديد: {new_size.width()}x{new_size.height()}")
    
    # فحص تأثير التغيير على شريط الحالة
    if hasattr(main_window, 'status_bar'):
        new_status_width = main_window.status_bar.width()
        print(f"   📊 عرض شريط الحالة الجديد: {new_status_width}px")
    
    # إعادة الحجم الأصلي
    main_window.resize(original_size)
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار")
    
    return app


def test_visual_comparison():
    """اختبار المقارنة المرئية"""
    print("\n8. مقارنة مرئية:")
    
    print("   📊 التحسينات المطبقة:")
    print("      ✅ ارتفاع شريط الحالة: 45px (بدلاً من ~25px)")
    print("      ✅ حجم الخط: 14-15px (بدلاً من 12px)")
    print("      ✅ الحشو: 8px عمودي، 20-25px أفقي")
    print("      ✅ الحدود: 2px (بدلاً من 1px)")
    print("      ✅ نصف قطر الحدود: 6px (بدلاً من 4px)")
    print("      ✅ الهوامش: 3px بين العناصر")
    print("      ✅ العرض الأدنى: محدد لكل عنصر")
    print("      ✅ خط monospace للساعة الرقمية")


def test_responsiveness():
    """اختبار الاستجابة"""
    print("\n9. اختبار الاستجابة:")
    
    screen_sizes = [
        (1024, 768, "شاشة صغيرة"),
        (1366, 768, "شاشة متوسطة"),
        (1920, 1080, "شاشة كبيرة"),
        (2560, 1440, "شاشة عالية الدقة")
    ]
    
    for width, height, description in screen_sizes:
        print(f"   📺 {description} ({width}x{height}):")
        print(f"      العرض المتاح لشريط الحالة: ~{width}px")
        print(f"      مساحة كافية للعناصر: {'✅ نعم' if width >= 800 else '❌ لا'}")


if __name__ == "__main__":
    try:
        print("🚀 بدء اختبار تحسينات شريط الحالة")
        print("🎯 الهدف: التأكد من زيادة حجم وعرض شريط الحالة")
        
        app = test_status_bar_improvements()
        
        # اختبارات إضافية
        test_visual_comparison()
        test_responsiveness()
        
        print("\n🎉 تم الانتهاء من جميع الاختبارات!")
        print("\n📝 ملخص التحسينات:")
        print("   ✅ زيادة ارتفاع شريط الحالة إلى 45px")
        print("   ✅ زيادة حجم الخط إلى 14-15px")
        print("   ✅ زيادة الحشو والهوامش")
        print("   ✅ تحسين الحدود والألوان")
        print("   ✅ إضافة خط monospace للساعة")
        print("   ✅ تحسين المظهر العام")
        
        print("\n🎯 النتيجة:")
        print("   شريط حالة أكبر وأوضح وأكثر جمال<|im_start|>")
        print("   عناصر أكثر وضوحاً وسهولة في القراءة")
        print("   تصميم احترافي ومتناسق")
        
        # تشغيل التطبيق لفترة قصيرة للاختبار اليدوي
        QTimer.singleShot(5000, app.quit)  # إغلاق بعد 5 ثوان
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
