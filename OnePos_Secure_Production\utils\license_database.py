"""
قاعدة بيانات الترخيص المتقدمة
Advanced License Database System
"""

import sqlite3
import hashlib
import uuid
import json
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
import base64
import os


class LicenseDatabase:
    """قاعدة بيانات الترخيص المتقدمة"""
    
    def __init__(self, db_path="data/licenses.db"):
        self.db_path = db_path
        self.encryption_key = self._get_encryption_key()
        self.cipher = Fernet(self.encryption_key)
        
        # إنشاء مجلد البيانات
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # إنشاء قاعدة البيانات
        self.init_database()
    
    def _get_encryption_key(self):
        """الحصول على مفتاح التشفير"""
        key_string = "OnePos_Master_License_Key_2024_Ultra_Secure"
        key_hash = hashlib.sha256(key_string.encode()).digest()
        return base64.urlsafe_b64encode(key_hash)
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول الأكواد المولدة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS license_codes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                serial_code TEXT UNIQUE NOT NULL,
                license_type TEXT DEFAULT 'standard',
                validity_days INTEGER DEFAULT 365,
                max_activations INTEGER DEFAULT 1,
                created_date TEXT NOT NULL,
                expiry_date TEXT NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                notes TEXT
            )
        ''')
        
        # جدول التفعيلات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                serial_code TEXT NOT NULL,
                machine_id TEXT NOT NULL,
                machine_fingerprint TEXT NOT NULL,
                ip_address TEXT,
                computer_name TEXT,
                os_info TEXT,
                activation_date TEXT NOT NULL,
                last_check_date TEXT,
                is_active BOOLEAN DEFAULT 1,
                activation_count INTEGER DEFAULT 1,
                FOREIGN KEY (serial_code) REFERENCES license_codes (serial_code),
                UNIQUE(serial_code, machine_id)
            )
        ''')
        
        # جدول سجل الأنشطة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                serial_code TEXT,
                machine_id TEXT,
                action TEXT NOT NULL,
                details TEXT,
                ip_address TEXT,
                timestamp TEXT NOT NULL
            )
        ''')
        
        # إنشاء فهارس للأداء
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_serial_code ON license_codes(serial_code)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_machine_id ON activations(machine_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_activation_date ON activations(activation_date)')
        
        conn.commit()
        conn.close()
        print("✅ تم إنشاء قاعدة بيانات الترخيص بنجاح")
    
    def generate_serial_code(self, license_type="standard", validity_days=365):
        """توليد كود سيريال فريد"""
        # توليد معرف فريد
        unique_id = str(uuid.uuid4()).replace('-', '')
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        
        # بيانات الترخيص
        license_data = {
            'id': unique_id[:16],
            'type': license_type,
            'created': timestamp,
            'validity': validity_days,
            'product': 'OnePos',
            'version': '1.0'
        }
        
        # تشفير البيانات
        encrypted_data = self.cipher.encrypt(json.dumps(license_data).encode())
        
        # تحويل إلى كود سيريال
        serial_code = base64.b64encode(encrypted_data).decode()
        
        # تنسيق الكود (مجموعات من 5 أحرف)
        formatted_code = '-'.join([serial_code[i:i+5] for i in range(0, len(serial_code), 5)])
        
        return formatted_code
    
    def add_license_code(self, serial_code, license_type="standard", validity_days=365, max_activations=1, notes=""):
        """إضافة كود ترخيص جديد لقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            created_date = datetime.now().isoformat()
            expiry_date = (datetime.now() + timedelta(days=validity_days)).isoformat()
            
            cursor.execute('''
                INSERT INTO license_codes 
                (serial_code, license_type, validity_days, max_activations, created_date, expiry_date, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (serial_code, license_type, validity_days, max_activations, created_date, expiry_date, notes))
            
            conn.commit()
            return True, "تم إضافة الكود بنجاح"
            
        except sqlite3.IntegrityError:
            return False, "الكود موجود مسبقاً"
        except Exception as e:
            return False, f"خطأ في إضافة الكود: {str(e)}"
        finally:
            conn.close()
    
    def generate_bulk_licenses(self, count=5000, license_type="standard", validity_days=365):
        """توليد عدد كبير من أكواد الترخيص"""
        print(f"🔄 بدء توليد {count} كود ترخيص...")
        
        generated_codes = []
        failed_count = 0
        
        for i in range(count):
            try:
                # توليد كود جديد
                serial_code = self.generate_serial_code(license_type, validity_days)
                
                # إضافة للقاعدة
                success, message = self.add_license_code(
                    serial_code, license_type, validity_days, 1, 
                    f"Generated in bulk batch {i+1}/{count}"
                )
                
                if success:
                    generated_codes.append(serial_code)
                    if (i + 1) % 100 == 0:
                        print(f"✅ تم توليد {i + 1} كود...")
                else:
                    failed_count += 1
                    print(f"❌ فشل في توليد الكود {i+1}: {message}")
                    
            except Exception as e:
                failed_count += 1
                print(f"❌ خطأ في توليد الكود {i+1}: {str(e)}")
        
        print(f"🎉 انتهى التوليد: {len(generated_codes)} نجح، {failed_count} فشل")
        return generated_codes
    
    def validate_and_activate(self, serial_code, machine_info):
        """التحقق من الكود وتفعيله"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # البحث عن الكود
            cursor.execute('''
                SELECT serial_code, license_type, validity_days, max_activations, expiry_date, is_active
                FROM license_codes 
                WHERE serial_code = ? AND is_active = 1
            ''', (serial_code,))
            
            license_row = cursor.fetchone()
            if not license_row:
                self._log_activity(serial_code, machine_info.get('machine_id'), 
                                 'VALIDATION_FAILED', 'Code not found', machine_info.get('ip_address'))
                return False, "كود السيريال غير صحيح أو غير موجود"
            
            # التحقق من تاريخ انتهاء الصلاحية
            expiry_date = datetime.fromisoformat(license_row[4])
            if datetime.now() > expiry_date:
                self._log_activity(serial_code, machine_info.get('machine_id'), 
                                 'VALIDATION_FAILED', 'Code expired', machine_info.get('ip_address'))
                return False, "كود السيريال منتهي الصلاحية"
            
            # التحقق من التفعيلات الحالية
            cursor.execute('''
                SELECT COUNT(*), machine_id 
                FROM activations 
                WHERE serial_code = ? AND is_active = 1
                GROUP BY machine_id
            ''', (serial_code,))
            
            activations = cursor.fetchall()
            max_activations = license_row[3]
            
            # التحقق إذا كان الجهاز مفعل مسبقاً
            machine_id = machine_info.get('machine_id')
            for count, existing_machine in activations:
                if existing_machine == machine_id:
                    self._log_activity(serial_code, machine_id, 
                                     'ACTIVATION_EXISTS', 'Already activated on this machine', 
                                     machine_info.get('ip_address'))
                    return True, "الكود مفعل مسبقاً على هذا الجهاز"
            
            # التحقق من عدد التفعيلات المسموح
            total_activations = len(activations)
            if total_activations >= max_activations:
                self._log_activity(serial_code, machine_id, 
                                 'ACTIVATION_FAILED', 'Max activations reached', 
                                 machine_info.get('ip_address'))
                return False, f"تم الوصول للحد الأقصى من التفعيلات ({max_activations})"
            
            # تفعيل الكود
            machine_fingerprint = self._generate_machine_fingerprint(machine_info)
            activation_date = datetime.now().isoformat()
            
            cursor.execute('''
                INSERT INTO activations 
                (serial_code, machine_id, machine_fingerprint, ip_address, computer_name, 
                 os_info, activation_date, last_check_date, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
            ''', (serial_code, machine_id, machine_fingerprint, 
                  machine_info.get('ip_address'), machine_info.get('computer_name'),
                  machine_info.get('os_info'), activation_date, activation_date))
            
            conn.commit()
            
            self._log_activity(serial_code, machine_id, 'ACTIVATION_SUCCESS', 
                             'Successfully activated', machine_info.get('ip_address'))
            
            return True, "تم تفعيل التطبيق بنجاح!"
            
        except Exception as e:
            self._log_activity(serial_code, machine_info.get('machine_id'), 
                             'ACTIVATION_ERROR', str(e), machine_info.get('ip_address'))
            return False, f"خطأ في التفعيل: {str(e)}"
        finally:
            conn.close()
    
    def _generate_machine_fingerprint(self, machine_info):
        """توليد بصمة فريدة للجهاز"""
        fingerprint_data = {
            'machine_id': machine_info.get('machine_id'),
            'computer_name': machine_info.get('computer_name'),
            'os_info': machine_info.get('os_info'),
            'ip_address': machine_info.get('ip_address')
        }
        
        fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
        return hashlib.sha256(fingerprint_str.encode()).hexdigest()
    
    def _log_activity(self, serial_code, machine_id, action, details, ip_address):
        """تسجيل النشاط"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO activity_log (serial_code, machine_id, action, details, ip_address, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (serial_code, machine_id, action, details, ip_address, datetime.now().isoformat()))
            
            conn.commit()
        except Exception as e:
            print(f"خطأ في تسجيل النشاط: {e}")
        finally:
            conn.close()
    
    def export_codes_to_file(self, filename="license_codes.txt", limit=None):
        """تصدير الأكواد إلى ملف"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = 'SELECT serial_code, license_type, created_date FROM license_codes WHERE is_active = 1'
        if limit:
            query += f' LIMIT {limit}'
        
        cursor.execute(query)
        codes = cursor.fetchall()
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("# OnePos License Codes\n")
            f.write(f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# Total Codes: {len(codes)}\n")
            f.write("# Format: SERIAL_CODE | TYPE | CREATED_DATE\n")
            f.write("-" * 80 + "\n\n")
            
            for code, license_type, created_date in codes:
                f.write(f"{code} | {license_type} | {created_date}\n")
        
        conn.close()
        return len(codes)
    
    def get_statistics(self):
        """الحصول على إحصائيات الترخيص"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # إجمالي الأكواد
        cursor.execute('SELECT COUNT(*) FROM license_codes WHERE is_active = 1')
        total_codes = cursor.fetchone()[0]
        
        # الأكواد المفعلة
        cursor.execute('SELECT COUNT(DISTINCT serial_code) FROM activations WHERE is_active = 1')
        activated_codes = cursor.fetchone()[0]
        
        # الأكواد المتاحة
        available_codes = total_codes - activated_codes
        
        # إجمالي التفعيلات
        cursor.execute('SELECT COUNT(*) FROM activations WHERE is_active = 1')
        total_activations = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'total_codes': total_codes,
            'activated_codes': activated_codes,
            'available_codes': available_codes,
            'total_activations': total_activations,
            'activation_rate': (activated_codes / total_codes * 100) if total_codes > 0 else 0
        }


# إنشاء مثيل عام
license_db = LicenseDatabase()
