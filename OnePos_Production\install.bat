@echo off
echo ========================================
echo    OnePos Installation - تثبيت OnePos
echo ========================================
echo.

echo جاري التحقق من متطلبات النظام...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo تحذير: Python غير مثبت
    echo يرجى تحميل وتثبيت Python من python.org
    echo ثم إعادة تشغيل هذا الملف
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
echo.

echo إنشاء البيئة الافتراضية...
python -m venv venv
if errorlevel 1 (
    echo خطأ في إنشاء البيئة الافتراضية
    pause
    exit /b 1
)

echo ✅ تم إنشاء البيئة الافتراضية
echo.

echo تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

echo تثبيت المتطلبات...
pip install --upgrade pip
pip install -r requirements.txt

if errorlevel 1 (
    echo خطأ في تثبيت المتطلبات
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ تم تثبيت OnePos بنجاح!
echo ========================================
echo.
echo يمكنك الآن تشغيل التطبيق من ملف run.bat
echo.
pause

deactivate
