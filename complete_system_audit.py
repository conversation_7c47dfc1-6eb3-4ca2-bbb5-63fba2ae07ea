#!/usr/bin/env python3
"""
فحص شامل ومنهجي لجميع أقسام التطبيق
Complete System Audit for OnePos Application
"""

import os
import json
import re
from datetime import datetime

class SystemAuditor:
    def __init__(self):
        self.issues = []
        self.fixes_applied = []
        
    def log_issue(self, category, file_path, issue, severity="medium"):
        """تسجيل مشكلة"""
        self.issues.append({
            'category': category,
            'file': file_path,
            'issue': issue,
            'severity': severity,
            'timestamp': datetime.now().isoformat()
        })
    
    def log_fix(self, description):
        """تسجيل إصلاح"""
        self.fixes_applied.append({
            'description': description,
            'timestamp': datetime.now().isoformat()
        })

    def audit_login_widget(self):
        """فحص واجهة تسجيل الدخول"""
        print("\n🔍 فحص واجهة تسجيل الدخول...")
        
        file_path = "views/login_widget.py"
        if not os.path.exists(file_path):
            self.log_issue("UI", file_path, "ملف غير موجود", "high")
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص حجم النافذة
            if "setFixedSize(450, 600)" in content:
                print("   ❌ حجم نافذة تسجيل الدخول صغير جداً")
                self.log_issue("UI", file_path, "حجم نافذة تسجيل الدخول صغير", "medium")
                
                # إصلاح الحجم
                new_content = content.replace(
                    "setFixedSize(450, 600)",
                    "setFixedSize(600, 750)"
                )
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("   ✅ تم تكبير حجم نافذة تسجيل الدخول")
                self.log_fix("تكبير حجم نافذة تسجيل الدخول من 450x600 إلى 600x750")
                return True
            else:
                print("   ✅ حجم نافذة تسجيل الدخول مناسب")
                return True
                
        except Exception as e:
            self.log_issue("System", file_path, f"خطأ في القراءة: {e}", "high")
            return False

    def audit_main_window_translations(self):
        """فحص ترجمات النافذة الرئيسية"""
        print("\n🔍 فحص ترجمات النافذة الرئيسية...")
        
        file_path = "views/main_window.py"
        if not os.path.exists(file_path):
            self.log_issue("Translation", file_path, "ملف غير موجود", "high")
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص مشكلة navigation.
            if "tr(f'navigation.{key}')" in content:
                print("   ❌ لا تزال تستخدم navigation. في الترجمات")
                self.log_issue("Translation", file_path, "استخدام navigation. في الترجمات", "high")
                
                # إصلاح المشكلة
                new_content = content.replace(
                    "tr(f'navigation.{key}')",
                    "tr(key)"
                )
                
                # إصلاح زر تسجيل الخروج أيضاً
                new_content = new_content.replace(
                    "tr('logout')",
                    "tr('navigation.logout')"
                )
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("   ✅ تم إصلاح مشكلة navigation. في الترجمات")
                self.log_fix("إصلاح مشكلة navigation. في ترجمات النافذة الرئيسية")
                return True
            else:
                print("   ✅ ترجمات النافذة الرئيسية صحيحة")
                return True
                
        except Exception as e:
            self.log_issue("System", file_path, f"خطأ في القراءة: {e}", "high")
            return False

    def audit_translation_files(self):
        """فحص ملفات الترجمة"""
        print("\n🔍 فحص ملفات الترجمة...")
        
        translation_files = ["ar.json", "en.json", "fr.json"]
        required_keys = [
            "pos", "products", "sales", "customers", "purchases", 
            "users", "reports", "performance", "settings"
        ]
        
        all_good = True
        
        for filename in translation_files:
            file_path = f"translations/{filename}"
            print(f"\n   📄 فحص {filename}...")
            
            if not os.path.exists(file_path):
                print(f"   ❌ ملف غير موجود: {filename}")
                self.log_issue("Translation", file_path, "ملف ترجمة غير موجود", "high")
                all_good = False
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # فحص وجود المفاتيح المطلوبة
                missing_keys = []
                for key in required_keys:
                    if key not in data:
                        missing_keys.append(key)
                
                if missing_keys:
                    print(f"   ❌ مفاتيح مفقودة في {filename}: {missing_keys}")
                    self.log_issue("Translation", file_path, f"مفاتيح مفقودة: {missing_keys}", "medium")
                    
                    # إضافة المفاتيح المفقودة
                    translations = {
                        "ar.json": {
                            "pos": "نقطة البيع",
                            "products": "المنتجات",
                            "sales": "المبيعات", 
                            "customers": "العملاء",
                            "purchases": "المشتريات",
                            "users": "المستخدمين",
                            "reports": "التقارير",
                            "performance": "الأداء",
                            "settings": "الإعدادات"
                        },
                        "en.json": {
                            "pos": "Point of Sale",
                            "products": "Products",
                            "sales": "Sales",
                            "customers": "Customers", 
                            "purchases": "Purchases",
                            "users": "Users",
                            "reports": "Reports",
                            "performance": "Performance",
                            "settings": "Settings"
                        },
                        "fr.json": {
                            "pos": "Point de Vente",
                            "products": "Produits",
                            "sales": "Ventes",
                            "customers": "Clients",
                            "purchases": "Achats", 
                            "users": "Utilisateurs",
                            "reports": "Rapports",
                            "performance": "Performance",
                            "settings": "Paramètres"
                        }
                    }
                    
                    for key in missing_keys:
                        if filename in translations and key in translations[filename]:
                            data[key] = translations[filename][key]
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=4)
                    
                    print(f"   ✅ تم إضافة المفاتيح المفقودة في {filename}")
                    self.log_fix(f"إضافة مفاتيح ترجمة مفقودة في {filename}")
                    
                else:
                    print(f"   ✅ جميع المفاتيح موجودة في {filename}")
                    
            except Exception as e:
                print(f"   ❌ خطأ في قراءة {filename}: {e}")
                self.log_issue("System", file_path, f"خطأ في القراءة: {e}", "high")
                all_good = False
        
        return all_good

    def audit_pos_widget(self):
        """فحص واجهة نقطة البيع"""
        print("\n🔍 فحص واجهة نقطة البيع...")
        
        file_path = "views/pos_widget.py"
        if not os.path.exists(file_path):
            self.log_issue("UI", file_path, "ملف غير موجود", "high")
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            issues_found = []
            
            # فحص Hold Order
            if '"Hold order functionality will be implemented"' in content:
                issues_found.append("Hold Order غير مطبق")
                print("   ❌ Hold Order غير مطبق")
            else:
                print("   ✅ Hold Order مطبق")
            
            # فحص الترجمات
            untranslated_patterns = [
                r'"[A-Za-z\s]+"',  # نصوص إنجليزية مباشرة
                r"'[A-Za-z\s]+'"   # نصوص إنجليزية مباشرة
            ]
            
            for pattern in untranslated_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    # تصفية النتائج لاستبعاد أسماء الملفات والمتغيرات
                    actual_text = [m for m in matches if len(m) > 5 and ' ' in m]
                    if actual_text:
                        issues_found.append(f"نصوص غير مترجمة: {actual_text[:3]}")
            
            if issues_found:
                for issue in issues_found:
                    self.log_issue("POS", file_path, issue, "medium")
                return False
            else:
                print("   ✅ واجهة نقطة البيع سليمة")
                return True
                
        except Exception as e:
            self.log_issue("System", file_path, f"خطأ في القراءة: {e}", "high")
            return False

    def audit_printer_system(self):
        """فحص نظام الطابعات"""
        print("\n🔍 فحص نظام الطابعات...")
        
        files_to_check = [
            "utils/printer_manager.py",
            "views/printer_settings_widget.py"
        ]
        
        all_good = True
        
        for file_path in files_to_check:
            if not os.path.exists(file_path):
                print(f"   ❌ ملف غير موجود: {file_path}")
                self.log_issue("Printer", file_path, "ملف نظام الطابعات غير موجود", "high")
                all_good = False
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص الترجمات
                if "tr(" in content:
                    print(f"   ✅ {file_path} يستخدم نظام الترجمة")
                else:
                    print(f"   ⚠️ {file_path} قد لا يستخدم نظام الترجمة")
                    self.log_issue("Printer", file_path, "لا يستخدم نظام الترجمة", "low")
                
            except Exception as e:
                print(f"   ❌ خطأ في قراءة {file_path}: {e}")
                self.log_issue("System", file_path, f"خطأ في القراءة: {e}", "medium")
                all_good = False
        
        return all_good

    def audit_barcode_system(self):
        """فحص نظام ماسح الباركود"""
        print("\n🔍 فحص نظام ماسح الباركود...")
        
        files_to_check = [
            "utils/barcode_scanner_manager.py"
        ]
        
        all_good = True
        
        for file_path in files_to_check:
            if not os.path.exists(file_path):
                print(f"   ❌ ملف غير موجود: {file_path}")
                self.log_issue("Barcode", file_path, "ملف نظام ماسح الباركود غير موجود", "high")
                all_good = False
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"   ✅ {file_path} موجود ويعمل")
                
            except Exception as e:
                print(f"   ❌ خطأ في قراءة {file_path}: {e}")
                self.log_issue("System", file_path, f"خطأ في القراءة: {e}", "medium")
                all_good = False
        
        return all_good

    def generate_report(self):
        """إنشاء تقرير شامل"""
        print("\n" + "="*80)
        print("📋 تقرير الفحص الشامل للتطبيق")
        print("="*80)
        
        print(f"\n🔧 الإصلاحات المطبقة: {len(self.fixes_applied)}")
        for fix in self.fixes_applied:
            print(f"   ✅ {fix['description']}")
        
        print(f"\n⚠️ المشاكل المكتشفة: {len(self.issues)}")
        
        # تجميع المشاكل حسب الفئة
        issues_by_category = {}
        for issue in self.issues:
            category = issue['category']
            if category not in issues_by_category:
                issues_by_category[category] = []
            issues_by_category[category].append(issue)
        
        for category, category_issues in issues_by_category.items():
            print(f"\n   📂 {category}:")
            for issue in category_issues:
                severity_icon = {"high": "🔴", "medium": "🟡", "low": "🟢"}
                icon = severity_icon.get(issue['severity'], "⚪")
                print(f"      {icon} {issue['file']}: {issue['issue']}")
        
        print("\n" + "="*80)

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الفحص الشامل لتطبيق OnePos")
    print("="*80)
    
    auditor = SystemAuditor()
    
    # تشغيل جميع عمليات الفحص
    auditor.audit_login_widget()
    auditor.audit_main_window_translations()
    auditor.audit_translation_files()
    auditor.audit_pos_widget()
    auditor.audit_printer_system()
    auditor.audit_barcode_system()
    
    # إنشاء التقرير
    auditor.generate_report()
    
    print("\n🎯 الخطوات التالية:")
    print("1. مراجعة التقرير أعلاه")
    print("2. بناء التطبيق مرة أخرى")
    print("3. اختبار جميع الوظائف")

if __name__ == "__main__":
    main()
