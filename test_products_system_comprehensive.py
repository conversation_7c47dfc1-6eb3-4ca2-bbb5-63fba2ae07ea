#!/usr/bin/env python3
"""
اختبار شامل لنظام إدارة المنتجات المتقدم - OnePos
Comprehensive Products Management System Test
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.main_window import MainWindow
from models.user import User
from models.product import Product, Category
from models.stock import StockMovement, StockAlert
from utils.translator import translator, tr

def test_product_model():
    """اختبار موديل المنتجات"""
    print("📦 اختبار موديل المنتجات")
    print("=" * 35)
    
    try:
        # اختبار إنشاء فئة
        category = Category.create("Electronics", "Electronic products")
        if category:
            print("✅ إنشاء فئة المنتجات: نجح")
        else:
            print("❌ إنشاء فئة المنتجات: فشل")
            return False
        
        # اختبار إنشاء منتج
        product_data = {
            'name': 'Test Product',
            'description': 'Test product description',
            'category_id': category.id,
            'cost_price': 50.0,
            'selling_price': 100.0,
            'stock_quantity': 10,
            'min_stock_level': 5,
            'unit': 'piece'
        }
        
        product = Product.create(**product_data)
        if product:
            print("✅ إنشاء منتج: نجح")
            print(f"   📝 اسم المنتج: {product.name}")
            print(f"   🏷️ الباركود: {product.barcode}")
            print(f"   📊 SKU: {product.sku}")
        else:
            print("❌ إنشاء منتج: فشل")
            return False
        
        # اختبار البحث عن المنتج
        found_products = Product.search("Test")
        if found_products and len(found_products) > 0:
            print("✅ البحث عن المنتجات: نجح")
            print(f"   📊 عدد النتائج: {len(found_products)}")
        else:
            print("❌ البحث عن المنتجات: فشل")
        
        # اختبار البحث بالباركود
        if product.barcode:
            barcode_product = Product.get_by_barcode(product.barcode)
            if barcode_product:
                print("✅ البحث بالباركود: نجح")
            else:
                print("❌ البحث بالباركود: فشل")
        
        # اختبار تحديث المنتج
        product.update(selling_price=120.0, stock_quantity=15)
        updated_product = Product.get_by_id(product.id)
        if updated_product and updated_product.selling_price == 120.0:
            print("✅ تحديث المنتج: نجح")
        else:
            print("❌ تحديث المنتج: فشل")
        
        # اختبار فحص المخزون المنخفض
        low_stock_products = Product.get_low_stock()
        print(f"📊 المنتجات ذات المخزون المنخفض: {len(low_stock_products)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار موديل المنتجات: {e}")
        return False

def test_stock_management():
    """اختبار إدارة المخزون"""
    print("\n📊 اختبار إدارة المخزون")
    print("=" * 30)
    
    try:
        # الحصول على منتج للاختبار
        products = Product.get_all()
        if not products:
            print("❌ لا توجد منتجات للاختبار")
            return False
        
        product = products[0]
        initial_stock = product.stock_quantity
        
        # اختبار إضافة مخزون
        product.adjust_stock(10, 'purchase', notes='Test purchase')
        updated_product = Product.get_by_id(product.id)
        
        if updated_product.stock_quantity == initial_stock + 10:
            print("✅ إضافة مخزون: نجح")
            print(f"   📊 المخزون السابق: {initial_stock}")
            print(f"   📊 المخزون الحالي: {updated_product.stock_quantity}")
        else:
            print("❌ إضافة مخزون: فشل")
        
        # اختبار تقليل المخزون
        product.adjust_stock(-5, 'sale', notes='Test sale')
        updated_product = Product.get_by_id(product.id)
        
        if updated_product.stock_quantity == initial_stock + 5:
            print("✅ تقليل المخزون: نجح")
        else:
            print("❌ تقليل المخزون: فشل")
        
        # اختبار حركات المخزون
        movements = StockMovement.get_by_product_id(product.id)
        if movements and len(movements) >= 2:
            print("✅ تسجيل حركات المخزون: نجح")
            print(f"   📊 عدد الحركات: {len(movements)}")
        else:
            print("❌ تسجيل حركات المخزون: فشل")
        
        # اختبار تنبيهات المخزون
        alerts = StockAlert.check_stock_alerts()
        print(f"📊 تنبيهات المخزون:")
        print(f"   ⚠️ مخزون منخفض: {len(alerts['low_stock'])}")
        print(f"   🔴 نفد المخزون: {len(alerts['out_of_stock'])}")
        print(f"   📈 مخزون زائد: {len(alerts['overstock'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة المخزون: {e}")
        return False

def test_category_management():
    """اختبار إدارة الفئات"""
    print("\n📂 اختبار إدارة الفئات")
    print("=" * 30)
    
    try:
        # اختبار إنشاء فئة رئيسية
        main_category = Category.create("Main Category", "Main category description")
        if main_category:
            print("✅ إنشاء فئة رئيسية: نجح")
        else:
            print("❌ إنشاء فئة رئيسية: فشل")
            return False
        
        # اختبار إنشاء فئة فرعية
        sub_category = Category.create("Sub Category", "Sub category description", main_category.id)
        if sub_category:
            print("✅ إنشاء فئة فرعية: نجح")
        else:
            print("❌ إنشاء فئة فرعية: فشل")
        
        # اختبار الحصول على جميع الفئات
        all_categories = Category.get_all()
        if all_categories and len(all_categories) > 0:
            print(f"✅ الحصول على الفئات: نجح ({len(all_categories)} فئة)")
        else:
            print("❌ الحصول على الفئات: فشل")
        
        # اختبار الحصول على الفئات النشطة فقط
        active_categories = Category.get_all(active_only=True)
        print(f"📊 الفئات النشطة: {len(active_categories)}")
        
        # اختبار تحديث الفئة
        main_category.update(name="Updated Main Category")
        updated_category = Category.get_by_id(main_category.id)
        if updated_category and updated_category.name == "Updated Main Category":
            print("✅ تحديث الفئة: نجح")
        else:
            print("❌ تحديث الفئة: فشل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة الفئات: {e}")
        return False

def test_products_ui():
    """اختبار واجهة إدارة المنتجات"""
    print("\n🖼️ اختبار واجهة إدارة المنتجات")
    print("=" * 40)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        main_window = MainWindow(admin_user)
        
        # الانتقال لقسم المنتجات
        main_window.load_module('products')
        app.processEvents()
        
        # البحث عن واجهة المنتجات
        products_widget = None
        for i in range(main_window.content_frame.count()):
            widget = main_window.content_frame.widget(i)
            if hasattr(widget, 'products_table'):
                products_widget = widget
                break
        
        if not products_widget:
            print("❌ لم يتم العثور على واجهة المنتجات")
            return False
        
        print("✅ تم العثور على واجهة المنتجات")
        
        # فحص العناصر الأساسية
        elements_to_check = [
            ('products_table', 'جدول المنتجات'),
            ('search_edit', 'حقل البحث'),
            ('category_filter', 'فلتر الفئات'),
            ('stock_filter', 'فلتر المخزون'),
            ('add_button', 'زر إضافة منتج'),
            ('edit_button', 'زر تعديل منتج'),
            ('delete_button', 'زر حذف منتج'),
            ('print_labels_button', 'زر طباعة الملصقات'),
            ('export_button', 'زر التصدير'),
            ('import_button', 'زر الاستيراد')
        ]
        
        missing_elements = []
        for element_name, description in elements_to_check:
            if hasattr(products_widget, element_name):
                print(f"   ✅ {description}: موجود")
            else:
                print(f"   ❌ {description}: مفقود")
                missing_elements.append(description)
        
        # فحص الجدول
        table = products_widget.products_table
        if table.columnCount() >= 10:
            print(f"✅ جدول المنتجات: {table.columnCount()} أعمدة")
        else:
            print(f"❌ جدول المنتجات: أعمدة ناقصة ({table.columnCount()}/10)")
        
        # اختبار تحميل البيانات
        products_widget.load_products()
        app.processEvents()
        
        if table.rowCount() >= 0:
            print(f"✅ تحميل البيانات: {table.rowCount()} منتج")
        else:
            print("❌ تحميل البيانات: فشل")
        
        # اختبار البحث
        products_widget.search_edit.setText("test")
        products_widget.search_products()
        app.processEvents()
        print("✅ اختبار البحث: تم")
        
        return len(missing_elements) == 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المنتجات: {e}")
        return False

def test_product_translations():
    """اختبار ترجمات المنتجات"""
    print("\n🌍 اختبار ترجمات المنتجات")
    print("=" * 35)
    
    languages = [
        ('ar', 'العربية'),
        ('en', 'English'),
        ('fr', 'Français')
    ]
    
    product_keys = [
        'products.name',
        'products.category',
        'products.barcode',
        'products.sku',
        'products.cost_price',
        'products.selling_price',
        'products.stock',
        'products.min_stock',
        'products.add_product',
        'products.edit_product',
        'products.delete_product'
    ]
    
    all_correct = True
    
    try:
        for lang_code, lang_name in languages:
            print(f"\n📝 اختبار اللغة: {lang_name} ({lang_code})")
            
            # تغيير اللغة
            translator.set_language(lang_code)
            
            missing_translations = []
            for key in product_keys:
                translation = tr(key)
                if translation == key:  # لم يتم العثور على ترجمة
                    missing_translations.append(key)
                else:
                    print(f"   ✅ {key}: {translation}")
            
            if missing_translations:
                print(f"   ❌ ترجمات مفقودة: {len(missing_translations)}")
                for key in missing_translations:
                    print(f"      - {key}")
                all_correct = False
            else:
                print(f"   ✅ جميع الترجمات متوفرة")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ترجمات المنتجات: {e}")
        return False

def test_barcode_generation():
    """اختبار توليد الباركود"""
    print("\n🏷️ اختبار توليد الباركود")
    print("=" * 30)
    
    try:
        # اختبار توليد باركود تلقائي
        product = Product.create("Barcode Test Product", 50.0)
        
        if product and product.barcode:
            print("✅ توليد باركود تلقائي: نجح")
            print(f"   🏷️ الباركود: {product.barcode}")
            
            # التحقق من طول الباركود
            if len(product.barcode) >= 8:
                print("✅ طول الباركود: صحيح")
            else:
                print("❌ طول الباركود: قصير جداً")
            
            # التحقق من أن الباركود رقمي
            if product.barcode.isdigit():
                print("✅ تنسيق الباركود: صحيح (رقمي)")
            else:
                print("❌ تنسيق الباركود: غير صحيح")
        else:
            print("❌ توليد باركود تلقائي: فشل")
            return False
        
        # اختبار توليد SKU تلقائي
        if product.sku:
            print("✅ توليد SKU تلقائي: نجح")
            print(f"   📊 SKU: {product.sku}")
        else:
            print("❌ توليد SKU تلقائي: فشل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار توليد الباركود: {e}")
        return False

def run_products_system_comprehensive_test():
    """تشغيل جميع اختبارات نظام إدارة المنتجات"""
    print("📦 بدء الاختبار الشامل لنظام إدارة المنتجات المتقدم")
    print("=" * 80)
    
    results = []
    
    # اختبار 1: موديل المنتجات
    results.append(("موديل المنتجات", test_product_model()))
    
    # اختبار 2: إدارة المخزون
    results.append(("إدارة المخزون", test_stock_management()))
    
    # اختبار 3: إدارة الفئات
    results.append(("إدارة الفئات", test_category_management()))
    
    # اختبار 4: واجهة المنتجات
    results.append(("واجهة المنتجات", test_products_ui()))
    
    # اختبار 5: ترجمات المنتجات
    results.append(("ترجمات المنتجات", test_product_translations()))
    
    # اختبار 6: توليد الباركود
    results.append(("توليد الباركود", test_barcode_generation()))
    
    # النتائج النهائية
    print("\n" + "=" * 80)
    print("📊 نتائج الاختبار الشامل لنظام إدارة المنتجات")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 نظام إدارة المنتجات يعمل بشكل مثالي!")
        print("✨ جميع الميزات متوفرة ومكتملة")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_products_system_comprehensive_test()
