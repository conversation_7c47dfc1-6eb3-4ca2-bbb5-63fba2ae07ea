; OnePos Professional Installer Script
; Created with Inno Setup

#define MyAppName "OnePos"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "ASSANAJE_APP"
#define MyAppURL "https://www.assanaje.com"
#define MyAppExeName "OnePos.exe"
#define MyAppDescription "Professional Point of Sale System"

[Setup]
; NOTE: The value of AppId uniquely identifies this application.
; Do not use the same AppId value in installers for other applications.
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}
AllowNoIcons=yes
LicenseFile=LICENSE.txt
InfoBeforeFile=README.txt
InfoAfterFile=INSTALL_COMPLETE.txt
OutputDir=installer_output
OutputBaseFilename=OnePos_Setup_v{#MyAppVersion}
SetupIconFile=app_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; Modern wizard appearance
WizardImageFile=wizard_image.bmp
WizardSmallImageFile=wizard_small.bmp

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "french"; MessagesFile: "compiler:Languages\French.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
; Main application files
Source: "dist\OnePos\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

; Additional resources
Source: "app_icon.ico"; DestDir: "{app}"; Flags: ignoreversion
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "LICENSE.txt"; DestDir: "{app}"; Flags: ignoreversion; AfterInstall: CreateLicenseFile
Source: "INSTALL_COMPLETE.txt"; DestDir: "{app}"; Flags: ignoreversion; AfterInstall: CreateInstallCompleteFile

; Documentation
Source: "PRINTER_BARCODE_GUIDE.md"; DestDir: "{app}\docs"; Flags: ignoreversion

[Icons]
; Start Menu icons
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\app_icon.ico"
Name: "{group}\User Guide"; Filename: "{app}\docs\PRINTER_BARCODE_GUIDE.md"
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"

; Desktop icon
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\app_icon.ico"; Tasks: desktopicon

; Quick Launch icon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\app_icon.ico"; Tasks: quicklaunchicon

[Registry]
; File associations (optional)
Root: HKCR; Subkey: ".onepos"; ValueType: string; ValueName: ""; ValueData: "OnePosFile"; Flags: uninsdeletevalue
Root: HKCR; Subkey: "OnePosFile"; ValueType: string; ValueName: ""; ValueData: "OnePos Data File"; Flags: uninsdeletekey
Root: HKCR; Subkey: "OnePosFile\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\{#MyAppExeName},0"
Root: HKCR; Subkey: "OnePosFile\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\{#MyAppExeName}"" ""%1"""

; Application settings
Root: HKCU; Subkey: "Software\{#MyAppPublisher}\{#MyAppName}"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"; Flags: uninsdeletekey
Root: HKCU; Subkey: "Software\{#MyAppPublisher}\{#MyAppName}"; ValueType: string; ValueName: "Version"; ValueData: "{#MyAppVersion}"

[Run]
; Run application after installation
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

; Open user guide
Filename: "notepad.exe"; Parameters: """{app}\docs\PRINTER_BARCODE_GUIDE.md"""; Description: "Open User Guide"; Flags: nowait postinstall skipifsilent unchecked

[UninstallDelete]
; Clean up user data (optional - be careful with this)
Type: filesandordirs; Name: "{app}\data\cache"
Type: filesandordirs; Name: "{app}\logs"

[Code]
procedure CreateLicenseFile();
var
  LicenseText: string;
begin
  LicenseText := 'OnePos Professional Point of Sale System' + #13#10 +
                 'Copyright (c) 2025 ASSANAJE_APP' + #13#10 + #13#10 +
                 'All rights reserved.' + #13#10 + #13#10 +
                 'This software is licensed for use by the purchaser only.' + #13#10 +
                 'Redistribution and use in source and binary forms, with or without' + #13#10 +
                 'modification, are not permitted without express written permission' + #13#10 +
                 'from the copyright holder.' + #13#10 + #13#10 +
                 'THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,' + #13#10 +
                 'EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF' + #13#10 +
                 'MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.' + #13#10 +
                 'IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY' + #13#10 +
                 'CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,' + #13#10 +
                 'TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE' + #13#10 +
                 'SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.';
  
  SaveStringToFile(ExpandConstant('{app}\LICENSE.txt'), LicenseText, False);
end;

procedure CreateInstallCompleteFile();
var
  CompleteText: string;
begin
  CompleteText := 'OnePos Installation Complete!' + #13#10 + #13#10 +
                  'Thank you for installing OnePos Professional POS System.' + #13#10 + #13#10 +
                  'Getting Started:' + #13#10 +
                  '1. Launch OnePos from the desktop shortcut or Start menu' + #13#10 +
                  '2. Follow the initial setup wizard' + #13#10 +
                  '3. Configure your printers and barcode scanners' + #13#10 +
                  '4. Add your products and customers' + #13#10 +
                  '5. Start selling!' + #13#10 + #13#10 +
                  'Support:' + #13#10 +
                  '- User Guide: Available in the Start menu or docs folder' + #13#10 +
                  '- Technical Support: <EMAIL>' + #13#10 +
                  '- Website: https://www.assanaje.com' + #13#10 + #13#10 +
                  'Enjoy using OnePos!';
  
  SaveStringToFile(ExpandConstant('{app}\INSTALL_COMPLETE.txt'), CompleteText, False);
end;

function InitializeSetup(): Boolean;
begin
  Result := True;
  
  // Check if .NET Framework is installed (if needed)
  // Add any pre-installation checks here
end;

procedure InitializeWizard();
begin
  // Customize the installer wizard appearance
  WizardForm.WelcomeLabel1.Caption := 'Welcome to OnePos Setup';
  WizardForm.WelcomeLabel2.Caption := 'This will install OnePos Professional Point of Sale System on your computer.';
end;
