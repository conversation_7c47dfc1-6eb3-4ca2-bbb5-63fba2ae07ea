"""
Comprehensive Testing for OnePos Purchases System
Tests all purchase management functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import db
from models.purchase import Supplier, Purchase, PurchaseItem
from models.product import Product, Category
from datetime import datetime

def test_database_connection():
    """Test database connection and tables"""
    print("🔗 Testing Database Connection...")
    
    try:
        # Test basic query
        result = db.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row['name'] for row in result]
        
        required_tables = ['suppliers', 'purchases', 'purchase_items']
        missing_tables = [table for table in required_tables if table not in tables]
        
        if missing_tables:
            print(f"❌ Missing tables: {missing_tables}")
            return False
        else:
            print(f"✅ All required tables exist: {required_tables}")
            return True
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_suppliers_functionality():
    """Test supplier management functionality"""
    print("\n🏢 Testing Suppliers Functionality...")
    
    try:
        # Test getting all suppliers
        suppliers = Supplier.get_all()
        print(f"✅ Found {len(suppliers)} suppliers in database")
        
        # Display existing suppliers
        for supplier in suppliers:
            print(f"   - {supplier.name} ({supplier.contact_person})")
        
        # Test creating new supplier
        new_supplier = Supplier.create(
            name="Test Supplier",
            contact_person="John Test",
            phone="************",
            email="<EMAIL>",
            address="123 Test Street",
            tax_number="TEST123"
        )
        
        if new_supplier:
            print(f"✅ Created new supplier: {new_supplier.name} (ID: {new_supplier.id})")
            
            # Test updating supplier
            new_supplier.contact_person = "Jane Test"
            new_supplier.update()
            print(f"✅ Updated supplier contact person to: {new_supplier.contact_person}")
            
            # Test searching suppliers
            search_results = Supplier.search("Test")
            print(f"✅ Search for 'Test' found {len(search_results)} suppliers")
            
            return True
        else:
            print("❌ Failed to create new supplier")
            return False
            
    except Exception as e:
        print(f"❌ Supplier functionality test failed: {e}")
        return False

def test_products_for_purchases():
    """Test that we have products for purchase testing"""
    print("\n📦 Testing Products for Purchases...")
    
    try:
        # Get all products
        products = Product.get_all()
        print(f"✅ Found {len(products)} products in database")
        
        if len(products) == 0:
            # Create test products if none exist
            print("📝 Creating test products...")
            
            # Get or create a category
            categories = Category.get_all()
            if categories:
                category_id = categories[0].id
            else:
                category = Category.create("Test Category", "Test category for purchases")
                category_id = category.id
            
            # Create test products
            test_products = [
                ("Test Product 1", "TP001", "123456789", 10.00, 15.00, 100),
                ("Test Product 2", "TP002", "123456790", 20.00, 30.00, 50),
                ("Test Product 3", "TP003", "123456791", 5.00, 8.00, 200)
            ]
            
            for name, sku, barcode, cost, price, stock in test_products:
                product = Product.create(
                    name=name,
                    sku=sku,
                    barcode=barcode,
                    category_id=category_id,
                    cost_price=cost,
                    selling_price=price,
                    stock_quantity=stock
                )
                print(f"   ✅ Created product: {product.name}")
            
            products = Product.get_all()
        
        # Display products
        for product in products[:5]:  # Show first 5 products
            print(f"   - {product.name} (SKU: {product.sku}, Stock: {product.stock_quantity})")
        
        return len(products) > 0
        
    except Exception as e:
        print(f"❌ Products test failed: {e}")
        return False

def test_purchase_creation():
    """Test purchase creation functionality"""
    print("\n📥 Testing Purchase Creation...")
    
    try:
        # Get a supplier
        suppliers = Supplier.get_all()
        if not suppliers:
            print("❌ No suppliers available for testing")
            return False
        
        supplier = suppliers[0]
        print(f"✅ Using supplier: {supplier.name}")
        
        # Create a new purchase
        purchase = Purchase.create(
            supplier_id=supplier.id,
            purchase_date=datetime.now().strftime('%Y-%m-%d'),
            user_id=1,  # Admin user
            notes="Test purchase for system testing"
        )
        
        if purchase:
            print(f"✅ Created purchase: {purchase.purchase_number}")
            print(f"   - Supplier: {purchase.get_supplier().name}")
            print(f"   - Date: {purchase.purchase_date}")
            print(f"   - Status: {purchase.status}")
            print(f"   - Notes: {purchase.notes}")
            
            return purchase
        else:
            print("❌ Failed to create purchase")
            return False
            
    except Exception as e:
        print(f"❌ Purchase creation test failed: {e}")
        return False

def test_purchase_items(purchase):
    """Test adding items to purchase"""
    print("\n📦 Testing Purchase Items...")
    
    try:
        # Get products
        products = Product.get_all()
        if len(products) < 2:
            print("❌ Need at least 2 products for testing")
            return False
        
        # Add first item
        product1 = products[0]
        item1 = purchase.add_item(
            product_id=product1.id,
            quantity=10,
            unit_cost=12.50
        )
        
        if item1:
            print(f"✅ Added item 1: {product1.name} x 10 @ $12.50")
        
        # Add second item
        product2 = products[1]
        item2 = purchase.add_item(
            product_id=product2.id,
            quantity=5,
            unit_cost=25.00
        )
        
        if item2:
            print(f"✅ Added item 2: {product2.name} x 5 @ $25.00")
        
        # Get all items for the purchase
        items = purchase.get_items()
        print(f"✅ Purchase has {len(items)} items")
        
        total_cost = sum(item.total_cost for item in items)
        print(f"✅ Total purchase cost: ${total_cost:.2f}")
        
        # Verify purchase totals were updated
        purchase_updated = Purchase.get_by_id(purchase.id)
        print(f"✅ Purchase total amount: ${purchase_updated.total_amount:.2f}")
        print(f"✅ Purchase net amount: ${purchase_updated.net_amount:.2f}")
        
        return len(items) == 2
        
    except Exception as e:
        print(f"❌ Purchase items test failed: {e}")
        return False

def test_purchase_status_updates(purchase):
    """Test purchase status updates"""
    print("\n🔄 Testing Purchase Status Updates...")
    
    try:
        # Check initial status
        print(f"✅ Initial status: {purchase.status}")
        
        # Get products before receiving
        items = purchase.get_items()
        product_stocks_before = {}
        for item in items:
            product = item.get_product()
            product_stocks_before[product.id] = product.stock_quantity
            print(f"   - {product.name} stock before: {product.stock_quantity}")
        
        # Update status to received
        purchase.update_status('received')
        print(f"✅ Updated status to: {purchase.status}")
        
        # Check if stock was updated
        print("📦 Checking stock updates...")
        for item in items:
            product = Product.get_by_id(item.product_id)
            old_stock = product_stocks_before[product.id]
            new_stock = product.stock_quantity
            expected_stock = old_stock + item.quantity
            
            print(f"   - {product.name}: {old_stock} + {item.quantity} = {new_stock}")
            
            if new_stock == expected_stock:
                print(f"   ✅ Stock updated correctly for {product.name}")
            else:
                print(f"   ❌ Stock update failed for {product.name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Purchase status update test failed: {e}")
        return False

def test_purchase_search():
    """Test purchase search functionality"""
    print("\n🔍 Testing Purchase Search...")
    
    try:
        # Search by purchase number
        all_purchases = Purchase.get_all()
        if all_purchases:
            test_purchase = all_purchases[0]
            search_results = Purchase.search(test_purchase.purchase_number)
            print(f"✅ Search by purchase number found {len(search_results)} results")
            
            # Search by supplier name
            supplier = test_purchase.get_supplier()
            if supplier:
                search_results = Purchase.search(supplier.name)
                print(f"✅ Search by supplier name found {len(search_results)} results")
            
            return True
        else:
            print("❌ No purchases available for search testing")
            return False
            
    except Exception as e:
        print(f"❌ Purchase search test failed: {e}")
        return False

def test_purchase_number_generation():
    """Test automatic purchase number generation"""
    print("\n🔢 Testing Purchase Number Generation...")
    
    try:
        # Create multiple purchases to test numbering
        supplier = Supplier.get_all()[0]
        
        purchase_numbers = []
        for i in range(3):
            purchase = Purchase.create(
                supplier_id=supplier.id,
                purchase_date=datetime.now().strftime('%Y-%m-%d'),
                user_id=1,
                notes=f"Test purchase {i+1}"
            )
            purchase_numbers.append(purchase.purchase_number)
            print(f"✅ Generated purchase number: {purchase.purchase_number}")
        
        # Check that numbers are unique and follow pattern
        unique_numbers = set(purchase_numbers)
        if len(unique_numbers) == len(purchase_numbers):
            print("✅ All purchase numbers are unique")
        else:
            print("❌ Duplicate purchase numbers found")
            return False
        
        # Check pattern (PO + YYYYMMDD + NNNN)
        today = datetime.now().strftime('%Y%m%d')
        for number in purchase_numbers:
            if number.startswith(f"PO{today}"):
                print(f"✅ Purchase number follows correct pattern: {number}")
            else:
                print(f"❌ Purchase number pattern incorrect: {number}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Purchase number generation test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all tests"""
    print("🧪 STARTING COMPREHENSIVE PURCHASES SYSTEM TEST")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: Database Connection
    test_results.append(("Database Connection", test_database_connection()))
    
    # Test 2: Suppliers Functionality
    test_results.append(("Suppliers Functionality", test_suppliers_functionality()))
    
    # Test 3: Products for Purchases
    test_results.append(("Products for Purchases", test_products_for_purchases()))
    
    # Test 4: Purchase Creation
    purchase = test_purchase_creation()
    test_results.append(("Purchase Creation", purchase is not False))
    
    if purchase:
        # Test 5: Purchase Items
        test_results.append(("Purchase Items", test_purchase_items(purchase)))
        
        # Test 6: Purchase Status Updates
        test_results.append(("Purchase Status Updates", test_purchase_status_updates(purchase)))
    
    # Test 7: Purchase Search
    test_results.append(("Purchase Search", test_purchase_search()))
    
    # Test 8: Purchase Number Generation
    test_results.append(("Purchase Number Generation", test_purchase_number_generation()))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Purchases system is working perfectly!")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_comprehensive_test()
