# OnePos Build Requirements
# Install these packages before building the application

# Core GUI Framework
PyQt5>=5.15.0

# Application Building
pyinstaller>=5.0.0

# Database
sqlite3  # Built into Python

# Printing Support
reportlab>=3.6.0
pillow>=8.0.0
python-escpos>=3.0.0

# Barcode Scanner Support
pyserial>=3.5
hidapi>=0.12.0
numpy>=1.21.0

# Optional Camera Support
opencv-python>=4.5.0
pyzbar>=0.1.8

# System Integration (Windows)
pywin32>=227

# Optional Bluetooth Support
# pybluez>=0.23  # Uncomment if Bluetooth scanners are needed

# Development and Testing
pytest>=6.0.0  # For testing
black>=21.0.0   # For code formatting

# Additional Utilities
psutil>=5.8.0   # System information
requests>=2.25.0  # For updates/licensing
