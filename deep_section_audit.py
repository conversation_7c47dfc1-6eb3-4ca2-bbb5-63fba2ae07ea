#!/usr/bin/env python3
"""
فحص عميق لجميع أقسام التطبيق واحد تلو الآخر
Deep audit of all application sections one by one
"""

import os
import json
import re
from datetime import datetime

class DeepSectionAuditor:
    def __init__(self):
        self.fixes_applied = []
        self.issues_found = []
        
    def log_fix(self, description):
        """تسجيل إصلاح"""
        self.fixes_applied.append(description)
        print(f"   ✅ {description}")
    
    def log_issue(self, description):
        """تسجيل مشكلة"""
        self.issues_found.append(description)
        print(f"   ❌ {description}")

    def audit_all_widgets(self):
        """فحص جميع واجهات التطبيق"""
        print("\n🔍 فحص جميع واجهات التطبيق...")
        
        widget_files = [
            "views/pos_widget.py",
            "views/products_widget.py", 
            "views/sales_widget.py",
            "views/customers_widget.py",
            "views/purchases_widget.py",
            "views/users_widget.py",
            "views/reports_widget.py",
            "views/performance_widget.py",
            "views/settings_widget.py"
        ]
        
        for widget_file in widget_files:
            print(f"\n   📄 فحص {widget_file}...")
            self.audit_single_widget(widget_file)

    def audit_single_widget(self, file_path):
        """فحص واجهة واحدة"""
        if not os.path.exists(file_path):
            self.log_issue(f"ملف غير موجود: {file_path}")
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص الترجمات
            self.check_translations_in_file(file_path, content)
            
            # فحص الأزرار والوظائف
            self.check_buttons_and_functions(file_path, content)
            
            # فحص النصوص المباشرة
            self.check_hardcoded_text(file_path, content)
            
        except Exception as e:
            self.log_issue(f"خطأ في قراءة {file_path}: {e}")

    def check_translations_in_file(self, file_path, content):
        """فحص الترجمات في الملف"""
        # البحث عن نصوص غير مترجمة
        patterns = [
            r'setText\("([^"]+)"\)',  # setText("text")
            r'setPlaceholderText\("([^"]+)"\)',  # setPlaceholderText("text")
            r'setWindowTitle\("([^"]+)"\)',  # setWindowTitle("text")
            r'QMessageBox\.\w+\([^,]+,\s*"([^"]+)"',  # QMessageBox.warning(self, "title"
        ]
        
        untranslated_texts = []
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                # تجاهل النصوص التي تحتوي على متغيرات أو أرقام فقط
                if (len(match) > 2 and 
                    not match.isdigit() and 
                    not match.startswith('tr(') and
                    any(c.isalpha() for c in match) and
                    not match.startswith('Q') and  # تجاهل أسماء الكلاسات
                    not match.endswith('.py')):  # تجاهل أسماء الملفات
                    untranslated_texts.append(match)
        
        if untranslated_texts:
            unique_texts = list(set(untranslated_texts))[:5]  # أول 5 نصوص فقط
            self.log_issue(f"نصوص غير مترجمة في {file_path}: {unique_texts}")

    def check_buttons_and_functions(self, file_path, content):
        """فحص الأزرار والوظائف"""
        # البحث عن أزرار بدون وظائف
        button_patterns = [
            r'(\w+_button)\s*=\s*QPushButton',
            r'(\w+Button)\s*=\s*QPushButton'
        ]
        
        buttons_found = []
        for pattern in button_patterns:
            matches = re.findall(pattern, content)
            buttons_found.extend(matches)
        
        # فحص إذا كانت الأزرار متصلة بوظائف
        for button in buttons_found:
            if f"{button}.clicked.connect" not in content:
                self.log_issue(f"زر غير متصل بوظيفة في {file_path}: {button}")

    def check_hardcoded_text(self, file_path, content):
        """فحص النصوص المكتوبة مباشرة"""
        # البحث عن رسائل خطأ أو تحذير مكتوبة مباشرة
        error_patterns = [
            r'QMessageBox\.critical\([^,]+,\s*"([^"]+)"',
            r'QMessageBox\.warning\([^,]+,\s*"([^"]+)"',
            r'QMessageBox\.information\([^,]+,\s*"([^"]+)"'
        ]
        
        for pattern in error_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if not match.startswith('tr(') and len(match) > 3:
                    self.log_issue(f"رسالة غير مترجمة في {file_path}: {match}")

    def fix_common_translation_issues(self):
        """إصلاح مشاكل الترجمة الشائعة"""
        print("\n🔧 إصلاح مشاكل الترجمة الشائعة...")
        
        # قائمة الملفات للإصلاح
        files_to_fix = [
            "views/pos_widget.py",
            "views/products_widget.py",
            "views/sales_widget.py",
            "views/customers_widget.py"
        ]
        
        # قاموس الترجمات الشائعة
        common_translations = {
            '"Add"': 'tr("common.add")',
            '"Edit"': 'tr("common.edit")',
            '"Delete"': 'tr("common.delete")',
            '"Save"': 'tr("common.save")',
            '"Cancel"': 'tr("common.cancel")',
            '"Search"': 'tr("common.search")',
            '"Refresh"': 'tr("common.refresh")',
            '"Export"': 'tr("common.export")',
            '"Import"': 'tr("common.import")',
            '"Print"': 'tr("common.print")',
            '"Success"': 'tr("common.success")',
            '"Error"': 'tr("common.error")',
            '"Warning"': 'tr("common.warning")',
            '"Confirm"': 'tr("common.confirm")',
            '"Yes"': 'tr("common.yes")',
            '"No"': 'tr("common.no")',
            '"OK"': 'tr("common.ok")',
            '"Close"': 'tr("common.close")',
            '"Name"': 'tr("common.name")',
            '"Description"': 'tr("common.description")',
            '"Price"': 'tr("common.price")',
            '"Quantity"': 'tr("common.quantity")',
            '"Total"': 'tr("common.total")',
            '"Date"': 'tr("common.date")',
            '"Time"': 'tr("common.time")',
            '"Status"': 'tr("common.status")',
            '"Active"': 'tr("common.active")',
            '"Inactive"': 'tr("common.inactive")',
        }
        
        for file_path in files_to_fix:
            if not os.path.exists(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # تطبيق الترجمات
                for english_text, translation in common_translations.items():
                    if english_text in content and translation not in content:
                        content = content.replace(english_text, translation)
                
                # حفظ الملف إذا تم تغييره
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.log_fix(f"إصلاح ترجمات في {file_path}")
                
            except Exception as e:
                self.log_issue(f"خطأ في إصلاح {file_path}: {e}")

    def add_missing_common_translations(self):
        """إضافة ترجمات شائعة مفقودة"""
        print("\n🔧 إضافة ترجمات شائعة مفقودة...")
        
        common_translations = {
            "ar.json": {
                "common": {
                    "add": "إضافة",
                    "edit": "تعديل", 
                    "delete": "حذف",
                    "save": "حفظ",
                    "cancel": "إلغاء",
                    "search": "بحث",
                    "refresh": "تحديث",
                    "export": "تصدير",
                    "import": "استيراد",
                    "print": "طباعة",
                    "success": "نجح",
                    "error": "خطأ",
                    "warning": "تحذير",
                    "confirm": "تأكيد",
                    "yes": "نعم",
                    "no": "لا",
                    "ok": "موافق",
                    "close": "إغلاق",
                    "name": "الاسم",
                    "description": "الوصف",
                    "price": "السعر",
                    "quantity": "الكمية",
                    "total": "المجموع",
                    "date": "التاريخ",
                    "time": "الوقت",
                    "status": "الحالة",
                    "active": "نشط",
                    "inactive": "غير نشط"
                }
            },
            "en.json": {
                "common": {
                    "add": "Add",
                    "edit": "Edit",
                    "delete": "Delete", 
                    "save": "Save",
                    "cancel": "Cancel",
                    "search": "Search",
                    "refresh": "Refresh",
                    "export": "Export",
                    "import": "Import",
                    "print": "Print",
                    "success": "Success",
                    "error": "Error",
                    "warning": "Warning",
                    "confirm": "Confirm",
                    "yes": "Yes",
                    "no": "No",
                    "ok": "OK",
                    "close": "Close",
                    "name": "Name",
                    "description": "Description",
                    "price": "Price",
                    "quantity": "Quantity",
                    "total": "Total",
                    "date": "Date",
                    "time": "Time",
                    "status": "Status",
                    "active": "Active",
                    "inactive": "Inactive"
                }
            },
            "fr.json": {
                "common": {
                    "add": "Ajouter",
                    "edit": "Modifier",
                    "delete": "Supprimer",
                    "save": "Enregistrer",
                    "cancel": "Annuler",
                    "search": "Rechercher",
                    "refresh": "Actualiser",
                    "export": "Exporter",
                    "import": "Importer",
                    "print": "Imprimer",
                    "success": "Succès",
                    "error": "Erreur",
                    "warning": "Avertissement",
                    "confirm": "Confirmer",
                    "yes": "Oui",
                    "no": "Non",
                    "ok": "OK",
                    "close": "Fermer",
                    "name": "Nom",
                    "description": "Description",
                    "price": "Prix",
                    "quantity": "Quantité",
                    "total": "Total",
                    "date": "Date",
                    "time": "Heure",
                    "status": "Statut",
                    "active": "Actif",
                    "inactive": "Inactif"
                }
            }
        }
        
        for filename, translations in common_translations.items():
            file_path = f"translations/{filename}"
            
            try:
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # دمج الترجمات الجديدة
                    if "common" not in data:
                        data["common"] = {}
                    
                    for key, value in translations["common"].items():
                        if key not in data["common"]:
                            data["common"][key] = value
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=4)
                    
                    self.log_fix(f"إضافة ترجمات شائعة في {filename}")
                
            except Exception as e:
                self.log_issue(f"خطأ في تحديث {filename}: {e}")

    def enhance_login_widget(self):
        """تحسين واجهة تسجيل الدخول"""
        print("\n🔧 تحسين واجهة تسجيل الدخول...")
        
        file_path = "views/login_widget.py"
        if not os.path.exists(file_path):
            self.log_issue("ملف تسجيل الدخول غير موجود")
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحسينات إضافية للواجهة
            improvements = [
                # تحسين التصميم
                ('setFixedSize(600, 750)', 'setFixedSize(700, 850)'),
                # إضافة المزيد من التحسينات حسب الحاجة
            ]
            
            modified = False
            for old, new in improvements:
                if old in content and new not in content:
                    content = content.replace(old, new)
                    modified = True
            
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.log_fix("تحسين إضافي لواجهة تسجيل الدخول")
            
        except Exception as e:
            self.log_issue(f"خطأ في تحسين واجهة تسجيل الدخول: {e}")

    def generate_final_report(self):
        """إنشاء التقرير النهائي"""
        print("\n" + "="*80)
        print("📋 تقرير الفحص العميق النهائي")
        print("="*80)
        
        print(f"\n🔧 الإصلاحات المطبقة: {len(self.fixes_applied)}")
        for fix in self.fixes_applied:
            print(f"   ✅ {fix}")
        
        print(f"\n⚠️ المشاكل المتبقية: {len(self.issues_found)}")
        for issue in self.issues_found:
            print(f"   ❌ {issue}")
        
        print("\n🎯 التوصيات:")
        print("1. بناء التطبيق مرة أخرى")
        print("2. اختبار جميع الأقسام واحد تلو الآخر")
        print("3. اختبار تغيير اللغة في كل قسم")
        print("4. اختبار جميع الأزرار والوظائف")
        
        print("\n" + "="*80)

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الفحص العميق لجميع أقسام التطبيق")
    print("="*80)
    
    auditor = DeepSectionAuditor()
    
    # تشغيل جميع عمليات الفحص والإصلاح
    auditor.add_missing_common_translations()
    auditor.fix_common_translation_issues()
    auditor.enhance_login_widget()
    auditor.audit_all_widgets()
    
    # إنشاء التقرير النهائي
    auditor.generate_final_report()

if __name__ == "__main__":
    main()
