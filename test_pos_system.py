"""
Comprehensive Testing for OnePos POS System
Tests all point of sale functionality including UI components and business logic
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from models.database import db
from models.product import Product, Category
from models.customer import Customer
from models.sale import Sale, SaleItem
from models.user import User
from views.pos_widget import POSWidget
from datetime import datetime

def test_pos_database_setup():
    """Test POS database setup and required data"""
    print("🔗 Testing POS Database Setup...")
    
    try:
        # Check required tables
        result = db.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row['name'] for row in result]
        
        required_tables = ['products', 'customers', 'sales', 'sale_items', 'categories']
        missing_tables = [table for table in required_tables if table not in tables]
        
        if missing_tables:
            print(f"❌ Missing tables: {missing_tables}")
            return False
        else:
            print(f"✅ All required tables exist: {required_tables}")
        
        # Check if we have products for testing
        products = Product.get_all()
        print(f"✅ Found {len(products)} products in database")
        
        if len(products) == 0:
            print("📝 Creating test products for POS testing...")
            create_test_products()
            products = Product.get_all()
        
        # Display some products
        for product in products[:5]:
            print(f"   - {product.name} (${product.selling_price:.2f}, Stock: {product.stock_quantity})")
        
        # Check customers
        customers = Customer.get_all()
        print(f"✅ Found {len(customers)} customers in database")
        
        if len(customers) == 0:
            print("📝 Creating test customers...")
            create_test_customers()
            customers = Customer.get_all()
        
        return True
        
    except Exception as e:
        print(f"❌ Database setup test failed: {e}")
        return False

def create_test_products():
    """Create test products for POS testing"""
    try:
        # Get or create category
        categories = Category.get_all()
        if categories:
            category_id = categories[0].id
        else:
            category = Category.create("POS Test Category", "Category for POS testing")
            category_id = category.id
        
        # Create test products with different scenarios
        test_products = [
            ("Coca Cola", "COKE001", "1234567890123", 1.50, 2.50, 100),
            ("Pepsi", "PEPSI001", "1234567890124", 1.40, 2.40, 80),
            ("Water Bottle", "WATER001", "1234567890125", 0.50, 1.00, 200),
            ("Chocolate Bar", "CHOC001", "1234567890126", 1.00, 2.00, 50),
            ("Chips", "CHIPS001", "1234567890127", 0.75, 1.50, 75),
            ("Coffee", "COFFEE001", "1234567890128", 3.00, 5.00, 30),
            ("Sandwich", "SAND001", "1234567890129", 2.50, 5.50, 25),
            ("Energy Drink", "ENERGY001", "1234567890130", 2.00, 3.50, 40)
        ]
        
        for name, sku, barcode, cost, price, stock in test_products:
            product = Product.create(
                name=name,
                sku=sku,
                barcode=barcode,
                category_id=category_id,
                cost_price=cost,
                selling_price=price,
                stock_quantity=stock,
                min_stock_level=10
            )
            print(f"   ✅ Created product: {product.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create test products: {e}")
        return False

def create_test_customers():
    """Create test customers for POS testing"""
    try:
        test_customers = [
            ("Walk-in Customer", "", ""),
            ("John Smith", "************", "123 Main St"),
            ("Jane Doe", "************", "456 Oak Ave"),
            ("Bob Johnson", "************", "789 Pine Rd")
        ]
        
        for name, phone, address in test_customers:
            # Check if customer already exists
            existing = Customer.search(name)
            if not existing:
                customer = Customer.create(name=name, phone=phone, address=address)
                print(f"   ✅ Created customer: {customer.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create test customers: {e}")
        return False

def test_pos_widget_creation():
    """Test POS widget creation and initialization"""
    print("\n🖥️ Testing POS Widget Creation...")
    
    try:
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Get a user for testing
        users = User.get_all()
        if not users:
            print("❌ No users available for testing")
            return False

        test_user = users[0]

        # Create POS widget
        pos_widget = POSWidget(test_user)
        
        if pos_widget:
            print("✅ POSWidget created successfully")
            
            # Check main components
            if hasattr(pos_widget, 'barcode_input'):
                print("✅ Barcode input field exists")

            if hasattr(pos_widget, 'cart_table'):
                print("✅ Cart table exists")
                columns = pos_widget.cart_table.columnCount()
                print(f"   - Columns: {columns}")

            if hasattr(pos_widget, 'customer_combo'):
                print("✅ Customer combo box exists")

            if hasattr(pos_widget, 'subtotal_label'):
                print("✅ Subtotal label exists")

            if hasattr(pos_widget, 'tax_label'):
                print("✅ Tax label exists")

            if hasattr(pos_widget, 'total_label'):
                print("✅ Total label exists")

            if hasattr(pos_widget, 'amount_paid_spin'):
                print("✅ Payment amount field exists")

            if hasattr(pos_widget, 'change_label'):
                print("✅ Change label exists")
            
            # Check buttons
            buttons_to_check = [
                'add_to_cart_button',
                'clear_cart_button',
                'complete_sale_button',
                'print_receipt_button',
                'print_invoice_button'
            ]

            for button_name in buttons_to_check:
                if hasattr(pos_widget, button_name):
                    print(f"✅ {button_name} exists")
                else:
                    print(f"❌ {button_name} missing")
            
            return pos_widget
        else:
            print("❌ Failed to create POSWidget")
            return False
            
    except Exception as e:
        print(f"❌ POS widget creation test failed: {e}")
        return False

def test_pos_data_loading(pos_widget):
    """Test POS data loading functionality"""
    print("\n📊 Testing POS Data Loading...")
    
    try:
        # Test loading customers
        pos_widget.load_customers()
        customers_count = pos_widget.customer_combo.count()
        print(f"✅ Loaded {customers_count} customers into combo")

        # Test product search
        if hasattr(pos_widget, 'search_product'):
            pos_widget.barcode_input.setText("Coca")
            pos_widget.search_product()
            print(f"✅ Product search for 'Coca' executed")

            # Clear search
            pos_widget.barcode_input.setText("")
            print(f"✅ Cleared search input")

        return True
        
    except Exception as e:
        print(f"❌ POS data loading test failed: {e}")
        return False

def test_cart_functionality(pos_widget):
    """Test shopping cart functionality"""
    print("\n🛒 Testing Cart Functionality...")
    
    try:
        # Clear cart first
        pos_widget.clear_cart()
        initial_cart_count = pos_widget.cart_table.rowCount()
        print(f"✅ Cart cleared, items: {initial_cart_count}")
        
        # Test adding products to cart
        products = Product.get_all()
        if len(products) >= 2:
            # Add first product
            product1 = products[0]
            pos_widget.quantity_spin.setValue(2)
            pos_widget.add_product_to_cart(product1)
            cart_count_1 = pos_widget.cart_table.rowCount()
            print(f"✅ Added {product1.name} x2 to cart, items: {cart_count_1}")

            # Add second product
            product2 = products[1]
            pos_widget.quantity_spin.setValue(1)
            pos_widget.add_product_to_cart(product2)
            cart_count_2 = pos_widget.cart_table.rowCount()
            print(f"✅ Added {product2.name} x1 to cart, items: {cart_count_2}")
            
            # Test cart calculations
            pos_widget.update_totals()
            
            # Get totals from labels
            subtotal_text = pos_widget.subtotal_label.text()
            tax_text = pos_widget.tax_label.text()
            total_text = pos_widget.total_label.text()
            
            print(f"✅ Cart totals updated:")
            print(f"   - Subtotal: {subtotal_text}")
            print(f"   - Tax: {tax_text}")
            print(f"   - Total: {total_text}")
            
            # Test removing item from cart
            if cart_count_2 > 0:
                pos_widget.cart_table.selectRow(0)
                if hasattr(pos_widget, 'remove_cart_item'):
                    pos_widget.remove_cart_item(0)
                    cart_count_3 = pos_widget.cart_table.rowCount()
                    print(f"✅ Removed item from cart, items: {cart_count_3}")
                else:
                    print("⚠️ Remove cart item function not found")
            
            return True
        else:
            print("❌ Not enough products for cart testing")
            return False
            
    except Exception as e:
        print(f"❌ Cart functionality test failed: {e}")
        return False

def test_payment_processing(pos_widget):
    """Test payment processing functionality"""
    print("\n💳 Testing Payment Processing...")
    
    try:
        # Ensure we have items in cart
        products = Product.get_all()
        if len(products) > 0:
            pos_widget.clear_cart()
            pos_widget.quantity_spin.setValue(1)
            pos_widget.add_product_to_cart(products[0])
            pos_widget.update_totals()

            # Get total amount
            total_text = pos_widget.total_label.text()
            # Extract numeric value from total text
            import re
            total_match = re.search(r'[\d.]+', total_text)
            if total_match:
                total_amount = float(total_match.group())
                print(f"✅ Cart total: ${total_amount:.2f}")

                # Test exact payment
                pos_widget.amount_paid_spin.setValue(total_amount)
                pos_widget.calculate_change()
                change_text = pos_widget.change_label.text()
                print(f"✅ Exact payment change: {change_text}")

                # Test overpayment
                overpay_amount = total_amount + 5.00
                pos_widget.amount_paid_spin.setValue(overpay_amount)
                pos_widget.calculate_change()
                change_text = pos_widget.change_label.text()
                print(f"✅ Overpayment change: {change_text}")

                # Test underpayment
                underpay_amount = total_amount - 1.00
                pos_widget.amount_paid_spin.setValue(underpay_amount)
                pos_widget.calculate_change()
                change_text = pos_widget.change_label.text()
                print(f"✅ Underpayment change: {change_text}")
                
                return True
            else:
                print("❌ Could not extract total amount")
                return False
        else:
            print("❌ No products available for payment testing")
            return False
            
    except Exception as e:
        print(f"❌ Payment processing test failed: {e}")
        return False

def test_sale_completion(pos_widget):
    """Test sale completion and database integration"""
    print("\n💰 Testing Sale Completion...")
    
    try:
        # Setup a complete sale
        products = Product.get_all()
        customers = Customer.get_all()
        
        if len(products) > 0 and len(customers) > 0:
            # Clear and setup cart
            pos_widget.clear_cart()

            # Add multiple products
            pos_widget.quantity_spin.setValue(2)
            pos_widget.add_product_to_cart(products[0])
            if len(products) > 1:
                pos_widget.quantity_spin.setValue(1)
                pos_widget.add_product_to_cart(products[1])

            # Select customer
            pos_widget.customer_combo.setCurrentIndex(1)  # Not walk-in

            # Update totals
            pos_widget.update_totals()

            # Set payment amount
            total_text = pos_widget.total_label.text()
            import re
            total_match = re.search(r'[\d.]+', total_text)
            if total_match:
                total_amount = float(total_match.group())
                pos_widget.amount_paid_spin.setValue(total_amount + 10)  # Overpay
                pos_widget.calculate_change()

                # Get initial sales count
                initial_sales = len(Sale.get_all())
                print(f"✅ Initial sales count: {initial_sales}")

                # Process sale
                result = pos_widget.complete_sale()
                
                if result:
                    # Check if sale was created
                    final_sales = len(Sale.get_all())
                    print(f"✅ Final sales count: {final_sales}")
                    
                    if final_sales > initial_sales:
                        print("✅ Sale created successfully in database")
                        
                        # Get the latest sale
                        latest_sale = Sale.get_all()[0]  # Assuming newest first
                        print(f"✅ Sale details:")
                        print(f"   - Invoice: {latest_sale.invoice_number}")
                        print(f"   - Total: ${latest_sale.total_amount:.2f}")
                        print(f"   - Status: {latest_sale.status}")
                        
                        # Check sale items
                        sale_items = latest_sale.get_items()
                        print(f"   - Items: {len(sale_items)}")
                        
                        for item in sale_items:
                            product = item.get_product()
                            print(f"     * {product.name} x{item.quantity} @ ${item.unit_price:.2f}")
                        
                        return True
                    else:
                        print("❌ Sale not created in database")
                        return False
                else:
                    print("❌ Sale processing failed")
                    return False
            else:
                print("❌ Could not extract total for sale")
                return False
        else:
            print("❌ Insufficient data for sale testing")
            return False
            
    except Exception as e:
        print(f"❌ Sale completion test failed: {e}")
        return False

def test_barcode_functionality(pos_widget):
    """Test barcode scanning functionality"""
    print("\n📱 Testing Barcode Functionality...")
    
    try:
        # Test barcode input
        products = Product.get_all()
        if len(products) > 0:
            product = products[0]
            if product.barcode:
                print(f"✅ Testing barcode: {product.barcode}")
                
                # Simulate barcode scan
                if hasattr(pos_widget, 'barcode_input'):
                    pos_widget.barcode_input.setText(product.barcode)
                    pos_widget.search_product()

                    # Check if product was added to cart
                    cart_count = pos_widget.cart_table.rowCount()
                    print(f"✅ Barcode scan result: {cart_count} items in cart")

                    return True
                else:
                    print("⚠️ Barcode field not found")
                    return True
            else:
                print("⚠️ Product has no barcode, skipping barcode test")
                return True
        else:
            print("❌ No products available for barcode testing")
            return False
            
    except Exception as e:
        print(f"❌ Barcode functionality test failed: {e}")
        return False

def test_keyboard_shortcuts(pos_widget):
    """Test keyboard shortcuts and hotkeys"""
    print("\n⌨️ Testing Keyboard Shortcuts...")
    
    try:
        # Test common shortcuts
        shortcuts_tested = 0
        
        # Test F1 - New Sale
        if hasattr(pos_widget, 'new_sale_shortcut'):
            print("✅ F1 (New Sale) shortcut exists")
            shortcuts_tested += 1
        
        # Test F2 - Process Sale
        if hasattr(pos_widget, 'process_sale_shortcut'):
            print("✅ F2 (Process Sale) shortcut exists")
            shortcuts_tested += 1
        
        # Test Delete - Remove from cart
        if hasattr(pos_widget, 'remove_item_shortcut'):
            print("✅ Delete (Remove Item) shortcut exists")
            shortcuts_tested += 1
        
        # Test Escape - Clear cart
        if hasattr(pos_widget, 'clear_cart_shortcut'):
            print("✅ Escape (Clear Cart) shortcut exists")
            shortcuts_tested += 1
        
        print(f"✅ Found {shortcuts_tested} keyboard shortcuts")
        return True
        
    except Exception as e:
        print(f"❌ Keyboard shortcuts test failed: {e}")
        return False

def run_comprehensive_pos_test():
    """Run all POS tests"""
    print("🧪 STARTING COMPREHENSIVE POS SYSTEM TEST")
    print("=" * 60)

    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    test_results = []
    pos_widget = None
    
    # Test 1: Database Setup
    test_results.append(("Database Setup", test_pos_database_setup()))
    
    # Test 2: Widget Creation
    pos_widget = test_pos_widget_creation()
    test_results.append(("Widget Creation", pos_widget is not False))
    
    if pos_widget:
        # Test 3: Data Loading
        test_results.append(("Data Loading", test_pos_data_loading(pos_widget)))
        
        # Test 4: Cart Functionality
        test_results.append(("Cart Functionality", test_cart_functionality(pos_widget)))
        
        # Test 5: Payment Processing
        test_results.append(("Payment Processing", test_payment_processing(pos_widget)))
        
        # Test 6: Sale Completion
        test_results.append(("Sale Completion", test_sale_completion(pos_widget)))
        
        # Test 7: Barcode Functionality
        test_results.append(("Barcode Functionality", test_barcode_functionality(pos_widget)))
        
        # Test 8: Keyboard Shortcuts
        test_results.append(("Keyboard Shortcuts", test_keyboard_shortcuts(pos_widget)))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 POS TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL POS TESTS PASSED! Point of Sale system is working perfectly!")
    else:
        print("⚠️  Some POS tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_comprehensive_pos_test()
