"""
Cache Manager for OnePos POS System
Provides intelligent caching for improved performance
"""

import time
from typing import Any, Dict, Optional, Callable
from threading import Lock
import weakref


class CacheItem:
    """Individual cache item with expiration"""
    
    def __init__(self, value: Any, ttl: int = 300):
        self.value = value
        self.created_at = time.time()
        self.ttl = ttl  # Time to live in seconds
        self.access_count = 0
        self.last_accessed = time.time()
    
    def is_expired(self) -> bool:
        """Check if cache item has expired"""
        return time.time() - self.created_at > self.ttl
    
    def is_stale(self, max_age: int = 60) -> bool:
        """Check if cache item is stale (not accessed recently)"""
        return time.time() - self.last_accessed > max_age
    
    def access(self) -> Any:
        """Access the cached value and update statistics"""
        self.access_count += 1
        self.last_accessed = time.time()
        return self.value


class CacheManager:
    """Intelligent cache manager with automatic cleanup"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.cache: Dict[str, CacheItem] = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.lock = Lock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'cleanups': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        with self.lock:
            if key in self.cache:
                item = self.cache[key]
                if item.is_expired():
                    del self.cache[key]
                    self.stats['misses'] += 1
                    return None
                
                self.stats['hits'] += 1
                return item.access()
            
            self.stats['misses'] += 1
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache"""
        if ttl is None:
            ttl = self.default_ttl
        
        with self.lock:
            # Check if we need to make space
            if len(self.cache) >= self.max_size:
                self._evict_items()
            
            self.cache[key] = CacheItem(value, ttl)
    
    def delete(self, key: str) -> bool:
        """Delete item from cache"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cache"""
        with self.lock:
            self.cache.clear()
            self.stats['cleanups'] += 1
    
    def cleanup_expired(self) -> int:
        """Remove expired items and return count"""
        with self.lock:
            expired_keys = [
                key for key, item in self.cache.items()
                if item.is_expired()
            ]
            
            for key in expired_keys:
                del self.cache[key]
            
            if expired_keys:
                self.stats['cleanups'] += 1
            
            return len(expired_keys)
    
    def _evict_items(self) -> None:
        """Evict least recently used items"""
        if not self.cache:
            return
        
        # Sort by last accessed time and access count
        items_by_usage = sorted(
            self.cache.items(),
            key=lambda x: (x[1].last_accessed, x[1].access_count)
        )
        
        # Remove 25% of items
        evict_count = max(1, len(self.cache) // 4)
        for i in range(evict_count):
            if i < len(items_by_usage):
                key = items_by_usage[i][0]
                del self.cache[key]
                self.stats['evictions'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hits': self.stats['hits'],
                'misses': self.stats['misses'],
                'hit_rate': f"{hit_rate:.1f}%",
                'evictions': self.stats['evictions'],
                'cleanups': self.stats['cleanups']
            }
    
    def get_or_set(self, key: str, factory: Callable[[], Any], ttl: Optional[int] = None) -> Any:
        """Get from cache or set using factory function"""
        value = self.get(key)
        if value is not None:
            return value
        
        # Generate new value
        value = factory()
        self.set(key, value, ttl)
        return value


class QueryCache:
    """Specialized cache for database queries"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
        self.query_ttl = {
            'products': 300,      # 5 minutes
            'categories': 600,    # 10 minutes
            'customers': 300,     # 5 minutes
            'users': 600,         # 10 minutes
            'sales': 60,          # 1 minute
            'reports': 120,       # 2 minutes
            'settings': 1800,     # 30 minutes
        }
    
    def get_query_key(self, table: str, query_hash: str, params: tuple = ()) -> str:
        """Generate cache key for query"""
        params_str = str(sorted(params)) if params else ""
        return f"query:{table}:{query_hash}:{params_str}"
    
    def get_query(self, table: str, query: str, params: tuple = ()) -> Optional[Any]:
        """Get cached query result"""
        query_hash = str(hash(query))
        key = self.get_query_key(table, query_hash, params)
        return self.cache.get(key)
    
    def set_query(self, table: str, query: str, params: tuple, result: Any) -> None:
        """Cache query result"""
        query_hash = str(hash(query))
        key = self.get_query_key(table, query_hash, params)
        ttl = self.query_ttl.get(table, 300)
        self.cache.set(key, result, ttl)
    
    def invalidate_table(self, table: str) -> None:
        """Invalidate all cached queries for a table"""
        with self.cache.lock:
            keys_to_delete = [
                key for key in self.cache.cache.keys()
                if key.startswith(f"query:{table}:")
            ]
            
            for key in keys_to_delete:
                del self.cache.cache[key]


class ModelCache:
    """Cache for model instances"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
        self.model_refs = weakref.WeakValueDictionary()
    
    def get_model(self, model_class: str, model_id: int) -> Optional[Any]:
        """Get cached model instance"""
        key = f"model:{model_class}:{model_id}"
        
        # Check weak reference first
        weak_key = f"{model_class}:{model_id}"
        if weak_key in self.model_refs:
            return self.model_refs[weak_key]
        
        # Check cache
        return self.cache.get(key)
    
    def set_model(self, model_class: str, model_id: int, instance: Any) -> None:
        """Cache model instance"""
        key = f"model:{model_class}:{model_id}"
        weak_key = f"{model_class}:{model_id}"
        
        # Store in cache
        self.cache.set(key, instance, 600)  # 10 minutes
        
        # Store weak reference
        try:
            self.model_refs[weak_key] = instance
        except TypeError:
            # Object doesn't support weak references
            pass
    
    def invalidate_model(self, model_class: str, model_id: int = None) -> None:
        """Invalidate cached model(s)"""
        if model_id:
            key = f"model:{model_class}:{model_id}"
            self.cache.delete(key)
            
            weak_key = f"{model_class}:{model_id}"
            if weak_key in self.model_refs:
                del self.model_refs[weak_key]
        else:
            # Invalidate all models of this class
            with self.cache.lock:
                keys_to_delete = [
                    key for key in self.cache.cache.keys()
                    if key.startswith(f"model:{model_class}:")
                ]
                
                for key in keys_to_delete:
                    del self.cache.cache[key]


class PerformanceMonitor:
    """Monitor and track performance metrics"""
    
    def __init__(self):
        self.metrics = {
            'query_times': [],
            'cache_operations': [],
            'slow_queries': [],
            'memory_usage': []
        }
        self.lock = Lock()
    
    def record_query_time(self, query: str, duration: float, cached: bool = False) -> None:
        """Record query execution time"""
        with self.lock:
            self.metrics['query_times'].append({
                'query': query[:100],  # Truncate long queries
                'duration': duration,
                'cached': cached,
                'timestamp': time.time()
            })
            
            # Keep only last 1000 entries
            if len(self.metrics['query_times']) > 1000:
                self.metrics['query_times'] = self.metrics['query_times'][-1000:]
            
            # Track slow queries (>100ms)
            if duration > 0.1 and not cached:
                self.metrics['slow_queries'].append({
                    'query': query[:200],
                    'duration': duration,
                    'timestamp': time.time()
                })
                
                # Keep only last 100 slow queries
                if len(self.metrics['slow_queries']) > 100:
                    self.metrics['slow_queries'] = self.metrics['slow_queries'][-100:]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        with self.lock:
            if not self.metrics['query_times']:
                return {'message': 'No performance data available'}
            
            recent_queries = [
                q for q in self.metrics['query_times']
                if time.time() - q['timestamp'] < 300  # Last 5 minutes
            ]
            
            if not recent_queries:
                return {'message': 'No recent performance data'}
            
            cached_queries = [q for q in recent_queries if q['cached']]
            uncached_queries = [q for q in recent_queries if not q['cached']]
            
            avg_cached_time = sum(q['duration'] for q in cached_queries) / len(cached_queries) if cached_queries else 0
            avg_uncached_time = sum(q['duration'] for q in uncached_queries) / len(uncached_queries) if uncached_queries else 0
            
            return {
                'total_queries': len(recent_queries),
                'cached_queries': len(cached_queries),
                'cache_hit_rate': f"{len(cached_queries) / len(recent_queries) * 100:.1f}%" if recent_queries else "0%",
                'avg_cached_time': f"{avg_cached_time * 1000:.1f}ms",
                'avg_uncached_time': f"{avg_uncached_time * 1000:.1f}ms",
                'slow_queries_count': len(self.metrics['slow_queries']),
                'performance_improvement': f"{((avg_uncached_time - avg_cached_time) / avg_uncached_time * 100):.1f}%" if avg_uncached_time > 0 else "N/A"
            }


# Global cache instances
cache_manager = CacheManager(max_size=2000, default_ttl=300)
query_cache = QueryCache(cache_manager)
model_cache = ModelCache(cache_manager)
performance_monitor = PerformanceMonitor()
