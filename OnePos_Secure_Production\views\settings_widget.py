"""
Settings widget for OnePos POS System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QFormLayout, QLineEdit, QSpinBox, QDoubleSpinBox,
                            QComboBox, QCheckBox, QPushButton, QGroupBox,
                            QLabel, QFileDialog, QMessageBox, QTextEdit,
                            QFrame, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap

from utils.config_manager import config
from utils.translator import tr, translator
from utils.license_manager import license_manager
from views.printer_settings_widget import PrinterSettingsWidget
from views.barcode_settings_widget import BarcodeScannerSettingsWidget

def create_dark_label(text):
    """Create a dark label with proper styling"""
    label = QLabel(text)
    label.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
    return label


class GeneralSettingsTab(QWidget):
    """General settings tab"""
    
    settings_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()
        
        # Application settings
        app_group = QGroupBox("Application Settings")
        app_layout = QFormLayout()
        
        # Language
        self.language_combo = QComboBox()
        languages = translator.get_available_languages()
        for code, name in languages.items():
            self.language_combo.addItem(name, code)
        self.language_combo.currentTextChanged.connect(self.on_language_changed)
        app_layout.addRow(create_dark_label(tr("settings.language") + ":"), self.language_combo)

        # Theme
        self.theme_combo = QComboBox()
        self.theme_combo.addItems([tr("settings.dark_theme"), tr("settings.light_theme")])
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        app_layout.addRow(create_dark_label(tr("settings.theme") + ":"), self.theme_combo)

        # Currency
        self.currency_edit = QLineEdit()
        self.currency_edit.setMaxLength(10)
        self.currency_edit.textChanged.connect(self.on_settings_changed)
        app_layout.addRow(create_dark_label(tr("settings.currency") + ":"), self.currency_edit)
        
        # Tax rate
        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setMaximum(100.0)
        self.tax_rate_spin.setDecimals(2)
        self.tax_rate_spin.setSuffix("%")
        self.tax_rate_spin.valueChanged.connect(self.on_settings_changed)
        app_layout.addRow(create_dark_label(tr("settings.tax_rate") + ":"), self.tax_rate_spin)

        # Auto backup
        self.auto_backup_check = QCheckBox()
        self.auto_backup_check.toggled.connect(self.on_settings_changed)
        app_layout.addRow(create_dark_label("Auto Backup:"), self.auto_backup_check)
        
        # Backup interval
        self.backup_interval_spin = QSpinBox()
        self.backup_interval_spin.setMinimum(1)
        self.backup_interval_spin.setMaximum(365)
        self.backup_interval_spin.setSuffix(" days")
        self.backup_interval_spin.valueChanged.connect(self.on_settings_changed)
        app_layout.addRow(create_dark_label("Backup Interval:"), self.backup_interval_spin)
        
        app_group.setLayout(app_layout)
        layout.addWidget(app_group)
        
        # UI settings
        ui_group = QGroupBox("User Interface")
        ui_layout = QFormLayout()
        
        # Window size
        self.window_width_spin = QSpinBox()
        self.window_width_spin.setMinimum(800)
        self.window_width_spin.setMaximum(3840)
        self.window_width_spin.valueChanged.connect(self.on_settings_changed)
        ui_layout.addRow(create_dark_label("Window Width:"), self.window_width_spin)
        
        self.window_height_spin = QSpinBox()
        self.window_height_spin.setMinimum(600)
        self.window_height_spin.setMaximum(2160)
        self.window_height_spin.valueChanged.connect(self.on_settings_changed)
        ui_layout.addRow(create_dark_label("Window Height:"), self.window_height_spin)
        
        # Fullscreen
        self.fullscreen_check = QCheckBox()
        self.fullscreen_check.toggled.connect(self.on_settings_changed)
        ui_layout.addRow(create_dark_label("Start Fullscreen:"), self.fullscreen_check)

        # Animations
        self.animations_check = QCheckBox()
        self.animations_check.toggled.connect(self.on_settings_changed)
        ui_layout.addRow(create_dark_label("Enable Animations:"), self.animations_check)

        # Sound effects
        self.sounds_check = QCheckBox()
        self.sounds_check.toggled.connect(self.on_settings_changed)
        ui_layout.addRow(create_dark_label("Sound Effects:"), self.sounds_check)
        
        ui_group.setLayout(ui_layout)
        layout.addWidget(ui_group)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def load_settings(self):
        """Load current settings"""
        # Language
        current_lang = config.get_language()
        for i in range(self.language_combo.count()):
            if self.language_combo.itemData(i) == current_lang:
                self.language_combo.setCurrentIndex(i)
                break
        
        # Theme
        theme = config.get_theme()
        self.theme_combo.setCurrentText(tr("settings.dark_theme") if theme == "dark" else tr("settings.light_theme"))
        
        # Currency
        self.currency_edit.setText(config.get_currency())
        
        # Tax rate
        self.tax_rate_spin.setValue(config.get_tax_rate())
        
        # Auto backup
        self.auto_backup_check.setChecked(config.get('app.auto_backup', True))
        
        # Backup interval
        self.backup_interval_spin.setValue(config.get('app.backup_interval_days', 7))
        
        # UI settings
        ui_config = config.get_ui_config()
        self.window_width_spin.setValue(ui_config.get('window_width', 1200))
        self.window_height_spin.setValue(ui_config.get('window_height', 800))
        self.fullscreen_check.setChecked(ui_config.get('fullscreen', False))
        self.animations_check.setChecked(ui_config.get('animations', True))
        self.sounds_check.setChecked(ui_config.get('sound_effects', True))
    
    def save_settings(self):
        """Save settings"""
        # Language
        selected_lang = self.language_combo.currentData()
        config.set_language(selected_lang)
        
        # Theme
        theme = "dark" if self.theme_combo.currentText() == tr("settings.dark_theme") else "light"
        config.set_theme(theme)
        
        # Currency
        config.set('app.currency', self.currency_edit.text())
        
        # Tax rate
        config.set('app.tax_rate', self.tax_rate_spin.value())
        
        # Auto backup
        config.set('app.auto_backup', self.auto_backup_check.isChecked())
        config.set('app.backup_interval_days', self.backup_interval_spin.value())
        
        # UI settings
        config.set('ui.window_width', self.window_width_spin.value())
        config.set('ui.window_height', self.window_height_spin.value())
        config.set('ui.fullscreen', self.fullscreen_check.isChecked())
        config.set('ui.animations', self.animations_check.isChecked())
        config.set('ui.sound_effects', self.sounds_check.isChecked())
    
    def on_settings_changed(self):
        """Handle settings change"""
        self.settings_changed.emit()

    def on_language_changed(self):
        """Handle language change immediately"""
        # Get selected language
        selected_lang = self.language_combo.currentData()

        if selected_lang and selected_lang != translator.get_current_language():
            # Change language immediately
            translator.set_language(selected_lang)

            # Show confirmation message
            QMessageBox.information(
                self,
                tr("settings.language_changed"),
                tr("settings.language_changed_message")
            )

    def on_theme_changed(self):
        """Handle theme change immediately"""
        # Get selected theme index
        selected_index = self.theme_combo.currentIndex()

        # Determine theme based on index (0 = dark, 1 = light)
        if selected_index == 0:
            theme_name = "dark"
        else:
            theme_name = "light"

        # Change theme immediately
        config.set_theme(theme_name)

        # Show confirmation message
        QMessageBox.information(
            self,
            tr("settings.theme_changed"),
            tr("settings.theme_changed_message")
        )

        # Emit settings changed signal
        self.settings_changed.emit()

        # Emit settings changed signal
        self.settings_changed.emit()


class CompanySettingsTab(QWidget):
    """Company settings tab"""
    
    settings_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()
        
        # Company information
        company_group = QGroupBox("Company Information")
        company_layout = QFormLayout()
        
        # Company name
        self.company_name_edit = QLineEdit()
        self.company_name_edit.textChanged.connect(self.on_settings_changed)
        company_layout.addRow(create_dark_label(tr("settings.company_name") + ":"), self.company_name_edit)

        # Address
        self.company_address_edit = QTextEdit()
        self.company_address_edit.setMaximumHeight(80)
        self.company_address_edit.textChanged.connect(self.on_settings_changed)
        company_layout.addRow(create_dark_label(tr("settings.company_address") + ":"), self.company_address_edit)

        # Phone
        self.company_phone_edit = QLineEdit()
        self.company_phone_edit.textChanged.connect(self.on_settings_changed)
        company_layout.addRow(create_dark_label(tr("settings.company_phone") + ":"), self.company_phone_edit)

        # Email
        self.company_email_edit = QLineEdit()
        self.company_email_edit.textChanged.connect(self.on_settings_changed)
        company_layout.addRow(create_dark_label(tr("settings.company_email") + ":"), self.company_email_edit)
        
        # Tax number
        self.tax_number_edit = QLineEdit()
        self.tax_number_edit.textChanged.connect(self.on_settings_changed)
        company_layout.addRow(create_dark_label("Tax Number:"), self.tax_number_edit)
        
        # Logo
        logo_layout = QHBoxLayout()
        self.logo_path_edit = QLineEdit()
        self.logo_path_edit.textChanged.connect(self.on_settings_changed)
        logo_layout.addWidget(self.logo_path_edit)
        
        self.browse_logo_button = QPushButton("Browse")
        self.browse_logo_button.clicked.connect(self.browse_logo)
        logo_layout.addWidget(self.browse_logo_button)
        
        logo_widget = QWidget()
        logo_widget.setLayout(logo_layout)
        company_layout.addRow(create_dark_label("Logo:"), logo_widget)
        
        company_group.setLayout(company_layout)
        layout.addWidget(company_group)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def load_settings(self):
        """Load company settings"""
        company_info = config.get_company_info()
        
        self.company_name_edit.setText(company_info.get('name', ''))
        self.company_address_edit.setPlainText(company_info.get('address', ''))
        self.company_phone_edit.setText(company_info.get('phone', ''))
        self.company_email_edit.setText(company_info.get('email', ''))
        self.tax_number_edit.setText(company_info.get('tax_number', ''))
        self.logo_path_edit.setText(company_info.get('logo_path', ''))
    
    def save_settings(self):
        """Save company settings"""
        config.set('company.name', self.company_name_edit.text())
        config.set('company.address', self.company_address_edit.toPlainText())
        config.set('company.phone', self.company_phone_edit.text())
        config.set('company.email', self.company_email_edit.text())
        config.set('company.tax_number', self.tax_number_edit.text())
        config.set('company.logo_path', self.logo_path_edit.text())
    
    def browse_logo(self):
        """Browse for company logo"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Company Logo", "",
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if file_path:
            self.logo_path_edit.setText(file_path)
    
    def on_settings_changed(self):
        """Handle settings change"""
        self.settings_changed.emit()



class LicenseSettingsTab(QWidget):
    """License and activation settings tab"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_license_info()

    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()

        # License status
        status_group = QGroupBox("🔐 حالة الترخيص")
        status_layout = QVBoxLayout()

        self.status_label = QLabel()
        self.status_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 20px;
                border: 2px solid #ddd;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)
        status_layout.addWidget(self.status_label)

        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

        # Activation section
        activation_group = QGroupBox("🔑 تفعيل التطبيق")
        activation_layout = QFormLayout()

        # Serial code input
        self.serial_input = QLineEdit()
        self.serial_input.setPlaceholderText("أدخل كود السيريال هنا...")
        self.serial_input.setFont(QFont("Courier", 11))
        self.serial_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #667eea;
            }
        """)
        activation_layout.addRow(create_dark_label("كود السيريال:"), self.serial_input)

        # Activation button
        self.activate_button = QPushButton("🚀 تفعيل التطبيق")
        self.activate_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.activate_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 12px 20px;
                border: none;
                border-radius: 6px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        self.activate_button.clicked.connect(self.activate_license)
        activation_layout.addRow("", self.activate_button)

        activation_group.setLayout(activation_layout)
        layout.addWidget(activation_group)

        # Machine info
        machine_group = QGroupBox("💻 معلومات الجهاز")
        machine_layout = QFormLayout()

        machine_id = license_manager._get_machine_id()
        self.machine_id_label = QLabel(machine_id)
        self.machine_id_label.setFont(QFont("Courier", 10))
        self.machine_id_label.setStyleSheet("color: #666; padding: 5px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 4px;")
        machine_layout.addRow(create_dark_label("معرف الجهاز:"), self.machine_id_label)

        # Note: Test serial generation removed for production version

        machine_group.setLayout(machine_layout)
        layout.addWidget(machine_group)

        layout.addStretch()
        self.setLayout(layout)

    def load_license_info(self):
        """Load current license information"""
        status = license_manager.get_license_status()

        if status['status'] == 'activated':
            self.status_label.setText("✅ التطبيق مفعل بنجاح")
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #28a745;
                    padding: 20px;
                    border: 2px solid #28a745;
                    border-radius: 8px;
                    background-color: #d4edda;
                    font-weight: bold;
                }
            """)
            self.serial_input.setEnabled(False)
            self.activate_button.setEnabled(False)
            self.activate_button.setText("✅ مفعل")

        elif status['status'] == 'trial':
            days = status['days_remaining']
            self.status_label.setText(f"⏰ فترة تجربة - متبقي {days} أيام")
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #856404;
                    padding: 20px;
                    border: 2px solid #ffc107;
                    border-radius: 8px;
                    background-color: #fff3cd;
                    font-weight: bold;
                }
            """)

        elif status['status'] == 'expired':
            self.status_label.setText("❌ فترة التجربة منتهية - يجب التفعيل")
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #721c24;
                    padding: 20px;
                    border: 2px solid #dc3545;
                    border-radius: 8px;
                    background-color: #f8d7da;
                    font-weight: bold;
                }
            """)

    def activate_license(self):
        """Activate license with serial code"""
        serial_code = self.serial_input.text().strip()

        if not serial_code:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كود السيريال")
            return

        success, message = license_manager.activate_license(serial_code)

        if success:
            QMessageBox.information(self, "نجح التفعيل", message)
            self.load_license_info()  # Refresh display
        else:
            QMessageBox.critical(self, "فشل التفعيل", message)




class BackupSettingsTab(QWidget):
    """Backup settings tab"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()

        # Backup actions
        backup_group = QGroupBox("Database Backup")
        backup_layout = QVBoxLayout()

        # Manual backup
        manual_layout = QHBoxLayout()
        manual_layout.addWidget(QLabel("Create manual backup:"))

        self.backup_button = QPushButton("Create Backup")
        self.backup_button.setStyleSheet("""
            QPushButton {
                background-color: #198754;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 12px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #157347;
            }
        """)
        self.backup_button.clicked.connect(self.create_backup)
        manual_layout.addWidget(self.backup_button)

        manual_layout.addStretch()
        backup_layout.addLayout(manual_layout)

        # Restore backup
        restore_layout = QHBoxLayout()
        restore_layout.addWidget(QLabel("Restore from backup:"))

        self.restore_button = QPushButton("Restore Backup")
        self.restore_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 12px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #bb2d3b;
            }
        """)
        self.restore_button.clicked.connect(self.restore_backup)
        restore_layout.addWidget(self.restore_button)

        restore_layout.addStretch()
        backup_layout.addLayout(restore_layout)

        backup_group.setLayout(backup_layout)
        layout.addWidget(backup_group)

        # Backup info
        info_group = QGroupBox("Backup Information")
        info_layout = QVBoxLayout()

        self.backup_info_label = QLabel("Backup information will be displayed here")
        self.backup_info_label.setWordWrap(True)
        info_layout.addWidget(self.backup_info_label)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        layout.addStretch()
        self.setLayout(layout)

    def create_backup(self):
        """Create database backup"""
        QMessageBox.information(self, "Backup", "Backup functionality will be implemented")

    def restore_backup(self):
        """Restore database backup"""
        QMessageBox.information(self, "Restore", "Restore functionality will be implemented")


class SettingsWidget(QWidget):
    """Settings widget"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.has_unsaved_changes = False
        self.setup_ui()
    
    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel(tr("settings.title"))
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        layout.addWidget(title_label)
        
        # Tabs
        self.tabs = QTabWidget()
        
        # General tab
        self.general_tab = GeneralSettingsTab()
        self.general_tab.settings_changed.connect(self.on_settings_changed)
        self.tabs.addTab(self.general_tab, tr("settings.general"))
        
        # Company tab
        self.company_tab = CompanySettingsTab()
        self.company_tab.settings_changed.connect(self.on_settings_changed)
        self.tabs.addTab(self.company_tab, tr("settings.company"))
        
        # Printer tab (new advanced printer settings)
        self.printer_tab = PrinterSettingsWidget()
        self.printer_tab.settings_changed.connect(self.on_settings_changed)
        self.tabs.addTab(self.printer_tab, "🖨️ إعدادات الطابعات")

        # Barcode scanner tab (new advanced barcode settings)
        self.barcode_tab = BarcodeScannerSettingsWidget()
        self.barcode_tab.settings_changed.connect(self.on_settings_changed)
        self.tabs.addTab(self.barcode_tab, "📱 " + tr("settings.barcode"))

        # License tab
        self.license_tab = LicenseSettingsTab()
        self.tabs.addTab(self.license_tab, "🔐 التفعيل والترخيص")

        # Backup tab
        self.backup_tab = BackupSettingsTab()
        self.tabs.addTab(self.backup_tab, tr("settings.backup"))
        
        layout.addWidget(self.tabs)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton(tr("common.save"))
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #198754;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 12px 24px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #157347;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #ffffff;
            }
        """)
        self.save_button.clicked.connect(self.save_settings)
        self.save_button.setEnabled(False)
        button_layout.addWidget(self.save_button)

        self.reset_button = QPushButton("Reset")
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 12px 24px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #5c636a;
            }
        """)
        self.reset_button.clicked.connect(self.reset_settings)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        # Status label
        self.status_label = QLabel()
        button_layout.addWidget(self.status_label)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def on_settings_changed(self):
        """Handle settings change"""
        self.has_unsaved_changes = True
        self.save_button.setEnabled(True)
        self.status_label.setText("Unsaved changes")
        self.status_label.setStyleSheet("color: #e74c3c;")
    
    def save_settings(self):
        """Save all settings"""
        try:
            self.general_tab.save_settings()
            self.company_tab.save_settings()
            self.printer_tab.save_settings()
            
            self.has_unsaved_changes = False
            self.save_button.setEnabled(False)
            self.status_label.setText("Settings saved")
            self.status_label.setStyleSheet("color: #27ae60;")
            
            QMessageBox.information(self, tr("common.success"), 
                                  "Settings saved successfully!\nSome changes may require application restart.")
            
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"Error saving settings: {str(e)}")
    
    def reset_settings(self):
        """Reset settings to current values"""
        if self.has_unsaved_changes:
            reply = QMessageBox.question(self, "Reset Settings",
                                       "Are you sure you want to discard unsaved changes?",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                return
        
        self.general_tab.load_settings()
        self.company_tab.load_settings()
        if hasattr(self.printer_tab, 'load_printers'):
            self.printer_tab.load_printers()

        # تحديث ترجمات الطابعات
        if hasattr(self.printer_tab, 'retranslate_ui'):
            self.printer_tab.retranslate_ui()

        # تحديث ترجمات الباركود
        if hasattr(self.barcode_tab, 'retranslate_ui'):
            self.barcode_tab.retranslate_ui()
        
        self.has_unsaved_changes = False
        self.save_button.setEnabled(False)
        self.status_label.setText("Settings reset")
        self.status_label.setStyleSheet("color: #3498db;")
