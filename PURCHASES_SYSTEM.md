# نظام إدارة المشتريات - OnePos

## 📥 **نظام إدارة المشتريات الشامل**

### 🆕 **النظام الجديد المضاف:**

#### 📦 **إدارة المشتريات الكاملة:**
- **إنشاء أوامر الشراء** مع ترقيم تلقائي
- **إدارة الموردين** مع معلومات شاملة
- **تتبع حالة المشتريات** (معلق، مستلم، ملغي)
- **تحديث المخزون** التلقائي عند الاستلام

### 🛠️ **الملفات الجديدة:**

#### 📄 **models/purchase.py:**
- **Supplier**: نموذج إدارة الموردين
- **Purchase**: نموذج إدارة المشتريات
- **PurchaseItem**: نموذج عناصر المشتريات

#### 📄 **views/purchases_widget.py:**
- **PurchasesWidget**: الواجهة الرئيسية للمشتريات
- **PurchaseDialog**: حوار إنشاء/تعديل المشتريات
- **SupplierDialog**: حوار إنشاء/تعديل الموردين

### 🗂️ **قاعدة البيانات المحدثة:**

#### 📊 **جداول جديدة:**

##### 🏢 **جدول الموردين (suppliers):**
```sql
CREATE TABLE suppliers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    contact_person TEXT,
    phone TEXT,
    email TEXT,
    address TEXT,
    tax_number TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### 📥 **جدول المشتريات (purchases):**
```sql
CREATE TABLE purchases (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    supplier_id INTEGER NOT NULL,
    purchase_number TEXT UNIQUE NOT NULL,
    purchase_date DATE NOT NULL,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    net_amount DECIMAL(10,2) DEFAULT 0.00,
    status TEXT DEFAULT 'pending',
    notes TEXT,
    user_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

##### 📦 **جدول عناصر المشتريات (purchase_items):**
```sql
CREATE TABLE purchase_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    purchase_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    unit_cost DECIMAL(10,2) NOT NULL,
    total_cost DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_id) REFERENCES purchases (id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products (id)
);
```

### 🎯 **ميزات النظام:**

#### 📥 **إدارة المشتريات:**
- **إنشاء أوامر شراء جديدة** مع ترقيم تلقائي
- **إضافة منتجات متعددة** لكل أمر شراء
- **حساب التكاليف** التلقائي للعناصر والإجمالي
- **تتبع حالة المشتريات** (معلق، مستلم، ملغي)
- **البحث والفلترة** في المشتريات

#### 🏢 **إدارة الموردين:**
- **إضافة موردين جدد** مع معلومات شاملة
- **تعديل بيانات الموردين** الموجودين
- **تتبع معلومات الاتصال** والعناوين
- **إدارة الأرقام الضريبية** للموردين
- **البحث في الموردين** بالاسم أو الهاتف

#### 📦 **إدارة المخزون:**
- **تحديث المخزون التلقائي** عند استلام المشتريات
- **تحديث أسعار التكلفة** للمنتجات
- **تتبع كميات المشتريات** لكل منتج
- **ربط المشتريات بالمنتجات** الموجودة

### 🎨 **واجهة المستخدم:**

#### 📥 **تبويب المشتريات:**
- **جدول المشتريات** مع جميع التفاصيل
- **أزرار الإجراءات** (عرض، استلام)
- **البحث السريع** في المشتريات
- **إضافة مشترى جديد** بسهولة

#### 🏢 **تبويب الموردين:**
- **جدول الموردين** مع معلومات الاتصال
- **تعديل بيانات الموردين**
- **البحث في الموردين**
- **إضافة موردين جدد**

#### 📝 **حوار إنشاء المشتريات:**
- **اختيار المورد** من قائمة منسدلة
- **تحديد تاريخ الشراء** مع تقويم
- **إضافة منتجات متعددة** مع الكميات والأسعار
- **حساب الإجمالي** التلقائي
- **إضافة ملاحظات** للمشترى

### 🔢 **نظام الترقيم التلقائي:**

#### 📋 **تنسيق رقم المشترى:**
```
PO + YYYYMMDD + NNNN
مثال: PO202412150001
```

- **PO**: بادئة أمر الشراء
- **YYYYMMDD**: تاريخ اليوم
- **NNNN**: رقم تسلسلي (4 أرقام)

### 📊 **حالات المشتريات:**

#### 🔄 **دورة حياة المشترى:**
1. **معلق (Pending)**: تم إنشاء الأمر ولم يتم استلامه
2. **مستلم (Received)**: تم استلام المشترى وتحديث المخزون
3. **ملغي (Cancelled)**: تم إلغاء أمر الشراء

### 🎯 **العمليات المتاحة:**

#### 📥 **للمشتريات:**
- **إنشاء مشترى جديد** مع تفاصيل كاملة
- **عرض تفاصيل المشترى** والعناصر
- **استلام المشترى** وتحديث المخزون
- **البحث والفلترة** في المشتريات
- **تتبع حالة المشتريات**

#### 🏢 **للموردين:**
- **إضافة مورد جديد** مع معلومات شاملة
- **تعديل بيانات المورد** الموجود
- **عرض قائمة الموردين** النشطين
- **البحث في الموردين**
- **إدارة حالة المورد** (نشط/غير نشط)

### 🔗 **التكامل مع النظام:**

#### 📦 **مع إدارة المنتجات:**
- **ربط المشتريات بالمنتجات** الموجودة
- **تحديث أسعار التكلفة** عند الاستلام
- **تحديث كميات المخزون** تلقائياً
- **تتبع تاريخ المشتريات** لكل منتج

#### 👥 **مع إدارة المستخدمين:**
- **تسجيل المستخدم** الذي أنشأ المشترى
- **تتبع العمليات** في سجلات التدقيق
- **صلاحيات الوصول** حسب دور المستخدم

### 📈 **البيانات الافتراضية:**

#### 🏢 **موردين افتراضيين:**
1. **General Supplier** - مورد عام
2. **Local Distributor** - موزع محلي  
3. **Wholesale Company** - شركة جملة

### 🎨 **التصميم والألوان:**

#### 🎯 **ألوان الأزرار:**
- **إضافة مشترى**: أخضر (#28a745)
- **إضافة مورد**: أزرق (#007bff)
- **عرض**: رمادي مع أيقونة عين
- **استلام**: أزرق مع أيقونة صندوق
- **تعديل**: أصفر مع أيقونة قلم

#### 📊 **تنسيق الجداول:**
- **ألوان متناوبة** للصفوف
- **رؤوس واضحة** باللغة العربية
- **أعمدة قابلة للتمدد**
- **أزرار إجراءات** في كل صف

### 🔍 **البحث والفلترة:**

#### 📥 **البحث في المشتريات:**
- **رقم المشترى**
- **اسم المورد**
- **الملاحظات**
- **التاريخ**

#### 🏢 **البحث في الموردين:**
- **اسم المورد**
- **الشخص المسؤول**
- **رقم الهاتف**
- **البريد الإلكتروني**

### 🚀 **الفوائد المحققة:**

#### 📊 **للأعمال:**
- **تتبع دقيق للمشتريات** والتكاليف
- **إدارة فعالة للموردين** والعلاقات
- **تحديث تلقائي للمخزون** عند الاستلام
- **تقارير شاملة** للمشتريات والتكاليف

#### 👥 **للمستخدمين:**
- **واجهة سهلة الاستخدام** ومفهومة
- **عمليات سريعة** لإنشاء المشتريات
- **بحث فعال** في البيانات
- **تتبع واضح** لحالة المشتريات

#### 🔧 **للإدارة:**
- **تحكم كامل** في عملية الشراء
- **تتبع التكاليف** والموردين
- **تحديث المخزون** التلقائي
- **سجلات مفصلة** لجميع العمليات

### 📈 **إحصائيات النظام:**

#### 🔢 **الأرقام:**
- **3 جداول جديدة** في قاعدة البيانات
- **10+ فهارس** لتحسين الأداء
- **2 تبويب رئيسي** (مشتريات، موردين)
- **3 موردين افتراضيين** للبدء

#### ⚡ **الأداء:**
- **بحث سريع** مع الفهارس المحسنة
- **تحديث فوري** للمخزون
- **حساب تلقائي** للتكاليف
- **واجهة متجاوبة** وسريعة

## ✅ **النتيجة النهائية:**

### 🎊 **نظام مشتريات متكامل وشامل:**
- **إدارة كاملة للمشتريات** من الإنشاء للاستلام
- **إدارة شاملة للموردين** مع جميع التفاصيل
- **تكامل مع المخزون** والمنتجات
- **واجهة عصرية** وسهلة الاستخدام

### 📦 **ميزات متقدمة:**
- **ترقيم تلقائي** لأوامر الشراء
- **تحديث المخزون** عند الاستلام
- **تتبع حالة المشتريات**
- **بحث وفلترة متقدمة**

النظام الآن يتضمن **نظام إدارة مشتريات متكامل وشامل** يغطي جميع احتياجات الأعمال! 🚀
