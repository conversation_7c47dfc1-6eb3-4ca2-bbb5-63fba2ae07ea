"""
Barcode generator for OnePos POS System
Generates and prints barcode labels for products
"""

import os
import tempfile
from PIL import Image, ImageDraw, ImageFont
from PyQt5.QtWidgets import QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QSpinBox, QComboBox, QGroupBox, QFormLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
from PyQt5.QtWidgets import QApplication

try:
    import barcode
    from barcode.writer import ImageWriter
    BARCODE_AVAILABLE = True
except ImportError:
    BARCODE_AVAILABLE = False

def create_dark_label(text):
    """Create a dark label with proper styling"""
    label = QLabel(text)
    label.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
    return label

class BarcodeGenerator:
    """Barcode generator class"""
    
    @staticmethod
    def generate_barcode_image(barcode_data, product_name="", price="", format_type="code128"):
        """Generate barcode image with product information"""
        if not BARCODE_AVAILABLE:
            raise ImportError("python-barcode library is not installed")
        
        try:
            # Create barcode
            if format_type.lower() == "ean13" and len(barcode_data) == 13:
                barcode_class = barcode.get_barcode_class('ean13')
            elif format_type.lower() == "ean8" and len(barcode_data) == 8:
                barcode_class = barcode.get_barcode_class('ean8')
            else:
                barcode_class = barcode.get_barcode_class('code128')
            
            # Generate barcode with custom writer
            writer = ImageWriter()
            writer.format = 'PNG'
            
            # Create barcode instance
            barcode_instance = barcode_class(barcode_data, writer=writer)
            
            # Generate barcode image
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            barcode_path = barcode_instance.save(temp_file.name)
            
            # Load the barcode image
            barcode_img = Image.open(barcode_path)
            
            # Create label with product information
            label_img = BarcodeGenerator._create_product_label(barcode_img, product_name, price, barcode_data)
            
            # Clean up temporary file
            os.unlink(barcode_path)
            
            return label_img
            
        except Exception as e:
            raise Exception(f"Error generating barcode: {str(e)}")
    
    @staticmethod
    def _create_product_label(barcode_img, product_name, price, barcode_data):
        """Create a complete product label with barcode and text"""
        # Label dimensions (in pixels, for 300 DPI)
        label_width = 600  # 2 inches at 300 DPI
        label_height = 400  # 1.33 inches at 300 DPI
        
        # Create new image for the label
        label = Image.new('RGB', (label_width, label_height), 'white')
        draw = ImageDraw.Draw(label)
        
        # Try to load fonts
        try:
            title_font = ImageFont.truetype("arial.ttf", 24)
            price_font = ImageFont.truetype("arial.ttf", 20)
            barcode_font = ImageFont.truetype("arial.ttf", 16)
        except:
            try:
                title_font = ImageFont.truetype("DejaVuSans.ttf", 24)
                price_font = ImageFont.truetype("DejaVuSans.ttf", 20)
                barcode_font = ImageFont.truetype("DejaVuSans.ttf", 16)
            except:
                title_font = ImageFont.load_default()
                price_font = ImageFont.load_default()
                barcode_font = ImageFont.load_default()
        
        # Calculate positions
        y_offset = 10
        
        # Add product name (truncate if too long)
        if product_name:
            if len(product_name) > 25:
                product_name = product_name[:22] + "..."
            
            # Get text size and center it
            bbox = draw.textbbox((0, 0), product_name, font=title_font)
            text_width = bbox[2] - bbox[0]
            x_pos = (label_width - text_width) // 2
            
            draw.text((x_pos, y_offset), product_name, fill='black', font=title_font)
            y_offset += 35
        
        # Add price
        if price:
            price_text = f"{price} DZD"
            bbox = draw.textbbox((0, 0), price_text, font=price_font)
            text_width = bbox[2] - bbox[0]
            x_pos = (label_width - text_width) // 2

            draw.text((x_pos, y_offset), price_text, fill='black', font=price_font)
            y_offset += 30
        
        # Resize and add barcode
        barcode_width = int(label_width * 0.9)  # 90% of label width
        barcode_height = int(barcode_img.height * (barcode_width / barcode_img.width))
        
        # Ensure barcode fits in remaining space
        remaining_height = label_height - y_offset - 40  # Leave space for barcode text
        if barcode_height > remaining_height:
            barcode_height = remaining_height
            barcode_width = int(barcode_img.width * (barcode_height / barcode_img.height))
        
        barcode_resized = barcode_img.resize((barcode_width, barcode_height), Image.Resampling.LANCZOS)
        
        # Center barcode horizontally
        x_pos = (label_width - barcode_width) // 2
        label.paste(barcode_resized, (x_pos, y_offset))
        y_offset += barcode_height + 5
        
        # Add barcode number below barcode
        bbox = draw.textbbox((0, 0), barcode_data, font=barcode_font)
        text_width = bbox[2] - bbox[0]
        x_pos = (label_width - text_width) // 2
        draw.text((x_pos, y_offset), barcode_data, fill='black', font=barcode_font)
        
        return label
    
    @staticmethod
    def save_barcode_image(barcode_data, product_name="", price="", filename=None):
        """Save barcode image to file"""
        try:
            label_img = BarcodeGenerator.generate_barcode_image(barcode_data, product_name, price)
            
            if filename is None:
                filename = f"barcode_{barcode_data}.png"
            
            label_img.save(filename, 'PNG', dpi=(300, 300))
            return filename
            
        except Exception as e:
            raise Exception(f"Error saving barcode: {str(e)}")

class BarcodePrintDialog(QDialog):
    """Dialog for printing barcode labels"""
    
    def __init__(self, product, parent=None):
        super().__init__(parent)
        self.product = product
        self.setWindowTitle(f"Print Barcode - {product.name}")
        self.setModal(True)
        self.resize(500, 400)
        
        self.setup_ui()
        # Generate preview after a short delay to ensure UI is ready
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(100, self.generate_preview)
    
    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()
        
        # Product info
        info_group = QGroupBox("Product Information")
        info_layout = QFormLayout()
        
        info_layout.addRow(create_dark_label("Product:"), QLabel(self.product.name))
        info_layout.addRow(create_dark_label("SKU:"), QLabel(self.product.sku or "N/A"))
        info_layout.addRow(create_dark_label("Barcode:"), QLabel(self.product.barcode or "N/A"))
        info_layout.addRow(create_dark_label("Price:"), QLabel(f"{self.product.selling_price:.2f} DZD"))
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # Print settings
        settings_group = QGroupBox("Print Settings")
        settings_layout = QFormLayout()
        
        # Number of copies
        self.copies_spin = QSpinBox()
        self.copies_spin.setMinimum(1)
        self.copies_spin.setMaximum(100)
        self.copies_spin.setValue(1)
        settings_layout.addRow(create_dark_label("Number of copies:"), self.copies_spin)
        
        # Label format
        self.format_combo = QComboBox()
        self.format_combo.addItems(["Standard Label", "Small Label", "Large Label"])
        self.format_combo.currentTextChanged.connect(self.generate_preview)
        settings_layout.addRow(create_dark_label("Label format:"), self.format_combo)
        
        # Include price
        self.include_price_combo = QComboBox()
        self.include_price_combo.addItems(["Yes", "No"])
        self.include_price_combo.currentTextChanged.connect(self.generate_preview)
        settings_layout.addRow(create_dark_label("Include price:"), self.include_price_combo)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # Preview
        preview_group = QGroupBox("Preview")
        preview_layout = QVBoxLayout()
        
        self.preview_label = QLabel()
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setStyleSheet("border: 1px solid #ccc; background-color: white;")
        self.preview_label.setMinimumHeight(200)
        preview_layout.addWidget(self.preview_label)
        
        # Refresh preview button
        refresh_button = QPushButton("Refresh Preview")
        refresh_button.clicked.connect(self.generate_preview)
        preview_layout.addWidget(refresh_button)
        
        preview_group.setLayout(preview_layout)
        layout.addWidget(preview_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        print_button = QPushButton("Print Labels")
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
        """)
        print_button.clicked.connect(self.print_labels)
        button_layout.addWidget(print_button)
        
        save_button = QPushButton("Save Image")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #198754;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #157347;
            }
        """)
        save_button.clicked.connect(self.save_image)
        button_layout.addWidget(save_button)
        
        button_layout.addStretch()
        
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def generate_preview(self):
        """Generate barcode preview"""
        try:
            print(f"🔍 Generating preview for product: {self.product.name}")
            print(f"📊 BARCODE_AVAILABLE: {BARCODE_AVAILABLE}")
            print(f"🏷️ Product barcode: {self.product.barcode}")

            if not BARCODE_AVAILABLE:
                error_msg = "Barcode library not available.\nInstall python-barcode to enable this feature."
                print(f"❌ {error_msg}")
                self.preview_label.setText(error_msg)
                return

            if not self.product.barcode:
                error_msg = "No barcode available for this product."
                print(f"❌ {error_msg}")
                self.preview_label.setText(error_msg)
                return

            # Get settings
            include_price = self.include_price_combo.currentText() == "Yes"
            price = f"{self.product.selling_price:.2f}" if include_price else ""

            print(f"💰 Include price: {include_price}, Price: {price}")

            # Generate barcode image
            print("🔄 Generating barcode image...")
            label_img = BarcodeGenerator.generate_barcode_image(
                self.product.barcode,
                self.product.name,
                price
            )
            print("✅ Barcode image generated successfully")

            # Convert PIL image to QPixmap using temporary file (more reliable)
            import tempfile
            import os

            # Save to temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            temp_path = temp_file.name
            temp_file.close()

            try:
                # Save PIL image to file
                label_img.save(temp_path, 'PNG', dpi=(300, 300))
                print(f"✅ Image saved to temp file: {temp_path}")

                # Load QPixmap from file
                pixmap = QPixmap(temp_path)
                if not pixmap.isNull():
                    print("✅ QPixmap loaded successfully")
                    # Scale to fit preview area
                    scaled_pixmap = pixmap.scaled(380, 280, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    self.preview_label.setPixmap(scaled_pixmap)
                    print("✅ Preview updated successfully")
                else:
                    error_msg = "Failed to load image from temporary file"
                    print(f"❌ {error_msg}")
                    self.preview_label.setText(error_msg)

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_path)
                    print("🗑️ Temporary file cleaned up")
                except:
                    pass

        except Exception as e:
            error_msg = f"Error generating preview:\n{str(e)}"
            print(f"❌ {error_msg}")
            self.preview_label.setText(error_msg)
            import traceback
            traceback.print_exc()
    
    def print_labels(self):
        """Print barcode labels"""
        try:
            if not BARCODE_AVAILABLE:
                QMessageBox.warning(self, "Warning", "Barcode library not available.\nInstall python-barcode to enable this feature.")
                return
            
            if not self.product.barcode:
                QMessageBox.warning(self, "Warning", "No barcode available for this product.")
                return
            
            # Create printer
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            
            # Show print dialog
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec_() == QPrintDialog.Accepted:
                # Get settings
                copies = self.copies_spin.value()
                include_price = self.include_price_combo.currentText() == "Yes"
                price = f"{self.product.selling_price:.2f}" if include_price else ""
                
                # Generate and print labels
                self._print_barcode_labels(copies, include_price, price)
                
                QMessageBox.information(self, "Success", f"Successfully sent {copies} label(s) to printer.")
                self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error printing labels:\n{str(e)}")

    def _print_barcode_labels(self, copies, include_price, price):
        """Actually print the barcode labels"""
        try:
            from PyQt5.QtGui import QPainter
            from PyQt5.QtCore import QRect

            # Create printer
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)

            # Create painter
            painter = QPainter()
            if not painter.begin(printer):
                QMessageBox.critical(self, "Error", "Could not start printing")
                return

            try:
                # Calculate label dimensions on page
                page_rect = printer.pageRect()
                labels_per_row = 3  # 3 labels per row
                labels_per_col = 8  # 8 rows per page

                label_width = page_rect.width() // labels_per_row
                label_height = page_rect.height() // labels_per_col

                current_label = 0

                for copy in range(copies):
                    # Generate barcode image
                    label_img = BarcodeGenerator.generate_barcode_image(
                        self.product.barcode,
                        self.product.name,
                        price
                    )

                    # Save to temporary file
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                    label_img.save(temp_file.name, 'PNG', dpi=(300, 300))

                    # Load as QPixmap
                    pixmap = QPixmap(temp_file.name)

                    # Calculate position on page
                    row = current_label // labels_per_row
                    col = current_label % labels_per_row

                    x = col * label_width
                    y = row * label_height

                    # Draw the label
                    target_rect = QRect(x, y, label_width, label_height)
                    painter.drawPixmap(target_rect, pixmap)

                    current_label += 1

                    # Start new page if needed
                    if current_label >= (labels_per_row * labels_per_col):
                        if copy < copies - 1:  # Not the last copy
                            printer.newPage()
                            current_label = 0

                    # Clean up temp file
                    os.unlink(temp_file.name)

                QMessageBox.information(self, "Success", f"Successfully printed {copies} barcode label(s)!")
                self.accept()

            finally:
                painter.end()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error during printing:\n{str(e)}")

    def save_image(self):
        """Save barcode image to file"""
        try:
            if not BARCODE_AVAILABLE:
                QMessageBox.warning(self, "Warning", "Barcode library not available.")
                return
            
            if not self.product.barcode:
                QMessageBox.warning(self, "Warning", "No barcode available for this product.")
                return
            
            from PyQt5.QtWidgets import QFileDialog
            
            # Get save location
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Save Barcode Image",
                f"barcode_{self.product.sku or self.product.barcode}.png",
                "PNG Images (*.png);;All Files (*)"
            )
            
            if filename:
                # Get settings
                include_price = self.include_price_combo.currentText() == "Yes"
                price = f"{self.product.selling_price:.2f}" if include_price else ""
                
                # Generate and save
                BarcodeGenerator.save_barcode_image(
                    self.product.barcode,
                    self.product.name,
                    price,
                    filename
                )
                
                QMessageBox.information(self, "Success", f"Barcode image saved to:\n{filename}")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error saving image:\n{str(e)}")
