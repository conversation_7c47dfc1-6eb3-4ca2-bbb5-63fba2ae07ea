"""
Login dialog for OnePos POS System
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame, QMessageBox,
                            QCheckBox, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon

from models.user import User
from utils.config_manager import config


class LoginDialog(QDialog):
    """Login dialog for user authentication"""
    
    login_successful = pyqtSignal(object)  # Emits User object
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.authenticated_user = None
        self.setup_ui()
        self.setup_connections()
        self.load_saved_credentials()
    
    def setup_ui(self):
        """Setup user interface"""
        self.setWindowTitle("OnePos - Login")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # Logo and title section
        self.create_header_section(main_layout)
        
        # Login form section
        self.create_form_section(main_layout)
        
        # Buttons section
        self.create_buttons_section(main_layout)
        
        # Footer section
        self.create_footer_section(main_layout)
        
        self.setLayout(main_layout)
        
        # Apply custom styling
        self.apply_styling()
    
    def create_header_section(self, layout):
        """Create header section with logo and title"""
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignCenter)
        
        # Logo (placeholder)
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setFixedSize(80, 80)
        logo_label.setStyleSheet("""
            QLabel {
                background-color: #3498db;
                border-radius: 40px;
                color: white;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        logo_label.setText("OP")
        header_layout.addWidget(logo_label)
        
        # Title
        title_label = QLabel(config.get_app_name())
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        header_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel("Point of Sale System")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setFont(QFont("Segoe UI", 10))
        subtitle_label.setStyleSheet("color: #7f8c8d;")
        header_layout.addWidget(subtitle_label)
        
        layout.addLayout(header_layout)
    
    def create_form_section(self, layout):
        """Create login form section"""
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)
        
        # Username field
        username_label = QLabel("Username:")
        username_label.setFont(QFont("Segoe UI", 9, QFont.Bold))
        form_layout.addWidget(username_label)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("Enter your username")
        self.username_edit.setFont(QFont("Segoe UI", 10))
        self.username_edit.setFixedHeight(35)
        form_layout.addWidget(self.username_edit)
        
        # Password field
        password_label = QLabel("Password:")
        password_label.setFont(QFont("Segoe UI", 9, QFont.Bold))
        form_layout.addWidget(password_label)
        
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("Enter your password")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setFont(QFont("Segoe UI", 10))
        self.password_edit.setFixedHeight(35)
        form_layout.addWidget(self.password_edit)
        
        # Remember me checkbox
        self.remember_checkbox = QCheckBox("Remember me")
        self.remember_checkbox.setFont(QFont("Segoe UI", 9))
        form_layout.addWidget(self.remember_checkbox)
        
        form_frame.setLayout(form_layout)
        layout.addWidget(form_frame)
    
    def create_buttons_section(self, layout):
        """Create buttons section"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        # Cancel button
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setFont(QFont("Segoe UI", 10))
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        buttons_layout.addWidget(self.cancel_button)
        
        # Login button
        self.login_button = QPushButton("Login")
        self.login_button.setFont(QFont("Segoe UI", 10))
        self.login_button.setFixedHeight(40)
        self.login_button.setDefault(True)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        buttons_layout.addWidget(self.login_button)
        
        layout.addLayout(buttons_layout)
    
    def create_footer_section(self, layout):
        """Create footer section"""
        # Spacer
        spacer = QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding)
        layout.addItem(spacer)
        
        # Version info
        version_label = QLabel(f"Version {config.get_app_version()}")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setFont(QFont("Segoe UI", 8))
        version_label.setStyleSheet("color: #95a5a6;")
        layout.addWidget(version_label)
    
    def apply_styling(self):
        """Apply custom styling to the dialog"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #2c3e50, stop: 1 #34495e);
                color: white;
            }
            
            QLabel {
                color: white;
            }
            
            QLineEdit {
                background-color: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                color: #2c3e50;
                font-size: 11px;
            }
            
            QLineEdit:focus {
                border: 2px solid #3498db;
                background-color: white;
            }
            
            QCheckBox {
                color: white;
                spacing: 8px;
            }
            
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid rgba(255, 255, 255, 0.5);
                border-radius: 3px;
                background-color: rgba(255, 255, 255, 0.1);
            }
            
            QCheckBox::indicator:checked {
                background-color: #3498db;
                border: 2px solid #3498db;
            }
            
            QCheckBox::indicator:checked:hover {
                background-color: #2980b9;
            }
        """)
    
    def setup_connections(self):
        """Setup signal connections"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.password_edit.returnPressed.connect(self.handle_login)
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)
    
    def load_saved_credentials(self):
        """Load saved credentials if remember me was checked"""
        # This would typically load from secure storage
        # For now, we'll just set focus to username
        self.username_edit.setFocus()
    
    def handle_login(self):
        """Handle login attempt"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        # Validate input
        if not username:
            QMessageBox.warning(self, "Login Error", "Please enter your username.")
            self.username_edit.setFocus()
            return
        
        if not password:
            QMessageBox.warning(self, "Login Error", "Please enter your password.")
            self.password_edit.setFocus()
            return
        
        # Disable login button during authentication
        self.login_button.setEnabled(False)
        self.login_button.setText("Logging in...")
        
        try:
            # Authenticate user
            user = User.authenticate(username, password)
            
            if user:
                self.authenticated_user = user
                
                # Save credentials if remember me is checked
                if self.remember_checkbox.isChecked():
                    self.save_credentials(username)
                
                # Emit success signal
                self.login_successful.emit(user)
                
                # Close dialog with success
                self.accept()
            else:
                QMessageBox.warning(
                    self,
                    "Login Failed",
                    "Invalid username or password.\nPlease try again."
                )
                self.password_edit.clear()
                self.password_edit.setFocus()
        
        except Exception as e:
            QMessageBox.critical(
                self,
                "Login Error",
                f"An error occurred during login:\n{str(e)}"
            )
        
        finally:
            # Re-enable login button
            self.login_button.setEnabled(True)
            self.login_button.setText("Login")
    
    def save_credentials(self, username):
        """Save credentials for remember me functionality"""
        # This would typically save to secure storage
        # For now, we'll just store the username in config
        try:
            config.set('login.remembered_username', username)
        except Exception as e:
            print(f"Error saving credentials: {e}")
    
    def get_authenticated_user(self):
        """Get the authenticated user"""
        return self.authenticated_user
    
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)
