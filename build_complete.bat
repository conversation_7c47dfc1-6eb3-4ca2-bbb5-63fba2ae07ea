@echo off
echo ========================================
echo OnePos - Complete Build Process
echo ========================================
echo.
echo This script will:
echo 1. Build the Python application into EXE
echo 2. Create a professional Windows installer
echo 3. Test both the application and installer
echo.

:: Set encoding to UTF-8
chcp 65001 > nul

set "START_TIME=%time%"
echo 🕐 Build started at: %START_TIME%
echo.

:: Step 1: Build the application
echo 📋 Step 1: Building Python application...
echo ========================================
call build_app.bat
if %errorlevel% neq 0 (
    echo ❌ Application build failed!
    goto :build_failed
)

echo.
echo ✅ Application build completed successfully!
echo.

:: Wait a moment
timeout /t 2 > nul

:: Step 2: Build the installer
echo 📋 Step 2: Building Windows installer...
echo ========================================
call build_installer.bat
if %errorlevel% neq 0 (
    echo ❌ Installer build failed!
    goto :build_failed
)

echo.
echo ✅ Installer build completed successfully!
echo.

:: Step 3: Final verification
echo 📋 Step 3: Final verification...
echo ========================================

:: Check application
if exist "dist\OnePos\OnePos.exe" (
    echo ✅ Application EXE: dist\OnePos\OnePos.exe
    
    :: Get file size
    for %%A in ("dist\OnePos\OnePos.exe") do (
        echo    Size: %%~zA bytes
    )
) else (
    echo ❌ Application EXE not found!
    goto :build_failed
)

:: Check installer
set "INSTALLER_FOUND=0"
for %%f in (installer_output\*.exe) do (
    echo ✅ Installer: %%f
    
    :: Get file size
    for %%A in ("%%f") do (
        echo    Size: %%~zA bytes
    )
    set "INSTALLER_FOUND=1"
)

if %INSTALLER_FOUND%==0 (
    echo ❌ Installer not found!
    goto :build_failed
)

:: Calculate build time
set "END_TIME=%time%"
echo.
echo 🕐 Build completed at: %END_TIME%

:: Success summary
echo.
echo ========================================
echo 🎉 BUILD COMPLETED SUCCESSFULLY! 🎉
echo ========================================
echo.
echo 📦 Build Results:
echo.
echo 🖥️ Application:
echo   - Executable: dist\OnePos\OnePos.exe
echo   - All dependencies included
echo   - Ready to run on any Windows machine
echo   - No Python installation required
echo.
echo 💿 Installer:
echo   - Professional Windows installer created
echo   - Includes desktop and Start menu shortcuts
echo   - Custom installation path selection
echo   - Automatic uninstaller creation
echo   - Multi-language support
echo.
echo 🚀 Distribution Ready:
echo   - Test the installer on different machines
echo   - Distribute installer_output\OnePos_Setup_v1.0.0.exe
echo   - Users can install like any commercial software
echo.
echo 📋 What's Included:
echo   ✅ Main application with all features
echo   ✅ Database system
echo   ✅ Printer support (thermal and standard)
echo   ✅ Barcode scanner integration
echo   ✅ Multi-language interface
echo   ✅ Professional UI
echo   ✅ All required libraries
echo   ✅ Configuration files
echo   ✅ Documentation
echo.
echo 💡 Next Steps:
echo   1. Test installer on clean Windows machine
echo   2. Verify all features work correctly
echo   3. Test printer and scanner integration
echo   4. Distribute to end users
echo.
echo 🎯 Your Python app is now a professional Windows application!
echo.
goto :end

:build_failed
echo.
echo ========================================
echo ❌ BUILD FAILED!
echo ========================================
echo.
echo Please check the error messages above and:
echo 1. Ensure all dependencies are installed
echo 2. Check that all source files are present
echo 3. Verify Python and PyInstaller are working
echo 4. Make sure Inno Setup is installed
echo.
echo For help, check:
echo - Python installation
echo - PyInstaller documentation
echo - Inno Setup documentation
echo.

:end
echo ========================================
pause
