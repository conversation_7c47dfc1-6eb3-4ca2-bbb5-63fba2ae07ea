"""
Lazy Loading Manager for OnePos POS System
Provides efficient data loading with pagination and background loading
"""

from PyQt5.QtCore import QThread, pyqtSignal, QTimer, QObject
from PyQt5.QtWidgets import QTableWidget, QTableWidgetItem, QProgressBar, QLabel
from typing import List, Dict, Any, Callable, Optional
import time


class DataLoader(QThread):
    """Background data loader thread"""
    
    data_loaded = pyqtSignal(list, int)  # data, total_count
    progress_updated = pyqtSignal(int)   # progress percentage
    error_occurred = pyqtSignal(str)     # error message
    
    def __init__(self, data_source: Callable, page_size: int = 50, search_query: str = ""):
        super().__init__()
        self.data_source = data_source
        self.page_size = page_size
        self.search_query = search_query
        self.current_page = 0
        self.is_loading = False
        self.should_stop = False
    
    def run(self):
        """Load data in background"""
        try:
            self.is_loading = True
            
            # Get total count first
            if hasattr(self.data_source, 'count'):
                total_count = self.data_source.count(self.search_query)
            else:
                # Fallback: load all data to count
                all_data = self.data_source(search_query=self.search_query)
                total_count = len(all_data)
            
            # Load data in chunks
            offset = self.current_page * self.page_size
            
            if hasattr(self.data_source, 'get_page'):
                data = self.data_source.get_page(
                    offset=offset,
                    limit=self.page_size,
                    search_query=self.search_query
                )
            else:
                # Fallback: load all and slice
                all_data = self.data_source(search_query=self.search_query)
                data = all_data[offset:offset + self.page_size]
            
            if not self.should_stop:
                self.data_loaded.emit(data, total_count)
                self.progress_updated.emit(100)
            
        except Exception as e:
            if not self.should_stop:
                self.error_occurred.emit(str(e))
        finally:
            self.is_loading = False
    
    def stop_loading(self):
        """Stop the loading process"""
        self.should_stop = True
        self.quit()
        self.wait()


class LazyTableWidget(QTableWidget):
    """Table widget with lazy loading capabilities"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_source = None
        self.page_size = 50
        self.current_page = 0
        self.total_count = 0
        self.search_query = ""
        self.loader_thread = None
        self.loading_timer = QTimer()
        self.loading_timer.timeout.connect(self.show_loading_indicator)
        
        # Loading indicator
        self.loading_label = QLabel("Loading...")
        self.loading_label.setStyleSheet("""
            QLabel {
                background-color: rgba(255, 255, 255, 200);
                color: #000000;
                font-weight: bold;
                font-size: 14px;
                padding: 20px;
                border-radius: 10px;
            }
        """)
        self.loading_label.hide()
        
        # Connect scroll events for infinite scrolling
        self.verticalScrollBar().valueChanged.connect(self.on_scroll)
    
    def set_data_source(self, data_source: Callable, page_size: int = 50):
        """Set the data source for lazy loading"""
        self.data_source = data_source
        self.page_size = page_size
        self.current_page = 0
        self.total_count = 0
    
    def load_data(self, search_query: str = "", reset: bool = True):
        """Load data with optional search"""
        if not self.data_source:
            return
        
        if reset:
            self.current_page = 0
            self.setRowCount(0)
        
        self.search_query = search_query
        
        # Stop any existing loader
        if self.loader_thread and self.loader_thread.isRunning():
            self.loader_thread.stop_loading()
        
        # Start new loader
        self.loader_thread = DataLoader(
            self.data_source,
            self.page_size,
            search_query
        )
        self.loader_thread.data_loaded.connect(self.on_data_loaded)
        self.loader_thread.error_occurred.connect(self.on_error)
        self.loader_thread.start()
        
        # Show loading indicator after delay
        self.loading_timer.start(500)  # 500ms delay
    
    def load_next_page(self):
        """Load the next page of data"""
        if self.current_page * self.page_size < self.total_count:
            self.current_page += 1
            self.load_data(self.search_query, reset=False)
    
    def on_data_loaded(self, data: List[Dict], total_count: int):
        """Handle loaded data"""
        self.loading_timer.stop()
        self.loading_label.hide()
        
        self.total_count = total_count
        
        if self.current_page == 0:
            # First page - clear and set headers
            self.setRowCount(0)
            if data and hasattr(self, 'setup_headers'):
                self.setup_headers(data[0])
        
        # Add data to table
        start_row = self.rowCount()
        self.setRowCount(start_row + len(data))
        
        for i, item in enumerate(data):
            row = start_row + i
            if hasattr(self, 'populate_row'):
                self.populate_row(row, item)
            else:
                # Default population
                for col, (key, value) in enumerate(item.items()):
                    if col < self.columnCount():
                        self.setItem(row, col, QTableWidgetItem(str(value)))
    
    def on_error(self, error_message: str):
        """Handle loading errors"""
        self.loading_timer.stop()
        self.loading_label.hide()
        print(f"Loading error: {error_message}")
    
    def on_scroll(self, value):
        """Handle scroll events for infinite scrolling"""
        scrollbar = self.verticalScrollBar()
        
        # Check if we're near the bottom (90% scrolled)
        if value >= scrollbar.maximum() * 0.9:
            # Check if we have more data to load
            if self.current_page * self.page_size < self.total_count:
                # Check if not already loading
                if not (self.loader_thread and self.loader_thread.isRunning()):
                    self.load_next_page()
    
    def show_loading_indicator(self):
        """Show loading indicator"""
        self.loading_timer.stop()
        
        # Position loading label in center of table
        self.loading_label.setParent(self)
        self.loading_label.resize(200, 60)
        
        # Center the label
        x = (self.width() - self.loading_label.width()) // 2
        y = (self.height() - self.loading_label.height()) // 2
        self.loading_label.move(x, y)
        
        self.loading_label.show()
        self.loading_label.raise_()
    
    def resizeEvent(self, event):
        """Handle resize events"""
        super().resizeEvent(event)
        
        # Reposition loading label if visible
        if self.loading_label.isVisible():
            x = (self.width() - self.loading_label.width()) // 2
            y = (self.height() - self.loading_label.height()) // 2
            self.loading_label.move(x, y)


class SearchableDataManager:
    """Manager for searchable data with debouncing"""
    
    def __init__(self, table_widget: LazyTableWidget, search_delay: int = 300):
        self.table = table_widget
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        self.search_delay = search_delay
        self.pending_query = ""
    
    def search(self, query: str):
        """Perform debounced search"""
        self.pending_query = query
        self.search_timer.stop()
        self.search_timer.start(self.search_delay)
    
    def perform_search(self):
        """Execute the actual search"""
        self.table.load_data(self.pending_query, reset=True)


class PerformanceOptimizer:
    """Optimize table performance for large datasets"""
    
    @staticmethod
    def optimize_table(table: QTableWidget):
        """Apply performance optimizations to table"""
        # Disable sorting during data loading
        table.setSortingEnabled(False)
        
        # Set uniform row heights for better performance
        table.verticalHeader().setDefaultSectionSize(25)
        table.verticalHeader().setSectionResizeMode(table.verticalHeader().Fixed)
        
        # Optimize selection behavior
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        
        # Disable word wrap for better performance
        table.setWordWrap(False)
        
        # Set alternating row colors
        table.setAlternatingRowColors(True)
    
    @staticmethod
    def enable_sorting(table: QTableWidget):
        """Re-enable sorting after data is loaded"""
        table.setSortingEnabled(True)
    
    @staticmethod
    def batch_update(table: QTableWidget, updates: List[Callable]):
        """Perform batch updates for better performance"""
        # Disable updates during batch operation
        table.setUpdatesEnabled(False)
        
        try:
            for update_func in updates:
                update_func()
        finally:
            # Re-enable updates
            table.setUpdatesEnabled(True)


class DataCache:
    """Simple data cache for frequently accessed data"""
    
    def __init__(self, max_size: int = 100):
        self.cache = {}
        self.access_order = []
        self.max_size = max_size
    
    def get(self, key: str) -> Optional[Any]:
        """Get cached data"""
        if key in self.cache:
            # Move to end (most recently used)
            self.access_order.remove(key)
            self.access_order.append(key)
            return self.cache[key]
        return None
    
    def set(self, key: str, data: Any):
        """Cache data"""
        if key in self.cache:
            # Update existing
            self.cache[key] = data
            self.access_order.remove(key)
            self.access_order.append(key)
        else:
            # Add new
            if len(self.cache) >= self.max_size:
                # Remove least recently used
                oldest_key = self.access_order.pop(0)
                del self.cache[oldest_key]
            
            self.cache[key] = data
            self.access_order.append(key)
    
    def clear(self):
        """Clear cache"""
        self.cache.clear()
        self.access_order.clear()


# Global instances
data_cache = DataCache(max_size=200)
performance_optimizer = PerformanceOptimizer()
