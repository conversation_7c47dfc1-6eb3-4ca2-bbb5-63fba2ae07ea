"""
Check database tables and structure
"""

from models.database import db

def check_tables():
    """Check what tables exist in the database"""
    print("🔍 Checking database tables...")
    
    # Get all tables
    result = db.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row['name'] for row in result]
    
    print(f"📊 Found {len(tables)} tables:")
    for table in tables:
        print(f"   - {table}")
    
    # Check purchases table specifically
    if 'purchases' in tables:
        print("\n📥 Checking purchases table structure...")
        result = db.execute_query("PRAGMA table_info(purchases)")
        columns = [row['name'] for row in result]
        print(f"   Columns: {columns}")
        
        if 'purchase_number' in columns:
            print("   ✅ purchase_number column exists")
        else:
            print("   ❌ purchase_number column missing")
    else:
        print("\n❌ purchases table does not exist")
    
    # Check suppliers table
    if 'suppliers' in tables:
        print("\n🏢 Checking suppliers table structure...")
        result = db.execute_query("PRAGMA table_info(suppliers)")
        columns = [row['name'] for row in result]
        print(f"   Columns: {columns}")
    else:
        print("\n❌ suppliers table does not exist")
    
    # Check purchase_items table
    if 'purchase_items' in tables:
        print("\n📦 Checking purchase_items table structure...")
        result = db.execute_query("PRAGMA table_info(purchase_items)")
        columns = [row['name'] for row in result]
        print(f"   Columns: {columns}")
    else:
        print("\n❌ purchase_items table does not exist")

if __name__ == "__main__":
    check_tables()
