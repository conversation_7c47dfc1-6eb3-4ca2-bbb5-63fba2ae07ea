#!/usr/bin/env python3
"""
Test script to verify OnePos setup
"""

import sys
import os

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all modules can be imported"""
    print("Testing imports...")
    
    try:
        # Test database
        from models.database import db
        print("✓ Database module imported successfully")
        
        # Test models
        from models.user import User
        from models.product import Product, Category
        from models.customer import Customer
        from models.sale import Sale, SaleItem
        from models.stock import StockMovement
        print("✓ Model modules imported successfully")
        
        # Test utilities
        from utils.config_manager import config
        from utils.translator import translator, tr
        try:
            from utils.barcode_utils import BarcodeGenerator, BarcodeScanner
            print("✓ Utility modules imported successfully")
        except Exception as barcode_error:
            print(f"⚠ Barcode utilities partially available: {barcode_error}")
            print("✓ Core utility modules imported successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def test_database():
    """Test database connection and basic operations"""
    print("\nTesting database...")
    
    try:
        from models.database import db
        
        # Test connection
        result = db.execute_query("SELECT COUNT(*) as count FROM users")
        user_count = result[0]['count'] if result else 0
        print(f"✓ Database connected. Users count: {user_count}")
        
        # Test admin user
        from models.user import User
        admin_user = User.get_by_username('admin')
        if admin_user:
            print(f"✓ Admin user found: {admin_user.full_name}")
        else:
            print("✗ Admin user not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False

def test_config():
    """Test configuration manager"""
    print("\nTesting configuration...")
    
    try:
        from utils.config_manager import config
        
        app_name = config.get_app_name()
        language = config.get_language()
        theme = config.get_theme()
        
        print(f"✓ App name: {app_name}")
        print(f"✓ Language: {language}")
        print(f"✓ Theme: {theme}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def test_translations():
    """Test translation system"""
    print("\nTesting translations...")
    
    try:
        from utils.translator import translator, tr
        
        # Test English
        translator.set_language('en')
        english_text = tr('common.save')
        print(f"✓ English: {english_text}")
        
        # Test Arabic
        translator.set_language('ar')
        arabic_text = tr('common.save')
        print(f"✓ Arabic: {arabic_text}")
        
        # Test French
        translator.set_language('fr')
        french_text = tr('common.save')
        print(f"✓ French: {french_text}")
        
        return True
        
    except Exception as e:
        print(f"✗ Translation error: {e}")
        return False

def test_models():
    """Test basic model operations"""
    print("\nTesting models...")
    
    try:
        from models.product import Category, Product
        from models.customer import Customer
        
        # Test categories
        categories = Category.get_all()
        print(f"✓ Categories count: {len(categories)}")
        
        # Test products
        products = Product.get_all()
        print(f"✓ Products count: {len(products)}")
        
        # Test customers
        customers = Customer.get_all()
        print(f"✓ Customers count: {len(customers)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Models error: {e}")
        return False

def test_barcode():
    """Test barcode utilities"""
    print("\nTesting barcode utilities...")

    try:
        from utils.barcode_utils import BarcodeGenerator

        # Test barcode generation (basic functionality)
        barcode_img = BarcodeGenerator.generate_barcode("123456789012")
        if barcode_img:
            print("✓ Barcode generation successful")
        else:
            print("✗ Barcode generation failed")
            return False

        print("✓ Barcode utilities basic functionality working")
        return True

    except Exception as e:
        print(f"✗ Barcode error: {e}")
        # Don't fail the test for optional features
        print("⚠ Barcode scanning may not work (optional feature)")
        return True

def main():
    """Run all tests"""
    print("OnePos Setup Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_database,
        test_config,
        test_translations,
        test_models,
        test_barcode
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! OnePos is ready to run.")
        return 0
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
