@echo off
echo ========================================
echo       OnePos - نظام نقاط البيع
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تشغيل install.bat أولاً
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo خطأ: البيئة الافتراضية غير موجودة
    echo يرجى تشغيل install.bat أولاً
    pause
    exit /b 1
)

echo تشغيل OnePos...
echo.

REM Activate virtual environment and run
call venv\Scripts\activate.bat
python main.py

if errorlevel 1 (
    echo.
    echo خطأ في تشغيل التطبيق
    pause
)

deactivate
