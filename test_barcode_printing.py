"""
Test barcode printing functionality for OnePos POS System
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from models.database import db
from models.product import Product
from utils.barcode_generator import BarcodeGenerator, BarcodePrintDialog

def test_barcode_generation():
    """Test barcode image generation"""
    print("🏷️ Testing Barcode Generation...")
    
    try:
        # Test basic barcode generation
        barcode_data = "1234567890123"
        product_name = "Test Product"
        price = "15.99"
        
        # Generate barcode image
        barcode_image = BarcodeGenerator.generate_barcode_image(
            barcode_data, product_name, price
        )
        
        if barcode_image:
            print("✅ Barcode image generated successfully")
            print(f"   - Image size: {barcode_image.size}")
            print(f"   - Image mode: {barcode_image.mode}")
        else:
            print("❌ Failed to generate barcode image")
            return False
        
        # Test saving barcode image
        filename = "test_barcode.png"
        saved_file = BarcodeGenerator.save_barcode_image(
            barcode_data, product_name, price, filename
        )
        
        if os.path.exists(saved_file):
            print(f"✅ Barcode image saved: {saved_file}")
            # Clean up
            os.remove(saved_file)
        else:
            print("❌ Failed to save barcode image")
            return False
        
        return True
        
    except ImportError as e:
        print(f"⚠️ Barcode library not available: {e}")
        print("   Install with: pip install python-barcode[images] pillow")
        return True  # Not a failure, just missing dependency
        
    except Exception as e:
        print(f"❌ Barcode generation test failed: {e}")
        return False

def test_barcode_formats():
    """Test different barcode formats"""
    print("\n📊 Testing Barcode Formats...")
    
    try:
        test_cases = [
            ("1234567890123", "code128", "13-digit Code128"),
            ("123456789012", "ean13", "12-digit EAN13"),
            ("12345678", "ean8", "8-digit EAN8"),
            ("ABCD1234", "code128", "Alphanumeric Code128")
        ]
        
        for barcode_data, format_type, description in test_cases:
            try:
                barcode_image = BarcodeGenerator.generate_barcode_image(
                    barcode_data, f"Test {description}", "10.00", format_type
                )
                
                if barcode_image:
                    print(f"✅ {description}: Generated successfully")
                else:
                    print(f"❌ {description}: Failed to generate")
                    
            except Exception as e:
                print(f"⚠️ {description}: {str(e)}")
        
        return True
        
    except ImportError:
        print("⚠️ Barcode library not available")
        return True
        
    except Exception as e:
        print(f"❌ Barcode formats test failed: {e}")
        return False

def test_barcode_dialog():
    """Test barcode print dialog"""
    print("\n🖥️ Testing Barcode Print Dialog...")
    
    try:
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Get a product to test with
        products = Product.get_all()
        if not products:
            print("⚠️ No products available for dialog testing")
            return True
        
        test_product = products[0]
        print(f"✅ Testing with product: {test_product.name}")
        
        # Ensure product has barcode
        if not test_product.barcode:
            test_product.update(barcode="1234567890123")
            print("✅ Added barcode to test product")
        
        # Create dialog (don't show it, just test creation)
        try:
            dialog = BarcodePrintDialog(test_product)
            print("✅ Barcode print dialog created successfully")
            
            # Test dialog components
            if hasattr(dialog, 'copies_spin'):
                print("✅ Copies spinner exists")
            
            if hasattr(dialog, 'format_combo'):
                print("✅ Format combo exists")
            
            if hasattr(dialog, 'include_price_combo'):
                print("✅ Include price combo exists")
            
            if hasattr(dialog, 'preview_label'):
                print("✅ Preview label exists")
            
            # Test preview generation
            dialog.generate_preview()
            print("✅ Preview generation completed")
            
            return True
            
        except ImportError:
            print("⚠️ Barcode library not available for dialog")
            return True
            
    except Exception as e:
        print(f"❌ Barcode dialog test failed: {e}")
        return False

def test_product_barcode_integration():
    """Test barcode integration with products"""
    print("\n🔗 Testing Product-Barcode Integration...")
    
    try:
        # Get products
        products = Product.get_all()
        print(f"✅ Found {len(products)} products")
        
        # Check barcode availability
        products_with_barcode = [p for p in products if p.barcode]
        print(f"✅ {len(products_with_barcode)} products have barcodes")
        
        # Test barcode generation for products without barcodes
        products_without_barcode = [p for p in products if not p.barcode]
        if products_without_barcode:
            test_product = products_without_barcode[0]
            
            # Generate barcode
            import random
            import string
            new_barcode = ''.join(random.choices(string.digits, k=13))
            
            test_product.update(barcode=new_barcode)
            print(f"✅ Generated barcode for {test_product.name}: {new_barcode}")
            
            # Test barcode lookup
            found_product = Product.get_by_barcode(new_barcode)
            if found_product and found_product.id == test_product.id:
                print("✅ Barcode lookup successful")
            else:
                print("❌ Barcode lookup failed")
                return False
        
        # Test barcode validation
        valid_barcodes = 0
        for product in products:
            if product.barcode:
                # Check barcode length and format
                if len(product.barcode) in [8, 12, 13] and product.barcode.isdigit():
                    valid_barcodes += 1
                elif len(product.barcode) > 0:  # Allow alphanumeric for Code128
                    valid_barcodes += 1
        
        print(f"✅ {valid_barcodes} products have valid barcodes")
        
        return True
        
    except Exception as e:
        print(f"❌ Product-barcode integration test failed: {e}")
        return False

def test_barcode_printing_workflow():
    """Test complete barcode printing workflow"""
    print("\n🖨️ Testing Barcode Printing Workflow...")
    
    try:
        # Get a product
        products = Product.get_all()
        if not products:
            print("⚠️ No products available for workflow testing")
            return True
        
        test_product = products[0]
        
        # Ensure product has all required data
        if not test_product.barcode:
            test_product.update(barcode="1234567890123")
        
        print(f"✅ Testing workflow with product: {test_product.name}")
        print(f"   - Barcode: {test_product.barcode}")
        print(f"   - Price: ${test_product.selling_price:.2f}")
        
        # Test data preparation for printing
        print_data = {
            'barcode': test_product.barcode,
            'name': test_product.name,
            'price': f"{test_product.selling_price:.2f}",
            'sku': test_product.sku or "",
            'copies': 1
        }
        
        print("✅ Print data prepared:")
        for key, value in print_data.items():
            print(f"   - {key}: {value}")
        
        # Test barcode image generation for printing
        try:
            barcode_image = BarcodeGenerator.generate_barcode_image(
                print_data['barcode'],
                print_data['name'],
                print_data['price']
            )
            
            if barcode_image:
                print("✅ Barcode image ready for printing")
                print(f"   - Dimensions: {barcode_image.size}")
            else:
                print("❌ Failed to generate barcode for printing")
                return False
                
        except ImportError:
            print("⚠️ Barcode library not available")
            return True
        
        return True
        
    except Exception as e:
        print(f"❌ Barcode printing workflow test failed: {e}")
        return False

def run_barcode_printing_tests():
    """Run all barcode printing tests"""
    print("🏷️ STARTING BARCODE PRINTING TESTS")
    print("=" * 50)
    
    test_results = []
    
    # Test 1: Barcode Generation
    test_results.append(("Barcode Generation", test_barcode_generation()))
    
    # Test 2: Barcode Formats
    test_results.append(("Barcode Formats", test_barcode_formats()))
    
    # Test 3: Barcode Dialog
    test_results.append(("Barcode Dialog", test_barcode_dialog()))
    
    # Test 4: Product Integration
    test_results.append(("Product Integration", test_product_barcode_integration()))
    
    # Test 5: Printing Workflow
    test_results.append(("Printing Workflow", test_barcode_printing_workflow()))
    
    # Print results summary
    print("\n" + "=" * 50)
    print("🏁 BARCODE PRINTING TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL BARCODE TESTS PASSED! Barcode printing is ready!")
    else:
        print("⚠️  Some barcode tests failed. Check dependencies and setup.")
    
    return passed == total

if __name__ == "__main__":
    run_barcode_printing_tests()
