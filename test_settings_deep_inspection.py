"""
Deep Inspection Test for OnePos Settings System
Comprehensive re-testing to find hidden bugs and issues
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QTabWidget, QComboBox, QLineEdit, QPushButton, QCheckBox, QTextEdit, QSpinBox
from PyQt5.QtCore import Qt
from utils.config_manager import config
from utils.translator import tr, change_language, get_current_language
from views.settings_widget import SettingsWidget, GeneralSettingsTab, CompanySettingsTab, PrinterSettingsTab, BackupSettingsTab
from views.main_window import MainWindow
from models.user import User
import time
import traceback

def test_settings_widget_structure():
    """Deep inspection of settings widget structure"""
    print("🔍 Deep Inspection: Settings Widget Structure...")

    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # Create settings widget
        settings_widget = SettingsWidget()

        # Check main widget properties
        print(f"✅ Settings widget type: {type(settings_widget).__name__}")
        print(f"✅ Widget visible: {settings_widget.isVisible()}")
        print(f"✅ Widget enabled: {settings_widget.isEnabled()}")

        # Check tabs widget
        if hasattr(settings_widget, 'tabs'):
            tabs = settings_widget.tabs
            print(f"✅ Tabs widget: {type(tabs).__name__}")
            print(f"✅ Tabs count: {tabs.count()}")
            print(f"✅ Current tab: {tabs.currentIndex()}")

            # Inspect each tab thoroughly
            for i in range(tabs.count()):
                tab_text = tabs.tabText(i)
                tab_widget = tabs.widget(i)

                print(f"\n   📋 Tab {i}: '{tab_text}'")
                print(f"     - Widget type: {type(tab_widget).__name__}")
                print(f"     - Widget visible: {tab_widget.isVisible()}")
                print(f"     - Widget enabled: {tab_widget.isEnabled()}")

                # Check tab widget layout
                layout = tab_widget.layout()
                if layout:
                    print(f"     - Layout type: {type(layout).__name__}")
                    print(f"     - Layout items: {layout.count()}")
                else:
                    print(f"     - Layout: None")

                # Count all child widgets
                all_children = tab_widget.findChildren(object)
                line_edits = tab_widget.findChildren(QLineEdit)
                combos = tab_widget.findChildren(QComboBox)
                buttons = tab_widget.findChildren(QPushButton)
                checkboxes = tab_widget.findChildren(QCheckBox)
                text_edits = tab_widget.findChildren(QTextEdit)
                spin_boxes = tab_widget.findChildren(QSpinBox)

                print(f"     - Total children: {len(all_children)}")
                print(f"     - Line edits: {len(line_edits)}")
                print(f"     - Combo boxes: {len(combos)}")
                print(f"     - Buttons: {len(buttons)}")
                print(f"     - Checkboxes: {len(checkboxes)}")
                print(f"     - Text edits: {len(text_edits)}")
                print(f"     - Spin boxes: {len(spin_boxes)}")
        else:
            print("❌ Tabs widget not found")
            return False

        # Check save button
        if hasattr(settings_widget, 'save_button'):
            save_button = settings_widget.save_button
            print(f"\n✅ Save button: {save_button.text()}")
            print(f"   - Enabled: {save_button.isEnabled()}")
            print(f"   - Visible: {save_button.isVisible()}")
        else:
            print("\n⚠️ Main save button not found")

        # Check reset button
        if hasattr(settings_widget, 'reset_button'):
            reset_button = settings_widget.reset_button
            print(f"✅ Reset button: {reset_button.text()}")
            print(f"   - Enabled: {reset_button.isEnabled()}")
            print(f"   - Visible: {reset_button.isVisible()}")
        else:
            print("⚠️ Reset button not found")

        # Check status label
        if hasattr(settings_widget, 'status_label'):
            status_label = settings_widget.status_label
            print(f"✅ Status label: '{status_label.text()}'")
            print(f"   - Visible: {status_label.isVisible()}")
        else:
            print("⚠️ Status label not found")

        return True

    except Exception as e:
        print(f"❌ Settings widget structure test failed: {e}")
        traceback.print_exc()
        return False

def test_general_settings_deep():
    """Deep inspection of general settings tab"""
    print("\n⚙️ Deep Inspection: General Settings Tab...")

    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        settings_widget = SettingsWidget()

        # Find general settings tab
        general_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, GeneralSettingsTab):
                general_tab = tab_widget
                settings_widget.tabs.setCurrentIndex(i)
                break

        if not general_tab:
            print("❌ General settings tab not found")
            return False

        print("✅ General settings tab found")

        # Test language combo in detail
        if hasattr(general_tab, 'language_combo'):
            lang_combo = general_tab.language_combo
            print(f"✅ Language combo found: {type(lang_combo).__name__}")
            print(f"   - Current index: {lang_combo.currentIndex()}")
            print(f"   - Current text: '{lang_combo.currentText()}'")
            print(f"   - Current data: {lang_combo.currentData()}")
            print(f"   - Enabled: {lang_combo.isEnabled()}")
            print(f"   - Visible: {lang_combo.isVisible()}")

            # Test each language option
            for i in range(lang_combo.count()):
                text = lang_combo.itemText(i)
                data = lang_combo.itemData(i)
                print(f"     Option {i}: '{text}' -> {data}")

                # Test if data is valid
                if data and isinstance(data, str) and len(data) == 2:
                    print(f"       ✅ Valid language code: {data}")
                else:
                    print(f"       ❌ Invalid language code: {data}")
                    return False

            # Test language change functionality
            original_index = lang_combo.currentIndex()
            original_language = get_current_language()

            print(f"   Testing language change from {original_language}...")

            # Try changing to each language
            for i in range(lang_combo.count()):
                if i != original_index:
                    lang_code = lang_combo.itemData(i)
                    if lang_code:
                        print(f"     Testing change to {lang_code}...")

                        # Change language
                        lang_combo.setCurrentIndex(i)

                        # Check if on_language_changed method exists
                        if hasattr(general_tab, 'on_language_changed'):
                            general_tab.on_language_changed()

                            # Verify language actually changed
                            new_language = get_current_language()
                            if new_language == lang_code:
                                print(f"       ✅ Language changed successfully to {lang_code}")
                            else:
                                print(f"       ❌ Language change failed: expected {lang_code}, got {new_language}")
                                return False
                        else:
                            print(f"       ❌ on_language_changed method not found")
                            return False

                        # Test only one change to avoid too many switches
                        break

            # Restore original language
            lang_combo.setCurrentIndex(original_index)
            if hasattr(general_tab, 'on_language_changed'):
                general_tab.on_language_changed()
        else:
            print("❌ Language combo not found")
            return False

        # Test theme combo in detail
        if hasattr(general_tab, 'theme_combo'):
            theme_combo = general_tab.theme_combo
            print(f"✅ Theme combo found: {type(theme_combo).__name__}")
            print(f"   - Current index: {theme_combo.currentIndex()}")
            print(f"   - Current text: '{theme_combo.currentText()}'")
            print(f"   - Enabled: {theme_combo.isEnabled()}")
            print(f"   - Visible: {theme_combo.isVisible()}")

            # Test each theme option
            for i in range(theme_combo.count()):
                text = theme_combo.itemText(i)
                print(f"     Theme {i}: '{text}'")

            # Test theme change
            if hasattr(general_tab, 'on_theme_changed'):
                original_index = theme_combo.currentIndex()

                # Try changing theme
                new_index = (original_index + 1) % theme_combo.count()
                theme_combo.setCurrentIndex(new_index)

                try:
                    general_tab.on_theme_changed()
                    print(f"   ✅ Theme change method works")
                except Exception as e:
                    print(f"   ❌ Theme change failed: {e}")
                    return False

                # Restore original theme
                theme_combo.setCurrentIndex(original_index)
            else:
                print("   ⚠️ on_theme_changed method not found")
        else:
            print("❌ Theme combo not found")
            return False

        # Test checkboxes
        checkbox_fields = [
            'auto_backup_check',
            'auto_update_check',
            'show_tips_check',
            'remember_window_check'
        ]

        for field_name in checkbox_fields:
            if hasattr(general_tab, field_name):
                checkbox = getattr(general_tab, field_name)
                print(f"✅ {field_name}: {checkbox.isChecked()}")
                print(f"   - Text: '{checkbox.text()}'")
                print(f"   - Enabled: {checkbox.isEnabled()}")
                print(f"   - Visible: {checkbox.isVisible()}")

                # Test toggling
                original_state = checkbox.isChecked()
                checkbox.setChecked(not original_state)
                new_state = checkbox.isChecked()

                if new_state != original_state:
                    print(f"   ✅ Checkbox toggle works: {original_state} -> {new_state}")
                    # Restore original state
                    checkbox.setChecked(original_state)
                else:
                    print(f"   ❌ Checkbox toggle failed")
                    return False
            else:
                print(f"⚠️ {field_name} not found")

        # Test save/load methods
        if hasattr(general_tab, 'load_settings'):
            try:
                general_tab.load_settings()
                print("✅ load_settings method works")
            except Exception as e:
                print(f"❌ load_settings failed: {e}")
                return False
        else:
            print("⚠️ load_settings method not found")

        if hasattr(general_tab, 'save_settings'):
            try:
                general_tab.save_settings()
                print("✅ save_settings method works")
            except Exception as e:
                print(f"❌ save_settings failed: {e}")
                return False
        else:
            print("⚠️ save_settings method not found")

        return True

    except Exception as e:
        print(f"❌ General settings deep test failed: {e}")
        traceback.print_exc()
        return False

def test_company_settings_deep():
    """Deep inspection of company settings tab"""
    print("\n🏢 Deep Inspection: Company Settings Tab...")

    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        settings_widget = SettingsWidget()

        # Find company settings tab
        company_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, CompanySettingsTab):
                company_tab = tab_widget
                settings_widget.tabs.setCurrentIndex(i)
                break

        if not company_tab:
            print("❌ Company settings tab not found")
            return False

        print("✅ Company settings tab found")

        # Test all company fields in detail
        company_fields = [
            ('company_name_edit', 'Company Name', QLineEdit),
            ('company_address_edit', 'Company Address', QTextEdit),
            ('company_phone_edit', 'Company Phone', QLineEdit),
            ('company_email_edit', 'Company Email', QLineEdit),
            ('tax_number_edit', 'Tax Number', QLineEdit)
        ]

        for field_name, description, expected_type in company_fields:
            if hasattr(company_tab, field_name):
                field = getattr(company_tab, field_name)
                print(f"✅ {description}: Found")
                print(f"   - Type: {type(field).__name__}")
                print(f"   - Expected: {expected_type.__name__}")
                print(f"   - Enabled: {field.isEnabled()}")
                print(f"   - Visible: {field.isVisible()}")

                # Check field content
                if isinstance(field, QLineEdit):
                    content = field.text()
                    print(f"   - Content: '{content}'")
                    print(f"   - Max length: {field.maxLength()}")
                    print(f"   - Read only: {field.isReadOnly()}")

                    # Test input
                    original_text = field.text()
                    test_text = "TEST_INPUT"
                    field.setText(test_text)

                    if field.text() == test_text:
                        print(f"   ✅ Input test passed")
                        # Restore original
                        field.setText(original_text)
                    else:
                        print(f"   ❌ Input test failed")
                        return False

                elif isinstance(field, QTextEdit):
                    content = field.toPlainText()
                    print(f"   - Content: '{content}'")
                    print(f"   - Read only: {field.isReadOnly()}")

                    # Test input
                    original_text = field.toPlainText()
                    test_text = "TEST_INPUT"
                    field.setPlainText(test_text)

                    if field.toPlainText() == test_text:
                        print(f"   ✅ Input test passed")
                        # Restore original
                        field.setPlainText(original_text)
                    else:
                        print(f"   ❌ Input test failed")
                        return False

                # Check if type matches expected
                if isinstance(field, expected_type):
                    print(f"   ✅ Type matches expected")
                else:
                    print(f"   ⚠️ Type mismatch: expected {expected_type.__name__}, got {type(field).__name__}")
            else:
                print(f"❌ {description} ({field_name}) not found")
                return False

        # Test logo upload button
        if hasattr(company_tab, 'logo_button'):
            logo_button = company_tab.logo_button
            print(f"✅ Logo button: '{logo_button.text()}'")
            print(f"   - Enabled: {logo_button.isEnabled()}")
            print(f"   - Visible: {logo_button.isVisible()}")
        else:
            print("⚠️ Logo upload button not found")

        # Test currency combo
        if hasattr(company_tab, 'currency_combo'):
            currency_combo = company_tab.currency_combo
            print(f"✅ Currency combo: {currency_combo.count()} options")

            for i in range(currency_combo.count()):
                currency = currency_combo.itemText(i)
                print(f"   - Currency {i}: {currency}")
        else:
            print("⚠️ Currency combo not found")

        # Test validation methods
        validation_methods = [
            'validate_email',
            'validate_phone',
            'validate_tax_number'
        ]

        for method_name in validation_methods:
            if hasattr(company_tab, method_name):
                method = getattr(company_tab, method_name)
                print(f"✅ {method_name} method available")

                # Test email validation if available
                if method_name == 'validate_email':
                    try:
                        # Test valid email
                        valid_result = method("<EMAIL>")
                        print(f"   - Valid email test: {valid_result}")

                        # Test invalid email
                        invalid_result = method("invalid-email")
                        print(f"   - Invalid email test: {invalid_result}")

                        if valid_result and not invalid_result:
                            print(f"   ✅ Email validation works correctly")
                        else:
                            print(f"   ⚠️ Email validation may be lenient")
                    except Exception as e:
                        print(f"   ❌ Email validation failed: {e}")
                        return False
            else:
                print(f"⚠️ {method_name} method not found")

        # Test save/load methods
        if hasattr(company_tab, 'load_settings'):
            try:
                company_tab.load_settings()
                print("✅ Company load_settings works")
            except Exception as e:
                print(f"❌ Company load_settings failed: {e}")
                return False
        else:
            print("⚠️ Company load_settings not found")

        if hasattr(company_tab, 'save_settings'):
            try:
                company_tab.save_settings()
                print("✅ Company save_settings works")
            except Exception as e:
                print(f"❌ Company save_settings failed: {e}")
                return False
        else:
            print("⚠️ Company save_settings not found")

        return True

    except Exception as e:
        print(f"❌ Company settings deep test failed: {e}")
        traceback.print_exc()
        return False

def test_printer_settings_deep():
    """Deep inspection of printer settings tab"""
    print("\n🖨️ Deep Inspection: Printer Settings Tab...")

    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        settings_widget = SettingsWidget()

        # Find printer settings tab
        printer_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, PrinterSettingsTab):
                printer_tab = tab_widget
                settings_widget.tabs.setCurrentIndex(i)
                break

        if not printer_tab:
            print("❌ Printer settings tab not found")
            return False

        print("✅ Printer settings tab found")

        # Test printer combo boxes
        printer_combos = [
            ('receipt_printer_combo', 'Receipt Printer'),
            ('invoice_printer_combo', 'Invoice Printer'),
            ('label_printer_combo', 'Label Printer'),
            ('paper_size_combo', 'Paper Size')
        ]

        for field_name, description in printer_combos:
            if hasattr(printer_tab, field_name):
                combo = getattr(printer_tab, field_name)
                print(f"✅ {description}: Found")
                print(f"   - Type: {type(combo).__name__}")
                print(f"   - Count: {combo.count()}")
                print(f"   - Current: '{combo.currentText()}'")
                print(f"   - Enabled: {combo.isEnabled()}")
                print(f"   - Visible: {combo.isVisible()}")

                # List all options
                for i in range(combo.count()):
                    option = combo.itemText(i)
                    print(f"     Option {i}: '{option}'")
            else:
                print(f"⚠️ {description} ({field_name}) not found")

        # Test printer checkboxes
        printer_checks = [
            ('print_logo_check', 'Print Logo'),
            ('print_header_check', 'Print Header'),
            ('print_footer_check', 'Print Footer'),
            ('auto_cut_check', 'Auto Cut'),
            ('cash_drawer_check', 'Cash Drawer')
        ]

        for field_name, description in printer_checks:
            if hasattr(printer_tab, field_name):
                checkbox = getattr(printer_tab, field_name)
                print(f"✅ {description}: {checkbox.isChecked()}")
                print(f"   - Text: '{checkbox.text()}'")
                print(f"   - Enabled: {checkbox.isEnabled()}")
                print(f"   - Visible: {checkbox.isVisible()}")
            else:
                print(f"⚠️ {description} ({field_name}) not found")

        # Test printer buttons
        printer_buttons = [
            ('detect_printers_button', 'Detect Printers'),
            ('test_print_button', 'Test Print'),
            ('print_settings_button', 'Print Settings')
        ]

        for field_name, description in printer_buttons:
            if hasattr(printer_tab, field_name):
                button = getattr(printer_tab, field_name)
                print(f"✅ {description}: '{button.text()}'")
                print(f"   - Enabled: {button.isEnabled()}")
                print(f"   - Visible: {button.isVisible()}")
            else:
                print(f"⚠️ {description} ({field_name}) not found")

        # Test printer methods
        printer_methods = [
            'detect_printers',
            'test_print_receipt',
            'test_print_invoice',
            'load_printer_settings',
            'save_printer_settings'
        ]

        for method_name in printer_methods:
            if hasattr(printer_tab, method_name):
                print(f"✅ {method_name} method available")
            else:
                print(f"⚠️ {method_name} method not found")

        return True

    except Exception as e:
        print(f"❌ Printer settings deep test failed: {e}")
        traceback.print_exc()
        return False

def test_backup_settings_deep():
    """Deep inspection of backup settings tab"""
    print("\n💾 Deep Inspection: Backup Settings Tab...")

    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        settings_widget = SettingsWidget()

        # Find backup settings tab
        backup_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, BackupSettingsTab):
                backup_tab = tab_widget
                settings_widget.tabs.setCurrentIndex(i)
                break

        if not backup_tab:
            print("❌ Backup settings tab not found")
            return False

        print("✅ Backup settings tab found")

        # Test backup fields
        backup_fields = [
            ('backup_path_edit', 'Backup Path', QLineEdit),
            ('backup_interval_spin', 'Backup Interval', QSpinBox),
            ('auto_backup_check', 'Auto Backup', QCheckBox)
        ]

        for field_name, description, expected_type in backup_fields:
            if hasattr(backup_tab, field_name):
                field = getattr(backup_tab, field_name)
                print(f"✅ {description}: Found")
                print(f"   - Type: {type(field).__name__}")
                print(f"   - Expected: {expected_type.__name__}")
                print(f"   - Enabled: {field.isEnabled()}")
                print(f"   - Visible: {field.isVisible()}")

                if isinstance(field, QLineEdit):
                    print(f"   - Content: '{field.text()}'")
                elif isinstance(field, QSpinBox):
                    print(f"   - Value: {field.value()}")
                    print(f"   - Range: {field.minimum()} - {field.maximum()}")
                elif isinstance(field, QCheckBox):
                    print(f"   - Checked: {field.isChecked()}")
                    print(f"   - Text: '{field.text()}'")
            else:
                print(f"⚠️ {description} ({field_name}) not found")

        # Test backup buttons
        backup_buttons = [
            ('backup_button', 'Create Backup'),
            ('restore_button', 'Restore Backup'),
            ('browse_button', 'Browse Path'),
            ('schedule_button', 'Schedule Backup')
        ]

        for field_name, description in backup_buttons:
            if hasattr(backup_tab, field_name):
                button = getattr(backup_tab, field_name)
                print(f"✅ {description}: '{button.text()}'")
                print(f"   - Enabled: {button.isEnabled()}")
                print(f"   - Visible: {button.isVisible()}")
            else:
                print(f"⚠️ {description} ({field_name}) not found")

        # Test backup methods
        backup_methods = [
            'create_backup',
            'restore_backup',
            'browse_backup_path',
            'schedule_backup',
            'load_backup_settings',
            'save_backup_settings'
        ]

        for method_name in backup_methods:
            if hasattr(backup_tab, method_name):
                print(f"✅ {method_name} method available")
            else:
                print(f"⚠️ {method_name} method not found")

        return True

    except Exception as e:
        print(f"❌ Backup settings deep test failed: {e}")
        traceback.print_exc()
        return False

def test_settings_integration_deep():
    """Deep inspection of settings integration with main app"""
    print("\n🔗 Deep Inspection: Settings Integration...")

    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"

        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()

        print("✅ Main window created")

        # Test navigation to settings
        if 'settings' in main_window.nav_buttons:
            settings_button = main_window.nav_buttons['settings']
            print(f"✅ Settings button found: '{settings_button.text()}'")

            # Click settings button
            settings_button.click()
            app.processEvents()

            # Check if settings widget is loaded
            current_widget = main_window.stacked_widget.currentWidget()
            if isinstance(current_widget, SettingsWidget):
                settings_widget = current_widget
                print("✅ Settings widget loaded in main window")

                # Test each tab in main window context
                for i in range(settings_widget.tabs.count()):
                    tab_text = settings_widget.tabs.tabText(i)
                    settings_widget.tabs.setCurrentIndex(i)
                    app.processEvents()

                    current_tab = settings_widget.tabs.currentWidget()
                    print(f"✅ Tab {i} ({tab_text}): {type(current_tab).__name__}")

                    # Test if tab is properly displayed
                    if current_tab.isVisible():
                        print(f"   - Visible: ✅")
                    else:
                        print(f"   - Visible: ❌")
                        return False

                    if current_tab.isEnabled():
                        print(f"   - Enabled: ✅")
                    else:
                        print(f"   - Enabled: ❌")
                        return False

                # Test language change integration
                general_tab = None
                for i in range(settings_widget.tabs.count()):
                    tab_widget = settings_widget.tabs.widget(i)
                    if isinstance(tab_widget, GeneralSettingsTab):
                        general_tab = tab_widget
                        settings_widget.tabs.setCurrentIndex(i)
                        app.processEvents()
                        break

                if general_tab and hasattr(general_tab, 'language_combo'):
                    language_combo = general_tab.language_combo
                    original_language = get_current_language()

                    print(f"   Testing language integration from {original_language}...")

                    # Test language change
                    for i in range(language_combo.count()):
                        lang_code = language_combo.itemData(i)
                        if lang_code and lang_code != original_language:
                            print(f"     Changing to {lang_code}...")

                            # Change language
                            language_combo.setCurrentIndex(i)
                            general_tab.on_language_changed()
                            app.processEvents()
                            time.sleep(0.1)

                            # Check if main window updated
                            new_language = get_current_language()
                            if new_language == lang_code:
                                print(f"     ✅ Language changed to {lang_code}")

                                # Check navigation button text
                                nav_text = main_window.nav_buttons['settings'].text()
                                print(f"       Settings button: '{nav_text}'")

                                # Check tab texts
                                for j in range(settings_widget.tabs.count()):
                                    tab_text = settings_widget.tabs.tabText(j)
                                    print(f"       Tab {j}: '{tab_text}'")

                                # Check window title
                                window_title = main_window.windowTitle()
                                print(f"       Window title: '{window_title}'")

                                # Restore original language
                                for k in range(language_combo.count()):
                                    if language_combo.itemData(k) == original_language:
                                        language_combo.setCurrentIndex(k)
                                        general_tab.on_language_changed()
                                        app.processEvents()
                                        break

                                print(f"     ✅ Language integration test passed")
                            else:
                                print(f"     ❌ Language change failed")
                                return False
                            break
                else:
                    print("   ⚠️ General tab or language combo not found")
            else:
                print("❌ Settings widget not loaded properly")
                return False
        else:
            print("❌ Settings button not found")
            return False

        # Clean up
        main_window.close()

        return True

    except Exception as e:
        print(f"❌ Settings integration deep test failed: {e}")
        traceback.print_exc()
        return False

def run_deep_settings_inspection():
    """Run all deep settings inspection tests"""
    print("🔍 STARTING DEEP SETTINGS INSPECTION")
    print("=" * 70)

    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    test_results = []

    # Test 1: Settings Widget Structure
    test_results.append(("Widget Structure", test_settings_widget_structure()))

    # Test 2: General Settings Deep
    test_results.append(("General Settings Deep", test_general_settings_deep()))

    # Test 3: Company Settings Deep
    test_results.append(("Company Settings Deep", test_company_settings_deep()))

    # Test 4: Printer Settings Deep
    test_results.append(("Printer Settings Deep", test_printer_settings_deep()))

    # Test 5: Backup Settings Deep
    test_results.append(("Backup Settings Deep", test_backup_settings_deep()))

    # Test 6: Settings Integration Deep
    test_results.append(("Settings Integration Deep", test_settings_integration_deep()))

    # Print results summary
    print("\n" + "=" * 70)
    print("🏁 DEEP SETTINGS INSPECTION RESULTS")
    print("=" * 70)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1

    print("=" * 70)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 ALL DEEP SETTINGS INSPECTIONS PASSED! No hidden bugs found!")
    else:
        print("⚠️  Some deep inspections failed. Hidden bugs detected!")
        print("🔧 Issues found that need attention:")

        for test_name, result in test_results:
            if not result:
                print(f"   ❌ {test_name}: Requires investigation")

    return passed == total

if __name__ == "__main__":
    run_deep_settings_inspection()