#!/usr/bin/env python3
"""
مولد أكواد الترخيص المتقدم
Advanced License Code Generator
"""

import sys
import os
from datetime import datetime

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.license_database import license_db


def generate_license_codes():
    """توليد 5000 كود ترخيص"""
    print("🔐 مولد أكواد الترخيص OnePos")
    print("=" * 60)
    
    # إعدادات التوليد
    total_codes = 5000
    license_type = "standard"
    validity_days = 365  # سنة واحدة
    
    print(f"📊 إعدادات التوليد:")
    print(f"   • عدد الأكواد: {total_codes}")
    print(f"   • نوع الترخيص: {license_type}")
    print(f"   • مدة الصلاحية: {validity_days} يوم")
    print(f"   • تاريخ البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # بدء التوليد
    print("🚀 بدء عملية التوليد...")
    start_time = datetime.now()
    
    generated_codes = license_db.generate_bulk_licenses(
        count=total_codes,
        license_type=license_type,
        validity_days=validity_days
    )
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    print()
    print("=" * 60)
    print(f"✅ انتهى التوليد بنجاح!")
    print(f"📊 النتائج:")
    print(f"   • تم توليد: {len(generated_codes)} كود")
    print(f"   • الوقت المستغرق: {duration:.2f} ثانية")
    print(f"   • معدل التوليد: {len(generated_codes)/duration:.1f} كود/ثانية")
    
    # تصدير الأكواد إلى ملفات
    print()
    print("📁 تصدير الأكواد إلى ملفات...")
    
    # ملف نصي بسيط
    txt_count = license_db.export_codes_to_file("OnePos_License_Codes.txt")
    print(f"✅ تم تصدير {txt_count} كود إلى: OnePos_License_Codes.txt")
    
    # ملف CSV للاستيراد
    export_to_csv()
    
    # ملف JSON للنسخ الاحتياطي
    export_to_json()
    
    # عرض الإحصائيات
    print()
    print("📊 إحصائيات قاعدة البيانات:")
    stats = license_db.get_statistics()
    print(f"   • إجمالي الأكواد: {stats['total_codes']}")
    print(f"   • الأكواد المفعلة: {stats['activated_codes']}")
    print(f"   • الأكواد المتاحة: {stats['available_codes']}")
    print(f"   • إجمالي التفعيلات: {stats['total_activations']}")
    print(f"   • معدل التفعيل: {stats['activation_rate']:.1f}%")
    
    # عرض عينة من الأكواد
    print()
    print("🔑 عينة من الأكواد المولدة:")
    print("-" * 60)
    for i, code in enumerate(generated_codes[:10]):
        print(f"{i+1:2d}. {code}")
    if len(generated_codes) > 10:
        print(f"    ... و {len(generated_codes) - 10} كود آخر")
    
    print()
    print("=" * 60)
    print("🎉 تم إنجاز المهمة بنجاح!")
    print("📁 الملفات المولدة:")
    print("   • OnePos_License_Codes.txt - قائمة الأكواد")
    print("   • OnePos_License_Codes.csv - ملف CSV")
    print("   • OnePos_License_Codes.json - نسخة احتياطية JSON")
    print("   • data/licenses.db - قاعدة البيانات")


def export_to_csv():
    """تصدير الأكواد إلى ملف CSV"""
    import sqlite3
    import csv
    
    try:
        conn = sqlite3.connect("data/licenses.db")
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT serial_code, license_type, validity_days, created_date, expiry_date
            FROM license_codes 
            WHERE is_active = 1
            ORDER BY created_date DESC
        ''')
        
        codes = cursor.fetchall()
        
        with open("OnePos_License_Codes.csv", 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Serial Code', 'License Type', 'Validity Days', 'Created Date', 'Expiry Date'])
            writer.writerows(codes)
        
        conn.close()
        print(f"✅ تم تصدير {len(codes)} كود إلى: OnePos_License_Codes.csv")
        
    except Exception as e:
        print(f"❌ خطأ في تصدير CSV: {e}")


def export_to_json():
    """تصدير الأكواد إلى ملف JSON"""
    import sqlite3
    import json
    
    try:
        conn = sqlite3.connect("data/licenses.db")
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT serial_code, license_type, validity_days, created_date, expiry_date, notes
            FROM license_codes 
            WHERE is_active = 1
            ORDER BY created_date DESC
        ''')
        
        codes = cursor.fetchall()
        
        # تحويل إلى قاموس
        codes_data = {
            'export_info': {
                'generated_date': datetime.now().isoformat(),
                'total_codes': len(codes),
                'product': 'OnePos',
                'version': '1.0'
            },
            'license_codes': []
        }
        
        for code in codes:
            codes_data['license_codes'].append({
                'serial_code': code[0],
                'license_type': code[1],
                'validity_days': code[2],
                'created_date': code[3],
                'expiry_date': code[4],
                'notes': code[5]
            })
        
        with open("OnePos_License_Codes.json", 'w', encoding='utf-8') as jsonfile:
            json.dump(codes_data, jsonfile, indent=2, ensure_ascii=False)
        
        conn.close()
        print(f"✅ تم تصدير {len(codes)} كود إلى: OnePos_License_Codes.json")
        
    except Exception as e:
        print(f"❌ خطأ في تصدير JSON: {e}")


if __name__ == "__main__":
    try:
        generate_license_codes()
    except KeyboardInterrupt:
        print("\n❌ تم إلغاء العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في التوليد: {e}")
        import traceback
        traceback.print_exc()
