#!/usr/bin/env python3
"""
اختبار ترجمات نظام الطباعة - OnePos
Test Printing System Translations
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.main_window import MainWindow
from models.user import User
from utils.translator import translator, tr

def test_pos_button_translations():
    """اختبار ترجمات أزرار POS"""
    print("🔤 اختبار ترجمات أزرار POS")
    print("=" * 35)
    
    languages = [
        ('ar', 'العربية'),
        ('en', 'English'),
        ('fr', 'Français')
    ]
    
    expected_translations = {
        'ar': {
            'pay_and_print': 'دفع وطباعة الفاتورة',
            'simple_print': 'طباعة مبسطة',
            'professional_print': 'طباعة احترافية'
        },
        'en': {
            'pay_and_print': 'Pay and Print Receipt',
            'simple_print': 'Simple Print',
            'professional_print': 'Professional Print'
        },
        'fr': {
            'pay_and_print': 'Payer et Imprimer Reçu',
            'simple_print': 'Impression Simple',
            'professional_print': 'Impression Professionnelle'
        }
    }
    
    all_correct = True
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        main_window = MainWindow(admin_user)
        
        for lang_code, lang_name in languages:
            print(f"\n📝 اختبار اللغة: {lang_name} ({lang_code})")
            
            # تغيير اللغة
            translator.set_language(lang_code)
            
            # اختبار الترجمات
            keys_to_test = ['pay_and_print', 'simple_print', 'professional_print']
            
            for key in keys_to_test:
                actual = tr(f"pos.{key}")
                expected = expected_translations[lang_code][key]
                
                if actual == expected:
                    print(f"   ✅ {key}: {actual}")
                else:
                    print(f"   ❌ {key}: متوقع '{expected}', الحالي '{actual}'")
                    all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ترجمات POS: {e}")
        return False

def test_printer_settings_translations():
    """اختبار ترجمات إعدادات الطابعات"""
    print("\n🖨️ اختبار ترجمات إعدادات الطابعات")
    print("=" * 45)
    
    languages = [
        ('ar', 'العربية'),
        ('en', 'English'),
        ('fr', 'Français')
    ]
    
    expected_translations = {
        'ar': {
            'title': 'إعدادات الطابعات',
            'available_printers': 'الطابعات المتاحة',
            'refresh_printers': 'تحديث الطابعات',
            'save_settings': 'حفظ الإعدادات',
            'test_printer': 'اختبار الطابعة'
        },
        'en': {
            'title': 'Printer Settings',
            'available_printers': 'Available Printers',
            'refresh_printers': 'Refresh Printers',
            'save_settings': 'Save Settings',
            'test_printer': 'Test Printer'
        },
        'fr': {
            'title': 'Paramètres d\'Imprimante',
            'available_printers': 'Imprimantes Disponibles',
            'refresh_printers': 'Actualiser les Imprimantes',
            'save_settings': 'Enregistrer les Paramètres',
            'test_printer': 'Tester l\'Imprimante'
        }
    }
    
    all_correct = True
    
    try:
        for lang_code, lang_name in languages:
            print(f"\n📝 اختبار اللغة: {lang_name} ({lang_code})")
            
            # تغيير اللغة
            translator.set_language(lang_code)
            
            # اختبار الترجمات
            for key, expected in expected_translations[lang_code].items():
                actual = tr(f"printers.{key}")
                
                if actual == expected:
                    print(f"   ✅ {key}: {actual}")
                else:
                    print(f"   ❌ {key}: متوقع '{expected}', الحالي '{actual}'")
                    all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ترجمات الطابعات: {e}")
        return False

def test_ui_with_translations():
    """اختبار الواجهة مع الترجمات"""
    print("\n🖼️ اختبار الواجهة مع الترجمات")
    print("=" * 40)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        main_window = MainWindow(admin_user)
        
        languages = [('ar', 'العربية'), ('en', 'English'), ('fr', 'Français')]
        
        for lang_code, lang_name in languages:
            print(f"\n📱 اختبار الواجهة باللغة: {lang_name}")
            
            # تغيير اللغة
            translator.set_language(lang_code)
            main_window.retranslate_ui()
            app.processEvents()
            
            # الانتقال لقسم POS
            main_window.load_module('pos')
            app.processEvents()
            
            # فحص أزرار POS
            pos_widget = None
            for i in range(main_window.content_frame.count()):
                widget = main_window.content_frame.widget(i)
                if hasattr(widget, 'complete_sale_button'):
                    pos_widget = widget
                    break
            
            if pos_widget:
                # فحص النصوص
                complete_text = pos_widget.complete_sale_button.text()
                receipt_text = pos_widget.print_receipt_button.text()
                invoice_text = pos_widget.print_invoice_button.text()
                
                print(f"   🔵 زر الدفع: {complete_text}")
                print(f"   🟢 زر الإيصال: {receipt_text}")
                print(f"   🟠 زر الفاتورة: {invoice_text}")
                
                # التحقق من وجود الأيقونات
                if '💳' in complete_text and '🧾' in receipt_text and '📄' in invoice_text:
                    print("   ✅ جميع الأيقونات موجودة")
                else:
                    print("   ⚠️ بعض الأيقونات مفقودة")
            else:
                print("   ❌ لم يتم العثور على واجهة POS")
                return False
            
            # الانتقال لقسم الإعدادات
            main_window.load_module('settings')
            app.processEvents()
            
            print(f"   ✅ تم اختبار اللغة {lang_name} بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        return False

def test_translation_completeness():
    """اختبار اكتمال الترجمات"""
    print("\n📋 اختبار اكتمال الترجمات")
    print("=" * 35)
    
    try:
        # المفاتيح المطلوبة
        required_keys = [
            'pos.pay_and_print',
            'pos.simple_print', 
            'pos.professional_print',
            'printers.title',
            'printers.available_printers',
            'printers.refresh_printers',
            'printers.save_settings',
            'printers.test_printer'
        ]
        
        languages = ['ar', 'en', 'fr']
        
        all_complete = True
        
        for lang in languages:
            print(f"\n🔍 فحص اكتمال الترجمات للغة: {lang}")
            translator.set_language(lang)
            
            missing_keys = []
            
            for key in required_keys:
                translation = tr(key)
                if translation == key:  # لم يتم العثور على ترجمة
                    missing_keys.append(key)
            
            if not missing_keys:
                print(f"   ✅ جميع الترجمات متوفرة ({len(required_keys)} مفتاح)")
            else:
                print(f"   ❌ ترجمات مفقودة ({len(missing_keys)} من {len(required_keys)}):")
                for key in missing_keys:
                    print(f"      - {key}")
                all_complete = False
        
        return all_complete
        
    except Exception as e:
        print(f"❌ خطأ في فحص اكتمال الترجمات: {e}")
        return False

def run_printing_translations_test():
    """تشغيل جميع اختبارات ترجمات الطباعة"""
    print("🌍 بدء اختبار ترجمات نظام الطباعة")
    print("=" * 60)
    
    results = []
    
    # اختبار 1: ترجمات أزرار POS
    results.append(("ترجمات أزرار POS", test_pos_button_translations()))
    
    # اختبار 2: ترجمات إعدادات الطابعات
    results.append(("ترجمات إعدادات الطابعات", test_printer_settings_translations()))
    
    # اختبار 3: الواجهة مع الترجمات
    results.append(("الواجهة مع الترجمات", test_ui_with_translations()))
    
    # اختبار 4: اكتمال الترجمات
    results.append(("اكتمال الترجمات", test_translation_completeness()))
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار ترجمات نظام الطباعة")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 جميع ترجمات نظام الطباعة تعمل بشكل مثالي!")
        print("✨ الترجمات متوفرة للغات الثلاث: العربية والإنجليزية والفرنسية")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_printing_translations_test()
