#!/usr/bin/env python3
"""
اختبار شامل لأنظمة الطابعات وماسح الباركود المحسنة
Comprehensive Test for Enhanced Printer and Barcode Scanner Systems
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit, QHBoxLayout
from PyQt5.QtCore import QTimer, pyqtSlot
from PyQt5.QtGui import QFont


class TestSystemsWindow(QMainWindow):
    """نافذة اختبار الأنظمة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔧 اختبار أنظمة الطابعات وماسح الباركود")
        self.setGeometry(100, 100, 1200, 800)
        
        # إنشاء الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # العنوان
        title = QLabel("🔧 اختبار أنظمة الطابعات وماسح الباركود المحسنة")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title.setStyleSheet("""
            QLabel {
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb);
                padding: 20px;
                border-radius: 12px;
                margin: 10px;
                text-align: center;
            }
        """)
        layout.addWidget(title)
        
        # منطقة الأزرار
        buttons_layout = QHBoxLayout()
        
        # أزرار اختبار الطابعات
        printer_layout = QVBoxLayout()
        
        printer_title = QLabel("🖨️ اختبار الطابعات")
        printer_title.setFont(QFont("Segoe UI", 14, QFont.Bold))
        printer_title.setStyleSheet("color: #1976d2; margin: 10px;")
        printer_layout.addWidget(printer_title)
        
        self.test_printers_btn = QPushButton("🔍 فحص الطابعات")
        self.test_printers_btn.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.test_printers_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #42a5f5, stop:1 #1976d2);
                color: white;
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                margin: 5px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #1976d2, stop:1 #1565c0);
            }
        """)
        self.test_printers_btn.clicked.connect(self.test_printers)
        printer_layout.addWidget(self.test_printers_btn)
        
        self.test_thermal_btn = QPushButton("🎫 اختبار طابعة حرارية")
        self.test_thermal_btn.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.test_thermal_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #4caf50, stop:1 #388e3c);
                color: white;
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                margin: 5px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #388e3c, stop:1 #2e7d32);
            }
        """)
        self.test_thermal_btn.clicked.connect(self.test_thermal_printer)
        printer_layout.addWidget(self.test_thermal_btn)
        
        self.print_receipt_btn = QPushButton("🧾 طباعة إيصال تجريبي")
        self.print_receipt_btn.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.print_receipt_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #ff9800, stop:1 #f57c00);
                color: white;
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                margin: 5px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #f57c00, stop:1 #ef6c00);
            }
        """)
        self.print_receipt_btn.clicked.connect(self.print_test_receipt)
        printer_layout.addWidget(self.print_receipt_btn)
        
        buttons_layout.addLayout(printer_layout)
        
        # أزرار اختبار ماسح الباركود
        scanner_layout = QVBoxLayout()
        
        scanner_title = QLabel("📱 اختبار ماسح الباركود")
        scanner_title.setFont(QFont("Segoe UI", 14, QFont.Bold))
        scanner_title.setStyleSheet("color: #388e3c; margin: 10px;")
        scanner_layout.addWidget(scanner_title)
        
        self.test_scanners_btn = QPushButton("🔍 فحص الماسحات")
        self.test_scanners_btn.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.test_scanners_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #4caf50, stop:1 #388e3c);
                color: white;
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                margin: 5px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #388e3c, stop:1 #2e7d32);
            }
        """)
        self.test_scanners_btn.clicked.connect(self.test_scanners)
        scanner_layout.addWidget(self.test_scanners_btn)
        
        self.start_scanning_btn = QPushButton("▶️ بدء المسح")
        self.start_scanning_btn.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.start_scanning_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #2196f3, stop:1 #1976d2);
                color: white;
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                margin: 5px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #1976d2, stop:1 #1565c0);
            }
        """)
        self.start_scanning_btn.clicked.connect(self.start_scanning)
        scanner_layout.addWidget(self.start_scanning_btn)
        
        self.stop_scanning_btn = QPushButton("⏹️ إيقاف المسح")
        self.stop_scanning_btn.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.stop_scanning_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #f44336, stop:1 #d32f2f);
                color: white;
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                margin: 5px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #d32f2f, stop:1 #c62828);
            }
        """)
        self.stop_scanning_btn.clicked.connect(self.stop_scanning)
        scanner_layout.addWidget(self.stop_scanning_btn)
        
        buttons_layout.addLayout(scanner_layout)
        
        layout.addLayout(buttons_layout)
        
        # منطقة النتائج
        self.results_text = QTextEdit()
        self.results_text.setFont(QFont("Consolas", 10))
        self.results_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 2px solid #555;
                border-radius: 8px;
                padding: 10px;
                margin: 10px;
            }
        """)
        self.results_text.setPlainText("🔧 جاهز لاختبار الأنظمة...\n")
        layout.addWidget(self.results_text)
        
        # تهيئة المدراء
        self.printer_manager = None
        self.scanner_manager = None
        self.init_managers()
    
    def init_managers(self):
        """تهيئة مدراء الأنظمة"""
        try:
            from utils.printer_manager import printer_manager
            from utils.barcode_scanner_manager import barcode_scanner_manager
            
            self.printer_manager = printer_manager
            self.scanner_manager = barcode_scanner_manager
            
            # ربط إشارات ماسح الباركود
            self.scanner_manager.barcode_scanned.connect(self.on_barcode_scanned)
            
            self.log("✅ تم تهيئة المدراء بنجاح")
            
        except Exception as e:
            self.log(f"❌ خطأ في تهيئة المدراء: {e}")
    
    def log(self, message):
        """إضافة رسالة للسجل"""
        self.results_text.append(f"[{self.get_timestamp()}] {message}")
        self.results_text.ensureCursorVisible()
    
    def get_timestamp(self):
        """الحصول على الوقت الحالي"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    def test_printers(self):
        """اختبار الطابعات"""
        self.log("🔍 بدء فحص الطابعات...")
        
        try:
            if not self.printer_manager:
                self.log("❌ مدير الطابعات غير متاح")
                return
            
            # تحديث قائمة الطابعات
            success = self.printer_manager.refresh_printers()
            
            if success:
                printers = self.printer_manager.get_available_printers()
                thermal_printers = self.printer_manager.get_thermal_printers()
                
                self.log(f"✅ تم العثور على {len(printers)} طابعة")
                self.log(f"🎫 منها {len(thermal_printers)} طابعة حرارية")
                
                for name, data in printers.items():
                    printer_type = "حرارية" if data.get('is_thermal', False) else "عادية"
                    status = data.get('status', 'غير معروف')
                    self.log(f"  📄 {name} - {printer_type} - {status}")
            else:
                self.log("❌ فشل في فحص الطابعات")
                
        except Exception as e:
            self.log(f"❌ خطأ في فحص الطابعات: {e}")
    
    def test_thermal_printer(self):
        """اختبار طابعة حرارية"""
        self.log("🎫 اختبار الطابعة الحرارية...")
        
        try:
            if not self.printer_manager:
                self.log("❌ مدير الطابعات غير متاح")
                return
            
            thermal_printers = self.printer_manager.get_thermal_printers()
            
            if not thermal_printers:
                self.log("⚠️ لا توجد طابعات حرارية متاحة")
                return
            
            # اختبار أول طابعة حرارية
            printer_name = list(thermal_printers.keys())[0]
            self.log(f"🧪 اختبار الطابعة: {printer_name}")
            
            success, message = self.printer_manager.test_printer(printer_name)
            
            if success:
                self.log(f"✅ {message}")
            else:
                self.log(f"❌ {message}")
                
        except Exception as e:
            self.log(f"❌ خطأ في اختبار الطابعة الحرارية: {e}")
    
    def print_test_receipt(self):
        """طباعة إيصال تجريبي"""
        self.log("🧾 طباعة إيصال تجريبي...")
        
        try:
            if not self.printer_manager:
                self.log("❌ مدير الطابعات غير متاح")
                return
            
            # بيانات إيصال تجريبي
            test_sale_data = {
                'invoice_number': 'TEST-001',
                'cashier': 'مدير النظام',
                'customer': 'عميل تجريبي',
                'items': [
                    {'name': 'منتج تجريبي 1', 'quantity': 2, 'unit_price': 10.50},
                    {'name': 'منتج تجريبي 2', 'quantity': 1, 'unit_price': 25.00},
                    {'name': 'منتج تجريبي 3', 'quantity': 3, 'unit_price': 5.75}
                ]
            }
            
            success, message = self.printer_manager.print_receipt(test_sale_data)
            
            if success:
                self.log(f"✅ {message}")
            else:
                self.log(f"❌ {message}")
                
        except Exception as e:
            self.log(f"❌ خطأ في طباعة الإيصال: {e}")
    
    def test_scanners(self):
        """اختبار ماسحات الباركود"""
        self.log("📱 بدء فحص ماسحات الباركود...")
        
        try:
            if not self.scanner_manager:
                self.log("❌ مدير الماسحات غير متاح")
                return
            
            # تحديث قائمة الماسحات
            success = self.scanner_manager.refresh_scanners()
            
            if success:
                scanners = self.scanner_manager.get_available_scanners()
                usb_scanners = self.scanner_manager.get_usb_scanners()
                serial_scanners = self.scanner_manager.get_serial_scanners()
                
                self.log(f"✅ تم العثور على {len(scanners)} ماسح")
                self.log(f"📱 منها {len(usb_scanners)} ماسح USB")
                self.log(f"🔌 منها {len(serial_scanners)} ماسح Serial")
                
                for scanner_id, data in scanners.items():
                    scanner_type = data.get('type', 'غير معروف')
                    scanner_name = data.get('name', 'غير معروف')
                    self.log(f"  📡 {scanner_name} - {scanner_type}")
            else:
                self.log("❌ فشل في فحص الماسحات")
                
        except Exception as e:
            self.log(f"❌ خطأ في فحص الماسحات: {e}")
    
    def start_scanning(self):
        """بدء المسح"""
        self.log("▶️ بدء مسح الباركود...")
        
        try:
            if not self.scanner_manager:
                self.log("❌ مدير الماسحات غير متاح")
                return
            
            success = self.scanner_manager.start_scanning()
            
            if success:
                self.log("✅ تم بدء المسح - امسح أي باركود")
            else:
                self.log("❌ فشل في بدء المسح")
                
        except Exception as e:
            self.log(f"❌ خطأ في بدء المسح: {e}")
    
    def stop_scanning(self):
        """إيقاف المسح"""
        self.log("⏹️ إيقاف مسح الباركود...")
        
        try:
            if not self.scanner_manager:
                self.log("❌ مدير الماسحات غير متاح")
                return
            
            success = self.scanner_manager.stop_scanning()
            
            if success:
                self.log("✅ تم إيقاف المسح")
            else:
                self.log("❌ فشل في إيقاف المسح")
                
        except Exception as e:
            self.log(f"❌ خطأ في إيقاف المسح: {e}")
    
    @pyqtSlot(str)
    def on_barcode_scanned(self, barcode):
        """معالجة الباركود المكتشف"""
        self.log(f"🎯 تم مسح باركود: {barcode}")


def test_systems():
    """اختبار الأنظمة"""
    
    app = QApplication(sys.argv)
    
    print("🔧 بدء اختبار أنظمة الطابعات وماسح الباركود")
    print("=" * 60)
    
    # إنشاء نافذة الاختبار
    test_window = TestSystemsWindow()
    test_window.show()
    
    print("\n🎯 الاختبارات المتاحة:")
    print("🖨️ اختبار الطابعات والطباعة")
    print("📱 اختبار ماسحات الباركود")
    print("🧾 طباعة إيصالات تجريبية")
    print("📡 مسح باركود مباشر")
    
    print("\n" + "=" * 60)
    print("🚀 جاهز للاختبار!")
    
    return app


if __name__ == "__main__":
    try:
        print("🔧 اختبار أنظمة الطابعات وماسح الباركود المحسنة")
        print("🎯 الهدف: التأكد من عمل الأنظمة بشكل صحيح")
        
        app = test_systems()
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
