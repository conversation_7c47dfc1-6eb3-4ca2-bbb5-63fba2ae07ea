#!/usr/bin/env python3
"""
إصلاح سريع لمشكلة الصلاحيات
Quick fix for permissions issue
"""

import os
import shutil

def fix_database_path():
    """Fix database path in models/database.py"""
    
    database_file = "models/database.py"
    
    # Read the file
    with open(database_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace the problematic line
    old_line = 'def __init__(self, db_path=None):'
    if old_line in content:
        print("✅ Database file already fixed")
        return True
    
    # Find and replace the init method
    old_init = 'def __init__(self, db_path="data/onepos.db"):'
    new_init = '''def __init__(self, db_path=None):
        """Initialize database connection"""
        if db_path is None:
            # Use user's Documents directory for better permissions
            import os
            user_docs = os.path.expanduser("~/Documents")
            data_dir = os.path.join(user_docs, "OnePos")
            self.db_path = os.path.join(data_dir, "onepos.db")
        else:
            self.db_path = db_path'''
    
    if old_init in content:
        content = content.replace(old_init, new_init)
        
        # Also fix the ensure_data_directory method
        old_ensure = '''def ensure_data_directory(self):
        """Ensure data directory exists"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)'''
        
        new_ensure = '''def ensure_data_directory(self):
        """Ensure data directory exists"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        except PermissionError:
            # Fallback to temp directory
            import tempfile
            temp_dir = tempfile.gettempdir()
            data_dir = os.path.join(temp_dir, "OnePos")
            os.makedirs(data_dir, exist_ok=True)
            self.db_path = os.path.join(data_dir, "onepos.db")
            print(f"Using fallback database path: {self.db_path}")'''
        
        content = content.replace(old_ensure, new_ensure)
        
        # Write back the file
        with open(database_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Database file fixed successfully")
        return True
    else:
        print("❌ Could not find the target line to replace")
        return False

def test_fix():
    """Test the fix"""
    try:
        # Import and test
        import sys
        sys.path.insert(0, '.')
        
        from models.database import Database
        db = Database()
        print(f"✅ Database path: {db.db_path}")
        print("✅ Database connection successful!")
        db.connection.close()
        return True
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Fixing database permissions issue...")
    print("=" * 50)
    
    if fix_database_path():
        print("\n🧪 Testing the fix...")
        if test_fix():
            print("\n🎉 Fix successful! Ready to rebuild.")
        else:
            print("\n❌ Fix failed. Manual intervention needed.")
    else:
        print("\n❌ Could not apply fix.")
    
    print("=" * 50)
