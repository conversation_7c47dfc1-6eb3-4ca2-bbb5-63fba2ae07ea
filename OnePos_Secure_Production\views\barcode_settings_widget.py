"""
واجهة إعدادات ماسحات الباركود - OnePos
Barcode Scanner Settings Interface
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QComboBox, QPushButton, QGroupBox, QTableWidget,
                            QTableWidgetItem, QCheckBox, QSpinBox, QFrame,
                            QMessageBox, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from utils.barcode_scanner_manager import barcode_scanner_manager
from utils.translator import tr
from utils.config_manager import config

class BarcodeScannerSettingsWidget(QWidget):
    """واجهة إعدادات ماسحات الباركود"""
    
    settings_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_scanners()
        self.load_settings()
        
        # ربط الإشارات
        barcode_scanner_manager.scanner_connected.connect(self.on_scanner_connected)
        barcode_scanner_manager.scanner_disconnected.connect(self.on_scanner_disconnected)
        barcode_scanner_manager.barcode_scanned.connect(self.on_barcode_scanned)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # العنوان
        self.title_label = QLabel("📱 " + tr("barcode.title"))
        self.title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        self.title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(self.title_label)
        
        # شريط الأزرار العلوي
        buttons_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("🔄 " + tr("barcode.refresh_scanners"))
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_scanners)
        buttons_layout.addWidget(self.refresh_button)
        
        buttons_layout.addStretch()
        
        self.test_scanner_button = QPushButton("🧪 " + tr("barcode.test_scanner"))
        self.test_scanner_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        self.test_scanner_button.clicked.connect(self.test_selected_scanner)
        buttons_layout.addWidget(self.test_scanner_button)
        
        self.save_button = QPushButton("💾 " + tr("barcode.save_settings"))
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.save_button.clicked.connect(self.save_settings)
        buttons_layout.addWidget(self.save_button)
        
        layout.addLayout(buttons_layout)
        
        # المحتوى الرئيسي
        content_layout = QHBoxLayout()
        
        # قائمة الماسحات
        content_layout.addWidget(self.create_scanners_list(), 2)
        
        # إعدادات الماسحات
        content_layout.addWidget(self.create_scanner_settings(), 1)
        
        layout.addLayout(content_layout)
    
    def create_scanners_list(self):
        """إنشاء قائمة الماسحات"""
        self.scanners_group = QGroupBox("📋 " + tr("barcode.available_scanners"))
        self.scanners_group.setFont(QFont("Segoe UI", 12, QFont.Bold))
        layout = QVBoxLayout(self.scanners_group)
        
        # جدول الماسحات
        self.scanners_table = QTableWidget()
        self.scanners_table.setColumnCount(5)
        self.scanners_table.setHorizontalHeaderLabels([
            tr("barcode.scanner_name"),
            tr("barcode.scanner_type"),
            tr("barcode.scanner_status"),
            tr("barcode.connection_type"),
            tr("barcode.scanner_description")
        ])
        
        # تنسيق الجدول
        self.scanners_table.setAlternatingRowColors(True)
        self.scanners_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.scanners_table.setSelectionMode(QAbstractItemView.SingleSelection)
        
        # تعديل عرض الأعمدة
        header = self.scanners_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        self.scanners_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        layout.addWidget(self.scanners_table)
        
        # أزرار التحكم
        control_layout = QHBoxLayout()
        
        self.start_scan_button = QPushButton("▶️ " + tr("barcode.start_scanning"))
        self.start_scan_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.start_scan_button.clicked.connect(self.start_scanning)
        control_layout.addWidget(self.start_scan_button)
        
        self.stop_scan_button = QPushButton("⏹️ " + tr("barcode.stop_scanning"))
        self.stop_scan_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.stop_scan_button.clicked.connect(self.stop_scanning)
        self.stop_scan_button.setEnabled(False)
        control_layout.addWidget(self.stop_scan_button)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        return self.scanners_group
    
    def create_scanner_settings(self):
        """إنشاء إعدادات الماسحات"""
        self.settings_group = QGroupBox("⚙️ " + tr("barcode.scanner_settings"))
        self.settings_group.setFont(QFont("Segoe UI", 12, QFont.Bold))
        layout = QVBoxLayout(self.settings_group)
        
        # الماسح النشط
        active_frame = QFrame()
        active_frame.setFrameStyle(QFrame.StyledPanel)
        active_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        active_layout = QVBoxLayout(active_frame)
        
        self.active_label = QLabel("🎯 " + tr("barcode.active_scanner"))
        self.active_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.active_label.setStyleSheet("color: #155724;")
        active_layout.addWidget(self.active_label)
        
        self.active_scanner_combo = QComboBox()
        self.active_scanner_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #27ae60;
                border-radius: 4px;
                background-color: white;
                font-size: 11px;
            }
            QComboBox:focus {
                border-color: #2ecc71;
            }
        """)
        self.active_scanner_combo.currentTextChanged.connect(self.on_active_scanner_changed)
        active_layout.addWidget(self.active_scanner_combo)
        
        layout.addWidget(active_frame)
        
        # إعدادات المسح
        scan_frame = QFrame()
        scan_frame.setFrameStyle(QFrame.StyledPanel)
        scan_frame.setStyleSheet("""
            QFrame {
                background-color: #fff3cd;
                border: 2px solid #ffc107;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        scan_layout = QVBoxLayout(scan_frame)
        
        # وضع المسح
        mode_label = QLabel("🔄 " + tr("barcode.scan_mode"))
        mode_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        mode_label.setStyleSheet("color: #856404;")
        scan_layout.addWidget(mode_label)
        
        self.scan_mode_combo = QComboBox()
        self.scan_mode_combo.addItems([
            tr("barcode.continuous_mode"),
            tr("barcode.single_mode"),
            tr("barcode.manual_mode")
        ])
        self.scan_mode_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ffc107;
                border-radius: 4px;
                background-color: white;
                font-size: 11px;
            }
        """)
        scan_layout.addWidget(self.scan_mode_combo)
        
        # مهلة المسح
        timeout_label = QLabel("⏱️ " + tr("barcode.scan_timeout"))
        timeout_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        timeout_label.setStyleSheet("color: #856404;")
        scan_layout.addWidget(timeout_label)
        
        timeout_layout = QHBoxLayout()
        self.timeout_spinbox = QSpinBox()
        self.timeout_spinbox.setRange(1000, 30000)
        self.timeout_spinbox.setValue(5000)
        self.timeout_spinbox.setSuffix(" " + tr("barcode.milliseconds"))
        self.timeout_spinbox.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #ffc107;
                border-radius: 4px;
                background-color: white;
                font-size: 11px;
            }
        """)
        timeout_layout.addWidget(self.timeout_spinbox)
        timeout_layout.addStretch()
        scan_layout.addLayout(timeout_layout)
        
        layout.addWidget(scan_frame)
        
        # الكشف التلقائي
        auto_frame = QFrame()
        auto_frame.setFrameStyle(QFrame.StyledPanel)
        auto_frame.setStyleSheet("""
            QFrame {
                background-color: #d1ecf1;
                border: 2px solid #17a2b8;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        auto_layout = QVBoxLayout(auto_frame)
        
        self.auto_detect_checkbox = QCheckBox("🔍 " + tr("barcode.auto_detect"))
        self.auto_detect_checkbox.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.auto_detect_checkbox.setStyleSheet("color: #0c5460;")
        self.auto_detect_checkbox.setChecked(True)
        auto_layout.addWidget(self.auto_detect_checkbox)
        
        layout.addWidget(auto_frame)
        
        # معلومات إضافية
        info_text = QLabel(tr("barcode.settings_info"))
        info_text.setWordWrap(True)
        info_text.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 10px;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 4px;
                border: 1px solid #dee2e6;
            }
        """)
        layout.addWidget(info_text)
        
        return self.settings_group
    
    def load_scanners(self):
        """تحميل قائمة الماسحات"""
        try:
            barcode_scanner_manager.refresh_scanners()
            self.update_scanners_table()
            self.update_scanner_combos()
            
        except Exception as e:
            print(f"خطأ في تحميل الماسحات: {e}")
    
    def update_scanners_table(self):
        """تحديث جدول الماسحات"""
        try:
            scanners = barcode_scanner_manager.get_available_scanners()
            
            self.scanners_table.setRowCount(len(scanners))
            
            for row, (scanner_id, data) in enumerate(scanners.items()):
                # اسم الماسح
                name_item = QTableWidgetItem(data['name'])
                name_item.setData(Qt.UserRole, scanner_id)
                self.scanners_table.setItem(row, 0, name_item)
                
                # النوع
                scanner_type = data['type']
                if scanner_type == 'USB' or scanner_type == 'USB HID':
                    type_text = "🔌 " + tr("barcode.usb_scanner")
                elif scanner_type == 'Serial':
                    type_text = "🔗 " + tr("barcode.serial_scanner")
                elif scanner_type == 'Bluetooth':
                    type_text = "📶 " + tr("barcode.bluetooth_scanner")
                elif scanner_type == 'Camera':
                    type_text = "📷 " + tr("barcode.camera_scanner")
                else:
                    type_text = "❓ " + tr("barcode.unknown_scanner")
                
                type_item = QTableWidgetItem(type_text)
                self.scanners_table.setItem(row, 1, type_item)
                
                # الحالة
                status = data.get('status', 'غير معروف')
                if status == 'متاح':
                    status_text = "✅ " + tr("barcode.available")
                elif status == 'مشغول':
                    status_text = "🔄 " + tr("barcode.busy")
                elif status == 'خطأ':
                    status_text = "❌ " + tr("barcode.error")
                else:
                    status_text = "❓ " + tr("barcode.unknown")
                
                status_item = QTableWidgetItem(status_text)
                self.scanners_table.setItem(row, 2, status_item)
                
                # نوع الاتصال
                connection = data.get('connection', 'غير معروف')
                connection_item = QTableWidgetItem(connection)
                self.scanners_table.setItem(row, 3, connection_item)
                
                # الوصف
                manufacturer = data.get('manufacturer', 'Unknown')
                model = data.get('model', 'Unknown Model')
                description = f"{manufacturer} - {model}"
                description_item = QTableWidgetItem(description)
                self.scanners_table.setItem(row, 4, description_item)
                
                # تمييز الماسح النشط
                active_scanner = barcode_scanner_manager.get_active_scanner()
                if scanner_id == active_scanner:
                    for col in range(5):
                        item = self.scanners_table.item(row, col)
                        if item:
                            item.setBackground(Qt.lightGreen)
            
        except Exception as e:
            print(f"خطأ في تحديث جدول الماسحات: {e}")
    
    def update_scanner_combos(self):
        """تحديث القوائم المنسدلة للماسحات"""
        try:
            scanners = barcode_scanner_manager.get_available_scanners()
            
            # مسح القائمة
            self.active_scanner_combo.clear()
            
            # إضافة خيار "لا شيء"
            no_scanner_text = "-- " + tr("barcode.no_scanner") + " --"
            self.active_scanner_combo.addItem(no_scanner_text, None)
            
            # إضافة الماسحات
            for scanner_id, data in scanners.items():
                scanner_type = data['type']
                if scanner_type == 'USB' or scanner_type == 'USB HID':
                    icon = "🔌"
                elif scanner_type == 'Serial':
                    icon = "🔗"
                elif scanner_type == 'Bluetooth':
                    icon = "📶"
                elif scanner_type == 'Camera':
                    icon = "📷"
                else:
                    icon = "❓"
                
                self.active_scanner_combo.addItem(f"{icon} {data['name']}", scanner_id)
            
            # تعيين الماسح النشط
            active_scanner = barcode_scanner_manager.get_active_scanner()
            if active_scanner:
                for i in range(self.active_scanner_combo.count()):
                    if self.active_scanner_combo.itemData(i) == active_scanner:
                        self.active_scanner_combo.setCurrentIndex(i)
                        break
            
        except Exception as e:
            print(f"خطأ في تحديث قوائم الماسحات: {e}")

    def refresh_scanners(self):
        """تحديث قائمة الماسحات"""
        try:
            self.load_scanners()
            QMessageBox.information(self, tr("barcode.refresh_complete"),
                                  tr("barcode.scanners_refreshed"))
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"),
                               f"{tr('barcode.refresh_failed')}:\n{str(e)}")

    def test_selected_scanner(self):
        """اختبار الماسح المحدد"""
        try:
            current_row = self.scanners_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, tr("common.warning"),
                                  tr("barcode.select_scanner_warning"))
                return

            scanner_id_item = self.scanners_table.item(current_row, 0)
            scanner_id = scanner_id_item.data(Qt.UserRole)

            if scanner_id:
                success, message = barcode_scanner_manager.test_scanner(scanner_id)

                if success:
                    QMessageBox.information(self, tr("barcode.test_success"),
                                          f"{tr('barcode.scanner_test_success')}:\n{message}")
                else:
                    QMessageBox.critical(self, tr("barcode.test_failed"),
                                       f"{tr('barcode.scanner_test_failed')}:\n{message}")

        except Exception as e:
            QMessageBox.critical(self, tr("common.error"),
                               f"{tr('barcode.test_error')}:\n{str(e)}")

    def start_scanning(self):
        """بدء المسح"""
        try:
            if barcode_scanner_manager.start_scanning():
                self.start_scan_button.setEnabled(False)
                self.stop_scan_button.setEnabled(True)
                QMessageBox.information(self, tr("barcode.scan_started"),
                                      tr("barcode.scanning_active"))
            else:
                QMessageBox.warning(self, tr("common.warning"),
                                  tr("barcode.scan_start_failed"))
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"),
                               f"{tr('barcode.scan_error')}:\n{str(e)}")

    def stop_scanning(self):
        """إيقاف المسح"""
        try:
            if barcode_scanner_manager.stop_scanning():
                self.start_scan_button.setEnabled(True)
                self.stop_scan_button.setEnabled(False)
                QMessageBox.information(self, tr("barcode.scan_stopped"),
                                      tr("barcode.scanning_stopped"))
            else:
                QMessageBox.warning(self, tr("common.warning"),
                                  tr("barcode.scan_stop_failed"))
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"),
                               f"{tr('barcode.scan_error')}:\n{str(e)}")

    def on_active_scanner_changed(self):
        """عند تغيير الماسح النشط"""
        try:
            scanner_id = self.active_scanner_combo.currentData()
            if scanner_id:
                barcode_scanner_manager.set_active_scanner(scanner_id)
                self.update_scanners_table()  # تحديث الجدول لإظهار الماسح النشط
        except Exception as e:
            print(f"خطأ في تغيير الماسح النشط: {e}")

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            # وضع المسح
            scan_mode = config.get('barcode.scan_mode', 'continuous')
            if scan_mode == 'continuous':
                self.scan_mode_combo.setCurrentIndex(0)
            elif scan_mode == 'single':
                self.scan_mode_combo.setCurrentIndex(1)
            else:
                self.scan_mode_combo.setCurrentIndex(2)

            # مهلة المسح
            timeout = config.get('barcode.scan_timeout', 5000)
            self.timeout_spinbox.setValue(timeout)

            # الكشف التلقائي
            auto_detect = config.get('barcode.auto_detect', True)
            self.auto_detect_checkbox.setChecked(auto_detect)

        except Exception as e:
            print(f"خطأ في تحميل إعدادات الباركود: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # وضع المسح
            scan_mode_index = self.scan_mode_combo.currentIndex()
            if scan_mode_index == 0:
                scan_mode = 'continuous'
            elif scan_mode_index == 1:
                scan_mode = 'single'
            else:
                scan_mode = 'manual'

            config.set('barcode.scan_mode', scan_mode)
            barcode_scanner_manager.scan_mode = scan_mode

            # مهلة المسح
            timeout = self.timeout_spinbox.value()
            config.set('barcode.scan_timeout', timeout)
            barcode_scanner_manager.scan_timeout = timeout

            # الكشف التلقائي
            auto_detect = self.auto_detect_checkbox.isChecked()
            config.set('barcode.auto_detect', auto_detect)
            barcode_scanner_manager.auto_detect = auto_detect

            # حفظ الإعدادات
            if barcode_scanner_manager.save_settings():
                QMessageBox.information(self, tr("barcode.settings_saved"),
                                      tr("barcode.settings_saved_successfully"))
                self.settings_changed.emit()
            else:
                QMessageBox.warning(self, tr("common.warning"),
                                  tr("barcode.settings_save_failed"))

        except Exception as e:
            QMessageBox.critical(self, tr("common.error"),
                               f"{tr('barcode.settings_save_error')}:\n{str(e)}")

    def on_scanner_connected(self, scanner_info):
        """عند اتصال ماسح جديد"""
        self.load_scanners()
        print(f"🔌 تم اتصال ماسح جديد: {scanner_info}")

    def on_scanner_disconnected(self, scanner_info):
        """عند انقطاع ماسح"""
        self.load_scanners()
        print(f"🔌 تم انقطاع ماسح: {scanner_info}")

    def on_barcode_scanned(self, barcode):
        """عند مسح باركود"""
        print(f"📱 تم مسح باركود: {barcode}")
        # يمكن إضافة معالجة إضافية هنا

    def retranslate_ui(self):
        """تحديث النصوص عند تغيير اللغة"""
        try:
            # تحديث العنوان الرئيسي
            if hasattr(self, 'title_label'):
                self.title_label.setText("📱 " + tr("barcode.title"))

            # تحديث أزرار التحكم
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setText("🔄 " + tr("barcode.refresh_scanners"))

            if hasattr(self, 'test_scanner_button'):
                self.test_scanner_button.setText("🧪 " + tr("barcode.test_scanner"))

            if hasattr(self, 'save_button'):
                self.save_button.setText("💾 " + tr("barcode.save_settings"))

            if hasattr(self, 'start_scan_button'):
                self.start_scan_button.setText("▶️ " + tr("barcode.start_scanning"))

            if hasattr(self, 'stop_scan_button'):
                self.stop_scan_button.setText("⏹️ " + tr("barcode.stop_scanning"))

            # تحديث عناوين المجموعات
            if hasattr(self, 'scanners_group'):
                self.scanners_group.setTitle("📋 " + tr("barcode.available_scanners"))

            if hasattr(self, 'settings_group'):
                self.settings_group.setTitle("⚙️ " + tr("barcode.scanner_settings"))

            # تحديث رؤوس الجدول
            if hasattr(self, 'scanners_table'):
                headers = [
                    tr("barcode.scanner_name"),
                    tr("barcode.scanner_type"),
                    tr("barcode.scanner_status"),
                    tr("barcode.connection_type"),
                    tr("barcode.scanner_description")
                ]
                self.scanners_table.setHorizontalHeaderLabels(headers)

            # تحديث تسميات الإعدادات
            if hasattr(self, 'active_label'):
                self.active_label.setText("🎯 " + tr("barcode.active_scanner"))

            # تحديث خيارات وضع المسح
            if hasattr(self, 'scan_mode_combo'):
                current_index = self.scan_mode_combo.currentIndex()
                self.scan_mode_combo.clear()
                self.scan_mode_combo.addItems([
                    tr("barcode.continuous_mode"),
                    tr("barcode.single_mode"),
                    tr("barcode.manual_mode")
                ])
                self.scan_mode_combo.setCurrentIndex(current_index)

            # تحديث خيار "لا شيء" في القوائم المنسدلة
            self.update_combo_no_scanner_option()

            # تحديث محتوى الجدول
            self.update_scanners_table()

        except Exception as e:
            print(f"خطأ في تحديث ترجمات الباركود: {e}")

    def update_combo_no_scanner_option(self):
        """تحديث خيار 'لا شيء' في القوائم المنسدلة"""
        try:
            if hasattr(self, 'active_scanner_combo'):
                # حفظ القيمة المحددة حالياً
                current_data = self.active_scanner_combo.currentData()

                # تحديث النص للعنصر الأول (لا شيء)
                if self.active_scanner_combo.count() > 0:
                    self.active_scanner_combo.setItemText(0, "-- " + tr("barcode.no_scanner") + " --")

                # استعادة القيمة المحددة
                for i in range(self.active_scanner_combo.count()):
                    if self.active_scanner_combo.itemData(i) == current_data:
                        self.active_scanner_combo.setCurrentIndex(i)
                        break
        except Exception as e:
            print(f"خطأ في تحديث خيارات القوائم المنسدلة: {e}")
