"""
Final Verification Test for OnePos Settings System
Complete verification after bug fixes and improvements
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from utils.config_manager import config
from utils.translator import tr, change_language, get_current_language
from views.settings_widget import SettingsWidget, GeneralSettingsTab, CompanySettingsTab, PrinterSettingsTab, BackupSettingsTab
from views.main_window import MainWindow
from models.user import User
import time

def test_theme_change_functionality():
    """Test the new theme change functionality"""
    print("🎨 Testing Theme Change Functionality...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create settings widget
        settings_widget = SettingsWidget()
        
        # Find general settings tab
        general_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, GeneralSettingsTab):
                general_tab = tab_widget
                settings_widget.tabs.setCurrentIndex(i)
                break
        
        if not general_tab:
            print("❌ General settings tab not found")
            return False
        
        # Test theme change method
        if hasattr(general_tab, 'on_theme_changed'):
            print("✅ on_theme_changed method found")
            
            # Test theme change
            theme_combo = general_tab.theme_combo
            original_index = theme_combo.currentIndex()
            original_theme = config.get_theme()
            
            print(f"   Original theme: {original_theme}")
            
            # Change to different theme
            new_index = (original_index + 1) % theme_combo.count()
            theme_combo.setCurrentIndex(new_index)
            
            try:
                general_tab.on_theme_changed()
                new_theme = config.get_theme()
                print(f"   New theme: {new_theme}")
                
                if new_theme != original_theme:
                    print("   ✅ Theme change successful")
                    
                    # Restore original theme
                    theme_combo.setCurrentIndex(original_index)
                    general_tab.on_theme_changed()
                    
                    restored_theme = config.get_theme()
                    if restored_theme == original_theme:
                        print("   ✅ Theme restoration successful")
                    else:
                        print("   ⚠️ Theme restoration may have issues")
                else:
                    print("   ❌ Theme change failed")
                    return False
            except Exception as e:
                print(f"   ❌ Theme change method failed: {e}")
                return False
        else:
            print("❌ on_theme_changed method not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Theme change functionality test failed: {e}")
        return False

def test_settings_translations():
    """Test settings translations completeness"""
    print("\n🌐 Testing Settings Translations...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Test translation keys
        translation_keys = [
            "settings.theme_changed",
            "settings.theme_changed_message",
            "settings.language_changed",
            "settings.language_changed_message"
        ]
        
        languages = ['ar', 'en', 'fr']
        
        for lang in languages:
            print(f"   Testing {lang} translations...")
            change_language(lang)
            
            for key in translation_keys:
                translation = tr(key)
                if translation and translation != key:
                    print(f"     ✅ {key}: '{translation}'")
                else:
                    print(f"     ❌ {key}: Missing or untranslated")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Settings translations test failed: {e}")
        return False

def test_settings_real_user_interaction():
    """Test settings with real user interaction simulation"""
    print("\n👤 Testing Real User Interaction...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Navigate to settings
        settings_button = main_window.nav_buttons['settings']
        settings_button.click()
        app.processEvents()
        
        # Get settings widget
        current_widget = main_window.stacked_widget.currentWidget()
        if isinstance(current_widget, SettingsWidget):
            settings_widget = current_widget
            
            # Test tab switching
            print("   Testing tab switching...")
            for i in range(settings_widget.tabs.count()):
                tab_text = settings_widget.tabs.tabText(i)
                settings_widget.tabs.setCurrentIndex(i)
                app.processEvents()
                time.sleep(0.05)
                
                current_tab = settings_widget.tabs.currentWidget()
                if current_tab.isVisible() and current_tab.isEnabled():
                    print(f"     ✅ Tab {i} ({tab_text}): Accessible")
                else:
                    print(f"     ❌ Tab {i} ({tab_text}): Not accessible")
                    return False
            
            # Test language change through UI
            print("   Testing language change through UI...")
            
            # Find general tab
            general_tab = None
            for i in range(settings_widget.tabs.count()):
                tab_widget = settings_widget.tabs.widget(i)
                if isinstance(tab_widget, GeneralSettingsTab):
                    general_tab = tab_widget
                    settings_widget.tabs.setCurrentIndex(i)
                    app.processEvents()
                    break
            
            if general_tab:
                language_combo = general_tab.language_combo
                original_language = get_current_language()
                
                # Test changing to each language
                for i in range(language_combo.count()):
                    lang_code = language_combo.itemData(i)
                    lang_name = language_combo.itemText(i)
                    
                    if lang_code and lang_code != original_language:
                        print(f"     Testing change to {lang_name} ({lang_code})...")
                        
                        # Change language
                        language_combo.setCurrentIndex(i)
                        general_tab.on_language_changed()
                        app.processEvents()
                        time.sleep(0.1)
                        
                        # Verify change
                        new_language = get_current_language()
                        if new_language == lang_code:
                            print(f"       ✅ Language changed to {lang_code}")
                            
                            # Check UI updates
                            nav_text = main_window.nav_buttons['settings'].text()
                            print(f"       - Settings button: '{nav_text}'")
                            
                            # Test only one change to avoid too many switches
                            break
                        else:
                            print(f"       ❌ Language change failed")
                            return False
                
                # Restore original language
                for i in range(language_combo.count()):
                    if language_combo.itemData(i) == original_language:
                        language_combo.setCurrentIndex(i)
                        general_tab.on_language_changed()
                        app.processEvents()
                        break
            
            # Test theme change through UI
            print("   Testing theme change through UI...")
            
            if general_tab:
                theme_combo = general_tab.theme_combo
                original_theme = config.get_theme()
                original_index = theme_combo.currentIndex()
                
                # Change theme
                new_index = (original_index + 1) % theme_combo.count()
                theme_combo.setCurrentIndex(new_index)
                
                try:
                    general_tab.on_theme_changed()
                    new_theme = config.get_theme()
                    
                    if new_theme != original_theme:
                        print(f"     ✅ Theme changed: {original_theme} → {new_theme}")
                        
                        # Restore original theme
                        theme_combo.setCurrentIndex(original_index)
                        general_tab.on_theme_changed()
                    else:
                        print(f"     ❌ Theme change failed")
                        return False
                except Exception as e:
                    print(f"     ❌ Theme change error: {e}")
                    return False
        else:
            print("   ❌ Settings widget not loaded")
            return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Real user interaction test failed: {e}")
        return False

def test_settings_data_integrity():
    """Test settings data integrity and persistence"""
    print("\n💾 Testing Settings Data Integrity...")
    
    try:
        # Test config manager functions
        print("   Testing config manager integrity...")
        
        # Test all config functions
        config_functions = [
            ('get_language', 'Language'),
            ('get_theme', 'Theme'),
            ('get_company_config', 'Company Config'),
            ('get_printer_config', 'Printer Config'),
            ('get_ui_config', 'UI Config')
        ]
        
        for func_name, description in config_functions:
            if hasattr(config, func_name):
                try:
                    result = getattr(config, func_name)()
                    print(f"     ✅ {description}: {type(result).__name__}")
                except Exception as e:
                    print(f"     ❌ {description}: Failed ({e})")
                    return False
            else:
                print(f"     ❌ {description}: Function not found")
                return False
        
        # Test setting and getting values
        print("   Testing value persistence...")
        
        # Test language persistence
        original_language = config.get_language()
        test_language = 'fr' if original_language != 'fr' else 'ar'
        
        config.set_language(test_language)
        saved_language = config.get_language()
        
        if saved_language == test_language:
            print(f"     ✅ Language persistence: {original_language} → {test_language}")
            config.set_language(original_language)  # Restore
        else:
            print(f"     ❌ Language persistence failed")
            return False
        
        # Test theme persistence
        original_theme = config.get_theme()
        test_theme = 'dark' if original_theme != 'dark' else 'light'
        
        config.set_theme(test_theme)
        saved_theme = config.get_theme()
        
        if saved_theme == test_theme:
            print(f"     ✅ Theme persistence: {original_theme} → {test_theme}")
            config.set_theme(original_theme)  # Restore
        else:
            print(f"     ❌ Theme persistence failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Settings data integrity test failed: {e}")
        return False

def test_settings_error_resilience():
    """Test settings error resilience"""
    print("\n🛡️ Testing Settings Error Resilience...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Test creating multiple settings widgets
        print("   Testing multiple widget creation...")
        
        widgets = []
        for i in range(3):
            try:
                widget = SettingsWidget()
                widgets.append(widget)
                print(f"     ✅ Widget {i+1}: Created successfully")
            except Exception as e:
                print(f"     ❌ Widget {i+1}: Creation failed ({e})")
                return False
        
        # Test rapid tab switching
        print("   Testing rapid operations...")
        
        if widgets:
            widget = widgets[0]
            
            # Rapid tab switching
            for iteration in range(5):
                for i in range(widget.tabs.count()):
                    widget.tabs.setCurrentIndex(i)
                    app.processEvents()
            
            print("     ✅ Rapid tab switching: Stable")
            
            # Test with invalid indices
            try:
                widget.tabs.setCurrentIndex(999)  # Invalid index
                print("     ✅ Invalid index handling: Safe")
            except Exception as e:
                print(f"     ⚠️ Invalid index handling: {e}")
        
        # Clean up
        for widget in widgets:
            widget.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Settings error resilience test failed: {e}")
        return False

def run_final_settings_verification():
    """Run final settings verification"""
    print("🔍 STARTING FINAL SETTINGS VERIFICATION")
    print("=" * 60)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # Test 1: Theme Change Functionality
    test_results.append(("Theme Change Functionality", test_theme_change_functionality()))
    
    # Test 2: Settings Translations
    test_results.append(("Settings Translations", test_settings_translations()))
    
    # Test 3: Real User Interaction
    test_results.append(("Real User Interaction", test_settings_real_user_interaction()))
    
    # Test 4: Data Integrity
    test_results.append(("Data Integrity", test_settings_data_integrity()))
    
    # Test 5: Error Resilience
    test_results.append(("Error Resilience", test_settings_error_resilience()))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 FINAL SETTINGS VERIFICATION RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL FINAL SETTINGS VERIFICATIONS PASSED!")
        print("✅ Settings system is fully functional and bug-free!")
        print("✅ Theme change functionality added and working")
        print("✅ All translations complete and accurate")
        print("✅ Real user interactions work perfectly")
        print("✅ Data integrity and persistence confirmed")
        print("✅ Error resilience and stability verified")
    else:
        print("⚠️  Some final verifications failed.")
        print("🔧 Remaining issues that need attention:")
        
        for test_name, result in test_results:
            if not result:
                print(f"   ❌ {test_name}: Requires further investigation")
    
    return passed == total

if __name__ == "__main__":
    run_final_settings_verification()
