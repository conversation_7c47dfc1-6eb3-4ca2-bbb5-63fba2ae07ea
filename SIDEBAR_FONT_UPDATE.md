# تحديث خط أسماء الأقسام في الشريط الجانبي

## 🔤 التحسينات المطبقة على أسماء الأقسام

### 📏 **حجم الخط:**
- **من**: `14px`
- **إلى**: `16px`
- **زيادة**: `+2px`

### 💪 **وزن الخط:**
- **من**: `700` (Bold)
- **إلى**: `800` (ExtraBold)
- **تحسين**: خط أكثر سماكة ووضوحاً

### 📐 **المساحة والحشو:**
- **من**: `15px 25px`
- **إلى**: `18px 30px`
- **تحسين**: مساحة أكبر حول النص

### 🎯 **أسماء الأقسام المحدثة:**

✅ **🛒 نقطة البيع** - `16px ExtraBold`

✅ **📦 المنتجات** - `16px ExtraBold`

✅ **🧍 العملاء** - `16px ExtraBold`

✅ **💰 المبيعات** - `16px ExtraBold`

✅ **⚙️ الإعدادات** - `16px ExtraBold`

### 🎨 **المظهر النهائي:**

🔤 **خط أكبر وأوضح** لأسماء الأقسام

💪 **وزن خط أقوى** (ExtraBold) للبروز

📐 **مساحة أكبر** للراحة البصرية

🎯 **سهولة التنقل** بين الأقسام

💙 **تناسق مع الثيم الأزرق** البارد

### 📁 **الملفات المحدثة:**

1. `main.py` - تنسيق الشريط الجانبي CSS
2. `views/main_window.py` - خط أزرار الشريط الجانبي

### ✅ **النتيجة:**

أسماء الأقسام في الشريط الجانبي أصبحت **أكبر وأوضح وأكثر بروزاً** مع الحفاظ على باقي خطوط التطبيق كما هي.

## 🎊 **التطبيق الآن يتميز بـ:**

- 🔤 **أسماء أقسام كبيرة وواضحة** (`16px ExtraBold`)
- 📱 **سهولة التنقل** بين الأقسام
- 💙 **ثيم أزرق بارد جميل**
- ⚫ **نصوص سوداء داكنة** في المحتوى
- 🎯 **تباين مثالي** للقراءة
- 📏 **تخطيط متوازن ومريح**
