#!/usr/bin/env python3
"""
إنشاء أيقونة احترافية لتطبيق OnePos
Create Professional Icon for OnePos Application
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_onepos_icon():
    """إنشاء أيقونة OnePos احترافية"""
    
    # أحجام الأيقونة المختلفة (ICO يدعم أحجام متعددة)
    sizes = [16, 24, 32, 48, 64, 128, 256]
    images = []
    
    for size in sizes:
        # إنشاء صورة جديدة بخلفية شفافة
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # ألوان احترافية
        primary_color = (41, 128, 185)    # أزرق احترافي
        secondary_color = (52, 152, 219)  # أزرق فاتح
        accent_color = (231, 76, 60)      # أحمر للنقطة
        white_color = (255, 255, 255)     # أبيض
        
        # رسم الخلفية الدائرية
        margin = size // 16
        circle_size = size - (margin * 2)
        
        # رسم ظل خفيف
        shadow_offset = max(1, size // 32)
        draw.ellipse([
            margin + shadow_offset, 
            margin + shadow_offset, 
            margin + circle_size + shadow_offset, 
            margin + circle_size + shadow_offset
        ], fill=(0, 0, 0, 50))
        
        # رسم الدائرة الرئيسية
        draw.ellipse([
            margin, margin, 
            margin + circle_size, 
            margin + circle_size
        ], fill=primary_color)
        
        # رسم حلقة داخلية
        inner_margin = margin + size // 8
        inner_size = circle_size - (size // 4)
        draw.ellipse([
            inner_margin, inner_margin,
            inner_margin + inner_size, 
            inner_margin + inner_size
        ], outline=secondary_color, width=max(1, size // 32))
        
        # رسم رمز POS (نقطة البيع)
        center_x = size // 2
        center_y = size // 2
        
        # رسم شاشة الكاشير (مستطيل)
        screen_width = size // 3
        screen_height = size // 4
        screen_x = center_x - screen_width // 2
        screen_y = center_y - screen_height // 2 - size // 8
        
        draw.rectangle([
            screen_x, screen_y,
            screen_x + screen_width, 
            screen_y + screen_height
        ], fill=white_color, outline=secondary_color, width=max(1, size // 64))
        
        # رسم خطوط على الشاشة (تمثل النص)
        if size >= 32:
            line_spacing = max(1, size // 32)
            for i in range(3):
                y_pos = screen_y + (i + 1) * line_spacing * 2
                if y_pos < screen_y + screen_height - line_spacing:
                    draw.line([
                        screen_x + line_spacing, y_pos,
                        screen_x + screen_width - line_spacing, y_pos
                    ], fill=primary_color, width=max(1, size // 64))
        
        # رسم رمز العملة (دولار أو رقم 1)
        if size >= 48:
            try:
                # محاولة استخدام خط
                font_size = max(8, size // 8)
                font = ImageFont.truetype("arial.ttf", font_size)
                text = "1"
            except:
                # استخدام خط افتراضي
                font = ImageFont.load_default()
                text = "1"
            
            # حساب موقع النص
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            text_x = center_x - text_width // 2
            text_y = center_y + size // 8
            
            # رسم النص
            draw.text((text_x, text_y), text, fill=white_color, font=font)
        
        # رسم نقطة حمراء (إشارة نشاط)
        dot_size = max(2, size // 16)
        dot_x = center_x + size // 4
        dot_y = center_y - size // 4
        
        draw.ellipse([
            dot_x - dot_size, dot_y - dot_size,
            dot_x + dot_size, dot_y + dot_size
        ], fill=accent_color)
        
        # إضافة لمعة (highlight)
        if size >= 64:
            highlight_size = size // 6
            highlight_x = center_x - size // 4
            highlight_y = center_y - size // 4
            
            draw.ellipse([
                highlight_x, highlight_y,
                highlight_x + highlight_size, 
                highlight_y + highlight_size
            ], fill=(255, 255, 255, 80))
        
        images.append(img)
    
    return images

def save_icon():
    """حفظ الأيقونة كملف ICO"""
    try:
        print("🎨 إنشاء أيقونة OnePos احترافية...")
        
        # إنشاء الأيقونات
        images = create_onepos_icon()
        
        # حفظ كملف ICO
        icon_path = "app_icon.ico"
        images[0].save(
            icon_path, 
            format='ICO', 
            sizes=[(img.width, img.height) for img in images],
            append_images=images[1:]
        )
        
        print(f"✅ تم إنشاء الأيقونة: {icon_path}")
        print(f"📏 الأحجام المتاحة: {[img.size for img in images]}")
        
        # إنشاء نسخة PNG للمعاينة
        preview_path = "app_icon_preview.png"
        images[-1].save(preview_path, format='PNG')
        print(f"🖼️ معاينة الأيقونة: {preview_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الأيقونة: {e}")
        return False

def create_alternative_icon():
    """إنشاء أيقونة بديلة أبسط"""
    try:
        print("🎨 إنشاء أيقونة بديلة...")
        
        # إنشاء أيقونة بسيطة 256x256
        size = 256
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # ألوان
        bg_color = (52, 152, 219)     # أزرق
        text_color = (255, 255, 255)  # أبيض
        accent_color = (231, 76, 60)  # أحمر
        
        # رسم مربع مدور
        margin = 20
        draw.rounded_rectangle([
            margin, margin, 
            size - margin, size - margin
        ], radius=30, fill=bg_color)
        
        # رسم النص "1"
        try:
            font = ImageFont.truetype("arial.ttf", 120)
        except:
            font = ImageFont.load_default()
        
        text = "1"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        text_x = (size - text_width) // 2
        text_y = (size - text_height) // 2 - 10
        
        draw.text((text_x, text_y), text, fill=text_color, font=font)
        
        # رسم نقطة صغيرة
        dot_size = 15
        dot_x = size - margin - 30
        dot_y = margin + 30
        
        draw.ellipse([
            dot_x - dot_size, dot_y - dot_size,
            dot_x + dot_size, dot_y + dot_size
        ], fill=accent_color)
        
        # حفظ الأيقونة
        img.save("app_icon.ico", format='ICO')
        img.save("app_icon_simple.png", format='PNG')
        
        print("✅ تم إنشاء الأيقونة البديلة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الأيقونة البديلة: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إنشاء أيقونة OnePos")
    print("=" * 50)
    
    # محاولة إنشاء الأيقونة الاحترافية
    success = save_icon()
    
    if not success:
        print("\n🔄 محاولة إنشاء أيقونة بديلة...")
        success = create_alternative_icon()
    
    if success:
        print("\n🎉 تم إنشاء الأيقونة بنجاح!")
        print("📁 الملفات المنشأة:")
        
        if os.path.exists("app_icon.ico"):
            size = os.path.getsize("app_icon.ico")
            print(f"  ✅ app_icon.ico ({size} bytes)")
        
        if os.path.exists("app_icon_preview.png"):
            print(f"  🖼️ app_icon_preview.png (للمعاينة)")
        
        if os.path.exists("app_icon_simple.png"):
            print(f"  🖼️ app_icon_simple.png (للمعاينة)")
        
        print("\n🎯 الأيقونة جاهزة للاستخدام في البناء!")
        
    else:
        print("\n❌ فشل في إنشاء الأيقونة")
        print("💡 يمكنك استخدام أي ملف .ico آخر")
    
    print("\n" + "=" * 50)
