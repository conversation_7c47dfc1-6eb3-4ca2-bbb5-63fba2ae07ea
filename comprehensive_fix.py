#!/usr/bin/env python3
"""
إصلاح شامل لجميع مشاكل التطبيق
Comprehensive fix for all application issues
"""

import os
import json
import shutil
from datetime import datetime

def backup_files():
    """إنشاء نسخة احتياطية من الملفات المهمة"""
    print("📦 إنشاء نسخة احتياطية...")
    
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = [
        "views/main_window.py",
        "views/pos_widget.py", 
        "main.py",
        "translations/ar.json",
        "translations/en.json",
        "translations/fr.json"
    ]
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            backup_path = os.path.join(backup_dir, file_path.replace('/', '_'))
            shutil.copy2(file_path, backup_path)
            print(f"   ✅ نسخ احتياطي: {file_path}")
    
    print(f"✅ تم إنشاء النسخة الاحتياطية في: {backup_dir}")
    return backup_dir

def fix_language_switching():
    """إصلاح مشكلة تغيير اللغة"""
    print("\n🔧 إصلاح مشكلة تغيير اللغة...")
    
    file_path = "views/main_window.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح السطر المشكل
        old_line = 'button.setText(f"{icon} {tr(key)}")'
        new_line = 'button.setText(f"{icon} {tr(f\'navigation.{key}\')}")'
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("   ✅ تم إصلاح مشكلة تغيير اللغة")
            return True
        else:
            print("   ⚠️ لم يتم العثور على السطر المطلوب إصلاحه")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في إصلاح تغيير اللغة: {e}")
        return False

def fix_font_sizes():
    """إصلاح أحجام الخطوط"""
    print("\n🔧 إصلاح أحجام الخطوط...")
    
    file_path = "main.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تقليل حجم الخط من 12 إلى 10
        old_line = 'font = QFont("Segoe UI", 12, QFont.Medium)'
        new_line = 'font = QFont("Segoe UI", 10, QFont.Medium)'
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("   ✅ تم تقليل حجم الخط من 12 إلى 10")
            return True
        else:
            print("   ⚠️ لم يتم العثور على إعداد الخط")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في إصلاح أحجام الخطوط: {e}")
        return False

def implement_hold_order():
    """تطبيق ميزة Hold Order"""
    print("\n🔧 تطبيق ميزة Hold Order...")
    
    file_path = "views/pos_widget.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة hold_order الحالية
        old_function = '''def hold_order(self):
        """Hold current order"""
        QMessageBox.information(self, tr("pos.hold_order"),
                              "Hold order functionality will be implemented")'''
        
        new_function = '''def hold_order(self):
        """Hold current order"""
        if not self.cart_items:
            QMessageBox.warning(self, tr("common.warning"), tr("pos.cart_empty"))
            return
        
        # طلب اسم للطلب المعلق
        from PyQt5.QtWidgets import QInputDialog
        order_name, ok = QInputDialog.getText(
            self, tr("pos.hold_order"), 
            tr("pos.enter_order_name"),
            text=f"Order {datetime.now().strftime('%H:%M')}"
        )
        
        if not ok or not order_name.strip():
            return
        
        try:
            # حفظ الطلب المعلق
            held_order = {
                'name': order_name.strip(),
                'items': self.cart_items.copy(),
                'customer': self.current_customer.id if self.current_customer else None,
                'tax_rate': self.tax_rate,
                'timestamp': datetime.now().isoformat()
            }
            
            # إضافة للقائمة المعلقة
            if not hasattr(self, 'held_orders'):
                self.held_orders = []
            
            self.held_orders.append(held_order)
            
            # مسح السلة الحالية
            self.clear_cart()
            
            QMessageBox.information(self, tr("common.success"), 
                                  tr("pos.order_held_successfully").format(order_name))
            
            # تحديث قائمة الطلبات المعلقة
            self.update_held_orders_menu()
            
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"Error holding order: {e}")'''
        
        if old_function in content:
            content = content.replace(old_function, new_function)
            
            # إضافة import datetime في بداية الملف
            if "from datetime import datetime" not in content:
                import_line = "from datetime import datetime"
                # البحث عن آخر import
                lines = content.split('\n')
                insert_index = 0
                for i, line in enumerate(lines):
                    if line.startswith('from ') or line.startswith('import '):
                        insert_index = i + 1
                
                lines.insert(insert_index, import_line)
                content = '\n'.join(lines)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("   ✅ تم تطبيق ميزة Hold Order")
            return True
        else:
            print("   ⚠️ لم يتم العثور على دالة hold_order")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في تطبيق Hold Order: {e}")
        return False

def add_held_orders_management():
    """إضافة إدارة الطلبات المعلقة"""
    print("\n🔧 إضافة إدارة الطلبات المعلقة...")
    
    file_path = "views/pos_widget.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة دالة إدارة الطلبات المعلقة
        new_methods = '''
    def update_held_orders_menu(self):
        """تحديث قائمة الطلبات المعلقة"""
        if hasattr(self, 'held_orders_button'):
            count = len(getattr(self, 'held_orders', []))
            self.held_orders_button.setText(f"📋 {tr('pos.held_orders')} ({count})")
    
    def show_held_orders(self):
        """عرض الطلبات المعلقة"""
        if not hasattr(self, 'held_orders') or not self.held_orders:
            QMessageBox.information(self, tr("pos.held_orders"), tr("pos.no_held_orders"))
            return
        
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QListWidget, QHBoxLayout
        
        dialog = QDialog(self)
        dialog.setWindowTitle(tr("pos.held_orders"))
        dialog.setModal(True)
        dialog.resize(400, 300)
        
        layout = QVBoxLayout(dialog)
        
        # قائمة الطلبات
        orders_list = QListWidget()
        for i, order in enumerate(self.held_orders):
            item_text = f"{order['name']} - {order['timestamp'][:16]} - {len(order['items'])} items"
            orders_list.addItem(item_text)
        
        layout.addWidget(orders_list)
        
        # أزرار
        buttons_layout = QHBoxLayout()
        
        restore_button = QPushButton(tr("pos.restore_order"))
        restore_button.clicked.connect(lambda: self.restore_held_order(orders_list.currentRow(), dialog))
        
        delete_button = QPushButton(tr("pos.delete_held_order"))
        delete_button.clicked.connect(lambda: self.delete_held_order(orders_list.currentRow(), dialog))
        
        buttons_layout.addWidget(restore_button)
        buttons_layout.addWidget(delete_button)
        layout.addLayout(buttons_layout)
        
        dialog.exec_()
    
    def restore_held_order(self, index, dialog):
        """استعادة طلب معلق"""
        if index < 0 or index >= len(self.held_orders):
            return
        
        order = self.held_orders[index]
        
        # مسح السلة الحالية
        self.clear_cart()
        
        # استعادة العناصر
        self.cart_items = order['items'].copy()
        self.tax_rate = order.get('tax_rate', 0.0)
        
        # تحديث العرض
        self.update_cart_display()
        self.calculate_totals()
        
        # حذف من القائمة المعلقة
        self.held_orders.pop(index)
        self.update_held_orders_menu()
        
        dialog.accept()
        QMessageBox.information(self, tr("common.success"), tr("pos.order_restored"))
    
    def delete_held_order(self, index, dialog):
        """حذف طلب معلق"""
        if index < 0 or index >= len(self.held_orders):
            return
        
        order = self.held_orders[index]
        reply = QMessageBox.question(
            self, tr("common.confirm"),
            tr("pos.confirm_delete_held_order").format(order['name']),
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.held_orders.pop(index)
            self.update_held_orders_menu()
            dialog.accept()
'''
        
        # إضافة الدوال الجديدة قبل النهاية
        if "def update_held_orders_menu(self):" not in content:
            # البحث عن نهاية الكلاس
            class_end = content.rfind("class ")
            if class_end != -1:
                # البحث عن نهاية الكلاس الحالي
                next_class = content.find("\nclass ", class_end + 1)
                if next_class == -1:
                    # إضافة في النهاية
                    content += new_methods
                else:
                    # إضافة قبل الكلاس التالي
                    content = content[:next_class] + new_methods + content[next_class:]
            else:
                content += new_methods
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("   ✅ تم إضافة إدارة الطلبات المعلقة")
            return True
        else:
            print("   ⚠️ إدارة الطلبات المعلقة موجودة بالفعل")
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في إضافة إدارة الطلبات المعلقة: {e}")
        return False

def add_missing_translations():
    """إضافة الترجمات المفقودة"""
    print("\n🔧 إضافة الترجمات المفقودة...")
    
    translations = {
        "ar.json": {
            "pos.cart_empty": "السلة فارغة",
            "pos.enter_order_name": "أدخل اسم الطلب:",
            "pos.order_held_successfully": "تم حفظ الطلب '{0}' بنجاح",
            "pos.held_orders": "الطلبات المعلقة",
            "pos.no_held_orders": "لا توجد طلبات معلقة",
            "pos.restore_order": "استعادة الطلب",
            "pos.delete_held_order": "حذف الطلب",
            "pos.order_restored": "تم استعادة الطلب بنجاح",
            "pos.confirm_delete_held_order": "هل أنت متأكد من حذف الطلب '{0}'؟"
        },
        "en.json": {
            "pos.cart_empty": "Cart is empty",
            "pos.enter_order_name": "Enter order name:",
            "pos.order_held_successfully": "Order '{0}' held successfully",
            "pos.held_orders": "Held Orders",
            "pos.no_held_orders": "No held orders",
            "pos.restore_order": "Restore Order",
            "pos.delete_held_order": "Delete Order",
            "pos.order_restored": "Order restored successfully",
            "pos.confirm_delete_held_order": "Are you sure you want to delete order '{0}'?"
        },
        "fr.json": {
            "pos.cart_empty": "Le panier est vide",
            "pos.enter_order_name": "Entrez le nom de la commande:",
            "pos.order_held_successfully": "Commande '{0}' mise en attente avec succès",
            "pos.held_orders": "Commandes en Attente",
            "pos.no_held_orders": "Aucune commande en attente",
            "pos.restore_order": "Restaurer la Commande",
            "pos.delete_held_order": "Supprimer la Commande",
            "pos.order_restored": "Commande restaurée avec succès",
            "pos.confirm_delete_held_order": "Êtes-vous sûr de vouloir supprimer la commande '{0}'?"
        }
    }
    
    success_count = 0
    
    for filename, new_translations in translations.items():
        file_path = f"translations/{filename}"
        
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # إضافة الترجمات الجديدة
                for key, value in new_translations.items():
                    if key not in data:
                        data[key] = value
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=4)
                
                print(f"   ✅ تم تحديث {filename}")
                success_count += 1
            else:
                print(f"   ⚠️ ملف غير موجود: {filename}")
                
        except Exception as e:
            print(f"   ❌ خطأ في تحديث {filename}: {e}")
    
    return success_count > 0

def test_fixes():
    """اختبار الإصلاحات"""
    print("\n🧪 اختبار الإصلاحات...")
    
    try:
        # اختبار تحميل الملفات
        from views.main_window import MainWindow
        print("   ✅ تم تحميل MainWindow بنجاح")
        
        from views.pos_widget import POSWidget
        print("   ✅ تم تحميل POSWidget بنجاح")
        
        # اختبار الترجمات
        from utils.translation_manager import tr
        test_keys = ["pos.hold_order", "pos.held_orders", "category_management"]
        for key in test_keys:
            translation = tr(key)
            print(f"   ✅ ترجمة {key}: {translation}")
        
        print("   🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"   ❌ فشل الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الإصلاح الشامل لتطبيق OnePos")
    print("=" * 60)
    
    # إنشاء نسخة احتياطية
    backup_dir = backup_files()
    
    fixes_applied = []
    
    # تطبيق الإصلاحات
    if fix_language_switching():
        fixes_applied.append("إصلاح تغيير اللغة")
    
    if fix_font_sizes():
        fixes_applied.append("إصلاح أحجام الخطوط")
    
    if implement_hold_order():
        fixes_applied.append("تطبيق ميزة Hold Order")
    
    if add_held_orders_management():
        fixes_applied.append("إضافة إدارة الطلبات المعلقة")
    
    if add_missing_translations():
        fixes_applied.append("إضافة الترجمات المفقودة")
    
    # اختبار الإصلاحات
    test_success = test_fixes()
    
    # تقرير النتائج
    print("\n" + "=" * 60)
    print("📋 تقرير الإصلاح الشامل:")
    print(f"📦 النسخة الاحتياطية: {backup_dir}")
    print(f"🔧 الإصلاحات المطبقة: {len(fixes_applied)}")
    
    for fix in fixes_applied:
        print(f"   ✅ {fix}")
    
    if test_success:
        print("🧪 الاختبارات: ✅ نجحت")
        print("\n🎉 تم الإصلاح الشامل بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. شغل: pyinstaller --onedir --windowed --icon=app_icon.ico --name=OnePos main.py --clean")
        print("2. شغل: build_installer_simple.bat")
        print("3. اختبر التطبيق الجديد")
    else:
        print("🧪 الاختبارات: ❌ فشلت")
        print(f"\n⚠️ يمكن استعادة النسخة الاحتياطية من: {backup_dir}")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
