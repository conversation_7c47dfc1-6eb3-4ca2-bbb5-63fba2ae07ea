"""
Theme manager for OnePos POS System
Enhanced styling and theme management
"""

from PyQt5.QtGui import QFont, QPalette, QColor
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QApplication


class ThemeManager:
    """Manage application themes and styling"""
    
    @staticmethod
    def get_enhanced_light_theme():
        """Get enhanced light theme with better contrast and readability"""
        return """
        /* Enhanced Light Theme for OnePos */
        * {
            font-family: "Segoe UI", "Arial", sans-serif;
            font-size: 12px;
            font-weight: 600;
            color: #000000;
        }
        
        QMainWindow {
            background-color: #f8f9fa;
            color: #000000;
        }

        QWidget {
            background-color: #ffffff;
            color: #000000;
            selection-background-color: #0d6efd;
            selection-color: #ffffff;
        }
        
        /* Buttons */
        QPushButton {
            background-color: #e9ecef;
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px 16px;
            color: #000000;
            font-weight: 700;
            font-size: 12px;
            min-height: 20px;
        }

        QPushButton:hover {
            background-color: #dee2e6;
            border-color: #adb5bd;
            color: #000000;
        }
        
        QPushButton:pressed {
            background-color: #ced4da;
            border-color: #adb5bd;
        }
        
        QPushButton:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            border-color: #dee2e6;
        }
        
        /* Input Fields */
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #ffffff;
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px 12px;
            color: #000000;
            font-weight: 600;
            font-size: 12px;
            selection-background-color: #0d6efd;
            selection-color: #ffffff;
        }
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #0d6efd;
            background-color: #ffffff;
        }
        
        QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
            border-color: #dee2e6;
        }
        
        /* ComboBox */
        QComboBox {
            background-color: #ffffff;
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px 12px;
            color: #000000;
            font-weight: 600;
            font-size: 12px;
            min-height: 20px;
        }
        
        QComboBox:hover {
            border-color: #adb5bd;
        }
        
        QComboBox:focus {
            border-color: #0d6efd;
        }
        
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        
        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #212529;
            margin-right: 5px;
        }
        
        QComboBox QAbstractItemView {
            background-color: #ffffff;
            border: 2px solid #ced4da;
            border-radius: 6px;
            color: #000000;
            selection-background-color: #0d6efd;
            selection-color: #ffffff;
        }
        
        /* SpinBox */
        QSpinBox, QDoubleSpinBox {
            background-color: #ffffff;
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px 12px;
            color: #000000;
            font-weight: 600;
            font-size: 12px;
            min-height: 20px;
        }
        
        QSpinBox:focus, QDoubleSpinBox:focus {
            border-color: #0d6efd;
        }
        
        /* Tables */
        QTableWidget {
            background-color: #ffffff;
            alternate-background-color: #f8f9fa;
            gridline-color: #dee2e6;
            color: #000000;
            font-weight: 600;
            font-size: 12px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
        }
        
        QTableWidget::item:selected {
            background-color: #0d6efd;
            color: #ffffff;
        }
        
        QTableWidget::item:hover {
            background-color: #e7f1ff;
        }
        
        QHeaderView::section {
            background-color: #e9ecef;
            color: #000000;
            padding: 8px;
            border: 1px solid #ced4da;
            font-weight: 700;
            font-size: 12px;
        }
        
        /* Labels */
        QLabel {
            color: #000000;
            font-weight: 600;
            font-size: 12px;
        }
        
        /* GroupBox */
        QGroupBox {
            color: #000000;
            font-weight: 700;
            font-size: 13px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin-top: 12px;
            padding-top: 12px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 8px 0 8px;
            color: #000000;
            font-weight: bold;
            background-color: #ffffff;
        }
        
        /* CheckBox */
        QCheckBox {
            color: #000000;
            font-weight: 600;
            font-size: 12px;
            spacing: 8px;
        }
        
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #ced4da;
            border-radius: 4px;
            background-color: #ffffff;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
            image: none;
        }
        
        QCheckBox::indicator:checked:hover {
            background-color: #0b5ed7;
            border-color: #0b5ed7;
        }
        
        /* RadioButton */
        QRadioButton {
            color: #000000;
            font-weight: 600;
            font-size: 12px;
            spacing: 8px;
        }
        
        QRadioButton::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #ced4da;
            border-radius: 9px;
            background-color: #ffffff;
        }
        
        QRadioButton::indicator:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        /* Tabs */
        QTabWidget::pane {
            border: 2px solid #dee2e6;
            border-radius: 6px;
            background-color: #ffffff;
            margin-top: -1px;
        }
        
        QTabBar::tab {
            background-color: #f8f9fa;
            color: #000000;
            padding: 10px 16px;
            margin-right: 2px;
            font-weight: 700;
            font-size: 12px;
            border: 2px solid #dee2e6;
            border-bottom: none;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }
        
        QTabBar::tab:selected {
            background-color: #0d6efd;
            color: #ffffff;
            border-color: #0d6efd;
        }
        
        QTabBar::tab:hover {
            background-color: #e9ecef;
            color: #000000;
        }
        
        /* Scrollbars */
        QScrollBar:vertical {
            background-color: #f8f9fa;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #ced4da;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #adb5bd;
        }
        
        QScrollBar:horizontal {
            background-color: #f8f9fa;
            height: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:horizontal {
            background-color: #ced4da;
            border-radius: 6px;
            min-width: 20px;
        }
        
        QScrollBar::handle:horizontal:hover {
            background-color: #adb5bd;
        }
        
        /* Progress Bar */
        QProgressBar {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            text-align: center;
            color: #212529;
            font-weight: 600;
        }
        
        QProgressBar::chunk {
            background-color: #0d6efd;
            border-radius: 4px;
        }
        
        /* Status Bar */
        QStatusBar {
            background-color: #e9ecef;
            color: #212529;
            border-top: 1px solid #ced4da;
            font-weight: 500;
        }
        
        /* Menu Bar */
        QMenuBar {
            background-color: #f8f9fa;
            color: #212529;
            border-bottom: 1px solid #dee2e6;
            font-weight: 500;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 8px 12px;
        }
        
        QMenuBar::item:selected {
            background-color: #e9ecef;
        }
        
        QMenu {
            background-color: #ffffff;
            color: #212529;
            border: 2px solid #dee2e6;
            border-radius: 6px;
        }
        
        QMenu::item {
            padding: 8px 16px;
        }
        
        QMenu::item:selected {
            background-color: #0d6efd;
            color: #ffffff;
        }
        """
    
    @staticmethod
    def apply_enhanced_font():
        """Apply enhanced font settings"""
        app = QApplication.instance()
        if app:
            # Set better font with improved readability
            font = QFont("Segoe UI", 12, QFont.DemiBold)
            font.setHintingPreference(QFont.PreferFullHinting)
            font.setStyleStrategy(QFont.PreferAntialias)
            font.setWeight(QFont.DemiBold)
            app.setFont(font)
    
    @staticmethod
    def get_color_palette():
        """Get color palette for the application"""
        return {
            'primary': '#0d6efd',
            'secondary': '#6c757d',
            'success': '#198754',
            'danger': '#dc3545',
            'warning': '#fd7e14',
            'info': '#0dcaf0',
            'light': '#f8f9fa',
            'dark': '#212529',
            'white': '#ffffff',
            'gray_100': '#f8f9fa',
            'gray_200': '#e9ecef',
            'gray_300': '#dee2e6',
            'gray_400': '#ced4da',
            'gray_500': '#adb5bd',
            'gray_600': '#6c757d',
            'gray_700': '#495057',
            'gray_800': '#343a40',
            'gray_900': '#212529'
        }
