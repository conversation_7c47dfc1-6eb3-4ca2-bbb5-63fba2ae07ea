"""
Enhanced Reports Widget for OnePos POS System
Comprehensive reporting and analytics interface
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime, date, timedelta
from models.reports import ReportsManager
from utils.translator import tr

def create_dark_label(text):
    """Create a dark label with proper styling"""
    label = QLabel(text)
    label.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
    return label

class ReportsWidget(QWidget):
    """Enhanced reports and analytics widget"""
    
    def __init__(self):
        super().__init__()
        self.reports_manager = ReportsManager()
        self.init_ui()
        self.load_reports()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("📊 " + tr("reports.title"))
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title_label.setStyleSheet("color: #000000; font-weight: 800; font-size: 18px;")
        header_layout.addWidget(title_label)
        
        # Refresh button
        self.refresh_button = QPushButton("🔄 " + tr("common.refresh"))
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
        """)
        self.refresh_button.clicked.connect(self.load_reports)
        header_layout.addWidget(self.refresh_button)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Create tabs for different report categories
        self.tab_widget = QTabWidget()
        
        # Daily Reports Tab
        self.daily_tab = self.create_daily_reports_tab()
        self.tab_widget.addTab(self.daily_tab, "📅 " + tr("reports.daily"))
        
        # Weekly Reports Tab
        self.weekly_tab = self.create_weekly_reports_tab()
        self.tab_widget.addTab(self.weekly_tab, "📊 " + tr("reports.weekly"))
        
        # Monthly Reports Tab
        self.monthly_tab = self.create_monthly_reports_tab()
        self.tab_widget.addTab(self.monthly_tab, "📈 " + tr("reports.monthly"))
        
        # Products Analytics Tab
        self.products_tab = self.create_products_analytics_tab()
        self.tab_widget.addTab(self.products_tab, "📦 " + tr("reports.products"))
        
        # Customers Analytics Tab
        self.customers_tab = self.create_customers_analytics_tab()
        self.tab_widget.addTab(self.customers_tab, "🧍 " + tr("reports.customers"))
        
        layout.addWidget(self.tab_widget)
    
    def create_daily_reports_tab(self):
        """Create daily reports tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Date selection
        date_layout = QHBoxLayout()
        date_layout.addWidget(create_dark_label(tr("reports.select_date") + ":"))
        
        self.daily_date_edit = QDateEdit()
        self.daily_date_edit.setDate(QDate.currentDate())
        self.daily_date_edit.setCalendarPopup(True)
        self.daily_date_edit.dateChanged.connect(self.load_daily_report)
        date_layout.addWidget(self.daily_date_edit)
        date_layout.addStretch()
        
        layout.addLayout(date_layout)
        
        # Daily summary cards
        self.daily_summary_layout = QHBoxLayout()
        layout.addLayout(self.daily_summary_layout)
        
        # Daily sales table
        self.daily_table = QTableWidget()
        self.daily_table.setColumnCount(6)
        self.daily_table.setHorizontalHeaderLabels([
            tr("sales.invoice_number"),
            tr("sales.time"),
            tr("customers.customer"),
            tr("sales.amount"),
            tr("pos.payment"),
            tr("common.status")
        ])
        self.daily_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.daily_table)
        
        return widget
    
    def create_weekly_reports_tab(self):
        """Create weekly reports tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Week selection
        week_layout = QHBoxLayout()
        week_layout.addWidget(create_dark_label(tr("reports.week_start") + ":"))
        
        self.weekly_date_edit = QDateEdit()
        self.weekly_date_edit.setDate(QDate.currentDate().addDays(-6))
        self.weekly_date_edit.setCalendarPopup(True)
        self.weekly_date_edit.dateChanged.connect(self.load_weekly_report)
        week_layout.addWidget(self.weekly_date_edit)
        week_layout.addStretch()
        
        layout.addLayout(week_layout)
        
        # Weekly summary
        self.weekly_summary_layout = QHBoxLayout()
        layout.addLayout(self.weekly_summary_layout)
        
        # Weekly chart area (placeholder)
        self.weekly_chart_area = QTextEdit()
        self.weekly_chart_area.setMaximumHeight(200)
        self.weekly_chart_area.setReadOnly(True)
        layout.addWidget(self.weekly_chart_area)
        
        return widget
    
    def create_monthly_reports_tab(self):
        """Create monthly reports tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Month selection
        month_layout = QHBoxLayout()
        month_layout.addWidget(create_dark_label(tr("reports.month") + ":"))
        
        self.month_combo = QComboBox()
        months = [
            "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"
        ]
        self.month_combo.addItems(months)
        self.month_combo.setCurrentIndex(date.today().month - 1)
        self.month_combo.currentIndexChanged.connect(self.load_monthly_report)
        month_layout.addWidget(self.month_combo)
        
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2030)
        self.year_spin.setValue(date.today().year)
        self.year_spin.valueChanged.connect(self.load_monthly_report)
        month_layout.addWidget(self.year_spin)
        month_layout.addStretch()
        
        layout.addLayout(month_layout)
        
        # Monthly summary
        self.monthly_summary_layout = QHBoxLayout()
        layout.addLayout(self.monthly_summary_layout)
        
        # Profit analysis
        profit_group = QGroupBox("💰 " + tr("reports.profit_analysis"))
        profit_layout = QFormLayout(profit_group)
        
        self.profit_revenue_label = QLabel("0.00")
        self.profit_cost_label = QLabel("0.00")
        self.profit_profit_label = QLabel("0.00")
        self.profit_margin_label = QLabel("0.00%")
        
        profit_layout.addRow(create_dark_label(tr("reports.total_revenue") + ":"), self.profit_revenue_label)
        profit_layout.addRow(create_dark_label(tr("reports.total_cost") + ":"), self.profit_cost_label)
        profit_layout.addRow(create_dark_label(tr("reports.total_profit") + ":"), self.profit_profit_label)
        profit_layout.addRow(create_dark_label(tr("reports.profit_margin") + ":"), self.profit_margin_label)
        
        layout.addWidget(profit_group)
        
        return widget
    
    def create_products_analytics_tab(self):
        """Create products analytics tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Top selling products
        top_products_group = QGroupBox("🏆 " + tr("reports.top_products"))
        top_products_layout = QVBoxLayout(top_products_group)
        
        self.top_products_table = QTableWidget()
        self.top_products_table.setColumnCount(5)
        self.top_products_table.setHorizontalHeaderLabels([
            tr("products.name"),
            tr("products.sku"),
            tr("reports.quantity_sold"),
            tr("reports.revenue"),
            tr("reports.times_sold")
        ])
        self.top_products_table.horizontalHeader().setStretchLastSection(True)
        top_products_layout.addWidget(self.top_products_table)
        
        layout.addWidget(top_products_group)
        
        # Low stock products
        low_stock_group = QGroupBox("⚠️ " + tr("reports.low_stock"))
        low_stock_layout = QVBoxLayout(low_stock_group)
        
        self.low_stock_table = QTableWidget()
        self.low_stock_table.setColumnCount(4)
        self.low_stock_table.setHorizontalHeaderLabels([
            tr("products.name"),
            tr("products.sku"),
            tr("products.stock"),
            tr("products.min_stock")
        ])
        self.low_stock_table.horizontalHeader().setStretchLastSection(True)
        low_stock_layout.addWidget(self.low_stock_table)
        
        layout.addWidget(low_stock_group)
        
        return widget
    
    def create_customers_analytics_tab(self):
        """Create customers analytics tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Top customers
        top_customers_group = QGroupBox("👑 " + tr("reports.top_customers"))
        top_customers_layout = QVBoxLayout(top_customers_group)
        
        self.top_customers_table = QTableWidget()
        self.top_customers_table.setColumnCount(5)
        self.top_customers_table.setHorizontalHeaderLabels([
            tr("customers.name"),
            tr("customers.phone"),
            tr("reports.total_purchases"),
            tr("reports.total_spent"),
            tr("reports.last_purchase")
        ])
        self.top_customers_table.horizontalHeader().setStretchLastSection(True)
        top_customers_layout.addWidget(self.top_customers_table)
        
        layout.addWidget(top_customers_group)
        
        # Payment methods
        payment_group = QGroupBox("💳 " + tr("reports.payment_methods"))
        payment_layout = QVBoxLayout(payment_group)
        
        self.payment_table = QTableWidget()
        self.payment_table.setColumnCount(4)
        self.payment_table.setHorizontalHeaderLabels([
            tr("pos.payment"),
            tr("reports.transactions"),
            tr("reports.total_amount"),
            tr("reports.average_amount")
        ])
        self.payment_table.horizontalHeader().setStretchLastSection(True)
        payment_layout.addWidget(self.payment_table)
        
        layout.addWidget(payment_group)
        
        return widget
    
    def create_summary_card(self, title: str, value: str, icon: str = "📊") -> QWidget:
        """Create a summary card widget"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setStyleSheet("""
            QFrame {
                background-color: #f0f8ff;
                border: 2px solid #000000;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        
        layout = QVBoxLayout(card)
        
        # Icon and title
        title_layout = QHBoxLayout()
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Segoe UI", 20))
        title_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("color: #000000; font-weight: 700; font-size: 12px;")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addLayout(title_layout)
        
        # Value
        value_label = QLabel(value)
        value_label.setStyleSheet("color: #000000; font-weight: 800; font-size: 18px;")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        return card
    
    def load_reports(self):
        """Load all reports"""
        self.load_daily_report()
        self.load_weekly_report()
        self.load_monthly_report()
        self.load_products_analytics()
        self.load_customers_analytics()
    
    def load_daily_report(self):
        """Load daily report"""
        try:
            selected_date = self.daily_date_edit.date().toPyDate()
            summary = self.reports_manager.get_daily_sales_summary(selected_date)
            
            # Clear previous cards
            for i in reversed(range(self.daily_summary_layout.count())):
                self.daily_summary_layout.itemAt(i).widget().setParent(None)
            
            # Add summary cards
            if summary:
                cards = [
                    ("Total Sales", str(summary['total_sales']), "🛒"),
                    ("Revenue", f"{summary['total_revenue']:.2f}", "💰"),
                    ("Avg Sale", f"{summary['average_sale']:.2f}", "📊"),
                    ("Customers", str(summary['unique_customers']), "🧍")
                ]
                
                for title, value, icon in cards:
                    card = self.create_summary_card(title, value, icon)
                    self.daily_summary_layout.addWidget(card)
            
        except Exception as e:
            print(f"Error loading daily report: {e}")
    
    def load_weekly_report(self):
        """Load weekly report"""
        try:
            start_date = self.weekly_date_edit.date().toPyDate()
            summary = self.reports_manager.get_weekly_sales_summary(start_date)
            
            # Clear previous cards
            for i in reversed(range(self.weekly_summary_layout.count())):
                self.weekly_summary_layout.itemAt(i).widget().setParent(None)
            
            # Add summary cards
            if summary:
                cards = [
                    ("Total Sales", str(summary['total_sales']), "🛒"),
                    ("Total Revenue", f"{summary['total_revenue']:.2f}", "💰"),
                    ("Daily Avg", f"{summary['average_daily_revenue']:.2f}", "📊")
                ]
                
                for title, value, icon in cards:
                    card = self.create_summary_card(title, value, icon)
                    self.weekly_summary_layout.addWidget(card)
            
            # Update chart area with daily breakdown
            if summary and summary['daily_data']:
                chart_text = "Daily Sales Breakdown:\n\n"
                for day_data in summary['daily_data']:
                    chart_text += f"{day_data['date']}: {day_data['sales']} sales, {day_data['revenue']:.2f} revenue\n"
                self.weekly_chart_area.setText(chart_text)
            
        except Exception as e:
            print(f"Error loading weekly report: {e}")
    
    def load_monthly_report(self):
        """Load monthly report"""
        try:
            year = self.year_spin.value()
            month = self.month_combo.currentIndex() + 1
            summary = self.reports_manager.get_monthly_sales_summary(year, month)
            
            # Clear previous cards
            for i in reversed(range(self.monthly_summary_layout.count())):
                self.monthly_summary_layout.itemAt(i).widget().setParent(None)
            
            # Add summary cards
            if summary:
                cards = [
                    ("Total Sales", str(summary['total_sales']), "🛒"),
                    ("Revenue", f"{summary['total_revenue']:.2f}", "💰"),
                    ("Customers", str(summary['unique_customers']), "🧍"),
                    ("Cashiers", str(summary['active_cashiers']), "👤")
                ]
                
                for title, value, icon in cards:
                    card = self.create_summary_card(title, value, icon)
                    self.monthly_summary_layout.addWidget(card)
            
            # Load profit analysis
            profit_data = self.reports_manager.get_profit_analysis(30)
            if profit_data:
                self.profit_revenue_label.setText(f"{profit_data['total_revenue']:.2f}")
                self.profit_cost_label.setText(f"{profit_data['total_cost']:.2f}")
                self.profit_profit_label.setText(f"{profit_data['total_profit']:.2f}")
                self.profit_margin_label.setText(f"{profit_data['profit_margin']:.1f}%")
            
        except Exception as e:
            print(f"Error loading monthly report: {e}")
    
    def load_products_analytics(self):
        """Load products analytics"""
        try:
            # Top selling products
            top_products = self.reports_manager.get_top_selling_products(10, 30)
            self.top_products_table.setRowCount(len(top_products))
            
            for row, product in enumerate(top_products):
                self.top_products_table.setItem(row, 0, QTableWidgetItem(product['name']))
                self.top_products_table.setItem(row, 1, QTableWidgetItem(product['sku']))
                self.top_products_table.setItem(row, 2, QTableWidgetItem(str(product['total_quantity'])))
                self.top_products_table.setItem(row, 3, QTableWidgetItem(f"{product['total_revenue']:.2f}"))
                self.top_products_table.setItem(row, 4, QTableWidgetItem(str(product['times_sold'])))
            
            # Low stock products
            low_stock = self.reports_manager.get_low_stock_products()
            self.low_stock_table.setRowCount(len(low_stock))
            
            for row, product in enumerate(low_stock):
                self.low_stock_table.setItem(row, 0, QTableWidgetItem(product['name']))
                self.low_stock_table.setItem(row, 1, QTableWidgetItem(product['sku']))
                self.low_stock_table.setItem(row, 2, QTableWidgetItem(str(product['current_stock'])))
                self.low_stock_table.setItem(row, 3, QTableWidgetItem(str(product['min_stock'])))
            
        except Exception as e:
            print(f"Error loading products analytics: {e}")
    
    def load_customers_analytics(self):
        """Load customers analytics"""
        try:
            # Top customers
            top_customers = self.reports_manager.get_customer_analytics(30)
            self.top_customers_table.setRowCount(len(top_customers))
            
            for row, customer in enumerate(top_customers):
                self.top_customers_table.setItem(row, 0, QTableWidgetItem(customer['name']))
                self.top_customers_table.setItem(row, 1, QTableWidgetItem(customer['phone']))
                self.top_customers_table.setItem(row, 2, QTableWidgetItem(str(customer['total_purchases'])))
                self.top_customers_table.setItem(row, 3, QTableWidgetItem(f"{customer['total_spent']:.2f}"))
                self.top_customers_table.setItem(row, 4, QTableWidgetItem(customer['last_purchase']))
            
            # Payment methods
            payment_methods = self.reports_manager.get_payment_method_analytics(30)
            self.payment_table.setRowCount(len(payment_methods))
            
            for row, method in enumerate(payment_methods):
                self.payment_table.setItem(row, 0, QTableWidgetItem(method['method']))
                self.payment_table.setItem(row, 1, QTableWidgetItem(str(method['transaction_count'])))
                self.payment_table.setItem(row, 2, QTableWidgetItem(f"{method['total_amount']:.2f}"))
                self.payment_table.setItem(row, 3, QTableWidgetItem(f"{method['average_amount']:.2f}"))
            
        except Exception as e:
            print(f"Error loading customers analytics: {e}")
