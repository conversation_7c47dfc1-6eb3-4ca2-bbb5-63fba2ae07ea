"""
Customers management widget for OnePos POS System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QDialog, QFormLayout, QDialogButtonBox, QMessageBox,
                            QHeaderView, QAbstractItemView, QGroupBox, QTextEdit,
                            QDoubleSpinBox, QCheckBox, QTabWidget, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from models.customer import Customer
from utils.translator import tr

def create_dark_label(text):
    """Create a dark label with proper styling for high contrast"""
    label = QLabel(text)
    label.setStyleSheet("""
        QLabel {
            color: #ffffff !important;
            font-weight: 700;
            font-size: 13px;
            background-color: transparent;
            padding: 2px;
        }
    """)
    return label


class CustomerDialog(QDialog):
    """Dialog for adding/editing customers"""
    
    def __init__(self, customer=None, parent=None):
        super().__init__(parent)
        self.customer = customer
        self.is_edit_mode = customer is not None

        self.setWindowTitle(tr("customers.edit_customer") if self.is_edit_mode else tr("customers.add_customer"))
        self.setModal(True)
        self.resize(500, 400)

        # Apply dialog styling for better contrast
        self.setStyleSheet("""
            QDialog {
                background-color: #2c3e50;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff !important;
                font-weight: 700;
                font-size: 13px;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                background-color: #ffffff;
                color: #000000;
                border: 2px solid #34495e;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
            QCheckBox {
                color: #ffffff;
                font-weight: 600;
            }
            QPushButton {
                background-color: #3498db;
                color: #ffffff;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        self.setup_ui()

        if self.is_edit_mode:
            self.load_customer_data()
    
    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()
        
        # Form layout
        form_layout = QFormLayout()
        
        # Customer name
        self.name_edit = QLineEdit()
        self.name_edit.setMaxLength(255)
        form_layout.addRow(create_dark_label(tr("customers.name") + ":"), self.name_edit)

        # Phone
        self.phone_edit = QLineEdit()
        self.phone_edit.setMaxLength(20)
        form_layout.addRow(create_dark_label(tr("customers.phone") + ":"), self.phone_edit)

        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setMaxLength(255)
        form_layout.addRow(create_dark_label(tr("customers.email") + ":"), self.email_edit)
        
        # Address
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        form_layout.addRow(create_dark_label(tr("customers.address") + ":"), self.address_edit)

        # Tax number
        self.tax_number_edit = QLineEdit()
        self.tax_number_edit.setMaxLength(50)
        form_layout.addRow(create_dark_label(tr("customers.tax_number") + ":"), self.tax_number_edit)
        
        # Credit limit
        self.credit_limit_spin = QDoubleSpinBox()
        self.credit_limit_spin.setMaximum(999999.99)
        self.credit_limit_spin.setDecimals(2)
        form_layout.addRow(create_dark_label(tr("customers.credit_limit") + ":"), self.credit_limit_spin)
        
        # Current balance (read-only for edit mode)
        if self.is_edit_mode:
            self.balance_spin = QDoubleSpinBox()
            self.balance_spin.setMaximum(999999.99)
            self.balance_spin.setMinimum(-999999.99)
            self.balance_spin.setDecimals(2)
            self.balance_spin.setReadOnly(True)
            form_layout.addRow(create_dark_label(tr("customers.balance") + ":"), self.balance_spin)

        # Active checkbox
        self.active_check = QCheckBox()
        self.active_check.setChecked(True)
        form_layout.addRow(create_dark_label("Active:"), self.active_check)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def load_customer_data(self):
        """Load customer data for editing"""
        if not self.customer:
            return
        
        self.name_edit.setText(self.customer.name or "")
        self.phone_edit.setText(self.customer.phone or "")
        self.email_edit.setText(self.customer.email or "")
        self.address_edit.setPlainText(self.customer.address or "")
        self.tax_number_edit.setText(self.customer.tax_number or "")
        self.credit_limit_spin.setValue(self.customer.credit_limit)
        self.active_check.setChecked(self.customer.is_active)
        
        if hasattr(self, 'balance_spin'):
            self.balance_spin.setValue(self.customer.current_balance)
    
    def get_customer_data(self):
        """Get customer data from form"""
        return {
            'name': self.name_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'address': self.address_edit.toPlainText().strip(),
            'tax_number': self.tax_number_edit.text().strip(),
            'credit_limit': self.credit_limit_spin.value(),
            'is_active': self.active_check.isChecked()
        }
    
    def accept(self):
        """Validate and accept dialog"""
        data = self.get_customer_data()
        
        # Validation
        if not data['name']:
            QMessageBox.warning(self, tr("common.warning"), tr("messages.required_field") + ": " + tr("customers.name"))
            return
        
        super().accept()


class CustomersWidget(QWidget):
    """Customers management widget"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_customers()
    
    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Create splitter for main content
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Customer list
        left_panel = self.create_customer_list_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Customer details
        right_panel = self.create_customer_details_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([600, 400])
        
        layout.addWidget(splitter)
        self.setLayout(layout)
    
    def create_customer_list_panel(self):
        """Create customer list panel"""
        panel = QGroupBox(tr("customers.title"))
        layout = QVBoxLayout()
        
        # Top toolbar
        toolbar_layout = QHBoxLayout()
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(tr("common.search") + " " + tr("customers.name"))
        self.search_edit.textChanged.connect(self.search_customers)
        toolbar_layout.addWidget(self.search_edit)
        
        toolbar_layout.addStretch()
        
        # Action buttons
        self.add_button = QPushButton(tr("customers.add_customer"))
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #198754;
                color: #ffffff;
                font-weight: bold;
                font-size: 11px;
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #157347;
            }
        """)
        self.add_button.clicked.connect(self.add_customer)
        toolbar_layout.addWidget(self.add_button)

        self.edit_button = QPushButton(tr("customers.edit_customer"))
        self.edit_button.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: #ffffff;
                font-weight: bold;
                font-size: 11px;
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
        """)
        self.edit_button.clicked.connect(self.edit_customer)
        toolbar_layout.addWidget(self.edit_button)

        self.delete_button = QPushButton(tr("customers.delete_customer"))
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: #ffffff;
                font-weight: bold;
                font-size: 11px;
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #bb2d3b;
            }
        """)
        self.delete_button.clicked.connect(self.delete_customer)
        toolbar_layout.addWidget(self.delete_button)
        
        layout.addLayout(toolbar_layout)
        
        # Customers table
        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(6)
        self.customers_table.setHorizontalHeaderLabels([
            tr("customers.name"),
            tr("customers.phone"),
            tr("customers.email"),
            tr("customers.balance"),
            tr("customers.credit_limit"),
            "Status"
        ])
        
        # Configure table
        header = self.customers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Name
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Phone
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Email
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Balance
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Credit limit
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Status
        
        self.customers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.customers_table.setAlternatingRowColors(True)
        self.customers_table.setSortingEnabled(True)
        self.customers_table.doubleClicked.connect(self.edit_customer)
        self.customers_table.itemSelectionChanged.connect(self.on_customer_selected)
        
        layout.addWidget(self.customers_table)
        
        # Bottom toolbar
        bottom_toolbar = QHBoxLayout()
        
        self.refresh_button = QPushButton(tr("common.refresh"))
        self.refresh_button.clicked.connect(self.load_customers)
        bottom_toolbar.addWidget(self.refresh_button)
        
        bottom_toolbar.addStretch()
        
        # Customer count label
        self.count_label = QLabel()
        bottom_toolbar.addWidget(self.count_label)
        
        layout.addLayout(bottom_toolbar)
        
        panel.setLayout(layout)
        return panel
    
    def create_customer_details_panel(self):
        """Create customer details panel"""
        panel = QGroupBox("Customer Details")
        layout = QVBoxLayout()
        
        # Customer info
        self.customer_info_label = QLabel("Select a customer to view details")
        self.customer_info_label.setAlignment(Qt.AlignTop)
        self.customer_info_label.setWordWrap(True)
        self.customer_info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.customer_info_label)
        
        # Tabs for additional info
        self.details_tabs = QTabWidget()
        
        # Purchase history tab
        self.purchase_history_table = QTableWidget()
        self.purchase_history_table.setColumnCount(5)
        self.purchase_history_table.setHorizontalHeaderLabels([
            "Invoice", "Date", "Amount", "Payment", "Status"
        ])
        self.details_tabs.addTab(self.purchase_history_table, tr("customers.purchase_history"))
        
        # Account statement tab
        self.account_statement_table = QTableWidget()
        self.account_statement_table.setColumnCount(4)
        self.account_statement_table.setHorizontalHeaderLabels([
            "Date", "Description", "Debit", "Credit"
        ])
        self.details_tabs.addTab(self.account_statement_table, "Account Statement")
        
        layout.addWidget(self.details_tabs)
        
        panel.setLayout(layout)
        return panel
    
    def load_customers(self):
        """Load customers into table"""
        customers = Customer.get_all()
        self.populate_table(customers)
    
    def populate_table(self, customers):
        """Populate table with customers"""
        self.customers_table.setRowCount(len(customers))
        
        for row, customer in enumerate(customers):
            # Name
            self.customers_table.setItem(row, 0, QTableWidgetItem(customer.name))
            
            # Phone
            self.customers_table.setItem(row, 1, QTableWidgetItem(customer.phone or ""))
            
            # Email
            self.customers_table.setItem(row, 2, QTableWidgetItem(customer.email or ""))
            
            # Balance
            balance_item = QTableWidgetItem(f"{customer.current_balance:.2f}")
            if customer.current_balance < 0:
                balance_item.setBackground(Qt.red)
            elif customer.current_balance > 0:
                balance_item.setBackground(Qt.green)
            self.customers_table.setItem(row, 3, balance_item)
            
            # Credit limit
            self.customers_table.setItem(row, 4, QTableWidgetItem(f"{customer.credit_limit:.2f}"))
            
            # Status
            status = "Active" if customer.is_active else "Inactive"
            self.customers_table.setItem(row, 5, QTableWidgetItem(status))
            
            # Store customer ID in first column
            self.customers_table.item(row, 0).setData(Qt.UserRole, customer.id)
        
        # Update count
        self.count_label.setText(f"Total: {len(customers)} customers")
    
    def search_customers(self):
        """Search customers"""
        query = self.search_edit.text().strip()
        if query:
            customers = Customer.search(query)
        else:
            customers = Customer.get_all()
        
        self.populate_table(customers)
    
    def get_selected_customer(self):
        """Get currently selected customer"""
        current_row = self.customers_table.currentRow()
        if current_row >= 0:
            customer_id = self.customers_table.item(current_row, 0).data(Qt.UserRole)
            return Customer.get_by_id(customer_id)
        return None
    
    def on_customer_selected(self):
        """Handle customer selection"""
        customer = self.get_selected_customer()
        if customer:
            self.show_customer_details(customer)
        else:
            self.clear_customer_details()
    
    def show_customer_details(self, customer):
        """Show customer details"""
        # Update info label
        info_text = f"""
        <h3>{customer.name}</h3>
        <p><b>Phone:</b> {customer.phone or 'N/A'}</p>
        <p><b>Email:</b> {customer.email or 'N/A'}</p>
        <p><b>Address:</b> {customer.address or 'N/A'}</p>
        <p><b>Tax Number:</b> {customer.tax_number or 'N/A'}</p>
        <p><b>Current Balance:</b> {customer.current_balance:.2f}</p>
        <p><b>Credit Limit:</b> {customer.credit_limit:.2f}</p>
        <p><b>Total Purchases:</b> {customer.get_total_purchases():.2f}</p>
        <p><b>Purchase Count:</b> {customer.get_purchase_count()}</p>
        """
        self.customer_info_label.setText(info_text)
        
        # Load purchase history
        self.load_purchase_history(customer)
    
    def clear_customer_details(self):
        """Clear customer details"""
        self.customer_info_label.setText("Select a customer to view details")
        self.purchase_history_table.setRowCount(0)
        self.account_statement_table.setRowCount(0)
    
    def load_purchase_history(self, customer):
        """Load customer purchase history"""
        try:
            history = customer.get_purchase_history(limit=50)
            self.purchase_history_table.setRowCount(len(history))
            
            for row, sale in enumerate(history):
                self.purchase_history_table.setItem(row, 0, QTableWidgetItem(sale['invoice_number']))
                self.purchase_history_table.setItem(row, 1, QTableWidgetItem(sale['created_at']))
                self.purchase_history_table.setItem(row, 2, QTableWidgetItem(f"{sale['total_amount']:.2f}"))
                self.purchase_history_table.setItem(row, 3, QTableWidgetItem(sale['payment_method']))
                self.purchase_history_table.setItem(row, 4, QTableWidgetItem(sale['status']))
        
        except Exception as e:
            print(f"Error loading purchase history: {e}")
    
    def add_customer(self):
        """Add new customer"""
        dialog = CustomerDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                data = dialog.get_customer_data()
                customer = Customer.create(**data)
                
                if customer:
                    QMessageBox.information(self, tr("common.success"), tr("messages.item_saved"))
                    self.load_customers()
                
            except Exception as e:
                QMessageBox.critical(self, tr("common.error"), f"Error creating customer: {str(e)}")
    
    def edit_customer(self):
        """Edit selected customer"""
        customer = self.get_selected_customer()
        if not customer:
            QMessageBox.warning(self, tr("common.warning"), tr("messages.please_select_item"))
            return
        
        dialog = CustomerDialog(customer, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                data = dialog.get_customer_data()
                customer.update(**data)
                
                QMessageBox.information(self, tr("common.success"), tr("messages.item_saved"))
                self.load_customers()
                self.show_customer_details(customer)  # Refresh details
                
            except Exception as e:
                QMessageBox.critical(self, tr("common.error"), f"Error updating customer: {str(e)}")
    
    def delete_customer(self):
        """Delete selected customer"""
        customer = self.get_selected_customer()
        if not customer:
            QMessageBox.warning(self, tr("common.warning"), tr("messages.please_select_item"))
            return
        
        # Check if customer has purchases
        if customer.get_purchase_count() > 0:
            QMessageBox.warning(self, tr("common.warning"), 
                              "Cannot delete customer with purchase history. Customer will be deactivated instead.")
            customer.update(is_active=False)
            self.load_customers()
            return
        
        reply = QMessageBox.question(self, tr("common.delete"), 
                                   tr("messages.confirm_delete"),
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                customer.delete()
                QMessageBox.information(self, tr("common.success"), tr("messages.item_deleted"))
                self.load_customers()
                self.clear_customer_details()
                
            except Exception as e:
                QMessageBox.critical(self, tr("common.error"), f"Error deleting customer: {str(e)}")
