"""
Test RTL Layout Switching for OnePos POS System
Tests the dynamic layout switching based on language direction
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QHBoxLayout
from PyQt5.QtCore import Qt
from utils.translator import change_language, get_current_language
from views.main_window import MainWindow
from models.user import User
import time

def test_layout_arrangement():
    """Test layout arrangement for different languages"""
    print("🔄 Testing Layout Arrangement...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        print(f"✅ Main window created and shown")
        
        # Test layout for different languages
        languages_to_test = [
            ('ar', 'Arabic RTL'),
            ('en', 'English LTR'),
            ('fr', 'French LTR')
        ]
        
        for lang_code, description in languages_to_test:
            print(f"\n   Testing {description}...")
            
            # Change language
            change_language(lang_code)
            app.processEvents()
            time.sleep(0.1)  # Give time for layout changes
            
            # Check layout direction
            layout_direction = main_window.layoutDirection()
            expected_direction = Qt.RightToLeft if lang_code == 'ar' else Qt.LeftToRight
            
            if layout_direction == expected_direction:
                print(f"   ✅ Layout direction correct: {layout_direction}")
            else:
                print(f"   ❌ Layout direction incorrect: expected {expected_direction}, got {layout_direction}")
                return False
            
            # Check component arrangement
            main_layout = main_window.main_layout
            if main_layout.count() >= 2:
                first_widget = main_layout.itemAt(0).widget()
                second_widget = main_layout.itemAt(1).widget()
                
                # Check if sidebar and content are in correct positions
                sidebar_frame = main_window.sidebar_frame
                content_frame = main_window.content_frame
                
                if lang_code == 'ar':
                    # Arabic: Content first, then sidebar
                    if first_widget == content_frame and second_widget == sidebar_frame:
                        print(f"   ✅ Arabic layout: Content left, Sidebar right")
                    else:
                        print(f"   ❌ Arabic layout incorrect")
                        return False
                else:
                    # English/French: Sidebar first, then content
                    if first_widget == sidebar_frame and second_widget == content_frame:
                        print(f"   ✅ {description} layout: Sidebar left, Content right")
                    else:
                        print(f"   ❌ {description} layout incorrect")
                        return False
            else:
                print(f"   ❌ Layout doesn't have enough components")
                return False
            
            # Check sidebar styles
            sidebar_style = sidebar_frame.styleSheet()
            if lang_code == 'ar':
                if 'border-left' in sidebar_style and 'border-right: none' in sidebar_style:
                    print(f"   ✅ Arabic sidebar style: Border on left")
                else:
                    print(f"   ⚠️ Arabic sidebar style may not be optimal")
            else:
                if 'border-right' in sidebar_style and 'border-left: none' in sidebar_style:
                    print(f"   ✅ {description} sidebar style: Border on right")
                else:
                    print(f"   ⚠️ {description} sidebar style may not be optimal")
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Layout arrangement test failed: {e}")
        return False

def test_dynamic_layout_switching():
    """Test dynamic layout switching during runtime"""
    print("\n🔄 Testing Dynamic Layout Switching...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Start with English
        change_language('en')
        app.processEvents()
        time.sleep(0.1)
        
        # Record initial layout
        initial_layout = []
        for i in range(main_window.main_layout.count()):
            widget = main_window.main_layout.itemAt(i).widget()
            if widget == main_window.sidebar_frame:
                initial_layout.append('sidebar')
            elif widget == main_window.content_frame:
                initial_layout.append('content')
        
        print(f"   English layout: {' → '.join(initial_layout)}")
        
        # Switch to Arabic
        change_language('ar')
        app.processEvents()
        time.sleep(0.1)
        
        # Record Arabic layout
        arabic_layout = []
        for i in range(main_window.main_layout.count()):
            widget = main_window.main_layout.itemAt(i).widget()
            if widget == main_window.sidebar_frame:
                arabic_layout.append('sidebar')
            elif widget == main_window.content_frame:
                arabic_layout.append('content')
        
        print(f"   Arabic layout: {' → '.join(arabic_layout)}")
        
        # Check if layouts are different and correct
        expected_english = ['sidebar', 'content']
        expected_arabic = ['content', 'sidebar']
        
        if initial_layout == expected_english and arabic_layout == expected_arabic:
            print("   ✅ Dynamic layout switching works correctly")
        else:
            print(f"   ❌ Layout switching failed:")
            print(f"     Expected English: {expected_english}, Got: {initial_layout}")
            print(f"     Expected Arabic: {expected_arabic}, Got: {arabic_layout}")
            return False
        
        # Switch back to French
        change_language('fr')
        app.processEvents()
        time.sleep(0.1)
        
        # Record French layout
        french_layout = []
        for i in range(main_window.main_layout.count()):
            widget = main_window.main_layout.itemAt(i).widget()
            if widget == main_window.sidebar_frame:
                french_layout.append('sidebar')
            elif widget == main_window.content_frame:
                french_layout.append('content')
        
        print(f"   French layout: {' → '.join(french_layout)}")
        
        if french_layout == expected_english:
            print("   ✅ French layout matches English (LTR)")
        else:
            print(f"   ❌ French layout incorrect: {french_layout}")
            return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Dynamic layout switching test failed: {e}")
        return False

def test_rapid_layout_switching():
    """Test rapid layout switching for stability"""
    print("\n⚡ Testing Rapid Layout Switching...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Test rapid switching
        languages = ['ar', 'en', 'fr']
        
        print("   Performing rapid layout switches...")
        start_time = time.time()
        
        for i in range(15):  # 15 rapid switches
            lang = languages[i % len(languages)]
            change_language(lang)
            app.processEvents()
            
            # Verify layout is correct
            main_layout = main_window.main_layout
            if main_layout.count() >= 2:
                first_widget = main_layout.itemAt(0).widget()
                sidebar_frame = main_window.sidebar_frame
                content_frame = main_window.content_frame
                
                if lang == 'ar':
                    # Arabic: Content should be first
                    if first_widget != content_frame:
                        print(f"   ❌ Rapid switch {i+1} failed: Arabic layout incorrect")
                        return False
                else:
                    # English/French: Sidebar should be first
                    if first_widget != sidebar_frame:
                        print(f"   ❌ Rapid switch {i+1} failed: {lang} layout incorrect")
                        return False
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"   ✅ 15 rapid layout switches completed in {total_time:.4f} seconds")
        print(f"   ✅ Average time per switch: {total_time/15:.4f} seconds")
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Rapid layout switching test failed: {e}")
        return False

def test_layout_persistence():
    """Test layout persistence across widget operations"""
    print("\n🔄 Testing Layout Persistence...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Set to Arabic
        change_language('ar')
        app.processEvents()
        time.sleep(0.1)
        
        # Navigate to different modules
        modules_to_test = ['pos', 'products', 'customers', 'settings']
        
        for module in modules_to_test:
            if module in main_window.nav_buttons:
                print(f"   Testing layout persistence in {module} module...")
                
                # Navigate to module
                main_window.nav_buttons[module].click()
                app.processEvents()
                time.sleep(0.1)
                
                # Check if layout is still correct
                main_layout = main_window.main_layout
                if main_layout.count() >= 2:
                    first_widget = main_layout.itemAt(0).widget()
                    content_frame = main_window.content_frame
                    
                    if first_widget == content_frame:
                        print(f"     ✅ Arabic layout maintained in {module}")
                    else:
                        print(f"     ❌ Arabic layout lost in {module}")
                        return False
        
        # Switch to English and test again
        change_language('en')
        app.processEvents()
        time.sleep(0.1)
        
        for module in modules_to_test[:2]:  # Test fewer modules for English
            if module in main_window.nav_buttons:
                print(f"   Testing English layout persistence in {module} module...")
                
                # Navigate to module
                main_window.nav_buttons[module].click()
                app.processEvents()
                time.sleep(0.1)
                
                # Check if layout is correct
                main_layout = main_window.main_layout
                if main_layout.count() >= 2:
                    first_widget = main_layout.itemAt(0).widget()
                    sidebar_frame = main_window.sidebar_frame
                    
                    if first_widget == sidebar_frame:
                        print(f"     ✅ English layout maintained in {module}")
                    else:
                        print(f"     ❌ English layout lost in {module}")
                        return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Layout persistence test failed: {e}")
        return False

def run_rtl_layout_test():
    """Run all RTL layout tests"""
    print("🔄 STARTING RTL LAYOUT SYSTEM TEST")
    print("=" * 60)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # Test 1: Layout Arrangement
    test_results.append(("Layout Arrangement", test_layout_arrangement()))
    
    # Test 2: Dynamic Layout Switching
    test_results.append(("Dynamic Layout Switching", test_dynamic_layout_switching()))
    
    # Test 3: Rapid Layout Switching
    test_results.append(("Rapid Layout Switching", test_rapid_layout_switching()))
    
    # Test 4: Layout Persistence
    test_results.append(("Layout Persistence", test_layout_persistence()))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 RTL LAYOUT TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL RTL LAYOUT TESTS PASSED! Layout system is working perfectly!")
    else:
        print("⚠️  Some RTL layout tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_rtl_layout_test()
