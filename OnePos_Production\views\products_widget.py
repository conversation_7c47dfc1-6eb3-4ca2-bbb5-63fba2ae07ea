"""
Products management widget for OnePos POS System
"""

from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QComboBox, QSpinBox, QDoubleSpinBox, QTextEdit,
                            QDialog, QFormLayout, QDialogButtonBox, QMessageBox,
                            QHeaderView, QAbstractItemView, QGroupBox, QFileDialog,
                            QCheckBox, QFrame, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap

from models.product import Product, Category
from utils.translator import tr
from utils.lazy_loader import LazyTableWidget, SearchableDataManager, performance_optimizer

def create_dark_label(text):
    """Create a dark label with proper styling for high contrast"""
    label = QLabel(text)
    label.setStyleSheet("""
        QLabel {
            color: #ffffff !important;
            font-weight: 700;
            font-size: 13px;
            background-color: transparent;
            padding: 2px;
        }
    """)
    return label

# Optional barcode utilities
try:
    from utils.barcode_generator import BarcodeGenerator, BarcodePrintDialog
    BARCODE_AVAILABLE = True
except (ImportError, OSError, FileNotFoundError) as e:
    print(f"Barcode utilities not available: {e}")
    BARCODE_AVAILABLE = False

    # Create dummy functions
    class BarcodeGenerator:
        @staticmethod
        def generate_barcode(*args, **kwargs):
            return None

    class BarcodePrintDialog:
        def __init__(self, *args, **kwargs):
            pass


class ProductDialog(QDialog):
    """Dialog for adding/editing products"""
    
    def __init__(self, product=None, parent=None):
        super().__init__(parent)
        self.product = product
        self.is_edit_mode = product is not None

        self.setWindowTitle(tr("products.edit_product") if self.is_edit_mode else tr("products.add_product"))
        self.setModal(True)
        self.resize(500, 600)

        # Apply dialog styling for better contrast
        self.setStyleSheet("""
            QDialog {
                background-color: #2c3e50;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff !important;
                font-weight: 700;
                font-size: 13px;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                background-color: #ffffff;
                color: #000000;
                border: 2px solid #34495e;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
            QCheckBox {
                color: #ffffff;
                font-weight: 600;
            }
            QPushButton {
                background-color: #3498db;
                color: #ffffff;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        self.setup_ui()

        if self.is_edit_mode:
            self.load_product_data()
    
    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()
        
        # Form layout
        form_layout = QFormLayout()
        
        # Product name
        self.name_edit = QLineEdit()
        self.name_edit.setMaxLength(255)
        form_layout.addRow(create_dark_label(tr("products.name") + ":"), self.name_edit)

        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        form_layout.addRow(create_dark_label(tr("products.description") + ":"), self.description_edit)

        # Category
        self.category_combo = QComboBox()
        self.load_categories()
        form_layout.addRow(create_dark_label(tr("products.category") + ":"), self.category_combo)
        
        # Barcode
        barcode_layout = QHBoxLayout()
        self.barcode_edit = QLineEdit()
        barcode_layout.addWidget(self.barcode_edit)
        
        self.generate_barcode_button = QPushButton(tr("products.generate_barcode"))
        self.generate_barcode_button.clicked.connect(self.generate_barcode)
        barcode_layout.addWidget(self.generate_barcode_button)
        
        barcode_widget = QWidget()
        barcode_widget.setLayout(barcode_layout)
        form_layout.addRow(create_dark_label(tr("products.barcode") + ":"), barcode_widget)

        # SKU
        self.sku_edit = QLineEdit()
        form_layout.addRow(create_dark_label(tr("products.sku") + ":"), self.sku_edit)

        # Cost price
        self.cost_price_spin = QDoubleSpinBox()
        self.cost_price_spin.setMaximum(999999.99)
        self.cost_price_spin.setDecimals(2)
        form_layout.addRow(create_dark_label(tr("products.cost_price") + ":"), self.cost_price_spin)
        
        # Selling price
        self.selling_price_spin = QDoubleSpinBox()
        self.selling_price_spin.setMaximum(999999.99)
        self.selling_price_spin.setDecimals(2)
        form_layout.addRow(create_dark_label(tr("products.selling_price") + ":"), self.selling_price_spin)

        # Stock quantity
        self.stock_spin = QSpinBox()
        self.stock_spin.setMaximum(999999)
        form_layout.addRow(create_dark_label(tr("products.stock") + ":"), self.stock_spin)

        # Minimum stock level
        self.min_stock_spin = QSpinBox()
        self.min_stock_spin.setMaximum(999999)
        form_layout.addRow(create_dark_label(tr("products.min_stock") + ":"), self.min_stock_spin)
        
        # Unit
        self.unit_edit = QLineEdit()
        self.unit_edit.setText("piece")
        form_layout.addRow(create_dark_label(tr("products.unit") + ":"), self.unit_edit)
        
        # Image path
        image_layout = QHBoxLayout()
        self.image_edit = QLineEdit()
        image_layout.addWidget(self.image_edit)
        
        self.browse_image_button = QPushButton(tr("common.search"))
        self.browse_image_button.clicked.connect(self.browse_image)
        image_layout.addWidget(self.browse_image_button)
        
        image_widget = QWidget()
        image_widget.setLayout(image_layout)
        form_layout.addRow(create_dark_label(tr("products.image") + ":"), image_widget)
        
        # Active checkbox
        self.active_check = QCheckBox()
        self.active_check.setChecked(True)
        form_layout.addRow(create_dark_label("Active:"), self.active_check)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def load_categories(self):
        """Load categories into combo box"""
        self.category_combo.clear()
        self.category_combo.addItem("-- Select Category --", None)
        
        categories = Category.get_all()
        for category in categories:
            self.category_combo.addItem(category.name, category.id)
    
    def generate_barcode(self):
        """Generate a new barcode"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, tr("common.warning"), "Please enter product name first")
            return

        # Generate barcode based on product name
        import random
        import string
        barcode = ''.join(random.choices(string.digits, k=13))
        self.barcode_edit.setText(barcode)
    
    def browse_image(self):
        """Browse for product image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Product Image", "",
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if file_path:
            self.image_edit.setText(file_path)
    
    def load_product_data(self):
        """Load product data for editing"""
        if not self.product:
            return
        
        self.name_edit.setText(self.product.name or "")
        self.description_edit.setPlainText(self.product.description or "")
        self.barcode_edit.setText(self.product.barcode or "")
        self.sku_edit.setText(self.product.sku or "")
        self.cost_price_spin.setValue(self.product.cost_price)
        self.selling_price_spin.setValue(self.product.selling_price)
        self.stock_spin.setValue(self.product.stock_quantity)
        self.min_stock_spin.setValue(self.product.min_stock_level)
        self.unit_edit.setText(self.product.unit or "piece")
        self.image_edit.setText(self.product.image_path or "")
        self.active_check.setChecked(self.product.is_active)
        
        # Set category
        if self.product.category_id:
            for i in range(self.category_combo.count()):
                if self.category_combo.itemData(i) == self.product.category_id:
                    self.category_combo.setCurrentIndex(i)
                    break
    
    def get_product_data(self):
        """Get product data from form"""
        return {
            'name': self.name_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'category_id': self.category_combo.currentData(),
            'barcode': self.barcode_edit.text().strip(),
            'sku': self.sku_edit.text().strip(),
            'cost_price': self.cost_price_spin.value(),
            'selling_price': self.selling_price_spin.value(),
            'stock_quantity': self.stock_spin.value(),
            'min_stock_level': self.min_stock_spin.value(),
            'unit': self.unit_edit.text().strip(),
            'image_path': self.image_edit.text().strip(),
            'is_active': self.active_check.isChecked()
        }
    
    def accept(self):
        """Validate and accept dialog"""
        data = self.get_product_data()
        
        # Validation
        if not data['name']:
            QMessageBox.warning(self, tr("common.warning"), tr("messages.required_field") + ": " + tr("products.name"))
            return
        
        if data['selling_price'] <= 0:
            QMessageBox.warning(self, tr("common.warning"), "Selling price must be greater than 0")
            return
        
        super().accept()


class ProductsWidget(QWidget):
    """Products management widget"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_products()
    
    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Top toolbar
        toolbar_layout = QHBoxLayout()
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(tr("common.search") + " " + tr("products.name"))
        self.search_edit.textChanged.connect(self.search_products)
        toolbar_layout.addWidget(self.search_edit)
        
        # Category filter
        self.category_filter = QComboBox()
        self.category_filter.addItem("All Categories", None)
        self.load_category_filter()
        self.category_filter.currentTextChanged.connect(self.filter_by_category)
        toolbar_layout.addWidget(self.category_filter)
        
        # Stock filter
        self.stock_filter = QComboBox()
        self.stock_filter.addItems(["All Products", tr("products.low_stock"), tr("products.out_of_stock")])
        self.stock_filter.currentTextChanged.connect(self.filter_by_stock)
        toolbar_layout.addWidget(self.stock_filter)
        
        toolbar_layout.addStretch()
        
        # Action buttons
        self.add_button = QPushButton(tr("products.add_product"))
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #198754;
                color: #ffffff;
                font-weight: bold;
                font-size: 11px;
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #157347;
            }
        """)
        self.add_button.clicked.connect(self.add_product)
        toolbar_layout.addWidget(self.add_button)

        self.edit_button = QPushButton(tr("products.edit_product"))
        self.edit_button.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: #ffffff;
                font-weight: bold;
                font-size: 11px;
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
        """)
        self.edit_button.clicked.connect(self.edit_product)
        toolbar_layout.addWidget(self.edit_button)

        self.delete_button = QPushButton(tr("products.delete_product"))
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: #ffffff;
                font-weight: bold;
                font-size: 11px;
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #bb2d3b;
            }
        """)
        self.delete_button.clicked.connect(self.delete_product)
        toolbar_layout.addWidget(self.delete_button)
        
        layout.addLayout(toolbar_layout)
        
        # Products table
        self.products_table = LazyTableWidget()
        self.products_table.setColumnCount(10)
        self.products_table.setHorizontalHeaderLabels([
            tr("products.name"),
            tr("products.category"),
            tr("products.barcode"),
            tr("products.sku"),
            tr("products.cost_price"),
            tr("products.selling_price"),
            tr("products.stock"),
            tr("products.min_stock"),
            tr("products.unit"),
            "Status"
        ])
        
        # Configure table
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Name
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Category
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Barcode
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # SKU
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Cost
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Price
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Stock
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Min stock
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # Unit
        header.setSectionResizeMode(9, QHeaderView.ResizeToContents)  # Status
        
        self.products_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.products_table.setAlternatingRowColors(True)
        self.products_table.setSortingEnabled(True)
        self.products_table.doubleClicked.connect(self.edit_product)

        # Add context menu
        self.products_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.products_table.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addWidget(self.products_table)

        # Set up data source and search manager
        self.products_table.set_data_source(self.get_products_data, page_size=50)
        self.products_table.populate_row = self.populate_product_row
        self.search_manager = SearchableDataManager(self.products_table)

        # Apply performance optimizations
        performance_optimizer.optimize_table(self.products_table)

        # Bottom toolbar
        bottom_toolbar = QHBoxLayout()
        
        self.refresh_button = QPushButton(tr("common.refresh"))
        self.refresh_button.clicked.connect(self.load_products)
        bottom_toolbar.addWidget(self.refresh_button)
        
        self.print_labels_button = QPushButton(tr("products.print_label"))
        self.print_labels_button.clicked.connect(self.print_labels)
        bottom_toolbar.addWidget(self.print_labels_button)
        
        self.export_button = QPushButton(tr("common.export"))
        self.export_button.clicked.connect(self.export_products)
        bottom_toolbar.addWidget(self.export_button)
        
        self.import_button = QPushButton(tr("common.import"))
        self.import_button.clicked.connect(self.import_products)
        bottom_toolbar.addWidget(self.import_button)
        
        bottom_toolbar.addStretch()
        
        # Product count label
        self.count_label = QLabel()
        self.count_label.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 11px;")
        bottom_toolbar.addWidget(self.count_label)
        
        layout.addLayout(bottom_toolbar)
        
        self.setLayout(layout)
    
    def load_category_filter(self):
        """Load categories for filter"""
        categories = Category.get_all()
        for category in categories:
            self.category_filter.addItem(category.name, category.id)
    
    def load_products(self):
        """Load products into table using lazy loading"""
        self.products_table.load_data()

        # Enable sorting after data is loaded
        performance_optimizer.enable_sorting(self.products_table)

    def get_products_data(self, search_query="", offset=0, limit=50):
        """Get products data for lazy loading"""
        if search_query:
            # Search products
            products = Product.search(search_query)
        else:
            # Get all products
            products = Product.get_all()

        # Convert to dictionaries for lazy loading
        products_data = []
        for product in products[offset:offset + limit]:
            category = product.get_category()
            products_data.append({
                'id': product.id,
                'name': product.name,
                'category': category.name if category else "",
                'barcode': product.barcode or "",
                'sku': product.sku or "",
                'cost_price': product.cost_price,
                'selling_price': product.selling_price,
                'stock_quantity': product.stock_quantity,
                'min_stock_level': product.min_stock_level,
                'unit': product.unit,
                'is_active': product.is_active,
                'is_low_stock': product.is_low_stock(),
                'product_obj': product  # Store for later use
            })

        return products_data

    def populate_product_row(self, row, product_data):
        """Populate a single product row"""
        # Name
        self.products_table.setItem(row, 0, QTableWidgetItem(product_data['name']))

        # Category
        self.products_table.setItem(row, 1, QTableWidgetItem(product_data['category']))

        # Barcode
        self.products_table.setItem(row, 2, QTableWidgetItem(product_data['barcode']))

        # SKU
        self.products_table.setItem(row, 3, QTableWidgetItem(product_data['sku']))

        # Cost price
        self.products_table.setItem(row, 4, QTableWidgetItem(f"{product_data['cost_price']:.2f}"))

        # Selling price
        self.products_table.setItem(row, 5, QTableWidgetItem(f"{product_data['selling_price']:.2f}"))

        # Stock with color coding
        stock_item = QTableWidgetItem(str(product_data['stock_quantity']))
        if product_data['is_low_stock']:
            stock_item.setBackground(Qt.yellow)
        if product_data['stock_quantity'] <= 0:
            stock_item.setBackground(Qt.red)
        self.products_table.setItem(row, 6, stock_item)

        # Min stock
        self.products_table.setItem(row, 7, QTableWidgetItem(str(product_data['min_stock_level'])))

        # Unit
        self.products_table.setItem(row, 8, QTableWidgetItem(product_data['unit']))

        # Status
        status = "Active" if product_data['is_active'] else "Inactive"
        self.products_table.setItem(row, 9, QTableWidgetItem(status))

        # Store product ID in first column
        self.products_table.item(row, 0).setData(Qt.UserRole, product_data['id'])
    
    def populate_table(self, products):
        """Populate table with products"""
        self.products_table.setRowCount(len(products))
        
        for row, product in enumerate(products):
            # Name
            self.products_table.setItem(row, 0, QTableWidgetItem(product.name))
            
            # Category
            category = product.get_category()
            category_name = category.name if category else ""
            self.products_table.setItem(row, 1, QTableWidgetItem(category_name))
            
            # Barcode
            self.products_table.setItem(row, 2, QTableWidgetItem(product.barcode or ""))
            
            # SKU
            self.products_table.setItem(row, 3, QTableWidgetItem(product.sku or ""))
            
            # Cost price
            self.products_table.setItem(row, 4, QTableWidgetItem(f"{product.cost_price:.2f}"))
            
            # Selling price
            self.products_table.setItem(row, 5, QTableWidgetItem(f"{product.selling_price:.2f}"))
            
            # Stock
            stock_item = QTableWidgetItem(str(product.stock_quantity))
            if product.is_low_stock():
                stock_item.setBackground(Qt.yellow)
            if product.stock_quantity <= 0:
                stock_item.setBackground(Qt.red)
            self.products_table.setItem(row, 6, stock_item)
            
            # Min stock
            self.products_table.setItem(row, 7, QTableWidgetItem(str(product.min_stock_level)))
            
            # Unit
            self.products_table.setItem(row, 8, QTableWidgetItem(product.unit))
            
            # Status
            status = "Active" if product.is_active else "Inactive"
            self.products_table.setItem(row, 9, QTableWidgetItem(status))
            
            # Store product ID in first column
            self.products_table.item(row, 0).setData(Qt.UserRole, product.id)
        
        # Update count
        self.count_label.setText(f"Total: {len(products)} products")
    
    def search_products(self):
        """Search products using lazy loading"""
        query = self.search_edit.text().strip()
        self.search_manager.search(query)
    
    def filter_by_category(self):
        """Filter products by category"""
        category_id = self.category_filter.currentData()
        if category_id:
            products = Product.get_all(category_id=category_id)
        else:
            products = Product.get_all()
        
        self.populate_table(products)
    
    def filter_by_stock(self):
        """Filter products by stock level"""
        filter_type = self.stock_filter.currentText()
        
        if filter_type == tr("products.low_stock"):
            products = Product.get_low_stock()
        elif filter_type == tr("products.out_of_stock"):
            products = [p for p in Product.get_all() if p.stock_quantity <= 0]
        else:
            products = Product.get_all()
        
        self.populate_table(products)
    
    def get_selected_product(self):
        """Get currently selected product"""
        current_row = self.products_table.currentRow()
        if current_row >= 0:
            product_id = self.products_table.item(current_row, 0).data(Qt.UserRole)
            return Product.get_by_id(product_id)
        return None
    
    def add_product(self):
        """Add new product"""
        dialog = ProductDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                data = dialog.get_product_data()
                product = Product.create(**data)
                
                if product:
                    QMessageBox.information(self, tr("common.success"), tr("messages.item_saved"))
                    self.load_products()
                
            except Exception as e:
                QMessageBox.critical(self, tr("common.error"), f"Error creating product: {str(e)}")
    
    def edit_product(self):
        """Edit selected product"""
        product = self.get_selected_product()
        if not product:
            QMessageBox.warning(self, tr("common.warning"), tr("messages.please_select_item"))
            return
        
        dialog = ProductDialog(product, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                data = dialog.get_product_data()
                product.update(**data)
                
                QMessageBox.information(self, tr("common.success"), tr("messages.item_saved"))
                self.load_products()
                
            except Exception as e:
                QMessageBox.critical(self, tr("common.error"), f"Error updating product: {str(e)}")
    
    def delete_product(self):
        """Delete selected product"""
        product = self.get_selected_product()
        if not product:
            QMessageBox.warning(self, tr("common.warning"), tr("messages.please_select_item"))
            return
        
        reply = QMessageBox.question(self, tr("common.delete"), 
                                   tr("messages.confirm_delete"),
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                product.delete()
                QMessageBox.information(self, tr("common.success"), tr("messages.item_deleted"))
                self.load_products()
                
            except Exception as e:
                QMessageBox.critical(self, tr("common.error"), f"Error deleting product: {str(e)}")
    
    def print_labels(self):
        """Print barcode labels for selected products"""
        selected_product = self.get_selected_product()
        if not selected_product:
            QMessageBox.warning(self, tr("common.warning"), tr("messages.please_select_item"))
            return

        if not BARCODE_AVAILABLE:
            QMessageBox.warning(self, tr("common.warning"),
                              "Barcode printing not available. Please install python-barcode library:\n"
                              "pip install python-barcode[images] pillow")
            return

        # Check if product has barcode, if not generate one
        if not selected_product.barcode:
            reply = QMessageBox.question(self, "Generate Barcode",
                                       f"Product '{selected_product.name}' does not have a barcode.\n"
                                       "Would you like to generate one automatically?",
                                       QMessageBox.Yes | QMessageBox.No)

            if reply == QMessageBox.Yes:
                # Generate barcode based on product ID or SKU
                import time
                if selected_product.sku:
                    new_barcode = selected_product.sku
                else:
                    # Generate barcode from product ID and timestamp
                    new_barcode = f"PRD{selected_product.id:06d}{int(time.time()) % 10000:04d}"

                # Update product with new barcode
                try:
                    selected_product.barcode = new_barcode
                    selected_product.save()
                    self.load_products()  # Refresh the table
                    QMessageBox.information(self, "Success", f"Generated barcode: {new_barcode}")
                except Exception as e:
                    QMessageBox.critical(self, "Error", f"Failed to save barcode: {str(e)}")
                    return
            else:
                return

        try:
            # Open barcode print dialog
            dialog = BarcodePrintDialog(selected_product, self)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"Error opening barcode print dialog:\n{str(e)}")

    def show_context_menu(self, position):
        """Show context menu for products table"""
        if self.products_table.itemAt(position) is None:
            return

        from PyQt5.QtWidgets import QMenu, QAction

        menu = QMenu(self)

        # Edit action
        edit_action = QAction("Edit Product", self)
        edit_action.triggered.connect(self.edit_product)
        menu.addAction(edit_action)

        # Print barcode action
        print_barcode_action = QAction("🏷️ Print Barcode", self)
        print_barcode_action.triggered.connect(self.print_labels)
        menu.addAction(print_barcode_action)

        # Delete action
        delete_action = QAction("Delete Product", self)
        delete_action.triggered.connect(self.delete_product)
        menu.addAction(delete_action)

        # Show menu
        menu.exec_(self.products_table.mapToGlobal(position))

    def export_products(self):
        """Export products to Excel"""
        QMessageBox.information(self, tr("common.export"), 
                              "Export functionality will be implemented")
    
    def import_products(self):
        """Import products from Excel"""
        QMessageBox.information(self, tr("common.import"), 
                              "Import functionality will be implemented")
