#!/usr/bin/env python3
"""
اختبار نظام الترخيص
Test License System
"""

import sys
import os

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.license_manager import license_manager

def test_license_system():
    """اختبار نظام الترخيص"""
    print("🔐 اختبار نظام الترخيص OnePos")
    print("=" * 50)
    
    # عرض معرف الجهاز
    machine_id = license_manager._get_machine_id()
    print(f"💻 معرف الجهاز: {machine_id}")
    print()
    
    # توليد كود سيريال
    print("🔑 توليد كود سيريال جديد...")
    serial_code = license_manager.generate_serial_for_current_machine(365)
    print(f"✅ كود السيريال: {serial_code}")
    print(f"📏 طول الكود: {len(serial_code)} حرف")
    print()
    
    # اختبار فحص الكود
    print("🔍 اختبار فحص كود السيريال...")
    is_valid, message = license_manager.validate_serial_code(serial_code)
    print(f"📊 النتيجة: {'✅ صحيح' if is_valid else '❌ خطأ'}")
    print(f"💬 الرسالة: {message}")
    print()
    
    # اختبار التفعيل
    if is_valid:
        print("🚀 اختبار تفعيل الترخيص...")
        success, activation_message = license_manager.activate_license(serial_code)
        print(f"📊 نتيجة التفعيل: {'✅ نجح' if success else '❌ فشل'}")
        print(f"💬 رسالة التفعيل: {activation_message}")
        print()
        
        # فحص حالة الترخيص
        print("📋 فحص حالة الترخيص...")
        status = license_manager.get_license_status()
        print(f"📊 الحالة: {status['status']}")
        print(f"💬 الرسالة: {status['message']}")
        print(f"📅 أيام متبقية: {status.get('days_remaining', 'غير محدد')}")
        print(f"🧪 فترة تجربة: {'نعم' if status.get('is_trial', False) else 'لا'}")
    
    print()
    print("=" * 50)
    print("✅ انتهى اختبار نظام الترخيص")

if __name__ == "__main__":
    test_license_system()
