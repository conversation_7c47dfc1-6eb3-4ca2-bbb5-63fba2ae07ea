#!/usr/bin/env python3
"""
فحص نهائي للتأكد من تطبيق جميع الإصلاحات
Final verification of all applied fixes
"""

import os
import json
import re

def verify_login_widget():
    """فحص واجهة تسجيل الدخول"""
    print("🔍 فحص واجهة تسجيل الدخول...")
    
    file_path = "views/login_widget.py"
    if not os.path.exists(file_path):
        print("   ❌ ملف غير موجود")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if "setFixedSize(700, 850)" in content:
        print("   ✅ حجم النافذة محدث (700x850)")
        return True
    elif "setFixedSize(600, 750)" in content:
        print("   ✅ حجم النافذة محدث (600x750)")
        return True
    else:
        print("   ❌ حجم النافذة لم يتم تحديثه")
        return False

def verify_main_window_translations():
    """فحص ترجمات النافذة الرئيسية"""
    print("\n🔍 فحص ترجمات النافذة الرئيسية...")
    
    file_path = "views/main_window.py"
    if not os.path.exists(file_path):
        print("   ❌ ملف غير موجود")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if "tr(key)" in content and "tr(f'navigation.{key}')" not in content:
        print("   ✅ تم إصلاح مشكلة navigation.")
        return True
    else:
        print("   ❌ مشكلة navigation. لا تزال موجودة")
        return False

def verify_translation_files():
    """فحص ملفات الترجمة"""
    print("\n🔍 فحص ملفات الترجمة...")
    
    files = ["ar.json", "en.json", "fr.json"]
    required_keys = ["pos", "products", "sales", "customers", "settings"]
    
    all_good = True
    
    for filename in files:
        file_path = f"translations/{filename}"
        print(f"   📄 فحص {filename}...")
        
        if not os.path.exists(file_path):
            print(f"   ❌ ملف غير موجود: {filename}")
            all_good = False
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            missing_keys = []
            for key in required_keys:
                if key not in data:
                    missing_keys.append(key)
            
            if missing_keys:
                print(f"   ❌ مفاتيح مفقودة: {missing_keys}")
                all_good = False
            else:
                print(f"   ✅ جميع المفاتيح موجودة")
                
            # فحص الترجمات الجديدة
            if "common" in data and "currency" in data["common"]:
                print(f"   ✅ ترجمات جديدة مضافة")
            else:
                print(f"   ⚠️ بعض الترجمات الجديدة مفقودة")
                
        except Exception as e:
            print(f"   ❌ خطأ في قراءة {filename}: {e}")
            all_good = False
    
    return all_good

def verify_pos_widget():
    """فحص واجهة نقطة البيع"""
    print("\n🔍 فحص واجهة نقطة البيع...")
    
    file_path = "views/pos_widget.py"
    if not os.path.exists(file_path):
        print("   ❌ ملف غير موجود")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Hold Order مطبق", "Hold order functionality will be implemented" not in content),
        ("ترجمة العملة", 'tr("common.currency")' in content),
        ("استيراد datetime", "from datetime import datetime" in content)
    ]
    
    all_good = True
    for check_name, condition in checks:
        if condition:
            print(f"   ✅ {check_name}")
        else:
            print(f"   ❌ {check_name}")
            all_good = False
    
    return all_good

def verify_products_widget():
    """فحص واجهة المنتجات"""
    print("\n🔍 فحص واجهة المنتجات...")
    
    file_path = "views/products_widget.py"
    if not os.path.exists(file_path):
        print("   ❌ ملف غير موجود")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("زر إدارة الفئات", "manage_categories" in content),
        ("ترجمة الرسائل", 'tr("common.error")' in content),
        ("استيراد إدارة الفئات", "CategoryManagementWidget" in content)
    ]
    
    all_good = True
    for check_name, condition in checks:
        if condition:
            print(f"   ✅ {check_name}")
        else:
            print(f"   ❌ {check_name}")
            all_good = False
    
    return all_good

def verify_category_management():
    """فحص إدارة الفئات"""
    print("\n🔍 فحص إدارة الفئات...")
    
    file_path = "views/category_management_widget.py"
    if not os.path.exists(file_path):
        print("   ❌ ملف إدارة الفئات غير موجود")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("كلاس CategoryDialog", "class CategoryDialog" in content),
        ("كلاس CategoryManagementWidget", "class CategoryManagementWidget" in content),
        ("دالة إضافة فئة", "def add_category" in content),
        ("دالة تعديل فئة", "def edit_category" in content),
        ("دالة حذف فئة", "def delete_category" in content)
    ]
    
    all_good = True
    for check_name, condition in checks:
        if condition:
            print(f"   ✅ {check_name}")
        else:
            print(f"   ❌ {check_name}")
            all_good = False
    
    return all_good

def verify_font_size():
    """فحص حجم الخط"""
    print("\n🔍 فحص حجم الخط...")
    
    file_path = "main.py"
    if not os.path.exists(file_path):
        print("   ❌ ملف main.py غير موجود")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if 'QFont("Segoe UI", 10, QFont.Medium)' in content:
        print("   ✅ حجم الخط محدث إلى 10")
        return True
    elif 'QFont("Segoe UI", 12, QFont.Medium)' in content:
        print("   ⚠️ حجم الخط لا يزال 12")
        return False
    else:
        print("   ❌ لم يتم العثور على إعداد الخط")
        return False

def check_common_issues():
    """فحص المشاكل الشائعة"""
    print("\n🔍 فحص المشاكل الشائعة...")
    
    widget_files = [
        "views/sales_widget.py",
        "views/customers_widget.py", 
        "views/settings_widget.py"
    ]
    
    issues_found = []
    
    for file_path in widget_files:
        if not os.path.exists(file_path):
            continue
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص النصوص غير المترجمة
        untranslated_patterns = [
            r'QMessageBox\.\w+\([^,]+,\s*"([A-Za-z][^"]*)"',
            r'setText\("([A-Za-z][^"]*)")',
        ]
        
        for pattern in untranslated_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if (len(match) > 3 and 
                    not match.startswith('tr(') and
                    not match.isdigit() and
                    any(c.isalpha() for c in match)):
                    issues_found.append(f"{file_path}: {match}")
    
    if issues_found:
        print("   ❌ نصوص غير مترجمة موجودة:")
        for issue in issues_found[:5]:  # أول 5 فقط
            print(f"      - {issue}")
        return False
    else:
        print("   ✅ لا توجد نصوص غير مترجمة واضحة")
        return True

def generate_verification_report():
    """إنشاء تقرير الفحص النهائي"""
    print("\n" + "="*80)
    print("📋 تقرير الفحص النهائي للإصلاحات")
    print("="*80)
    
    tests = [
        ("واجهة تسجيل الدخول", verify_login_widget),
        ("ترجمات النافذة الرئيسية", verify_main_window_translations),
        ("ملفات الترجمة", verify_translation_files),
        ("واجهة نقطة البيع", verify_pos_widget),
        ("واجهة المنتجات", verify_products_widget),
        ("إدارة الفئات", verify_category_management),
        ("حجم الخط", verify_font_size),
        ("المشاكل الشائعة", check_common_issues)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"   ❌ خطأ في اختبار {test_name}: {e}")
    
    print(f"\n📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الإصلاحات تمت بنجاح!")
        print("\n✅ التطبيق جاهز للبناء والاختبار")
    elif passed >= total * 0.8:
        print("✅ معظم الإصلاحات تمت بنجاح")
        print("⚠️ بعض التحسينات الطفيفة قد تكون مطلوبة")
    else:
        print("⚠️ بعض الإصلاحات تحتاج مراجعة")
        print("🔧 قد تحتاج إصلاحات إضافية")
    
    print("\n🎯 الخطوات التالية:")
    print("1. انتظار انتهاء البناء")
    print("2. إنشاء المثبت")
    print("3. اختبار شامل للتطبيق")
    
    print("="*80)

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الفحص النهائي للإصلاحات")
    generate_verification_report()

if __name__ == "__main__":
    main()
