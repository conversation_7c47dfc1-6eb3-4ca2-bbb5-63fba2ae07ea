"""
User model for OnePos POS System
"""

import bcrypt
import uuid
from datetime import datetime, timedelta
from .database import db


class User:
    """User model for authentication and authorization"""
    
    def __init__(self, user_id=None, username=None, password_hash=None, full_name=None, 
                 email=None, phone=None, role='cashier', permissions=None, is_active=True):
        self.id = user_id
        self.username = username
        self.password_hash = password_hash
        self.full_name = full_name
        self.email = email
        self.phone = phone
        self.role = role
        self.permissions = permissions or []
        self.is_active = is_active
        self.created_at = None
        self.updated_at = None
        self.last_login = None
        self.failed_attempts = 0
        self.locked_until = None
        self.session_token = None

        # Initialize permission checker
        self._permission_checker = None

    @property
    def permission_checker(self):
        """Get permission checker for this user"""
        if self._permission_checker is None:
            try:
                from models.permissions import PermissionChecker
                self._permission_checker = PermissionChecker(self)
            except ImportError:
                self._permission_checker = None
        return self._permission_checker

    def has_permission(self, permission):
        """Check if user has specific permission"""
        if self.permission_checker:
            return self.permission_checker.has_permission(permission)
        return False

    def is_account_locked(self):
        """Check if account is locked"""
        if self.locked_until:
            from datetime import datetime
            return datetime.now() < datetime.fromisoformat(self.locked_until)
        return False

    def lock_account(self, duration_minutes=30):
        """Lock account for specified duration"""
        from datetime import datetime, timedelta
        self.locked_until = (datetime.now() + timedelta(minutes=duration_minutes)).isoformat()
        self.save()

    def unlock_account(self):
        """Unlock account"""
        self.locked_until = None
        self.failed_attempts = 0
        self.save()

    def record_failed_login(self):
        """Record failed login attempt"""
        self.failed_attempts += 1
        if self.failed_attempts >= 5:
            self.lock_account(30)  # Lock for 30 minutes
        self.update()

    def save(self):
        """Save user changes"""
        if self.id:
            self.update()
        else:
            # This is a new user, should use create method
            raise ValueError("Cannot save new user, use create method instead")

    def record_successful_login(self):
        """Record successful login"""
        from datetime import datetime
        self.last_login = datetime.now().isoformat()
        self.failed_attempts = 0
        self.locked_until = None
        self.update()

    def verify_password(self, password):
        """Verify password against stored hash"""
        if not self.password_hash:
            return False
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))

    def update_password(self, new_password):
        """Update user password"""
        password_hash = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt())
        self.password_hash = password_hash.decode('utf-8')
        self.update(password_hash=self.password_hash)

    def change_password(self, old_password, new_password):
        """Change user password"""
        # Verify old password
        if not self.verify_password(old_password):
            raise ValueError("Current password is incorrect")

        # Check password strength
        try:
            from models.permissions import security_manager
            strength = security_manager.check_password_strength(new_password)
            if not strength['strong']:
                raise ValueError("New password is too weak")
        except ImportError:
            # Basic password check if security manager not available
            if len(new_password) < 6:
                raise ValueError("Password must be at least 6 characters long")

        # Hash new password
        new_hash = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt())

        # Update password in database
        db.execute_update(
            "UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
            (new_hash.decode('utf-8'), self.id)
        )

        # Update object
        self.password_hash = new_hash.decode('utf-8')

        # Log password change
        try:
            from models.permissions import AuditLogger
            AuditLogger.log_action(self.id, "PASSWORD_CHANGED", "User changed password")
        except ImportError:
            pass

        return True
    
    @classmethod
    def create(cls, username, password, full_name, email=None, phone=None, role='cashier', permissions=None):
        """Create a new user"""
        # Hash password
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
        
        # Convert permissions list to string
        permissions_str = ','.join(permissions) if permissions else ''
        
        user_id = db.execute_update("""
            INSERT INTO users (username, password_hash, full_name, email, phone, role, permissions)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (username, password_hash.decode('utf-8'), full_name, email, phone, role, permissions_str))
        
        return cls.get_by_id(user_id)
    
    @classmethod
    def get_by_id(cls, user_id):
        """Get user by ID"""
        result = db.execute_query("SELECT * FROM users WHERE id = ?", (user_id,))
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def get_by_username(cls, username):
        """Get user by username"""
        result = db.execute_query("SELECT * FROM users WHERE username = ?", (username,))
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def get_all(cls, active_only=True):
        """Get all users"""
        query = "SELECT * FROM users"
        params = None
        
        if active_only:
            query += " WHERE is_active = 1"
        
        query += " ORDER BY full_name"
        
        results = db.execute_query(query, params)
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def authenticate(cls, username, password, ip_address=""):
        """Authenticate user with enhanced security"""
        try:
            from models.permissions import security_manager, AuditLogger

            # Check if account is locked
            if security_manager.is_account_locked(username):
                AuditLogger.log_action(0, "LOGIN_BLOCKED", f"Account locked: {username}", ip_address)
                return None

            user = cls.get_by_username(username)
            if not user:
                security_manager.record_failed_attempt(username)
                AuditLogger.log_action(0, "LOGIN_FAILED", f"User not found: {username}", ip_address)
                return None

            if not user.is_active:
                AuditLogger.log_action(user.id, "LOGIN_FAILED", "Account inactive", ip_address)
                return None

            if user.is_account_locked():
                AuditLogger.log_action(user.id, "LOGIN_BLOCKED", "Account locked", ip_address)
                return None

            # Verify password
            if bcrypt.checkpw(password.encode('utf-8'), user.password_hash.encode('utf-8')):
                # Successful login
                security_manager.clear_failed_attempts(username)
                user.record_successful_login()

                # Generate session token
                user.session_token = str(uuid.uuid4())
                user.save()

                AuditLogger.log_login(user.id, True, ip_address)
                return user
            else:
                # Failed login
                security_manager.record_failed_attempt(username)
                user.record_failed_login()
                AuditLogger.log_login(user.id, False, ip_address)
                return None

        except Exception as e:
            print(f"Authentication error: {e}")
            return None
    
    def update(self, **kwargs):
        """Update user information"""
        allowed_fields = ['full_name', 'email', 'phone', 'role', 'permissions', 'is_active']
        update_fields = []
        params = []
        
        for field, value in kwargs.items():
            if field in allowed_fields:
                if field == 'permissions' and isinstance(value, list):
                    value = ','.join(value)
                update_fields.append(f"{field} = ?")
                params.append(value)
        
        if update_fields:
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(self.id)
            
            query = f"UPDATE users SET {', '.join(update_fields)} WHERE id = ?"
            db.execute_update(query, params)
            
            # Refresh object
            updated_user = self.get_by_id(self.id)
            if updated_user:
                self.__dict__.update(updated_user.__dict__)
    
    def change_password(self, new_password):
        """Change user password"""
        password_hash = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt())
        db.execute_update("""
            UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        """, (password_hash.decode('utf-8'), self.id))
        self.password_hash = password_hash.decode('utf-8')
    
    def has_permission(self, permission):
        """Check if user has specific permission"""
        if self.role == 'admin':
            return True
        
        if isinstance(self.permissions, str):
            permissions_list = self.permissions.split(',') if self.permissions else []
        else:
            permissions_list = self.permissions or []
        
        return permission in permissions_list or 'all' in permissions_list
    
    def delete(self):
        """Soft delete user (set inactive)"""
        self.update(is_active=False)
    
    def to_dict(self):
        """Convert user to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'full_name': self.full_name,
            'email': self.email,
            'phone': self.phone,
            'role': self.role,
            'permissions': self.permissions,
            'is_active': self.is_active,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def _from_db_row(cls, row):
        """Create User object from database row"""
        user = cls(
            user_id=row['id'],
            username=row['username'],
            password_hash=row['password_hash'],
            full_name=row['full_name'],
            email=row['email'],
            phone=row['phone'],
            role=row['role'],
            permissions=row['permissions'].split(',') if row['permissions'] else [],
            is_active=bool(row['is_active'])
        )
        user.created_at = row['created_at']
        user.updated_at = row['updated_at']
        return user


class UserSession:
    """User session management"""
    
    def __init__(self, session_id=None, user_id=None, login_time=None, logout_time=None, is_active=True):
        self.id = session_id
        self.user_id = user_id
        self.login_time = login_time
        self.logout_time = logout_time
        self.is_active = is_active
    
    @classmethod
    def create_session(cls, user_id):
        """Create new user session"""
        session_id = db.execute_update("""
            INSERT INTO user_sessions (user_id, login_time, is_active)
            VALUES (?, CURRENT_TIMESTAMP, 1)
        """, (user_id,))
        
        return cls.get_by_id(session_id)
    
    @classmethod
    def get_by_id(cls, session_id):
        """Get session by ID"""
        result = db.execute_query("SELECT * FROM user_sessions WHERE id = ?", (session_id,))
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def get_active_session(cls, user_id):
        """Get active session for user"""
        result = db.execute_query("""
            SELECT * FROM user_sessions 
            WHERE user_id = ? AND is_active = 1 
            ORDER BY login_time DESC LIMIT 1
        """, (user_id,))
        if result:
            return cls._from_db_row(result[0])
        return None
    
    def end_session(self):
        """End user session"""
        db.execute_update("""
            UPDATE user_sessions
            SET logout_time = CURRENT_TIMESTAMP, is_active = 0
            WHERE id = ?
        """, (self.id,))
        self.is_active = False
        self.logout_time = datetime.now()

    @classmethod
    def is_session_valid(cls, session_id):
        """Check if session is valid and active"""
        result = db.execute_query("""
            SELECT is_active FROM user_sessions
            WHERE id = ? AND is_active = 1
        """, (session_id,))
        return bool(result)

    @classmethod
    def end_session_by_id(cls, session_id):
        """End session by ID"""
        db.execute_update("""
            UPDATE user_sessions
            SET logout_time = CURRENT_TIMESTAMP, is_active = 0
            WHERE id = ?
        """, (session_id,))
    
    @classmethod
    def _from_db_row(cls, row):
        """Create UserSession object from database row"""
        session = cls(
            session_id=row[0],  # id
            user_id=row[1],     # user_id
            login_time=row[2],  # login_time
            logout_time=row[3] if len(row) > 3 else None,  # logout_time
            is_active=bool(row[4]) if len(row) > 4 else True  # is_active
        )
        return session
