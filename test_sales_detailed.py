"""
Detailed Testing for OnePos Sales System
More comprehensive tests with edge cases and error handling
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QDate, Qt
from models.database import db
from models.sale import Sale, SaleItem
from models.product import Product
from models.customer import Customer
from models.user import User
from views.sales_widget import SalesWidget, SaleDetailsDialog
from datetime import datetime, timedelta

def test_sales_data_integrity():
    """Test sales data integrity and relationships"""
    print("🔍 Testing Sales Data Integrity...")
    
    try:
        # Test sales with items
        sales = Sale.get_all()
        print(f"✅ Found {len(sales)} sales")
        
        for sale in sales:
            # Test sale items loading
            items = sale.get_items()
            print(f"   - Sale {sale.invoice_number}: {len(items)} items")
            
            # Test customer relationship
            customer = sale.get_customer()
            customer_name = customer.name if customer else "Walk-in"
            print(f"     Customer: {customer_name}")
            
            # Test user relationship
            user = User.get_by_id(sale.user_id)
            user_name = user.full_name if user else "Unknown"
            print(f"     Cashier: {user_name}")
            
            # Test item-product relationships
            for item in items:
                product = item.get_product()
                if product:
                    print(f"     Item: {product.name} x{item.quantity} @ ${item.unit_price:.2f}")
                else:
                    print(f"     ❌ Item with missing product: ID {item.product_id}")

            # Test calculations
            calculated_total = sum(item.total_amount for item in items)
            if abs(calculated_total - sale.subtotal) > 0.01:
                print(f"     ❌ Calculation mismatch: {calculated_total} vs {sale.subtotal}")
            else:
                print(f"     ✅ Calculations correct: ${calculated_total:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data integrity test failed: {e}")
        return False

def test_sales_widget_ui_components():
    """Test all UI components in detail"""
    print("\n🖥️ Testing Sales Widget UI Components...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        sales_widget = SalesWidget()
        
        # Test table structure
        table = sales_widget.sales_table
        print(f"✅ Sales table: {table.rowCount()} rows, {table.columnCount()} columns")
        
        # Test column headers
        headers = []
        for col in range(table.columnCount()):
            header = table.horizontalHeaderItem(col)
            headers.append(header.text() if header else f"Column {col}")
        print(f"✅ Column headers: {headers}")
        
        # Test table data
        if table.rowCount() > 0:
            for row in range(min(3, table.rowCount())):  # Test first 3 rows
                row_data = []
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    row_data.append(item.text() if item else "")
                print(f"   Row {row}: {row_data}")
        
        # Test filters
        print(f"✅ Status filter options: {[sales_widget.status_filter.itemText(i) for i in range(sales_widget.status_filter.count())]}")
        print(f"✅ Date range: {sales_widget.from_date.date().toString()} to {sales_widget.to_date.date().toString()}")
        
        # Test search functionality
        original_count = table.rowCount()
        sales_widget.search_edit.setText("INV")
        sales_widget.search_sales()
        search_count = table.rowCount()
        print(f"✅ Search test: {original_count} → {search_count} results")
        
        # Clear search
        sales_widget.search_edit.setText("")
        sales_widget.search_sales()
        cleared_count = table.rowCount()
        print(f"✅ Clear search: {cleared_count} results")
        
        return sales_widget
        
    except Exception as e:
        print(f"❌ UI components test failed: {e}")
        return False

def test_sales_filtering_edge_cases(sales_widget):
    """Test filtering with edge cases"""
    print("\n🔍 Testing Sales Filtering Edge Cases...")
    
    try:
        # Test empty search
        sales_widget.search_edit.setText("")
        sales_widget.search_sales()
        empty_search_count = sales_widget.sales_table.rowCount()
        print(f"✅ Empty search: {empty_search_count} results")
        
        # Test non-existent search
        sales_widget.search_edit.setText("NONEXISTENT123")
        sales_widget.search_sales()
        no_results_count = sales_widget.sales_table.rowCount()
        print(f"✅ Non-existent search: {no_results_count} results")
        
        # Test special characters
        sales_widget.search_edit.setText("@#$%")
        sales_widget.search_sales()
        special_char_count = sales_widget.sales_table.rowCount()
        print(f"✅ Special characters search: {special_char_count} results")
        
        # Test date range edge cases
        # Future date range
        future_date = QDate.currentDate().addDays(30)
        sales_widget.from_date.setDate(future_date)
        sales_widget.to_date.setDate(future_date.addDays(1))
        sales_widget.filter_sales()
        future_count = sales_widget.sales_table.rowCount()
        print(f"✅ Future date range: {future_count} results")
        
        # Past date range
        past_date = QDate.currentDate().addDays(-365)
        sales_widget.from_date.setDate(past_date)
        sales_widget.to_date.setDate(past_date.addDays(1))
        sales_widget.filter_sales()
        past_count = sales_widget.sales_table.rowCount()
        print(f"✅ Past date range: {past_count} results")
        
        # Reset to normal range
        sales_widget.from_date.setDate(QDate.currentDate().addDays(-30))
        sales_widget.to_date.setDate(QDate.currentDate())
        sales_widget.filter_sales()
        
        # Test status combinations
        statuses = ["All", "Completed", "Cancelled", "Pending"]
        for status in statuses:
            for i in range(sales_widget.status_filter.count()):
                if sales_widget.status_filter.itemText(i) == status:
                    sales_widget.status_filter.setCurrentIndex(i)
                    break
            sales_widget.filter_sales()
            status_count = sales_widget.sales_table.rowCount()
            print(f"✅ Status '{status}': {status_count} results")
        
        return True
        
    except Exception as e:
        print(f"❌ Filtering edge cases test failed: {e}")
        return False

def test_sale_details_dialog():
    """Test sale details dialog functionality"""
    print("\n👁️ Testing Sale Details Dialog...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Get a sale to test
        sales = Sale.get_all()
        if not sales:
            print("⚠️ No sales available for testing")
            return True
        
        test_sale = sales[0]
        print(f"✅ Testing with sale: {test_sale.invoice_number}")
        
        # Create dialog
        dialog = SaleDetailsDialog(test_sale)
        
        # Test dialog components
        if hasattr(dialog, 'invoice_label'):
            print(f"✅ Invoice label: {dialog.invoice_label.text()}")
        
        if hasattr(dialog, 'date_label'):
            print(f"✅ Date label: {dialog.date_label.text()}")
        
        if hasattr(dialog, 'customer_label'):
            print(f"✅ Customer label: {dialog.customer_label.text()}")
        
        if hasattr(dialog, 'status_label'):
            print(f"✅ Status label: {dialog.status_label.text()}")
        
        # Test items table
        if hasattr(dialog, 'items_table'):
            items_count = dialog.items_table.rowCount()
            print(f"✅ Items table: {items_count} rows")
            
            # Test item data
            for row in range(min(3, items_count)):
                row_data = []
                for col in range(dialog.items_table.columnCount()):
                    item = dialog.items_table.item(row, col)
                    row_data.append(item.text() if item else "")
                print(f"   Item {row}: {row_data}")
        
        # Test totals
        totals_labels = ['subtotal_label', 'tax_label', 'discount_label', 'total_label']
        for label_name in totals_labels:
            if hasattr(dialog, label_name):
                label = getattr(dialog, label_name)
                print(f"✅ {label_name}: {label.text()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Sale details dialog test failed: {e}")
        return False

def test_sales_calculations():
    """Test sales calculations accuracy"""
    print("\n🧮 Testing Sales Calculations...")
    
    try:
        sales = Sale.get_all()
        
        for sale in sales:
            print(f"\n📊 Testing sale: {sale.invoice_number}")
            
            # Get items
            items = sale.get_items()
            
            # Calculate subtotal
            calculated_subtotal = sum(item.total_amount for item in items)
            print(f"   Calculated subtotal: ${calculated_subtotal:.2f}")
            print(f"   Stored subtotal: ${sale.subtotal:.2f}")

            if abs(calculated_subtotal - sale.subtotal) > 0.01:
                print(f"   ❌ Subtotal mismatch!")
                return False
            else:
                print(f"   ✅ Subtotal correct")

            # Test total calculation
            expected_total = sale.subtotal + sale.tax_amount - sale.discount_amount
            print(f"   Expected total: ${expected_total:.2f}")
            print(f"   Stored total: ${sale.total_amount:.2f}")

            if abs(expected_total - sale.total_amount) > 0.01:
                print(f"   ❌ Total mismatch!")
                return False
            else:
                print(f"   ✅ Total correct")

            # Test item calculations
            for item in items:
                expected_item_total = item.quantity * item.unit_price - item.discount_amount
                if abs(expected_item_total - item.total_amount) > 0.01:
                    print(f"   ❌ Item total mismatch: {item.quantity} x {item.unit_price} - {item.discount_amount} = {expected_item_total} vs {item.total_amount}")
                    return False
        
        print("✅ All calculations are correct")
        return True
        
    except Exception as e:
        print(f"❌ Sales calculations test failed: {e}")
        return False

def test_sales_printing_data():
    """Test sales printing data preparation"""
    print("\n🖨️ Testing Sales Printing Data...")
    
    try:
        sales_widget = SalesWidget()
        sales = Sale.get_all()
        
        if not sales:
            print("⚠️ No sales available for printing test")
            return True
        
        test_sale = sales[0]
        print(f"✅ Testing printing data for: {test_sale.invoice_number}")
        
        # Simulate selecting the sale
        sales_widget.sales_table.selectRow(0)
        
        # Test print data preparation (without actually printing)
        try:
            # Get the sale data that would be sent to printer
            sale_data = {
                'invoice_number': test_sale.invoice_number,
                'date': test_sale.created_at.split(' ')[0] if hasattr(test_sale, 'created_at') else datetime.now().strftime('%Y-%m-%d'),
                'time': test_sale.created_at.split(' ')[1] if hasattr(test_sale, 'created_at') else datetime.now().strftime('%H:%M:%S'),
                'cashier': User.get_by_id(test_sale.user_id).full_name if test_sale.user_id else 'Admin',
                'customer': test_sale.get_customer().name if test_sale.get_customer() else 'Walk-in Customer',
                'items': [],
                'subtotal': test_sale.subtotal,
                'tax_amount': test_sale.tax_amount,
                'discount_amount': test_sale.discount_amount,
                'total_amount': test_sale.total_amount,
                'payment_method': test_sale.payment_method
            }
            
            # Add items
            for item in test_sale.get_items():
                product = item.get_product()
                sale_data['items'].append({
                    'name': product.name if product else 'Unknown',
                    'quantity': item.quantity,
                    'unit_price': item.unit_price,
                    'total_amount': item.total_amount
                })
            
            print(f"✅ Print data prepared:")
            print(f"   Invoice: {sale_data['invoice_number']}")
            print(f"   Date: {sale_data['date']}")
            print(f"   Customer: {sale_data['customer']}")
            print(f"   Items: {len(sale_data['items'])}")
            print(f"   Total: ${sale_data['total_amount']:.2f}")
            
            return True
            
        except Exception as e:
            print(f"⚠️ Print data preparation failed: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Sales printing test failed: {e}")
        return False

def test_sales_error_handling():
    """Test error handling in sales system"""
    print("\n⚠️ Testing Sales Error Handling...")
    
    try:
        sales_widget = SalesWidget()
        
        # Test with no selection
        print("Testing operations with no selection...")
        
        # Clear selection
        sales_widget.sales_table.clearSelection()
        
        # Test view sale with no selection
        try:
            sales_widget.view_sale()
            print("⚠️ View sale should show warning with no selection")
        except Exception as e:
            print(f"✅ View sale properly handles no selection: {type(e).__name__}")
        
        # Test print with no selection
        try:
            sales_widget.print_invoice()
            print("⚠️ Print invoice should show warning with no selection")
        except Exception as e:
            print(f"✅ Print invoice properly handles no selection: {type(e).__name__}")
        
        # Test with invalid date range
        print("Testing with invalid date ranges...")
        
        # Set from date after to date
        sales_widget.from_date.setDate(QDate.currentDate())
        sales_widget.to_date.setDate(QDate.currentDate().addDays(-1))
        
        try:
            sales_widget.filter_sales()
            print("✅ Invalid date range handled gracefully")
        except Exception as e:
            print(f"⚠️ Invalid date range caused error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def run_detailed_sales_test():
    """Run all detailed sales tests"""
    print("🔍 STARTING DETAILED SALES SYSTEM TEST")
    print("=" * 60)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # Test 1: Data Integrity
    test_results.append(("Data Integrity", test_sales_data_integrity()))
    
    # Test 2: UI Components
    sales_widget = test_sales_widget_ui_components()
    test_results.append(("UI Components", sales_widget is not False))
    
    if sales_widget:
        # Test 3: Filtering Edge Cases
        test_results.append(("Filtering Edge Cases", test_sales_filtering_edge_cases(sales_widget)))
    
    # Test 4: Sale Details Dialog
    test_results.append(("Sale Details Dialog", test_sale_details_dialog()))
    
    # Test 5: Sales Calculations
    test_results.append(("Sales Calculations", test_sales_calculations()))
    
    # Test 6: Printing Data
    test_results.append(("Printing Data", test_sales_printing_data()))
    
    # Test 7: Error Handling
    test_results.append(("Error Handling", test_sales_error_handling()))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 DETAILED SALES TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL DETAILED TESTS PASSED! Sales system is robust and reliable!")
    else:
        print("⚠️  Some detailed tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_detailed_sales_test()
