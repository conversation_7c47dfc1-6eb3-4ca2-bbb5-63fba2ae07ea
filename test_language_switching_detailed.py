"""
Detailed Re-testing for OnePos Language Switching System
More comprehensive tests with real UI interaction and edge cases
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QLabel, QPushButton, QTableWidget
from PyQt5.QtCore import QTimer, Qt
from utils.translator import translator, tr, change_language, get_current_language, register_for_translation
from utils.config_manager import config
from views.main_window import MainWindow
from views.settings_widget import SettingsWidget, GeneralSettingsTab
from views.pos_widget import POSWidget
from views.products_widget import ProductsWidget
from views.customers_widget import CustomersWidget
from views.reports_widget import ReportsWidget
from models.user import User
import time

def test_real_application_language_switching():
    """Test language switching in real application environment"""
    print("🌐 Testing Real Application Language Switching...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create a test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        
        # Process events to ensure UI is ready
        app.processEvents()
        
        print(f"✅ Main window created and shown")
        
        # Test initial language
        initial_language = get_current_language()
        print(f"✅ Initial language: {initial_language}")
        
        # Test navigation button texts
        nav_buttons = main_window.nav_buttons
        print(f"✅ Navigation buttons: {len(nav_buttons)}")
        
        for module_name, button in nav_buttons.items():
            button_text = button.text()
            print(f"   - {module_name}: '{button_text}'")
        
        # Test language switching through settings
        print("\n   Testing language switching through settings...")
        
        # Navigate to settings
        if 'settings' in nav_buttons:
            settings_button = nav_buttons['settings']
            settings_button.click()
            app.processEvents()
            print("   ✅ Navigated to settings")
            
            # Get settings widget
            current_widget = main_window.stacked_widget.currentWidget()
            if isinstance(current_widget, SettingsWidget):
                settings_widget = current_widget
                
                # Find general settings tab
                general_tab = None
                for i in range(settings_widget.tabs.count()):
                    widget = settings_widget.tabs.widget(i)
                    if isinstance(widget, GeneralSettingsTab):
                        general_tab = widget
                        settings_widget.tabs.setCurrentIndex(i)
                        app.processEvents()
                        break
                
                if general_tab:
                    print("   ✅ Found general settings tab")
                    
                    # Test language combo
                    language_combo = general_tab.language_combo
                    print(f"   ✅ Language combo has {language_combo.count()} options")
                    
                    # Test switching to each language
                    languages_to_test = []
                    for i in range(language_combo.count()):
                        lang_code = language_combo.itemData(i)
                        lang_name = language_combo.itemText(i)
                        if lang_code != initial_language:
                            languages_to_test.append((i, lang_code, lang_name))
                    
                    for index, lang_code, lang_name in languages_to_test:
                        print(f"\n   Testing switch to {lang_name} ({lang_code})...")
                        
                        # Change language
                        language_combo.setCurrentIndex(index)
                        app.processEvents()
                        
                        # Trigger language change
                        general_tab.on_language_changed()
                        app.processEvents()
                        
                        # Verify language changed
                        current_lang = get_current_language()
                        if current_lang == lang_code:
                            print(f"   ✅ Language changed to {lang_code}")
                            
                            # Test navigation button updates
                            for module_name, button in nav_buttons.items():
                                new_text = button.text()
                                print(f"     - {module_name}: '{new_text}'")
                            
                            # Test window title
                            window_title = main_window.windowTitle()
                            print(f"     - Window title: '{window_title}'")
                            
                            # Test module title
                            module_title = main_window.module_title_label.text()
                            print(f"     - Module title: '{module_title}'")
                            
                        else:
                            print(f"   ❌ Failed to change language to {lang_code}")
                            return False
                else:
                    print("   ❌ General settings tab not found")
                    return False
            else:
                print("   ❌ Settings widget not found")
                return False
        else:
            print("   ❌ Settings button not found")
            return False
        
        # Switch back to initial language
        for i in range(language_combo.count()):
            if language_combo.itemData(i) == initial_language:
                language_combo.setCurrentIndex(i)
                general_tab.on_language_changed()
                app.processEvents()
                break
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Real application test failed: {e}")
        return False

def test_all_widgets_language_switching():
    """Test language switching in all main widgets"""
    print("\n🖥️ Testing All Widgets Language Switching...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Test widgets to create
        widget_classes = [
            ("POS Widget", POSWidget),
            ("Products Widget", ProductsWidget),
            ("Customers Widget", CustomersWidget),
            ("Reports Widget", ReportsWidget),
            ("Settings Widget", SettingsWidget)
        ]
        
        created_widgets = []
        
        for widget_name, widget_class in widget_classes:
            try:
                print(f"   Testing {widget_name}...")
                
                # Create widget
                if widget_class == POSWidget:
                    widget = widget_class(test_user)
                else:
                    widget = widget_class()
                
                created_widgets.append((widget_name, widget))
                
                # Check if widget has translatable elements
                labels = widget.findChildren(QLabel)
                buttons = widget.findChildren(QPushButton)
                tables = widget.findChildren(QTableWidget)
                
                print(f"     - Labels: {len(labels)}")
                print(f"     - Buttons: {len(buttons)}")
                print(f"     - Tables: {len(tables)}")
                
                # Test language change
                original_language = get_current_language()
                test_language = 'fr' if original_language != 'fr' else 'en'
                
                # Record original texts
                original_texts = {}
                for i, label in enumerate(labels[:5]):  # Test first 5 labels
                    original_texts[f"label_{i}"] = label.text()
                
                for i, button in enumerate(buttons[:5]):  # Test first 5 buttons
                    original_texts[f"button_{i}"] = button.text()
                
                # Change language
                change_language(test_language)
                app.processEvents()
                
                # Check if texts changed
                texts_changed = 0
                for i, label in enumerate(labels[:5]):
                    new_text = label.text()
                    if new_text != original_texts.get(f"label_{i}", ""):
                        texts_changed += 1
                
                for i, button in enumerate(buttons[:5]):
                    new_text = button.text()
                    if new_text != original_texts.get(f"button_{i}", ""):
                        texts_changed += 1
                
                print(f"     - Texts changed: {texts_changed}")
                
                # Switch back
                change_language(original_language)
                app.processEvents()
                
                print(f"   ✅ {widget_name} tested successfully")
                
            except Exception as e:
                print(f"   ⚠️ {widget_name} test failed: {e}")
        
        # Clean up
        for widget_name, widget in created_widgets:
            try:
                widget.close()
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ All widgets test failed: {e}")
        return False

def test_translation_consistency():
    """Test translation consistency across all languages"""
    print("\n📚 Testing Translation Consistency...")
    
    try:
        # Test comprehensive key sets
        key_categories = {
            "Navigation": [
                "navigation.pos",
                "navigation.products", 
                "navigation.sales",
                "navigation.customers",
                "navigation.purchases",
                "navigation.users",
                "navigation.reports",
                "navigation.settings"
            ],
            "Common": [
                "common.ok",
                "common.cancel",
                "common.save",
                "common.delete",
                "common.edit",
                "common.add",
                "common.search",
                "common.refresh"
            ],
            "POS": [
                "pos.title",
                "pos.add_product",
                "pos.customer",
                "pos.payment",
                "pos.total",
                "pos.subtotal",
                "pos.tax",
                "pos.discount"
            ],
            "Products": [
                "products.title",
                "products.name",
                "products.price",
                "products.quantity",
                "products.category",
                "products.barcode"
            ],
            "Settings": [
                "settings.title",
                "settings.language",
                "settings.theme",
                "settings.company",
                "settings.printer",
                "settings.backup"
            ]
        }
        
        languages = ['ar', 'fr', 'en']
        original_language = get_current_language()
        
        translation_results = {}
        
        for lang in languages:
            print(f"   Testing {lang} translations...")
            change_language(lang)
            
            lang_results = {}
            for category, keys in key_categories.items():
                category_results = {}
                for key in keys:
                    translation = tr(key)
                    category_results[key] = translation
                    
                    # Check if translation is just the key (missing translation)
                    if translation == key:
                        print(f"     ⚠️ Missing translation for {key}")
                
                lang_results[category] = category_results
            
            translation_results[lang] = lang_results
        
        # Check for consistency issues
        print("\n   Checking consistency...")
        
        for category, keys in key_categories.items():
            print(f"   Category: {category}")
            
            for key in keys:
                translations = {}
                for lang in languages:
                    translations[lang] = translation_results[lang][category][key]
                
                # Check if all languages have different translations (good)
                unique_translations = set(translations.values())
                if len(unique_translations) == 1:
                    print(f"     ⚠️ Same translation in all languages for {key}: '{translations['ar']}'")
                elif len(unique_translations) < len(languages):
                    print(f"     ⚠️ Some languages have same translation for {key}")
                else:
                    print(f"     ✅ {key}: Unique translations in all languages")
        
        # Switch back to original language
        change_language(original_language)
        
        return True
        
    except Exception as e:
        print(f"❌ Translation consistency test failed: {e}")
        return False

def test_rtl_layout_switching():
    """Test RTL layout switching functionality"""
    print("\n🔄 Testing RTL Layout Switching...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user and main window
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Test layout direction changes
        languages_and_directions = [
            ('ar', Qt.RightToLeft, "Arabic RTL"),
            ('en', Qt.LeftToRight, "English LTR"),
            ('fr', Qt.LeftToRight, "French LTR")
        ]
        
        for lang_code, expected_direction, description in languages_and_directions:
            print(f"   Testing {description}...")
            
            # Change language
            change_language(lang_code)
            app.processEvents()
            
            # Check layout direction
            actual_direction = main_window.layoutDirection()
            
            if actual_direction == expected_direction:
                print(f"   ✅ Layout direction correct for {lang_code}: {actual_direction}")
            else:
                print(f"   ❌ Layout direction incorrect for {lang_code}: expected {expected_direction}, got {actual_direction}")
                return False
            
            # Check if child widgets also have correct direction
            child_widgets = main_window.findChildren(QLabel)[:5]  # Test first 5 labels
            
            correct_children = 0
            for child in child_widgets:
                if child.layoutDirection() == expected_direction:
                    correct_children += 1
            
            print(f"     - Child widgets with correct direction: {correct_children}/{len(child_widgets)}")
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ RTL layout test failed: {e}")
        return False

def test_rapid_language_switching():
    """Test rapid language switching for stability"""
    print("\n⚡ Testing Rapid Language Switching...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        languages = ['ar', 'fr', 'en']
        original_language = get_current_language()
        
        # Test rapid switching
        print("   Performing rapid language switches...")
        
        start_time = time.time()
        
        for i in range(20):  # 20 rapid switches
            lang = languages[i % len(languages)]
            change_language(lang)
            app.processEvents()
            
            # Verify language actually changed
            current_lang = get_current_language()
            if current_lang != lang:
                print(f"   ❌ Rapid switch {i+1} failed: expected {lang}, got {current_lang}")
                return False
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"   ✅ 20 rapid switches completed in {total_time:.4f} seconds")
        print(f"   ✅ Average time per switch: {total_time/20:.4f} seconds")
        
        # Test translation consistency after rapid switching
        test_keys = ["common.ok", "navigation.pos", "settings.title"]
        
        for lang in languages:
            change_language(lang)
            for key in test_keys:
                translation = tr(key)
                if translation == key:
                    print(f"   ⚠️ Translation missing after rapid switching: {key} in {lang}")
        
        # Switch back to original
        change_language(original_language)
        
        return True
        
    except Exception as e:
        print(f"❌ Rapid switching test failed: {e}")
        return False

def test_memory_leaks_in_translation():
    """Test for memory leaks in translation system"""
    print("\n🧠 Testing Memory Leaks in Translation...")
    
    try:
        import gc
        
        # Get initial widget count
        initial_widget_count = len(translator.registered_widgets)
        print(f"   Initial registered widgets: {initial_widget_count}")
        
        # Create and destroy widgets multiple times
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        for i in range(10):
            # Create widgets
            widgets = []
            
            try:
                settings_widget = SettingsWidget()
                register_for_translation(settings_widget)
                widgets.append(settings_widget)
                
                general_tab = GeneralSettingsTab()
                register_for_translation(general_tab)
                widgets.append(general_tab)
                
            except Exception as e:
                print(f"   ⚠️ Widget creation failed in iteration {i+1}: {e}")
                continue
            
            # Check widget count increased
            current_count = len(translator.registered_widgets)
            
            # Destroy widgets
            for widget in widgets:
                try:
                    widget.close()
                    widget.deleteLater()
                except:
                    pass
            
            # Force garbage collection
            gc.collect()
            app.processEvents()
        
        # Check final widget count
        final_widget_count = len(translator.registered_widgets)
        print(f"   Final registered widgets: {final_widget_count}")
        
        # Clean up any remaining dead references
        translator.registered_widgets = [w for w in translator.registered_widgets if w is not None]
        cleaned_count = len(translator.registered_widgets)
        print(f"   After cleanup: {cleaned_count}")
        
        if cleaned_count <= initial_widget_count + 2:  # Allow some tolerance
            print("   ✅ No significant memory leaks detected")
        else:
            print(f"   ⚠️ Possible memory leak: {cleaned_count - initial_widget_count} extra widgets")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory leak test failed: {e}")
        return False

def run_detailed_language_switching_test():
    """Run all detailed language switching tests"""
    print("🌐 STARTING DETAILED LANGUAGE SWITCHING SYSTEM TEST")
    print("=" * 70)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # Test 1: Real Application Language Switching
    test_results.append(("Real Application Switching", test_real_application_language_switching()))
    
    # Test 2: All Widgets Language Switching
    test_results.append(("All Widgets Switching", test_all_widgets_language_switching()))
    
    # Test 3: Translation Consistency
    test_results.append(("Translation Consistency", test_translation_consistency()))
    
    # Test 4: RTL Layout Switching
    test_results.append(("RTL Layout Switching", test_rtl_layout_switching()))
    
    # Test 5: Rapid Language Switching
    test_results.append(("Rapid Language Switching", test_rapid_language_switching()))
    
    # Test 6: Memory Leaks
    test_results.append(("Memory Leaks", test_memory_leaks_in_translation()))
    
    # Print results summary
    print("\n" + "=" * 70)
    print("🏁 DETAILED LANGUAGE SWITCHING TEST RESULTS")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("=" * 70)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL DETAILED LANGUAGE SWITCHING TESTS PASSED! System is robust and reliable!")
    else:
        print("⚠️  Some detailed tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_detailed_language_switching_test()
