"""
Detailed Re-testing for OnePos Settings System
More comprehensive tests with edge cases and error handling
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QTabWidget, QComboBox, QLineEdit, QPushButton, QCheckBox, QTextEdit
from PyQt5.QtCore import Qt
from utils.config_manager import config
from utils.translator import tr, change_language, get_current_language
from views.settings_widget import SettingsWidget, GeneralSettingsTab, CompanySettingsTab, PrinterSettingsTab, BackupSettingsTab
from views.main_window import MainWindow
from models.user import User
import time

def test_settings_components_detailed():
    """Test all settings components in detail"""
    print("🔍 Testing Settings Components in Detail...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create settings widget
        settings_widget = SettingsWidget()
        
        print(f"✅ Settings widget created with {settings_widget.tabs.count()} tabs")
        
        # Test each tab in detail
        for i in range(settings_widget.tabs.count()):
            tab_text = settings_widget.tabs.tabText(i)
            tab_widget = settings_widget.tabs.widget(i)
            
            print(f"\n   Testing Tab {i}: {tab_text}")
            
            # Switch to tab
            settings_widget.tabs.setCurrentIndex(i)
            app.processEvents()
            
            # Count components in tab
            labels = tab_widget.findChildren(QLineEdit)
            combos = tab_widget.findChildren(QComboBox)
            buttons = tab_widget.findChildren(QPushButton)
            checkboxes = tab_widget.findChildren(QCheckBox)
            text_edits = tab_widget.findChildren(QTextEdit)
            
            print(f"     - Line edits: {len(labels)}")
            print(f"     - Combo boxes: {len(combos)}")
            print(f"     - Buttons: {len(buttons)}")
            print(f"     - Checkboxes: {len(checkboxes)}")
            print(f"     - Text edits: {len(text_edits)}")
            
            # Test specific tab types
            if isinstance(tab_widget, GeneralSettingsTab):
                print("     ✅ General Settings Tab:")
                
                # Test language combo
                if hasattr(tab_widget, 'language_combo'):
                    lang_combo = tab_widget.language_combo
                    print(f"       - Language options: {lang_combo.count()}")
                    
                    for j in range(lang_combo.count()):
                        lang_name = lang_combo.itemText(j)
                        lang_code = lang_combo.itemData(j)
                        print(f"         {j+1}. {lang_name} ({lang_code})")
                
                # Test theme combo
                if hasattr(tab_widget, 'theme_combo'):
                    theme_combo = tab_widget.theme_combo
                    print(f"       - Theme options: {theme_combo.count()}")
                    
                    for j in range(theme_combo.count()):
                        theme_name = theme_combo.itemText(j)
                        print(f"         {j+1}. {theme_name}")
                
                # Test auto backup
                if hasattr(tab_widget, 'auto_backup_check'):
                    auto_backup = tab_widget.auto_backup_check
                    print(f"       - Auto backup: {auto_backup.isChecked()}")
            
            elif isinstance(tab_widget, CompanySettingsTab):
                print("     ✅ Company Settings Tab:")
                
                # Test company fields
                company_fields = [
                    'company_name_edit',
                    'company_address_edit', 
                    'company_phone_edit',
                    'company_email_edit',
                    'tax_number_edit'
                ]
                
                for field_name in company_fields:
                    if hasattr(tab_widget, field_name):
                        field = getattr(tab_widget, field_name)
                        if isinstance(field, QLineEdit):
                            value = field.text()
                            print(f"       - {field_name}: '{value}'")
                        else:
                            print(f"       - {field_name}: Available")
                    else:
                        print(f"       - {field_name}: Not found")
            
            elif isinstance(tab_widget, PrinterSettingsTab):
                print("     ✅ Printer Settings Tab:")
                
                # Test printer components
                printer_components = [
                    'receipt_printer_combo',
                    'invoice_printer_combo',
                    'label_printer_combo',
                    'paper_size_combo'
                ]
                
                for comp_name in printer_components:
                    if hasattr(tab_widget, comp_name):
                        comp = getattr(tab_widget, comp_name)
                        if isinstance(comp, QComboBox):
                            print(f"       - {comp_name}: {comp.count()} options")
                        else:
                            print(f"       - {comp_name}: Available")
                    else:
                        print(f"       - {comp_name}: Not found")
            
            elif isinstance(tab_widget, BackupSettingsTab):
                print("     ✅ Backup Settings Tab:")
                
                # Test backup components
                backup_components = [
                    'backup_path_edit',
                    'auto_backup_check',
                    'backup_interval_spin',
                    'backup_button',
                    'restore_button'
                ]
                
                for comp_name in backup_components:
                    if hasattr(tab_widget, comp_name):
                        print(f"       - {comp_name}: Available")
                    else:
                        print(f"       - {comp_name}: Not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings components detailed test failed: {e}")
        return False

def test_settings_language_integration():
    """Test settings language integration"""
    print("\n🌐 Testing Settings Language Integration...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create main window with settings
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        # Navigate to settings
        if 'settings' in main_window.nav_buttons:
            settings_button = main_window.nav_buttons['settings']
            settings_button.click()
            app.processEvents()
            
            current_widget = main_window.stacked_widget.currentWidget()
            if isinstance(current_widget, SettingsWidget):
                settings_widget = current_widget
                
                # Find general tab
                general_tab = None
                for i in range(settings_widget.tabs.count()):
                    tab_widget = settings_widget.tabs.widget(i)
                    if isinstance(tab_widget, GeneralSettingsTab):
                        general_tab = tab_widget
                        settings_widget.tabs.setCurrentIndex(i)
                        app.processEvents()
                        break
                
                if general_tab and hasattr(general_tab, 'language_combo'):
                    language_combo = general_tab.language_combo
                    
                    # Test each language
                    languages_tested = []
                    
                    for i in range(language_combo.count()):
                        lang_code = language_combo.itemData(i)
                        lang_name = language_combo.itemText(i)
                        
                        if lang_code:
                            print(f"   Testing {lang_name} ({lang_code})...")
                            
                            # Change language
                            language_combo.setCurrentIndex(i)
                            general_tab.on_language_changed()
                            app.processEvents()
                            time.sleep(0.1)
                            
                            # Check if language changed
                            current_lang = get_current_language()
                            if current_lang == lang_code:
                                print(f"     ✅ Language changed to {lang_code}")
                                
                                # Check tab texts
                                for j in range(settings_widget.tabs.count()):
                                    tab_text = settings_widget.tabs.tabText(j)
                                    print(f"       Tab {j}: '{tab_text}'")
                                
                                # Check navigation button
                                nav_text = main_window.nav_buttons['settings'].text()
                                print(f"       Settings button: '{nav_text}'")
                                
                                languages_tested.append(lang_code)
                            else:
                                print(f"     ❌ Language change failed")
                                return False
                    
                    print(f"   ✅ Tested {len(languages_tested)} languages successfully")
                else:
                    print("   ❌ General tab or language combo not found")
                    return False
            else:
                print("   ❌ Settings widget not loaded")
                return False
        else:
            print("   ❌ Settings button not found")
            return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Settings language integration test failed: {e}")
        return False

def test_settings_data_persistence():
    """Test settings data persistence"""
    print("\n💾 Testing Settings Data Persistence...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Test config manager functions
        print("   Testing config manager...")
        
        # Test company config
        try:
            company_config = config.get_company_config()
            print(f"   ✅ Company config: {len(company_config)} settings")
        except Exception as e:
            print(f"   ❌ Company config failed: {e}")
            return False
        
        # Test printer config
        try:
            printer_config = config.get_printer_config()
            print(f"   ✅ Printer config: {len(printer_config)} settings")
        except Exception as e:
            print(f"   ❌ Printer config failed: {e}")
            return False
        
        # Test setting and getting values
        original_language = config.get_language()
        original_theme = config.get_theme()
        
        # Test language persistence
        test_language = 'ar' if original_language != 'ar' else 'fr'
        config.set_language(test_language)
        
        # Create new config instance to test persistence
        from utils.config_manager import ConfigManager
        new_config = ConfigManager()
        
        saved_language = new_config.get_language()
        if saved_language == test_language:
            print(f"   ✅ Language persistence: {original_language} → {test_language}")
        else:
            print(f"   ❌ Language persistence failed: expected {test_language}, got {saved_language}")
            return False
        
        # Restore original language
        config.set_language(original_language)
        
        # Test theme persistence
        test_theme = 'dark' if original_theme != 'dark' else 'light'
        config.set_theme(test_theme)
        
        new_config2 = ConfigManager()
        saved_theme = new_config2.get_theme()
        
        if saved_theme == test_theme:
            print(f"   ✅ Theme persistence: {original_theme} → {test_theme}")
        else:
            print(f"   ❌ Theme persistence failed: expected {test_theme}, got {saved_theme}")
            return False
        
        # Restore original theme
        config.set_theme(original_theme)
        
        return True
        
    except Exception as e:
        print(f"❌ Settings data persistence test failed: {e}")
        return False

def test_settings_edge_cases():
    """Test settings edge cases"""
    print("\n🔍 Testing Settings Edge Cases...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create settings widget
        settings_widget = SettingsWidget()
        
        # Test rapid tab switching
        print("   Testing rapid tab switching...")
        
        for iteration in range(5):
            for i in range(settings_widget.tabs.count()):
                settings_widget.tabs.setCurrentIndex(i)
                app.processEvents()
            
            print(f"     ✅ Iteration {iteration + 1}: All tabs accessible")
        
        # Test language switching while in different tabs
        print("   Testing language switching in different tabs...")
        
        # Find general tab
        general_tab = None
        general_tab_index = -1
        
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, GeneralSettingsTab):
                general_tab = tab_widget
                general_tab_index = i
                break
        
        if general_tab and hasattr(general_tab, 'language_combo'):
            language_combo = general_tab.language_combo
            
            # Test language change from different tabs
            for i in range(settings_widget.tabs.count()):
                if i != general_tab_index:
                    # Switch to non-general tab
                    settings_widget.tabs.setCurrentIndex(i)
                    app.processEvents()
                    
                    # Switch to general tab
                    settings_widget.tabs.setCurrentIndex(general_tab_index)
                    app.processEvents()
                    
                    # Change language
                    current_index = language_combo.currentIndex()
                    new_index = (current_index + 1) % language_combo.count()
                    
                    language_combo.setCurrentIndex(new_index)
                    general_tab.on_language_changed()
                    app.processEvents()
                    
                    # Switch back to original tab
                    settings_widget.tabs.setCurrentIndex(i)
                    app.processEvents()
                    
                    print(f"     ✅ Language change from tab {i}: Success")
        
        # Test invalid data handling
        print("   Testing invalid data handling...")
        
        # Find company tab
        company_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, CompanySettingsTab):
                company_tab = tab_widget
                settings_widget.tabs.setCurrentIndex(i)
                app.processEvents()
                break
        
        if company_tab:
            # Test invalid email
            if hasattr(company_tab, 'company_email_edit'):
                email_field = company_tab.company_email_edit
                original_email = email_field.text()
                
                # Set invalid email
                email_field.setText("invalid-email-format")
                print("     ✅ Invalid email accepted (validation may be lenient)")
                
                # Restore original
                email_field.setText(original_email)
            
            # Test very long company name
            if hasattr(company_tab, 'company_name_edit'):
                name_field = company_tab.company_name_edit
                original_name = name_field.text()
                
                # Set very long name
                long_name = "A" * 1000
                name_field.setText(long_name)
                print("     ✅ Very long company name accepted")
                
                # Restore original
                name_field.setText(original_name)
        
        return True
        
    except Exception as e:
        print(f"❌ Settings edge cases test failed: {e}")
        return False

def test_settings_performance():
    """Test settings performance"""
    print("\n⚡ Testing Settings Performance...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        import time
        
        # Test widget creation performance
        start_time = time.time()
        settings_widget = SettingsWidget()
        creation_time = time.time() - start_time
        
        print(f"   ✅ Widget creation: {creation_time:.4f} seconds")
        
        # Test tab switching performance
        start_time = time.time()
        
        for i in range(settings_widget.tabs.count() * 5):  # Switch 5 times through all tabs
            tab_index = i % settings_widget.tabs.count()
            settings_widget.tabs.setCurrentIndex(tab_index)
            app.processEvents()
        
        switching_time = time.time() - start_time
        total_switches = settings_widget.tabs.count() * 5
        
        print(f"   ✅ Tab switching: {switching_time:.4f} seconds for {total_switches} switches")
        print(f"   ✅ Average per switch: {switching_time/total_switches:.4f} seconds")
        
        # Test language switching performance
        general_tab = None
        for i in range(settings_widget.tabs.count()):
            tab_widget = settings_widget.tabs.widget(i)
            if isinstance(tab_widget, GeneralSettingsTab):
                general_tab = tab_widget
                break
        
        if general_tab and hasattr(general_tab, 'language_combo'):
            language_combo = general_tab.language_combo
            
            start_time = time.time()
            
            # Switch languages multiple times
            for i in range(10):
                lang_index = i % language_combo.count()
                language_combo.setCurrentIndex(lang_index)
                general_tab.on_language_changed()
                app.processEvents()
            
            lang_switching_time = time.time() - start_time
            
            print(f"   ✅ Language switching: {lang_switching_time:.4f} seconds for 10 switches")
            print(f"   ✅ Average per language switch: {lang_switching_time/10:.4f} seconds")
        
        # Performance evaluation
        if creation_time < 1.0 and switching_time < 2.0:
            print("   ✅ Settings performance: Excellent")
        elif creation_time < 2.0 and switching_time < 5.0:
            print("   ✅ Settings performance: Good")
        else:
            print("   ⚠️ Settings performance: Could be improved")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings performance test failed: {e}")
        return False

def test_settings_error_handling():
    """Test settings error handling"""
    print("\n⚠️ Testing Settings Error Handling...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Test with corrupted config
        print("   Testing error handling...")
        
        # Test invalid language code
        try:
            config.set_language("invalid_lang")
            current_lang = config.get_language()
            print(f"   ✅ Invalid language handled: {current_lang}")
        except Exception as e:
            print(f"   ✅ Invalid language rejected: {type(e).__name__}")
        
        # Test invalid theme
        try:
            config.set_theme("invalid_theme")
            current_theme = config.get_theme()
            print(f"   ✅ Invalid theme handled: {current_theme}")
        except Exception as e:
            print(f"   ✅ Invalid theme rejected: {type(e).__name__}")
        
        # Test settings widget with missing components
        settings_widget = SettingsWidget()
        
        # Test accessing non-existent tab
        try:
            invalid_tab = settings_widget.tabs.widget(999)
            if invalid_tab is None:
                print("   ✅ Invalid tab index handled correctly")
            else:
                print("   ⚠️ Invalid tab index returned unexpected result")
        except Exception as e:
            print(f"   ✅ Invalid tab index rejected: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings error handling test failed: {e}")
        return False

def run_detailed_settings_test():
    """Run all detailed settings tests"""
    print("🧪 STARTING DETAILED SETTINGS SYSTEM TEST")
    print("=" * 60)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # Test 1: Settings Components Detailed
    test_results.append(("Settings Components", test_settings_components_detailed()))
    
    # Test 2: Settings Language Integration
    test_results.append(("Language Integration", test_settings_language_integration()))
    
    # Test 3: Settings Data Persistence
    test_results.append(("Data Persistence", test_settings_data_persistence()))
    
    # Test 4: Settings Edge Cases
    test_results.append(("Edge Cases", test_settings_edge_cases()))
    
    # Test 5: Settings Performance
    test_results.append(("Performance", test_settings_performance()))
    
    # Test 6: Settings Error Handling
    test_results.append(("Error Handling", test_settings_error_handling()))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 DETAILED SETTINGS TEST RESULTS")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL DETAILED SETTINGS TESTS PASSED! System is robust and reliable!")
    else:
        print("⚠️  Some detailed tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    run_detailed_settings_test()
