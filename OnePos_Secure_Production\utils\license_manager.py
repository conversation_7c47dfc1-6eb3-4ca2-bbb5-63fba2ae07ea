"""
نظام إدارة الترخيص والتفعيل لتطبيق OnePos
License and Activation Management System for OnePos
"""

import os
import json
import hashlib
import base64
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from PyQt5.QtCore import QTimer, pyqtSignal, QObject
from PyQt5.QtWidgets import QMessageBox
import uuid
import socket
import platform
import requests


class LicenseManager(QObject):
    """مدير الترخيص والتفعيل"""
    
    # إشارات
    license_expired = pyqtSignal()
    trial_warning = pyqtSignal(int)  # عدد الأيام المتبقية
    
    def __init__(self):
        super().__init__()
        self.license_file = "data/license.dat"
        self.trial_days = 3  # فترة التجربة 3 أيام
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_license_status)
        self.timer.start(60000)  # فحص كل دقيقة
        
        # مفتاح التشفير (في التطبيق الحقيقي يجب أن يكون مخفي أكثر)
        self.encryption_key = self._get_encryption_key()
        self.cipher = Fernet(self.encryption_key)
        
        # إنشاء مجلد البيانات إذا لم يكن موجود
        os.makedirs("data", exist_ok=True)
    
    def _get_encryption_key(self):
        """الحصول على مفتاح التشفير"""
        # في التطبيق الحقيقي، يجب استخدام مفتاح أكثر أماناً
        key_string = "OnePos_License_Key_2024_Secure"
        key_hash = hashlib.sha256(key_string.encode()).digest()
        return base64.urlsafe_b64encode(key_hash)
    
    def _get_machine_id(self):
        """الحصول على معرف فريد للجهاز"""
        try:
            # استخدام معرف الجهاز
            machine_id = str(uuid.getnode())
            return hashlib.md5(machine_id.encode()).hexdigest()[:16]
        except:
            return "DEFAULT_MACHINE_ID"

    def _get_machine_info(self):
        """جمع معلومات شاملة عن الجهاز"""
        try:
            machine_info = {
                'machine_id': self._get_machine_id(),
                'computer_name': platform.node(),
                'os_info': f"{platform.system()} {platform.release()} {platform.version()}",
                'ip_address': self._get_ip_address(),
                'mac_address': self._get_mac_address(),
                'processor': platform.processor(),
                'architecture': platform.architecture()[0]
            }
            return machine_info
        except Exception as e:
            print(f"خطأ في جمع معلومات الجهاز: {e}")
            return {
                'machine_id': self._get_machine_id(),
                'computer_name': 'Unknown',
                'os_info': 'Unknown',
                'ip_address': 'Unknown',
                'mac_address': 'Unknown',
                'processor': 'Unknown',
                'architecture': 'Unknown'
            }

    def _get_ip_address(self):
        """الحصول على عنوان IP"""
        try:
            # محاولة الحصول على IP العام
            response = requests.get('https://api.ipify.org?format=json', timeout=5)
            public_ip = response.json()['ip']
            return public_ip
        except:
            try:
                # الحصول على IP المحلي
                hostname = socket.gethostname()
                local_ip = socket.gethostbyname(hostname)
                return local_ip
            except:
                return "127.0.0.1"

    def _get_mac_address(self):
        """الحصول على عنوان MAC"""
        try:
            mac = uuid.getnode()
            mac_address = ':'.join(['{:02x}'.format((mac >> elements) & 0xff)
                                   for elements in range(0, 2*6, 2)][::-1])
            return mac_address
        except:
            return "00:00:00:00:00:00"
    
    def _generate_serial_code(self, machine_id, days=365):
        """توليد كود سيريال للجهاز"""
        # تاريخ انتهاء الصلاحية
        expiry_date = datetime.now() + timedelta(days=days)

        # بيانات الترخيص
        license_data = {
            'machine_id': machine_id,
            'expiry_date': expiry_date.isoformat(),
            'product': 'OnePos',
            'version': '1.0'
        }

        # تشفير البيانات
        encrypted_data = self.cipher.encrypt(json.dumps(license_data).encode())

        # تحويل إلى كود سيريال قابل للقراءة
        serial_code = base64.b64encode(encrypted_data).decode()

        # تقسيم الكود إلى مجموعات من 4 أحرف (بدون قطع الكود)
        formatted_code = '-'.join([serial_code[i:i+4] for i in range(0, len(serial_code), 4)])

        return formatted_code  # إرجاع الكود كاملاً بدون قطع
    
    def generate_serial_for_current_machine(self, days=365):
        """توليد كود سيريال للجهاز الحالي"""
        machine_id = self._get_machine_id()
        return self._generate_serial_code(machine_id, days)
    
    def validate_serial_code(self, serial_code):
        """التحقق من صحة كود السيريال"""
        try:
            print(f"🔍 فحص كود السيريال: {serial_code}")

            # إزالة الشرطات وفك التشفير
            clean_code = serial_code.replace('-', '').strip()
            print(f"🧹 الكود بعد التنظيف: {clean_code}")

            # فك تشفير base64
            try:
                encrypted_data = base64.b64decode(clean_code)
                print(f"✅ تم فك تشفير base64 بنجاح")
            except Exception as e:
                print(f"❌ خطأ في فك تشفير base64: {e}")
                return False, f"كود السيريال غير صحيح - خطأ في التشفير: {str(e)}"

            # فك تشفير البيانات
            try:
                decrypted_data = self.cipher.decrypt(encrypted_data)
                print(f"✅ تم فك التشفير بنجاح")
            except Exception as e:
                print(f"❌ خطأ في فك التشفير: {e}")
                return False, f"كود السيريال غير صحيح - خطأ في فك التشفير: {str(e)}"

            # تحليل JSON
            try:
                license_data = json.loads(decrypted_data.decode())
                print(f"✅ تم تحليل JSON بنجاح: {license_data}")
            except Exception as e:
                print(f"❌ خطأ في تحليل JSON: {e}")
                return False, f"كود السيريال غير صحيح - خطأ في البيانات: {str(e)}"

            # التحقق من معرف الجهاز
            current_machine_id = self._get_machine_id()
            license_machine_id = license_data.get('machine_id')
            print(f"🖥️ معرف الجهاز الحالي: {current_machine_id}")
            print(f"🔑 معرف الجهاز في الترخيص: {license_machine_id}")

            if license_machine_id != current_machine_id:
                return False, f"كود السيريال غير صالح لهذا الجهاز\nالجهاز الحالي: {current_machine_id}\nالجهاز المرخص: {license_machine_id}"

            # التحقق من تاريخ انتهاء الصلاحية
            expiry_date_str = license_data.get('expiry_date')
            try:
                expiry_date = datetime.fromisoformat(expiry_date_str)
                current_date = datetime.now()
                print(f"📅 تاريخ انتهاء الصلاحية: {expiry_date}")
                print(f"📅 التاريخ الحالي: {current_date}")

                if current_date > expiry_date:
                    return False, f"كود السيريال منتهي الصلاحية\nانتهى في: {expiry_date.strftime('%Y-%m-%d')}"
            except Exception as e:
                print(f"❌ خطأ في تحليل التاريخ: {e}")
                return False, f"كود السيريال غير صحيح - خطأ في التاريخ: {str(e)}"

            print(f"✅ كود السيريال صحيح ومقبول")
            return True, "كود السيريال صحيح"

        except Exception as e:
            print(f"❌ خطأ عام في فحص كود السيريال: {e}")
            import traceback
            traceback.print_exc()
            return False, f"كود السيريال غير صحيح: {str(e)}"
    
    def activate_license(self, serial_code):
        """تفعيل الترخيص بكود السيريال مع قاعدة البيانات"""
        try:
            # استيراد قاعدة بيانات الترخيص
            from utils.license_database import license_db

            # جمع معلومات الجهاز
            machine_info = self._get_machine_info()
            print(f"🖥️ معلومات الجهاز: {machine_info}")

            # التحقق والتفعيل عبر قاعدة البيانات
            success, message = license_db.validate_and_activate(serial_code, machine_info)

            if success:
                # حفظ بيانات الترخيص محلياً
                license_info = {
                    'serial_code': serial_code,
                    'activation_date': datetime.now().isoformat(),
                    'machine_id': machine_info['machine_id'],
                    'machine_fingerprint': machine_info,
                    'status': 'activated'
                }

                # تشفير وحفظ البيانات
                encrypted_info = self.cipher.encrypt(json.dumps(license_info).encode())

                with open(self.license_file, 'wb') as f:
                    f.write(encrypted_info)

                print(f"✅ تم تفعيل الترخيص بنجاح")
                return True, "تم تفعيل التطبيق بنجاح!"
            else:
                print(f"❌ فشل التفعيل: {message}")
                return False, message

        except Exception as e:
            print(f"❌ خطأ في التفعيل: {e}")
            import traceback
            traceback.print_exc()

            # العودة للطريقة القديمة في حالة الخطأ
            return self._activate_license_offline(serial_code)

    def _activate_license_offline(self, serial_code):
        """تفعيل الترخيص بدون قاعدة البيانات (طريقة احتياطية)"""
        is_valid, message = self.validate_serial_code(serial_code)

        if is_valid:
            # حفظ بيانات الترخيص
            license_info = {
                'serial_code': serial_code,
                'activation_date': datetime.now().isoformat(),
                'machine_id': self._get_machine_id(),
                'status': 'activated'
            }

            # تشفير وحفظ البيانات
            encrypted_info = self.cipher.encrypt(json.dumps(license_info).encode())

            with open(self.license_file, 'wb') as f:
                f.write(encrypted_info)

            return True, "تم تفعيل التطبيق بنجاح!"

        return False, message
    
    def get_license_status(self):
        """الحصول على حالة الترخيص"""
        # التحقق من وجود ملف الترخيص
        if not os.path.exists(self.license_file):
            return self._get_trial_status()
        
        try:
            # قراءة وفك تشفير بيانات الترخيص
            with open(self.license_file, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.cipher.decrypt(encrypted_data)
            license_info = json.loads(decrypted_data.decode())
            
            # التحقق من صحة الترخيص
            is_valid, message = self.validate_serial_code(license_info.get('serial_code'))
            
            if is_valid:
                return {
                    'status': 'activated',
                    'message': 'التطبيق مفعل',
                    'days_remaining': None,
                    'is_trial': False
                }
            else:
                # الترخيص غير صالح، العودة للتجربة
                return self._get_trial_status()
                
        except Exception as e:
            # خطأ في قراءة الترخيص، العودة للتجربة
            return self._get_trial_status()
    
    def _get_trial_status(self):
        """الحصول على حالة فترة التجربة"""
        trial_file = "data/trial.dat"
        
        # إذا لم يكن ملف التجربة موجود، إنشاؤه
        if not os.path.exists(trial_file):
            trial_start = datetime.now()
            trial_data = {
                'start_date': trial_start.isoformat(),
                'machine_id': self._get_machine_id()
            }
            
            encrypted_trial = self.cipher.encrypt(json.dumps(trial_data).encode())
            with open(trial_file, 'wb') as f:
                f.write(encrypted_trial)
        
        try:
            # قراءة بيانات التجربة
            with open(trial_file, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.cipher.decrypt(encrypted_data)
            trial_data = json.loads(decrypted_data.decode())
            
            # التحقق من معرف الجهاز
            if trial_data.get('machine_id') != self._get_machine_id():
                return {
                    'status': 'expired',
                    'message': 'فترة التجربة منتهية',
                    'days_remaining': 0,
                    'is_trial': True
                }
            
            # حساب الأيام المتبقية
            start_date = datetime.fromisoformat(trial_data.get('start_date'))
            days_passed = (datetime.now() - start_date).days
            days_remaining = max(0, self.trial_days - days_passed)
            
            if days_remaining > 0:
                return {
                    'status': 'trial',
                    'message': f'فترة تجربة - متبقي {days_remaining} أيام',
                    'days_remaining': days_remaining,
                    'is_trial': True
                }
            else:
                return {
                    'status': 'expired',
                    'message': 'فترة التجربة منتهية',
                    'days_remaining': 0,
                    'is_trial': True
                }
                
        except Exception as e:
            return {
                'status': 'expired',
                'message': 'خطأ في فترة التجربة',
                'days_remaining': 0,
                'is_trial': True
            }
    
    def check_license_status(self):
        """فحص حالة الترخيص دورياً"""
        status = self.get_license_status()
        
        if status['status'] == 'expired':
            self.license_expired.emit()
        elif status['status'] == 'trial' and status['days_remaining'] <= 1:
            self.trial_warning.emit(status['days_remaining'])
    
    def is_license_valid(self):
        """التحقق من صحة الترخيص"""
        status = self.get_license_status()
        return status['status'] in ['activated', 'trial']
    
    def get_trial_days_remaining(self):
        """الحصول على عدد أيام التجربة المتبقية"""
        status = self.get_license_status()
        return status.get('days_remaining', 0)


# إنشاء مثيل عام لمدير الترخيص
license_manager = LicenseManager()
