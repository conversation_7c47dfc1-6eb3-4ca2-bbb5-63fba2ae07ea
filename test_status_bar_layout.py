#!/usr/bin/env python3
"""
اختبار تخطيط شريط الحالة الجديد
Test New Status Bar Layout
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QLabel
from PyQt5.QtCore import QTimer
from models.user import User
from views.main_window import MainWindow
from utils.translator import translator, tr


def test_status_bar_layout():
    """اختبار تخطيط شريط الحالة الجديد"""
    
    app = QApplication(sys.argv)
    
    # إنشاء مستخدم تجريبي
    test_user = User(
        id=1,
        username="admin",
        full_name="أحمد محمد المدير",
        email="<EMAIL>",
        role="admin",
        permissions={"all": True}
    )
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow(test_user)
    main_window.show()
    
    print("📐 اختبار تخطيط شريط الحالة الجديد")
    print("=" * 60)
    
    # اختبار 1: فحص ترتيب العناصر
    print("\n1. فحص ترتيب العناصر:")
    
    # فحص العناصر الجانبية (addWidget - يسار)
    left_widgets = []
    # فحص العناصر الدائمة (addPermanentWidget - يمين)
    right_widgets = []
    
    # جمع العناصر من شريط الحالة
    for widget in main_window.status_bar.children():
        if isinstance(widget, QLabel) and widget.text():
            # تحديد موقع العنصر بناءً على محتواه
            text = widget.text()
            if "Database" in text or "قاعدة البيانات" in text:
                left_widgets.append(("Database Status", text))
            elif any(word in text for word in ["مفعل", "تجربة", "منتهي", "Activated", "Trial", "Expired"]):
                left_widgets.append(("License Status", text))
            elif "ASSANAJE_APP" in text or "حقوق" in text:
                right_widgets.append(("Copyright", text))
            elif "📅" in text and "🕒" in text:
                right_widgets.append(("DateTime", text))
    
    print("   📊 العناصر الجانبية (يسار):")
    for i, (name, text) in enumerate(left_widgets, 1):
        print(f"      {i}. {name}: {text}")
    
    print("   📊 العناصر الدائمة (يمين):")
    for i, (name, text) in enumerate(right_widgets, 1):
        print(f"      {i}. {name}: {text}")
    
    # اختبار 2: فحص نص حقوق الطبع
    print("\n2. فحص نص حقوق الطبع:")
    
    if hasattr(main_window, 'copyright_label'):
        copyright_text = main_window.copyright_label.text()
        print(f"   📊 النص الحالي: {copyright_text}")
        
        # التحقق من وجود العناصر المطلوبة
        required_elements = ["2025", "ASSANAJE_APP"]
        for element in required_elements:
            if element in copyright_text:
                print(f"      ✅ يحتوي على: {element}")
            else:
                print(f"      ❌ لا يحتوي على: {element}")
    else:
        print("   ❌ لم يتم العثور على تسمية حقوق الطبع")
    
    # اختبار 3: اختبار تغيير اللغة
    print("\n3. اختبار تغيير اللغة:")
    
    languages = [
        ("ar", "العربية"),
        ("en", "English"), 
        ("fr", "Français")
    ]
    
    for lang_code, lang_name in languages:
        print(f"   🌐 اختبار اللغة: {lang_name}")
        
        # تغيير اللغة
        translator.set_language(lang_code)
        main_window.retranslate_ui()
        
        # فحص نص حقوق الطبع المحدث
        if hasattr(main_window, 'copyright_label'):
            updated_copyright = main_window.copyright_label.text()
            print(f"      📊 حقوق الطبع: {updated_copyright}")
        
        # فحص نص قاعدة البيانات
        if hasattr(main_window, 'connection_label'):
            db_text = main_window.connection_label.text()
            print(f"      📊 قاعدة البيانات: {db_text}")
        
        # فحص نص الترخيص
        if hasattr(main_window, 'license_label'):
            license_text = main_window.license_label.text()
            print(f"      📊 الترخيص: {license_text}")
        
        # فحص التاريخ والساعة
        if hasattr(main_window, 'datetime_label'):
            datetime_text = main_window.datetime_label.text()
            print(f"      📊 التاريخ: {datetime_text}")
    
    # إعادة اللغة الأصلية
    translator.set_language("ar")
    main_window.retranslate_ui()
    
    # اختبار 4: فحص الأنماط والألوان
    print("\n4. فحص الأنماط والألوان:")
    
    elements_to_check = [
        ('connection_label', 'قاعدة البيانات'),
        ('license_label', 'الترخيص'),
        ('copyright_label', 'حقوق الطبع'),
        ('datetime_label', 'التاريخ والساعة')
    ]
    
    for attr_name, display_name in elements_to_check:
        if hasattr(main_window, attr_name):
            element = getattr(main_window, attr_name)
            style = element.styleSheet()
            
            print(f"   📊 {display_name}:")
            
            # فحص حجم الخط
            if "font-size" in style:
                if "11px" in style:
                    print("      ✅ حجم الخط: 11px (مناسب)")
                else:
                    print("      ⚠️ حجم خط مختلف")
            
            # فحص الحشو
            if "padding: 3px 8px" in style or "padding: 2px 10px" in style:
                print("      ✅ الحشو مناسب")
            else:
                print("      ⚠️ حشو مختلف")
            
            # فحص الحدود
            if "border: 1px" in style:
                print("      ✅ حدود رفيعة")
            else:
                print("      ⚠️ حدود مختلفة")
    
    # اختبار 5: فحص ارتفاع شريط الحالة
    print("\n5. فحص ارتفاع شريط الحالة:")
    
    if hasattr(main_window, 'status_bar'):
        height = main_window.status_bar.height()
        fixed_height = main_window.status_bar.minimumHeight()
        
        print(f"   📊 الارتفاع الحالي: {height}px")
        print(f"   📊 الارتفاع المحدد: {fixed_height}px")
        
        if height <= 35:
            print("   ✅ ارتفاع مناسب (≤35px)")
        else:
            print("   ⚠️ ارتفاع كبير (>35px)")
    
    # اختبار 6: فحص لون الخلفية
    print("\n6. فحص لون الخلفية:")
    
    if hasattr(main_window, 'status_bar'):
        style = main_window.status_bar.styleSheet()
        
        if "#ecf0f1" in style:
            print("   ✅ لون الخلفية الأصلي مستعاد")
        else:
            print("   ⚠️ لون خلفية مختلف")
        
        if "border-top: 1px" in style:
            print("   ✅ حدود رفيعة مستعادة")
        else:
            print("   ⚠️ حدود مختلفة")
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار")
    
    return app


def test_layout_positions():
    """اختبار مواقع العناصر"""
    print("\n7. اختبار مواقع العناصر:")
    
    expected_layout = {
        "يسار": ["Database Status", "License Status"],
        "وسط": ["Copyright Text"],
        "يمين": ["Date & Time"]
    }
    
    for position, elements in expected_layout.items():
        print(f"   📍 {position}:")
        for element in elements:
            print(f"      • {element}")


def test_translations():
    """اختبار الترجمات"""
    print("\n8. اختبار الترجمات:")
    
    translations = {
        "ar": "© 2025 ASSANAJE_APP - جميع الحقوق محفوظة",
        "en": "© 2025 ASSANAJE_APP - All Rights Reserved",
        "fr": "© 2025 ASSANAJE_APP - Tous Droits Réservés"
    }
    
    for lang_code, expected_text in translations.items():
        translator.set_language(lang_code)
        actual_text = tr("common.copyright_text")
        
        if actual_text == expected_text:
            print(f"   ✅ {lang_code}: {actual_text}")
        else:
            print(f"   ❌ {lang_code}: {actual_text} (متوقع: {expected_text})")
    
    # إعادة اللغة الأصلية
    translator.set_language("ar")


if __name__ == "__main__":
    try:
        print("🚀 بدء اختبار تخطيط شريط الحالة الجديد")
        print("🎯 الهدف: التأكد من ترتيب العناصر وإضافة حقوق الطبع")
        
        app = test_status_bar_layout()
        
        # اختبارات إضافية
        test_layout_positions()
        test_translations()
        
        print("\n🎉 تم الانتهاء من جميع الاختبارات!")
        print("\n📝 ملخص التغييرات:")
        print("   ✅ نقل Database و License إلى اليسار")
        print("   ✅ إضافة نص حقوق الطبع في الوسط")
        print("   ✅ إبقاء التاريخ والساعة في اليمين")
        print("   ✅ استعادة اللون الأصلي لشريط الحالة")
        print("   ✅ ترجمة نص حقوق الطبع لجميع اللغات")
        
        print("\n🎯 التخطيط النهائي:")
        print("   [يسار: Database + License] [وسط: © 2025 ASSANAJE_APP] [يمين: التاريخ والساعة]")
        
        # تشغيل التطبيق لفترة قصيرة للاختبار اليدوي
        QTimer.singleShot(5000, app.quit)  # إغلاق بعد 5 ثوان
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
