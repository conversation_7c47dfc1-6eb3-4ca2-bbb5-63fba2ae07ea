#!/usr/bin/env python3
"""
اختبار التخطيط الجديد - الشريط العلوي بدلاً من القائمة الجانبية
Test New Layout - Top Navigation Bar instead of Sidebar
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from views.main_window import MainWindow
from models.user import User
import time

def test_new_layout():
    """اختبار التخطيط الجديد"""
    print("🔄 اختبار التخطيط الجديد - الشريط العلوي")
    print("=" * 50)
    
    try:
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # الحصول على مستخدم للاختبار
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow(admin_user)
        
        print("🖼️ عرض النافذة مع التخطيط الجديد...")
        main_window.show()
        
        # معالجة الأحداث
        app.processEvents()
        
        print("\n🔍 فحص التخطيط الجديد:")
        
        # فحص الشريط العلوي
        if hasattr(main_window, 'top_nav_frame'):
            print("   ✅ الشريط العلوي موجود")
            
            # فحص أزرار التنقل
            if hasattr(main_window, 'nav_buttons'):
                nav_count = len(main_window.nav_buttons)
                print(f"   ✅ أزرار التنقل: {nav_count} أزرار")
                
                # عرض أسماء الأزرار
                for module_name, button in main_window.nav_buttons.items():
                    print(f"      - {button.text()} ({module_name})")
            else:
                print("   ❌ أزرار التنقل غير موجودة")
                return False
        else:
            print("   ❌ الشريط العلوي غير موجود")
            return False
        
        # فحص منطقة المحتوى
        if hasattr(main_window, 'content_frame'):
            print("   ✅ منطقة المحتوى موجودة")
            
            # فحص عدد الويدجت
            widget_count = main_window.content_frame.count()
            print(f"   📦 عدد الويدجت المحملة: {widget_count}")
        else:
            print("   ❌ منطقة المحتوى غير موجودة")
            return False
        
        # اختبار التنقل بين الأقسام
        print("\n🧪 اختبار التنقل بين الأقسام:")
        
        test_modules = ['pos', 'products', 'sales', 'customers']
        
        for module in test_modules:
            if module in main_window.nav_buttons:
                print(f"   🔄 تحميل قسم: {module}")
                main_window.load_module(module)
                app.processEvents()
                
                # فحص الزر المحدد
                button = main_window.nav_buttons[module]
                if button.isChecked():
                    print(f"   ✅ زر {module} محدد بشكل صحيح")
                else:
                    print(f"   ⚠️ زر {module} غير محدد")
                
                # فحص عنوان النافذة
                window_title = main_window.windowTitle()
                if module.upper() in window_title.upper():
                    print(f"   ✅ عنوان النافذة محدث: {window_title}")
                else:
                    print(f"   ⚠️ عنوان النافذة لم يتحدث: {window_title}")
        
        print("\n🎨 مميزات التخطيط الجديد:")
        print("   ✨ مساحة أكبر للمحتوى (لا توجد قائمة جانبية)")
        print("   🔝 شريط تنقل علوي أنيق ومنظم")
        print("   🎯 أزرار ملونة وواضحة للأقسام")
        print("   📱 تصميم عصري ومتجاوب")
        print("   👤 معلومات المستخدم وزر الخروج في الشريط")
        print("   🏪 شعار التطبيق في الشريط العلوي")
        
        print("\n📏 قياسات النافذة:")
        size = main_window.size()
        print(f"   - العرض: {size.width()}px")
        print(f"   - الارتفاع: {size.height()}px")
        print(f"   - الحد الأدنى: 1200x800px")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التخطيط: {e}")
        return False

def test_navigation_functionality():
    """اختبار وظائف التنقل"""
    print("\n🧭 اختبار وظائف التنقل")
    print("=" * 30)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        main_window = MainWindow(admin_user)
        
        # اختبار جميع الأقسام
        all_modules = ['pos', 'products', 'sales', 'customers', 'purchases', 'users', 'reports', 'performance', 'settings']
        
        working_modules = 0
        
        for module in all_modules:
            try:
                main_window.load_module(module)
                app.processEvents()
                working_modules += 1
                print(f"   ✅ {module}: يعمل")
            except Exception as e:
                print(f"   ❌ {module}: خطأ - {e}")
        
        print(f"\n📊 النتيجة: {working_modules}/{len(all_modules)} أقسام تعمل")
        
        return working_modules == len(all_modules)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التنقل: {e}")
        return False

def run_layout_test():
    """تشغيل جميع اختبارات التخطيط"""
    print("🎯 بدء اختبار التخطيط الجديد")
    print("=" * 60)
    
    results = []
    
    # اختبار 1: التخطيط الجديد
    results.append(("التخطيط الجديد", test_new_layout()))
    
    # اختبار 2: وظائف التنقل
    results.append(("وظائف التنقل", test_navigation_functionality()))
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار التخطيط الجديد")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 التخطيط الجديد يعمل بشكل مثالي!")
        print("✨ الشريط العلوي جاهز والمساحة محسنة")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_layout_test()
