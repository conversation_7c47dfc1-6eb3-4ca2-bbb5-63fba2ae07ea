@echo off
echo Building OnePos with permission fixes...

echo Cleaning previous builds...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

echo Building application...
pyinstaller --onedir --windowed --icon=app_icon.ico --name=OnePos main.py --clean

if exist "dist\OnePos\OnePos.exe" (
    echo Build successful!
    echo Executable: dist\OnePos\OnePos.exe
) else (
    echo Build failed!
    pause
    exit /b 1
)

echo Building installer...
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" "onepos_simple.iss"
    echo Installer build completed!
) else (
    echo Inno Setup not found. Install from: https://jrsoftware.org/isdl.php
)

pause
