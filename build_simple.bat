@echo off
echo ========================================
echo OnePos - Build Script
echo ========================================

:: Check Python
python --version
if %errorlevel% neq 0 (
    echo Python not found!
    pause
    exit /b 1
)

:: Install PyInstaller
echo Installing PyInstaller...
pip install pyinstaller

:: Install dependencies
echo Installing dependencies...
pip install PyQt5 reportlab pillow pyserial hidapi numpy

:: Clean previous builds
echo Cleaning previous builds...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

:: Build with PyInstaller
echo Building application...
pyinstaller --onedir --windowed --icon=app_icon.ico --name=OnePos main.py

:: Check result
if exist "dist\OnePos\OnePos.exe" (
    echo Build successful!
    echo Executable: dist\OnePos\OnePos.exe
) else (
    echo Build failed!
)

pause
