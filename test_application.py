#!/usr/bin/env python3
"""
Comprehensive testing script for OnePos POS System
Tests all major functionalities to ensure everything works correctly
"""

import sys
import os
import sqlite3
from datetime import datetime, date
import traceback

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """Test database connection and basic operations"""
    print("🔍 Testing database connection...")
    try:
        from models.database import get_db_connection
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Test basic query
        cursor.execute("SELECT COUNT(*) FROM products")
        product_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM customers")
        customer_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM sales")
        sales_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM users")
        users_count = cursor.fetchone()[0]
        
        # Don't close the connection as it's shared
        
        print(f"✅ Database connection successful!")
        print(f"   📦 Products: {product_count}")
        print(f"   🧍 Customers: {customer_count}")
        print(f"   💰 Sales: {sales_count}")
        print(f"   👤 Users: {users_count}")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_product_operations():
    """Test product CRUD operations"""
    print("\n🔍 Testing product operations...")
    try:
        from models.product import Product, Category
        
        # Test getting all products
        products = Product.get_all()
        print(f"✅ Retrieved {len(products)} products")
        
        # Test getting categories
        categories = Category.get_all()
        print(f"✅ Retrieved {len(categories)} categories")
        
        # Test creating a new product with unique SKU
        import random
        unique_sku = f"TEST{random.randint(1000, 9999)}"
        unique_barcode = f"123456789{random.randint(1000, 9999)}"

        test_product_data = {
            'name': 'Test Product',
            'description': 'Test Description',
            'category_id': 1,
            'barcode': unique_barcode,
            'sku': unique_sku,
            'cost_price': 10.00,
            'selling_price': 15.00,
            'stock': 100,
            'min_stock': 10,
            'unit': 'piece',
            'active': True
        }
        
        product_id = Product.create(**test_product_data)
        if product_id:
            print(f"✅ Created test product with ID: {product_id}")
            
            # Test updating the product
            updated = Product.update(product_id, name='Updated Test Product', stock=150)
            if updated:
                print("✅ Updated test product successfully")
            
            # Test deleting the product
            deleted = Product.delete(product_id)
            if deleted:
                print("✅ Deleted test product successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Product operations failed: {e}")
        traceback.print_exc()
        return False

def test_customer_operations():
    """Test customer CRUD operations"""
    print("\n🔍 Testing customer operations...")
    try:
        from models.customer import Customer
        
        # Test getting all customers
        customers = Customer.get_all()
        print(f"✅ Retrieved {len(customers)} customers")
        
        # Test creating a new customer
        test_customer_data = {
            'name': 'Test Customer',
            'phone': '1234567890',
            'email': '<EMAIL>',
            'address': 'Test Address',
            'tax_number': 'TAX123',
            'credit_limit': 1000.00,
            'balance': 0.00,
            'active': True
        }
        
        customer_id = Customer.create(**test_customer_data)
        if customer_id:
            print(f"✅ Created test customer with ID: {customer_id}")
            
            # Test updating the customer
            updated = Customer.update(customer_id, name='Updated Test Customer', phone='0987654321')
            if updated:
                print("✅ Updated test customer successfully")
            
            # Test deleting the customer
            deleted = Customer.delete(customer_id)
            if deleted:
                print("✅ Deleted test customer successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Customer operations failed: {e}")
        traceback.print_exc()
        return False

def test_user_operations():
    """Test user authentication and operations"""
    print("\n🔍 Testing user operations...")
    try:
        from models.user import User, UserSession
        
        # Test getting all users
        users = User.get_all()
        print(f"✅ Retrieved {len(users)} users")
        
        # Test authentication with default admin user
        user = User.authenticate('admin', 'admin123')
        if user:
            print("✅ Admin authentication successful")

            # Get user ID (user is a User object, not dict)
            user_id = user.id if hasattr(user, 'id') else 1

            # Test creating a session
            session = UserSession.create_session(user_id)
            if session:
                print(f"✅ Created user session: {session}")

                # Test validating session
                valid = UserSession.validate_session(session)
                if valid:
                    print("✅ Session validation successful")

                # Test ending session
                UserSession.end_session(session)
                print("✅ Session ended successfully")
        else:
            print("❌ Admin authentication failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ User operations failed: {e}")
        traceback.print_exc()
        return False

def test_sales_operations():
    """Test sales operations"""
    print("\n🔍 Testing sales operations...")
    try:
        from models.sale import Sale, SaleItem
        from models.product import Product
        
        # Test getting all sales
        sales = Sale.get_all()
        print(f"✅ Retrieved {len(sales)} sales")
        
        # Test creating a new sale
        products = Product.get_all()
        if products:
            # Prepare sale items
            test_items = [{
                'product_id': products[0]['id'],
                'quantity': 1,
                'unit_price': 15.00,
                'discount_amount': 0.00
            }]

            # Create sale with items
            sale_id = Sale.create(
                user_id=1,  # Admin user
                items=test_items,
                customer_id=None,  # Walk-in customer
                payment_method='cash',
                paid_amount=20.00,
                tax_rate=10.0,
                discount_amount=0.00
            )

            if sale_id:
                print(f"✅ Created test sale with ID: {sale_id}")

                # Test getting sale details
                sale_details = Sale.get_by_id(sale_id)
                if sale_details:
                    print("✅ Retrieved sale details successfully")

                # Test deleting the sale (cleanup)
                deleted = Sale.delete(sale_id)
                if deleted:
                    print("✅ Deleted test sale successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Sales operations failed: {e}")
        traceback.print_exc()
        return False

def test_config_operations():
    """Test configuration operations"""
    print("\n🔍 Testing configuration operations...")
    try:
        from utils.config_manager import config
        
        # Test getting configuration values
        language = config.get_language()
        theme = config.get_theme()
        currency = config.get_currency()
        tax_rate = config.get_tax_rate()
        
        print(f"✅ Configuration loaded successfully:")
        print(f"   🌐 Language: {language}")
        print(f"   🎨 Theme: {theme}")
        print(f"   💰 Currency: {currency}")
        print(f"   📊 Tax Rate: {tax_rate}%")
        
        # Test setting and getting a value
        original_currency = config.get_currency()
        config.set_currency("USD")
        new_currency = config.get_currency()
        config.set_currency(original_currency)  # Restore original
        
        if new_currency == "USD":
            print("✅ Configuration update successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration operations failed: {e}")
        traceback.print_exc()
        return False

def test_translation_system():
    """Test translation system"""
    print("\n🔍 Testing translation system...")
    try:
        from utils.translator import tr, translator
        
        # Test getting translations
        pos_title = tr("pos.title")
        products_title = tr("products.title")
        customers_title = tr("customers.title")
        
        print(f"✅ Translations loaded successfully:")
        print(f"   🛒 POS: {pos_title}")
        print(f"   📦 Products: {products_title}")
        print(f"   🧍 Customers: {customers_title}")
        
        # Test changing language
        original_lang = translator.current_language
        translator.set_language('en')
        english_title = tr("pos.title")
        translator.set_language('ar')
        arabic_title = tr("pos.title")
        translator.set_language(original_lang)  # Restore original
        
        print(f"✅ Language switching successful:")
        print(f"   🇺🇸 English: {english_title}")
        print(f"   🇸🇦 Arabic: {arabic_title}")
        
        return True
        
    except Exception as e:
        print(f"❌ Translation system failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run comprehensive tests"""
    print("🚀 Starting comprehensive OnePos testing...\n")
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Product Operations", test_product_operations),
        ("Customer Operations", test_customer_operations),
        ("User Operations", test_user_operations),
        ("Sales Operations", test_sales_operations),
        ("Configuration", test_config_operations),
        ("Translation System", test_translation_system),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 Running: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*50}")
    print("📊 TEST SUMMARY")
    print('='*50)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 All tests passed! The application is ready for use.")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review and fix the issues.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
