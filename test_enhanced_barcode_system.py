#!/usr/bin/env python3
"""
اختبار نظام ماسحات الباركود المحسن - OnePos
Test Enhanced Barcode Scanner System
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.main_window import MainWindow
from models.user import User
from utils.barcode_scanner_manager import barcode_scanner_manager
from utils.translator import translator, tr

def test_barcode_scanner_detection():
    """اختبار كشف ماسحات الباركود"""
    print("📱 اختبار كشف ماسحات الباركود")
    print("=" * 40)
    
    try:
        # تحديث قائمة الماسحات
        success = barcode_scanner_manager.refresh_scanners()
        
        if success:
            print("✅ تم تحديث قائمة الماسحات بنجاح")
        else:
            print("❌ فشل في تحديث قائمة الماسحات")
            return False
        
        # عرض الماسحات المكتشفة
        all_scanners = barcode_scanner_manager.get_available_scanners()
        usb_scanners = barcode_scanner_manager.get_usb_scanners()
        serial_scanners = barcode_scanner_manager.get_serial_scanners()
        bluetooth_scanners = barcode_scanner_manager.get_bluetooth_scanners()
        
        print(f"📊 إجمالي الماسحات: {len(all_scanners)}")
        print(f"🔌 ماسحات USB: {len(usb_scanners)}")
        print(f"🔗 ماسحات Serial: {len(serial_scanners)}")
        print(f"📶 ماسحات Bluetooth: {len(bluetooth_scanners)}")
        
        print("\n📋 قائمة الماسحات المتاحة:")
        for scanner_id, data in all_scanners.items():
            scanner_type = data['type']
            if scanner_type == 'USB' or scanner_type == 'USB HID':
                icon = "🔌"
            elif scanner_type == 'Serial':
                icon = "🔗"
            elif scanner_type == 'Bluetooth':
                icon = "📶"
            elif scanner_type == 'Camera':
                icon = "📷"
            else:
                icon = "❓"
            
            status = data.get('status', 'غير معروف')
            print(f"   {icon} {scanner_type} {data['name']} - {status}")
            
            if scanner_id == 'camera_0':
                print(f"      ⭐ ماسح الكاميرا المدمج")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار كشف الماسحات: {e}")
        return False

def test_scanner_assignment():
    """اختبار تعيين الماسحات"""
    print("\n⚙️ اختبار تعيين الماسحات")
    print("=" * 35)
    
    try:
        # عرض التعيينات الحالية
        active_scanner = barcode_scanner_manager.get_active_scanner()
        
        print("📋 التعيينات الحالية:")
        if active_scanner:
            scanner_data = barcode_scanner_manager.available_scanners.get(active_scanner)
            if scanner_data:
                print(f"   🎯 الماسح النشط: {scanner_data['name']}")
            else:
                print(f"   🎯 الماسح النشط: {active_scanner} (غير متاح)")
        else:
            print("   🎯 الماسح النشط: غير محدد")
        
        # اختبار التعيين التلقائي
        barcode_scanner_manager.auto_assign_scanner()
        
        new_active = barcode_scanner_manager.get_active_scanner()
        if new_active:
            scanner_data = barcode_scanner_manager.available_scanners.get(new_active)
            if scanner_data:
                print(f"🔄 تم تعيين الماسح تلقائياً: {scanner_data['name']}")
            else:
                print(f"🔄 تم تعيين الماسح تلقائياً: {new_active}")
        
        # اختبار حفظ الإعدادات
        if barcode_scanner_manager.save_settings():
            print("✅ تم حفظ إعدادات الماسحات")
        else:
            print("❌ فشل في حفظ إعدادات الماسحات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تعيين الماسحات: {e}")
        return False

def test_pos_barcode_integration():
    """اختبار تكامل الباركود مع POS"""
    print("\n🧾 اختبار تكامل الباركود مع POS")
    print("=" * 40)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        main_window = MainWindow(admin_user)
        
        # الانتقال لقسم POS
        main_window.load_module('pos')
        app.processEvents()
        
        # البحث عن واجهة POS
        pos_widget = None
        for i in range(main_window.content_frame.count()):
            widget = main_window.content_frame.widget(i)
            if hasattr(widget, 'on_barcode_scanned'):
                pos_widget = widget
                break
        
        if pos_widget:
            print("✅ تم العثور على واجهة POS")
            
            # فحص ربط إشارات الباركود
            if hasattr(pos_widget, 'on_barcode_scanned'):
                print("✅ دالة معالجة الباركود موجودة")
                
                # محاكاة مسح باركود
                test_barcode = "1234567890123"
                print(f"🧪 محاكاة مسح باركود: {test_barcode}")
                
                # استدعاء دالة معالجة الباركود مباشرة
                try:
                    pos_widget.on_barcode_scanned(test_barcode)
                    print("✅ تم معالجة الباركود بنجاح")
                except Exception as e:
                    print(f"⚠️ خطأ في معالجة الباركود: {e}")
            else:
                print("❌ دالة معالجة الباركود غير موجودة")
                return False
        else:
            print("❌ لم يتم العثور على واجهة POS")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تكامل POS: {e}")
        return False

def test_barcode_settings_ui():
    """اختبار واجهة إعدادات الباركود"""
    print("\n⚙️ اختبار واجهة إعدادات الباركود")
    print("=" * 45)

    try:
        # اختبار مبسط - فحص وجود الكلاسات والوحدات
        from views.barcode_settings_widget import BarcodeScannerSettingsWidget
        print("✅ تم استيراد BarcodeScannerSettingsWidget بنجاح")

        # فحص وجود الترجمات
        barcode_translations = [
            'barcode.title',
            'barcode.available_scanners',
            'barcode.refresh_scanners',
            'barcode.save_settings'
        ]

        for key in barcode_translations:
            translation = tr(key)
            if translation != key:  # إذا وجدت ترجمة
                print(f"   ✅ ترجمة {key}: موجودة")
            else:
                print(f"   ❌ ترجمة {key}: مفقودة")

        # فحص إضافة التبويب في الإعدادات
        try:
            from views.settings_widget import SettingsWidget
            print("✅ تم استيراد SettingsWidget مع دعم الباركود")
        except ImportError as e:
            print(f"❌ خطأ في استيراد SettingsWidget: {e}")
            return False

        # فحص وجود مدير الماسحات
        if hasattr(barcode_scanner_manager, 'get_available_scanners'):
            print("✅ مدير الماسحات يعمل بشكل صحيح")
        else:
            print("❌ مدير الماسحات لا يعمل")
            return False

        print("✅ جميع مكونات واجهة الباركود متوفرة")
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة الإعدادات: {e}")
        return False

def test_barcode_translations():
    """اختبار ترجمات الباركود"""
    print("\n🌍 اختبار ترجمات الباركود")
    print("=" * 35)
    
    languages = [
        ('ar', 'العربية'),
        ('en', 'English'),
        ('fr', 'Français')
    ]
    
    expected_translations = {
        'ar': {
            'title': 'إعدادات ماسحات الباركود',
            'available_scanners': 'الماسحات المتاحة',
            'refresh_scanners': 'تحديث الماسحات',
            'save_settings': 'حفظ الإعدادات'
        },
        'en': {
            'title': 'Barcode Scanner Settings',
            'available_scanners': 'Available Scanners',
            'refresh_scanners': 'Refresh Scanners',
            'save_settings': 'Save Settings'
        },
        'fr': {
            'title': 'Paramètres de Lecteur de Code-barres',
            'available_scanners': 'Lecteurs Disponibles',
            'refresh_scanners': 'Actualiser les Lecteurs',
            'save_settings': 'Enregistrer les Paramètres'
        }
    }
    
    all_correct = True
    
    try:
        for lang_code, lang_name in languages:
            print(f"\n📝 اختبار اللغة: {lang_name} ({lang_code})")
            
            # تغيير اللغة
            translator.set_language(lang_code)
            
            # اختبار الترجمات
            for key, expected in expected_translations[lang_code].items():
                actual = tr(f"barcode.{key}")
                
                if actual == expected:
                    print(f"   ✅ {key}: {actual}")
                else:
                    print(f"   ❌ {key}: متوقع '{expected}', الحالي '{actual}'")
                    all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ترجمات الباركود: {e}")
        return False

def test_scanner_functionality():
    """اختبار وظائف الماسح"""
    print("\n🧪 اختبار وظائف الماسح")
    print("=" * 30)
    
    try:
        # اختبار الماسحات المتاحة
        scanners = barcode_scanner_manager.get_available_scanners()
        
        if not scanners:
            print("⚠️ لا توجد ماسحات متاحة للاختبار")
            return True  # ليس خطأ، فقط لا توجد ماسحات
        
        # اختبار كل ماسح
        for scanner_id, data in scanners.items():
            print(f"\n🔍 اختبار الماسح: {data['name']}")
            
            try:
                success, message = barcode_scanner_manager.test_scanner(scanner_id)
                
                if success:
                    print(f"   ✅ الاختبار نجح: {message}")
                else:
                    print(f"   ❌ الاختبار فشل: {message}")
                    
            except Exception as e:
                print(f"   ❌ خطأ في الاختبار: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظائف الماسح: {e}")
        return False

def run_enhanced_barcode_system_test():
    """تشغيل جميع اختبارات نظام الباركود المحسن"""
    print("📱 بدء اختبار نظام ماسحات الباركود المحسن")
    print("=" * 70)
    
    results = []
    
    # اختبار 1: كشف الماسحات
    results.append(("كشف ماسحات الباركود", test_barcode_scanner_detection()))
    
    # اختبار 2: تعيين الماسحات
    results.append(("تعيين الماسحات", test_scanner_assignment()))
    
    # اختبار 3: تكامل POS
    results.append(("تكامل الباركود مع POS", test_pos_barcode_integration()))
    
    # اختبار 4: واجهة الإعدادات
    results.append(("واجهة إعدادات الباركود", test_barcode_settings_ui()))
    
    # اختبار 5: الترجمات
    results.append(("ترجمات الباركود", test_barcode_translations()))
    
    # اختبار 6: وظائف الماسح
    results.append(("وظائف الماسح", test_scanner_functionality()))
    
    # النتائج النهائية
    print("\n" + "=" * 70)
    print("📊 نتائج اختبار نظام ماسحات الباركود المحسن")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print("=" * 70)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 جميع اختبارات نظام الباركود تعمل بشكل مثالي!")
        print("✨ النظام جاهز للاستخدام مع الكشف التلقائي والترجمات الكاملة")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_enhanced_barcode_system_test()
