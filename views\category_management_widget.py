#!/usr/bin/env python3
"""
Category Management Widget
واجهة إدارة الفئات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                             QMessageBox, QDialog, QFormLayout, QTextEdit,
                             QHeaderView, QFrame, QSplitter, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor
from models.database import Database
from utils.translation_manager import tr


class CategoryDialog(QDialog):
    """نافذة إضافة/تعديل الفئة"""
    
    def __init__(self, parent=None, category_data=None):
        super().__init__(parent)
        self.category_data = category_data
        self.is_edit_mode = category_data is not None
        self.init_ui()
        
        if self.is_edit_mode:
            self.load_category_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(tr("edit_category") if self.is_edit_mode else tr("add_category"))
        self.setModal(True)
        self.setFixedSize(400, 300)
        
        # تخطيط رئيسي
        layout = QVBoxLayout(self)
        
        # عنوان النافذة
        title_label = QLabel(tr("edit_category") if self.is_edit_mode else tr("add_category"))
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # نموذج البيانات
        form_group = QGroupBox(tr("category_information"))
        form_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        form_layout = QFormLayout(form_group)
        
        # اسم الفئة
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText(tr("enter_category_name"))
        self.name_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        form_layout.addRow(tr("category_name") + ":", self.name_edit)
        
        # وصف الفئة
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText(tr("enter_category_description"))
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setStyleSheet("""
            QTextEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 12px;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        form_layout.addRow(tr("description") + ":", self.description_edit)
        
        layout.addWidget(form_group)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_button = QPushButton(tr("save"))
        self.save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #1e8449);
            }
            QPushButton:pressed {
                background: #1e8449;
            }
        """)
        self.save_button.clicked.connect(self.save_category)
        
        # زر الإلغاء
        self.cancel_button = QPushButton(tr("cancel"))
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #95a5a6, stop:1 #7f8c8d);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7f8c8d, stop:1 #6c7b7d);
            }
            QPushButton:pressed {
                background: #6c7b7d;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        layout.addLayout(buttons_layout)
    
    def load_category_data(self):
        """تحميل بيانات الفئة للتعديل"""
        if self.category_data:
            self.name_edit.setText(self.category_data.get('name', ''))
            self.description_edit.setPlainText(self.category_data.get('description', ''))
    
    def save_category(self):
        """حفظ الفئة"""
        name = self.name_edit.text().strip()
        description = self.description_edit.toPlainText().strip()
        
        if not name:
            QMessageBox.warning(self, tr("warning"), tr("category_name_required"))
            return
        
        try:
            db = Database()
            
            if self.is_edit_mode:
                # تحديث الفئة
                db.cursor.execute("""
                    UPDATE categories 
                    SET name = ?, description = ?
                    WHERE id = ?
                """, (name, description, self.category_data['id']))
                message = tr("category_updated")
            else:
                # إضافة فئة جديدة
                db.cursor.execute("""
                    INSERT INTO categories (name, description)
                    VALUES (?, ?)
                """, (name, description))
                message = tr("category_created")
            
            db.connection.commit()
            db.connection.close()
            
            QMessageBox.information(self, tr("success"), message)
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('database_error')}: {str(e)}")


class CategoryManagementWidget(QWidget):
    """واجهة إدارة الفئات"""
    
    category_updated = pyqtSignal()  # إشارة تحديث الفئات
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_categories()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # عنوان الصفحة
        title_label = QLabel(tr("category_management"))
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                padding: 15px;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        # زر إضافة فئة
        self.add_button = QPushButton(f"➕ {tr('add_category')}")
        self.add_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #1e8449);
            }
        """)
        self.add_button.clicked.connect(self.add_category)
        
        # زر تعديل فئة
        self.edit_button = QPushButton(f"✏️ {tr('edit_category')}")
        self.edit_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f39c12, stop:1 #e67e22);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e67e22, stop:1 #d35400);
            }
        """)
        self.edit_button.clicked.connect(self.edit_category)
        self.edit_button.setEnabled(False)
        
        # زر حذف فئة
        self.delete_button = QPushButton(f"🗑️ {tr('delete_category')}")
        self.delete_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
        """)
        self.delete_button.clicked.connect(self.delete_category)
        self.delete_button.setEnabled(False)
        
        # زر تحديث
        self.refresh_button = QPushButton(f"🔄 {tr('refresh')}")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
        """)
        self.refresh_button.clicked.connect(self.load_categories)
        
        toolbar_layout.addWidget(self.add_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addWidget(self.delete_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.refresh_button)
        
        layout.addLayout(toolbar_layout)
        
        # جدول الفئات
        self.categories_table = QTableWidget()
        self.categories_table.setColumnCount(3)
        self.categories_table.setHorizontalHeaderLabels([
            tr("category_name"),
            tr("description"),
            tr("products_count")
        ])
        
        # تنسيق الجدول
        self.categories_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34495e, stop:1 #2c3e50);
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        self.categories_table.setAlternatingRowColors(True)
        self.categories_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.categories_table.horizontalHeader().setStretchLastSection(True)
        self.categories_table.verticalHeader().setVisible(False)
        
        # ربط إشارة التحديد
        self.categories_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(self.categories_table)
    
    def load_categories(self):
        """تحميل الفئات من قاعدة البيانات"""
        try:
            db = Database()
            
            # جلب الفئات مع عدد المنتجات
            db.cursor.execute("""
                SELECT c.id, c.name, c.description,
                       COUNT(p.id) as products_count
                FROM categories c
                LEFT JOIN products p ON c.id = p.category_id
                GROUP BY c.id, c.name, c.description
                ORDER BY c.name
            """)
            
            categories = db.cursor.fetchall()
            
            # تحديث الجدول
            self.categories_table.setRowCount(len(categories))
            
            for row, category in enumerate(categories):
                self.categories_table.setItem(row, 0, QTableWidgetItem(str(category[1])))
                self.categories_table.setItem(row, 1, QTableWidgetItem(str(category[2] or '')))
                self.categories_table.setItem(row, 2, QTableWidgetItem(str(category[3])))
                
                # حفظ ID الفئة
                self.categories_table.item(row, 0).setData(Qt.UserRole, category[0])
            
            db.connection.close()
            
        except Exception as e:
            QMessageBox.critical(self, tr("error"), f"{tr('database_error')}: {str(e)}")
    
    def on_selection_changed(self):
        """تفعيل/تعطيل الأزرار حسب التحديد"""
        has_selection = len(self.categories_table.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
    
    def add_category(self):
        """إضافة فئة جديدة"""
        dialog = CategoryDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_categories()
            self.category_updated.emit()
    
    def edit_category(self):
        """تعديل الفئة المحددة"""
        current_row = self.categories_table.currentRow()
        if current_row < 0:
            return
        
        category_id = self.categories_table.item(current_row, 0).data(Qt.UserRole)
        category_name = self.categories_table.item(current_row, 0).text()
        category_description = self.categories_table.item(current_row, 1).text()
        
        category_data = {
            'id': category_id,
            'name': category_name,
            'description': category_description
        }
        
        dialog = CategoryDialog(self, category_data)
        if dialog.exec_() == QDialog.Accepted:
            self.load_categories()
            self.category_updated.emit()
    
    def delete_category(self):
        """حذف الفئة المحددة"""
        current_row = self.categories_table.currentRow()
        if current_row < 0:
            return
        
        category_id = self.categories_table.item(current_row, 0).data(Qt.UserRole)
        category_name = self.categories_table.item(current_row, 0).text()
        products_count = int(self.categories_table.item(current_row, 2).text())
        
        # تحذير إذا كانت الفئة تحتوي على منتجات
        if products_count > 0:
            reply = QMessageBox.question(
                self, 
                tr("confirm_delete"),
                tr("category_has_products").format(category_name, products_count),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
        else:
            reply = QMessageBox.question(
                self,
                tr("confirm_delete"),
                tr("confirm_delete_category").format(category_name),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
        
        if reply == QMessageBox.Yes:
            try:
                db = Database()
                
                if products_count > 0:
                    # نقل المنتجات إلى فئة "غير مصنف"
                    db.cursor.execute("""
                        UPDATE products 
                        SET category_id = NULL 
                        WHERE category_id = ?
                    """, (category_id,))
                
                # حذف الفئة
                db.cursor.execute("DELETE FROM categories WHERE id = ?", (category_id,))
                db.connection.commit()
                db.connection.close()
                
                QMessageBox.information(self, tr("success"), tr("category_deleted"))
                self.load_categories()
                self.category_updated.emit()
                
            except Exception as e:
                QMessageBox.critical(self, tr("error"), f"{tr('database_error')}: {str(e)}")
