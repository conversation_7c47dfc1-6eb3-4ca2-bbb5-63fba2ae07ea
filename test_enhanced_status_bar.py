#!/usr/bin/env python3
"""
اختبار شريط الحالة المحسن
Test Enhanced Status Bar
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QLabel
from PyQt5.QtCore import QTimer
from models.user import User
from views.main_window import MainWindow
from utils.translator import translator, tr


def test_enhanced_status_bar():
    """اختبار شريط الحالة المحسن"""
    
    app = QApplication(sys.argv)
    
    # إنشاء مستخدم تجريبي
    test_user = User(
        id=1,
        username="admin",
        full_name="أحمد محمد المدير",
        email="<EMAIL>",
        role="admin",
        permissions={"all": True}
    )
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow(test_user)
    main_window.show()
    
    print("🎨 اختبار شريط الحالة المحسن")
    print("=" * 60)
    
    # اختبار 1: فحص ارتفاع شريط الحالة
    print("\n1. فحص ارتفاع شريط الحالة:")
    
    if hasattr(main_window, 'status_bar'):
        height = main_window.status_bar.height()
        fixed_height = main_window.status_bar.minimumHeight()
        
        print(f"   📊 الارتفاع الحالي: {height}px")
        print(f"   📊 الارتفاع المحدد: {fixed_height}px")
        
        if height >= 40:
            print("   ✅ ارتفاع مثالي (≥40px)")
        else:
            print("   ❌ ارتفاع صغير (<40px)")
    else:
        print("   ❌ لم يتم العثور على شريط الحالة")
    
    # اختبار 2: فحص التدرج اللوني
    print("\n2. فحص التدرج اللوني:")
    
    if hasattr(main_window, 'status_bar'):
        style = main_window.status_bar.styleSheet()
        
        if "qlineargradient" in style:
            print("   ✅ يحتوي على تدرج لوني")
        else:
            print("   ❌ لا يحتوي على تدرج لوني")
        
        if "#f8f9fa" in style and "#e9ecef" in style:
            print("   ✅ ألوان التدرج صحيحة (مشابهة للشريط العلوي)")
        else:
            print("   ❌ ألوان التدرج مختلفة")
        
        if "border-top: 2px" in style:
            print("   ✅ حدود علوية محسنة")
        else:
            print("   ❌ حدود علوية غير محسنة")
    
    # اختبار 3: فحص أحجام العناصر
    print("\n3. فحص أحجام العناصر:")
    
    elements_to_check = [
        ('connection_label', 'قاعدة البيانات'),
        ('license_label', 'الترخيص'),
        ('copyright_label', 'حقوق الطبع'),
        ('datetime_label', 'التاريخ والساعة')
    ]
    
    for attr_name, display_name in elements_to_check:
        if hasattr(main_window, attr_name):
            element = getattr(main_window, attr_name)
            style = element.styleSheet()
            
            print(f"   📊 {display_name}:")
            
            # فحص حجم الخط
            if "font-size: 12px" in style:
                print("      ✅ حجم الخط: 12px (محسن)")
            elif "font-size: 11px" in style:
                print("      ✅ حجم الخط: 11px (مناسب)")
            else:
                print("      ⚠️ حجم خط مختلف")
            
            # فحص الحشو
            if "padding: 6px" in style or "padding: 8px" in style:
                print("      ✅ حشو محسن")
            else:
                print("      ⚠️ حشو مختلف")
            
            # فحص الحدود
            if "border: 2px" in style:
                print("      ✅ حدود محسنة (2px)")
            else:
                print("      ⚠️ حدود مختلفة")
            
            # فحص الارتفاع الأدنى
            if "min-height: 20px" in style:
                print("      ✅ ارتفاع أدنى محدد")
            else:
                print("      ⚠️ ارتفاع أدنى غير محدد")
    
    # اختبار 4: فحص التناسق مع الشريط العلوي
    print("\n4. فحص التناسق مع الشريط العلوي:")
    
    # فحص الشريط العلوي
    if hasattr(main_window, 'top_nav_frame'):
        nav_height = main_window.top_nav_frame.height()
        print(f"   📊 ارتفاع الشريط العلوي: {nav_height}px")
        
        if hasattr(main_window, 'status_bar'):
            status_height = main_window.status_bar.height()
            ratio = status_height / nav_height if nav_height > 0 else 0
            print(f"   📊 نسبة الارتفاع: {ratio:.2f}")
            
            if 0.6 <= ratio <= 0.8:
                print("   ✅ نسبة ارتفاع متناسقة")
            else:
                print("   ⚠️ نسبة ارتفاع غير متناسقة")
    
    # اختبار 5: فحص الألوان والتناسق
    print("\n5. فحص الألوان والتناسق:")
    
    color_tests = {
        "Database": "#27ae60",
        "License (Active)": "#27ae60", 
        "License (Trial)": "#f39c12",
        "License (Expired)": "#e74c3c",
        "DateTime": "#2c3e50",
        "Copyright": "#6c757d"
    }
    
    for element_name, expected_color in color_tests.items():
        print(f"   🎨 {element_name}: {expected_color}")
    
    # اختبار 6: اختبار تغيير اللغة
    print("\n6. اختبار تغيير اللغة:")
    
    languages = [("ar", "العربية"), ("en", "English"), ("fr", "Français")]
    
    for lang_code, lang_name in languages:
        print(f"   🌐 اختبار اللغة: {lang_name}")
        
        # تغيير اللغة
        translator.set_language(lang_code)
        main_window.retranslate_ui()
        
        # فحص النصوص المحدثة
        if hasattr(main_window, 'copyright_label'):
            copyright_text = main_window.copyright_label.text()
            if "ASSANAJE_APP" in copyright_text:
                print(f"      ✅ حقوق الطبع: {copyright_text}")
            else:
                print(f"      ❌ حقوق الطبع: {copyright_text}")
    
    # إعادة اللغة الأصلية
    translator.set_language("ar")
    main_window.retranslate_ui()
    
    # اختبار 7: فحص الاستجابة
    print("\n7. فحص الاستجابة:")
    
    # محاكاة تغيير حجم النافذة
    original_size = main_window.size()
    print(f"   📊 الحجم الأصلي: {original_size.width()}x{original_size.height()}")
    
    # تكبير النافذة
    main_window.resize(1400, 900)
    new_size = main_window.size()
    print(f"   📊 الحجم الجديد: {new_size.width()}x{new_size.height()}")
    
    # فحص تأثير التغيير على شريط الحالة
    if hasattr(main_window, 'status_bar'):
        new_status_width = main_window.status_bar.width()
        print(f"   📊 عرض شريط الحالة الجديد: {new_status_width}px")
        
        if new_status_width > 1000:
            print("   ✅ شريط الحالة يتوسع بشكل صحيح")
        else:
            print("   ⚠️ شريط الحالة لا يتوسع بشكل كافي")
    
    # إعادة الحجم الأصلي
    main_window.resize(original_size)
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار")
    
    return app


def test_visual_improvements():
    """اختبار التحسينات المرئية"""
    print("\n8. التحسينات المرئية:")
    
    improvements = [
        "✅ ارتفاع شريط الحالة: 45px (محسن)",
        "✅ تدرج لوني مشابه للشريط العلوي",
        "✅ حجم الخط: 12px (أكبر وأوضح)",
        "✅ حشو محسن: 6px عمودي، 12-15px أفقي",
        "✅ حدود محسنة: 2px (أكثر وضوحاً)",
        "✅ نصف قطر الحدود: 6px (أكثر نعومة)",
        "✅ هوامش محسنة: 3px بين العناصر",
        "✅ ارتفاع أدنى: 20px لكل عنصر",
        "✅ ألوان متناسقة ومتباينة",
        "✅ خط monospace للساعة الرقمية"
    ]
    
    for improvement in improvements:
        print(f"   {improvement}")


def test_comparison():
    """مقارنة قبل وبعد التحسين"""
    print("\n9. مقارنة قبل وبعد:")
    
    comparison = [
        ("الارتفاع", "30px", "45px"),
        ("الخلفية", "لون واحد", "تدرج لوني"),
        ("حجم الخط", "11px", "12px"),
        ("الحشو", "3px 8px", "6px 12px"),
        ("الحدود", "1px", "2px"),
        ("نصف القطر", "3px", "6px"),
        ("الهوامش", "2px", "3px"),
        ("التناسق", "منفصل", "متناسق مع الشريط العلوي")
    ]
    
    print("   📊 مقارنة التحسينات:")
    print("   " + "-" * 50)
    print("   | الجانب              | قبل      | بعد      |")
    print("   " + "-" * 50)
    
    for aspect, before, after in comparison:
        print(f"   | {aspect:<18} | {before:<8} | {after:<8} |")
    
    print("   " + "-" * 50)


if __name__ == "__main__":
    try:
        print("🚀 بدء اختبار شريط الحالة المحسن")
        print("🎯 الهدف: التأكد من زيادة الارتفاع والتناسق مع الشريط العلوي")
        
        app = test_enhanced_status_bar()
        
        # اختبارات إضافية
        test_visual_improvements()
        test_comparison()
        
        print("\n🎉 تم الانتهاء من جميع الاختبارات!")
        print("\n📝 ملخص التحسينات:")
        print("   ✅ زيادة ارتفاع شريط الحالة إلى 45px")
        print("   ✅ تطبيق تدرج لوني مشابه للشريط العلوي")
        print("   ✅ تحسين أحجام الخطوط والحشو")
        print("   ✅ تحسين الحدود والهوامش")
        print("   ✅ تحسين التناسق العام")
        
        print("\n🎯 النتيجة:")
        print("   شريط حالة أكثر جمالاً وتناسقاً مع التطبيق")
        print("   مظهر احترافي ومتناسق")
        print("   سهولة قراءة وتمييز العناصر")
        
        # تشغيل التطبيق لفترة قصيرة للاختبار اليدوي
        QTimer.singleShot(5000, app.quit)  # إغلاق بعد 5 ثوان
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
