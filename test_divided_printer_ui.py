#!/usr/bin/env python3
"""
اختبار واجهة إعدادات الطابعات المقسمة
Test Divided Printer Settings UI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QFont


class TestDividedPrinterUIWindow(QMainWindow):
    """نافذة اختبار واجهة الطابعات المقسمة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔄 اختبار واجهة إعدادات الطابعات المقسمة")
        self.setGeometry(100, 100, 1000, 800)
        
        # إنشاء الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # العنوان
        title = QLabel("🔄 اختبار واجهة إعدادات الطابعات المقسمة")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title.setStyleSheet("""
            QLabel {
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #667eea, stop:1 #764ba2);
                padding: 20px;
                border-radius: 12px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التقسيم الجديد
        info = QLabel("""
        🎯 التقسيم الجديد لواجهة إعدادات الطابعات:
        
        📋 القسم الأول: عرض جميع الطابعات
        • جدول منظم يعرض جميع الطابعات المتاحة تلقائياً
        • معلومات مفصلة: الاسم، النوع، الحالة
        • أزرار اختيار لكل طابعة
        • زر تحديث لإعادة البحث عن الطابعات
        
        📄 القسم الثاني: اختيار نوع الفاتورة
        • خيار التيكت الصغير للطابعات الحرارية
        • خيار الفاتورة الكبيرة للطابعات العادية
        • وصف مفصل لكل نوع
        • تصميم بطاقات جميل ومتناسق
        
        💾 زر الحفظ الموحد
        • زر واحد لحفظ جميع الإعدادات
        • حفظ الطابعة المختارة ونوع الفاتورة
        • رسائل تأكيد واضحة ومفصلة
        
        ✨ مميزات التصميم الجديد:
        • تقسيم واضح ومنطقي
        • ألوان متناسقة لكل قسم
        • سهولة في الاستخدام والفهم
        • تصميم احترافي وجذاب
        """)
        info.setFont(QFont("Segoe UI", 11))
        info.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
                color: #495057;
                line-height: 1.6;
            }
        """)
        layout.addWidget(info)
        
        # زر اختبار الواجهة المقسمة
        self.test_button = QPushButton("🚀 اختبار الواجهة المقسمة")
        self.test_button.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.test_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #4caf50, stop:1 #388e3c);
                color: white;
                padding: 15px 30px;
                border: none;
                border-radius: 10px;
                margin: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #388e3c, stop:1 #2e7d32);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #2e7d32, stop:1 #1b5e20);
            }
        """)
        self.test_button.clicked.connect(self.test_divided_ui)
        layout.addWidget(self.test_button)
        
        # منطقة النتائج
        self.result_label = QLabel("⏳ جاهز لاختبار الواجهة المقسمة...")
        self.result_label.setFont(QFont("Segoe UI", 12))
        self.result_label.setStyleSheet("""
            QLabel {
                background-color: #e9ecef;
                border: 2px solid #ced4da;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
                color: #6c757d;
            }
        """)
        layout.addWidget(self.result_label)
        
        layout.addStretch()
    
    def test_divided_ui(self):
        """اختبار الواجهة المقسمة"""
        self.result_label.setText("🔄 جاري تحميل الواجهة المقسمة...")
        self.result_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                border: 2px solid #ffeaa7;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
                color: #856404;
            }
        """)
        
        try:
            # محاولة تحميل واجهة الطابعات المقسمة
            from views.printer_settings_widget import PrinterSettingsWidget
            
            # إنشاء الواجهة المقسمة
            self.printer_widget = PrinterSettingsWidget()
            
            # إظهار النافذة
            self.printer_widget.show()
            
            # نجح الاختبار
            self.result_label.setText("""
            ✅ نجح اختبار الواجهة المقسمة!
            
            📋 القسم الأول - عرض الطابعات:
            • جدول منظم وواضح ✅
            • معلومات مفصلة عن كل طابعة ✅
            • أزرار اختيار تفاعلية ✅
            • زر تحديث محسن ✅
            
            📄 القسم الثاني - نوع الفاتورة:
            • خيارات واضحة ومفهومة ✅
            • تصميم بطاقات جميل ✅
            • وصف مفصل لكل نوع ✅
            • ألوان متناسقة ومميزة ✅
            
            💾 زر الحفظ الموحد:
            • حفظ جميع الإعدادات معاً ✅
            • رسائل تأكيد واضحة ✅
            • تصميم بارز وجذاب ✅
            
            🎨 التصميم العام:
            • تقسيم منطقي وواضح ✅
            • ألوان متناسقة لكل قسم ✅
            • سهولة في الاستخدام ✅
            • تصميم احترافي وعصري ✅
            
            🎉 الواجهة المقسمة جاهزة للاستخدام!
            """)
            self.result_label.setStyleSheet("""
                QLabel {
                    background-color: #d4edda;
                    border: 2px solid #c3e6cb;
                    border-radius: 8px;
                    padding: 15px;
                    margin: 10px;
                    color: #155724;
                    line-height: 1.5;
                }
            """)
            
        except Exception as e:
            # فشل الاختبار
            self.result_label.setText(f"""
            ❌ فشل في تحميل الواجهة المقسمة!
            
            📋 تفاصيل الخطأ:
            {str(e)}
            
            🔧 الحلول المقترحة:
            • تحقق من وجود جميع الملفات المطلوبة
            • تأكد من صحة المسارات
            • راجع ملفات الاستيراد
            • تحقق من إعدادات النظام
            """)
            self.result_label.setStyleSheet("""
                QLabel {
                    background-color: #f8d7da;
                    border: 2px solid #f5c6cb;
                    border-radius: 8px;
                    padding: 15px;
                    margin: 10px;
                    color: #721c24;
                }
            """)
            
            # طباعة تفاصيل الخطأ للمطور
            import traceback
            print("تفاصيل الخطأ:")
            traceback.print_exc()


def test_divided_printer_ui():
    """اختبار واجهة الطابعات المقسمة"""
    
    app = QApplication(sys.argv)
    
    print("🔄 بدء اختبار واجهة إعدادات الطابعات المقسمة")
    print("=" * 60)
    
    # إنشاء نافذة الاختبار
    test_window = TestDividedPrinterUIWindow()
    test_window.show()
    
    print("\n🎯 التقسيم الجديد:")
    print("📋 القسم الأول: جدول الطابعات المتاحة")
    print("📄 القسم الثاني: اختيار نوع الفاتورة")
    print("💾 زر الحفظ الموحد")
    
    print("\n✨ المميزات الجديدة:")
    print("🎨 تصميم مقسم ومنظم")
    print("🖨️ جدول طابعات تفاعلي")
    print("📋 خيارات فاتورة واضحة")
    print("💾 حفظ موحد لجميع الإعدادات")
    print("🎛️ ألوان متناسقة لكل قسم")
    
    print("\n📋 الاختبارات المطلوبة:")
    print("1. ✅ فتح نافذة الاختبار")
    print("2. 🔄 الضغط على زر 'اختبار الواجهة المقسمة'")
    print("3. 👀 مراقبة التقسيم والتصميم الجديد")
    print("4. 🖨️ اختبار جدول الطابعات")
    print("5. 📄 اختبار خيارات نوع الفاتورة")
    print("6. 💾 اختبار زر الحفظ الموحد")
    
    print("\n🎉 النتائج المتوقعة:")
    print("✅ واجهة مقسمة ومنظمة")
    print("✅ جدول طابعات واضح وتفاعلي")
    print("✅ خيارات فاتورة مفهومة")
    print("✅ حفظ موحد وسهل")
    print("✅ تصميم احترافي وجذاب")
    
    print("\n" + "=" * 60)
    print("🚀 جاهز للاختبار!")
    
    return app


if __name__ == "__main__":
    try:
        print("🔄 اختبار واجهة إعدادات الطابعات المقسمة")
        print("🎯 الهدف: التأكد من وضوح التقسيم وسهولة الاستخدام")
        
        app = test_divided_printer_ui()
        
        print("\n📝 ملاحظات مهمة:")
        print("• التقسيم يجعل الواجهة أكثر وضوحاً")
        print("• جدول الطابعات يعرض جميع المعلومات")
        print("• خيارات الفاتورة واضحة ومفهومة")
        print("• زر الحفظ الموحد يبسط العملية")
        print("• الألوان المتناسقة تحسن التجربة")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
