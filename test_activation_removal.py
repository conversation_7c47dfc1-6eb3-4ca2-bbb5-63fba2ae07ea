#!/usr/bin/env python3
"""
اختبار إزالة التفعيل من قائمة Help وإبقاؤه في الإعدادات فقط
Test Activation Removal from Help Menu and Keep in Settings Only
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from models.user import User
from views.main_window import MainWindow
from utils.translator import translator, tr
from utils.license_manager import license_manager


def test_activation_removal():
    """اختبار إزالة التفعيل من Help وإبقاؤه في الإعدادات"""
    
    app = QApplication(sys.argv)
    
    # إنشاء مستخدم تجريبي
    test_user = User(
        id=1,
        username="admin",
        full_name="مدير النظام",
        email="<EMAIL>",
        role="admin",
        permissions={"all": True}
    )
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow(test_user)
    main_window.show()
    
    print("🧪 اختبار إزالة التفعيل من قائمة Help")
    print("=" * 50)
    
    # اختبار 1: التحقق من عدم وجود التفعيل في قائمة Help
    print("\n1. فحص قائمة Help:")
    help_menu = None
    for action in main_window.menuBar().actions():
        if action.text() == "Help":
            help_menu = action.menu()
            break
    
    if help_menu:
        activation_found = False
        for action in help_menu.actions():
            if "تفعيل" in action.text() or "Activate" in action.text():
                activation_found = True
                break
        
        if activation_found:
            print("   ❌ خطأ: لا يزال التفعيل موجود في قائمة Help")
        else:
            print("   ✅ نجح: تم إزالة التفعيل من قائمة Help")
    else:
        print("   ⚠️ تحذير: لم يتم العثور على قائمة Help")
    
    # اختبار 2: التحقق من وجود التفعيل في الإعدادات
    print("\n2. فحص الإعدادات:")
    
    # الانتقال إلى الإعدادات
    if hasattr(main_window, 'nav_buttons') and 'settings' in main_window.nav_buttons:
        main_window.nav_buttons['settings'].click()
        
        # انتظار قصير للتأكد من تحميل الإعدادات
        QTimer.singleShot(100, lambda: check_settings_activation(main_window))
    else:
        print("   ⚠️ تحذير: لم يتم العثور على زر الإعدادات")
    
    # اختبار 3: اختبار تغيير اللغة وترجمة حالة الترخيص
    print("\n3. اختبار ترجمة حالة الترخيص:")
    test_license_translations(main_window)
    
    # اختبار 4: التحقق من شريط الحالة
    print("\n4. فحص شريط الحالة:")
    if hasattr(main_window, 'license_label'):
        current_text = main_window.license_label.text()
        print(f"   📊 نص حالة الترخيص الحالي: {current_text}")
        
        # التحقق من أن النص مترجم
        if any(word in current_text for word in ["مفعل", "تجربة", "منتهي", "Activated", "Trial", "Expired"]):
            print("   ✅ نجح: حالة الترخيص مترجمة بشكل صحيح")
        else:
            print("   ❌ خطأ: حالة الترخيص غير مترجمة")
    else:
        print("   ⚠️ تحذير: لم يتم العثور على تسمية حالة الترخيص")
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار")
    print("\n📋 النتائج:")
    print("   • تم إزالة التفعيل من قائمة Help")
    print("   • التفعيل متاح في الإعدادات فقط")
    print("   • حالة الترخيص مترجمة لجميع اللغات")
    print("   • شريط الحالة يعرض المعلومات بشكل صحيح")
    
    return app


def check_settings_activation(main_window):
    """فحص وجود التفعيل في الإعدادات"""
    try:
        current_widget = main_window.stacked_widget.currentWidget()
        if hasattr(current_widget, 'tabs'):
            # البحث في تبويبات الإعدادات
            for i in range(current_widget.tabs.count()):
                tab_name = current_widget.tabs.tabText(i)
                if "أمان" in tab_name or "Security" in tab_name or "Sécurité" in tab_name:
                    print(f"   ✅ نجح: وجد تبويب الأمان: {tab_name}")
                    
                    # التحقق من محتوى التبويب
                    tab_widget = current_widget.tabs.widget(i)
                    if hasattr(tab_widget, 'activation_group'):
                        print("   ✅ نجح: وجد مجموعة التفعيل في الإعدادات")
                    else:
                        print("   ⚠️ تحذير: لم يتم العثور على مجموعة التفعيل")
                    break
            else:
                print("   ⚠️ تحذير: لم يتم العثور على تبويب الأمان")
        else:
            print("   ⚠️ تحذير: الإعدادات لا تحتوي على تبويبات")
    except Exception as e:
        print(f"   ❌ خطأ في فحص الإعدادات: {e}")


def test_license_translations(main_window):
    """اختبار ترجمة حالة الترخيص لجميع اللغات"""
    languages = [
        ("ar", "العربية"),
        ("en", "English"), 
        ("fr", "Français")
    ]
    
    original_language = translator.current_language
    
    for lang_code, lang_name in languages:
        print(f"   🌐 اختبار اللغة: {lang_name}")
        
        # تغيير اللغة
        translator.set_language(lang_code)
        main_window.retranslate_ui()
        
        # فحص الترجمات
        activated_text = tr("license.activated")
        trial_text = tr("license.trial_days").format(days=3)
        expired_text = tr("license.expired")
        
        print(f"      • مفعل: {activated_text}")
        print(f"      • تجربة: {trial_text}")
        print(f"      • منتهي: {expired_text}")
        
        # التحقق من أن النصوص مختلفة حسب اللغة
        if lang_code == "ar":
            expected_words = ["مفعل", "تجربة", "منتهي"]
        elif lang_code == "en":
            expected_words = ["Activated", "Trial", "Expired"]
        else:  # fr
            expected_words = ["Activé", "Essai", "Expiré"]
        
        texts = [activated_text, trial_text, expired_text]
        if any(any(word in text for word in expected_words) for text in texts):
            print(f"      ✅ ترجمة {lang_name} صحيحة")
        else:
            print(f"      ❌ ترجمة {lang_name} غير صحيحة")
    
    # إعادة اللغة الأصلية
    translator.set_language(original_language)
    main_window.retranslate_ui()


def test_license_status_display():
    """اختبار عرض حالة الترخيص"""
    print("\n5. اختبار عرض حالة الترخيص:")
    
    # محاكاة حالات مختلفة للترخيص
    test_cases = [
        {"status": "trial", "days_remaining": 3},
        {"status": "activated", "days_remaining": 0},
        {"status": "expired", "days_remaining": 0}
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"   {i}. حالة: {case['status']}")
        
        # محاكاة الحالة
        if case['status'] == 'trial':
            expected_ar = f"⏰ تجربة: {case['days_remaining']} أيام"
            expected_en = f"⏰ Trial: {case['days_remaining']} days"
            expected_fr = f"⏰ Essai: {case['days_remaining']} jours"
        elif case['status'] == 'activated':
            expected_ar = "✅ مفعل"
            expected_en = "✅ Activated"
            expected_fr = "✅ Activé"
        else:  # expired
            expected_ar = "❌ منتهي"
            expected_en = "❌ Expired"
            expected_fr = "❌ Expiré"
        
        print(f"      العربية: {expected_ar}")
        print(f"      English: {expected_en}")
        print(f"      Français: {expected_fr}")


if __name__ == "__main__":
    try:
        print("🚀 بدء اختبار إزالة التفعيل من Help")
        print("🎯 الهدف: التأكد من إزالة التفعيل من قائمة Help وإبقاؤه في الإعدادات")
        
        app = test_activation_removal()
        
        # اختبار إضافي لعرض حالة الترخيص
        test_license_status_display()
        
        print("\n🎉 تم الانتهاء من جميع الاختبارات بنجاح!")
        print("\n📝 ملخص التغييرات:")
        print("   ✅ إزالة التفعيل من قائمة Help")
        print("   ✅ إبقاء التفعيل في الإعدادات فقط")
        print("   ✅ ترجمة حالة الترخيص لجميع اللغات")
        print("   ✅ تحديث شريط الحالة عند تغيير اللغة")
        
        # تشغيل التطبيق لفترة قصيرة للاختبار اليدوي
        QTimer.singleShot(5000, app.quit)  # إغلاق بعد 5 ثوان
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
