#!/usr/bin/env python3
"""
Simple test to verify basic functionality
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        from models.database import db
        print("✅ Database module imported")
        
        from models.product import Product, Category
        print("✅ Product module imported")
        
        from models.customer import Customer
        print("✅ Customer module imported")
        
        from models.user import User
        print("✅ User module imported")
        
        from models.sale import Sale
        print("✅ Sale module imported")
        
        from utils.config_manager import config
        print("✅ Config module imported")
        
        from utils.translator import tr
        print("✅ Translator module imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_basic_operations():
    """Test basic operations"""
    print("\n🔍 Testing basic operations...")
    
    try:
        from models.product import Product
        from models.customer import Customer
        from models.user import User
        
        # Test getting data
        products = Product.get_all()
        print(f"✅ Found {len(products)} products")
        
        customers = Customer.get_all()
        print(f"✅ Found {len(customers)} customers")
        
        users = User.get_all()
        print(f"✅ Found {len(users)} users")
        
        # Test authentication
        user = User.authenticate('admin', 'admin123')
        if user:
            print("✅ Admin authentication successful")
        else:
            print("❌ Admin authentication failed")
            
        return True
        
    except Exception as e:
        print(f"❌ Basic operations failed: {e}")
        return False

def test_config():
    """Test configuration"""
    print("\n🔍 Testing configuration...")
    
    try:
        from utils.config_manager import config
        
        language = config.get_language()
        theme = config.get_theme()
        currency = config.get_currency()
        
        print(f"✅ Language: {language}")
        print(f"✅ Theme: {theme}")
        print(f"✅ Currency: {currency}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_translation():
    """Test translation"""
    print("\n🔍 Testing translation...")
    
    try:
        from utils.translator import tr
        
        pos_title = tr("pos.title")
        products_title = tr("products.title")
        
        print(f"✅ POS title: {pos_title}")
        print(f"✅ Products title: {products_title}")
        
        return True
        
    except Exception as e:
        print(f"❌ Translation test failed: {e}")
        return False

def main():
    """Run simple tests"""
    print("🚀 Running simple OnePos tests...\n")
    
    tests = [
        ("Imports", test_imports),
        ("Basic Operations", test_basic_operations),
        ("Configuration", test_config),
        ("Translation", test_translation),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"{'='*40}")
        print(f"🧪 {test_name}")
        print('='*40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} FAILED: {e}")
    
    print(f"\n{'='*40}")
    print("📊 SUMMARY")
    print('='*40)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All basic tests passed!")
    else:
        print(f"\n⚠️  {failed} test(s) failed.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
