"""
Purchase Management Models for OnePos POS System
Handles purchases, suppliers, and purchase items
"""

from datetime import datetime
from .database import db


class Supplier:
    """Supplier model for managing suppliers"""
    
    def __init__(self, id=None, name=None, contact_person=None, phone=None, 
                 email=None, address=None, tax_number=None, is_active=True, 
                 created_at=None, updated_at=None):
        self.id = id
        self.name = name
        self.contact_person = contact_person
        self.phone = phone
        self.email = email
        self.address = address
        self.tax_number = tax_number
        self.is_active = is_active
        self.created_at = created_at
        self.updated_at = updated_at
    
    @classmethod
    def create(cls, name, contact_person=None, phone=None, email=None, 
               address=None, tax_number=None):
        """Create a new supplier"""
        query = """
        INSERT INTO suppliers (name, contact_person, phone, email, address, tax_number, is_active)
        VALUES (?, ?, ?, ?, ?, ?, 1)
        """
        
        supplier_id = db.execute_update(query, (
            name, contact_person, phone, email, address, tax_number
        ))
        
        return cls.get_by_id(supplier_id)
    
    @classmethod
    def get_by_id(cls, supplier_id):
        """Get supplier by ID"""
        query = "SELECT * FROM suppliers WHERE id = ?"
        result = db.execute_query(query, (supplier_id,))
        
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def get_all(cls, active_only=True):
        """Get all suppliers"""
        query = "SELECT * FROM suppliers"
        params = []
        
        if active_only:
            query += " WHERE is_active = 1"
        
        query += " ORDER BY name"
        
        results = db.execute_query(query, params)
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def search(cls, search_term):
        """Search suppliers"""
        query = """
        SELECT * FROM suppliers 
        WHERE (name LIKE ? OR contact_person LIKE ? OR phone LIKE ? OR email LIKE ?)
        AND is_active = 1
        ORDER BY name
        """
        search_pattern = f"%{search_term}%"
        results = db.execute_query(query, (search_pattern, search_pattern, search_pattern, search_pattern))
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def _from_db_row(cls, row):
        """Create supplier instance from database row"""
        return cls(
            id=row['id'],
            name=row['name'],
            contact_person=row['contact_person'],
            phone=row['phone'],
            email=row['email'],
            address=row['address'],
            tax_number=row['tax_number'],
            is_active=bool(row['is_active']),
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )
    
    def update(self):
        """Update supplier"""
        query = """
        UPDATE suppliers 
        SET name = ?, contact_person = ?, phone = ?, email = ?, 
            address = ?, tax_number = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        
        db.execute_update(query, (
            self.name, self.contact_person, self.phone, self.email,
            self.address, self.tax_number, self.is_active, self.id
        ))
    
    def delete(self):
        """Soft delete supplier"""
        self.is_active = False
        self.update()


class Purchase:
    """Purchase model for managing purchases"""
    
    def __init__(self, id=None, supplier_id=None, purchase_number=None, 
                 purchase_date=None, total_amount=0.0, tax_amount=0.0, 
                 discount_amount=0.0, net_amount=0.0, status='pending', 
                 notes=None, user_id=None, created_at=None, updated_at=None):
        self.id = id
        self.supplier_id = supplier_id
        self.purchase_number = purchase_number
        self.purchase_date = purchase_date
        self.total_amount = total_amount
        self.tax_amount = tax_amount
        self.discount_amount = discount_amount
        self.net_amount = net_amount
        self.status = status  # pending, received, cancelled
        self.notes = notes
        self.user_id = user_id
        self.created_at = created_at
        self.updated_at = updated_at
        
        # Related objects
        self._supplier = None
        self._items = None
    
    @classmethod
    def create(cls, supplier_id, purchase_date=None, user_id=None, notes=None):
        """Create a new purchase"""
        if not purchase_date:
            purchase_date = datetime.now().strftime('%Y-%m-%d')
        
        # Generate purchase number
        purchase_number = cls._generate_purchase_number()
        
        query = """
        INSERT INTO purchases (supplier_id, purchase_number, purchase_date, 
                             total_amount, tax_amount, discount_amount, net_amount,
                             status, notes, user_id)
        VALUES (?, ?, ?, 0.0, 0.0, 0.0, 0.0, 'pending', ?, ?)
        """
        
        purchase_id = db.execute_update(query, (
            supplier_id, purchase_number, purchase_date, notes, user_id
        ))
        
        return cls.get_by_id(purchase_id)
    
    @classmethod
    def _generate_purchase_number(cls):
        """Generate unique purchase number"""
        from datetime import datetime
        today = datetime.now()
        prefix = f"PO{today.strftime('%Y%m%d')}"
        
        # Get last purchase number for today
        query = """
        SELECT purchase_number FROM purchases 
        WHERE purchase_number LIKE ? 
        ORDER BY purchase_number DESC LIMIT 1
        """
        
        result = db.execute_query(query, (f"{prefix}%",))
        
        if result:
            last_number = result[0]['purchase_number']
            sequence = int(last_number[-4:]) + 1
        else:
            sequence = 1
        
        return f"{prefix}{sequence:04d}"
    
    @classmethod
    def get_by_id(cls, purchase_id):
        """Get purchase by ID"""
        query = "SELECT * FROM purchases WHERE id = ?"
        result = db.execute_query(query, (purchase_id,))
        
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def get_all(cls, status=None, limit=100):
        """Get all purchases"""
        query = "SELECT * FROM purchases"
        params = []
        
        if status:
            query += " WHERE status = ?"
            params.append(status)
        
        query += " ORDER BY created_at DESC LIMIT ?"
        params.append(limit)
        
        results = db.execute_query(query, params)
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def search(cls, search_term):
        """Search purchases"""
        query = """
        SELECT p.* FROM purchases p
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        WHERE (p.purchase_number LIKE ? OR s.name LIKE ? OR p.notes LIKE ?)
        ORDER BY p.created_at DESC
        """
        search_pattern = f"%{search_term}%"
        results = db.execute_query(query, (search_pattern, search_pattern, search_pattern))
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def _from_db_row(cls, row):
        """Create purchase instance from database row"""
        return cls(
            id=row['id'],
            supplier_id=row['supplier_id'],
            purchase_number=row['purchase_number'],
            purchase_date=row['purchase_date'],
            total_amount=float(row['total_amount']),
            tax_amount=float(row['tax_amount']),
            discount_amount=float(row['discount_amount']),
            net_amount=float(row['net_amount']),
            status=row['status'],
            notes=row['notes'],
            user_id=row['user_id'],
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )
    
    def get_supplier(self):
        """Get purchase supplier"""
        if not self._supplier and self.supplier_id:
            self._supplier = Supplier.get_by_id(self.supplier_id)
        return self._supplier
    
    def get_items(self):
        """Get purchase items"""
        if not self._items:
            self._items = PurchaseItem.get_by_purchase_id(self.id)
        return self._items
    
    def add_item(self, product_id, quantity, unit_cost, total_cost=None):
        """Add item to purchase"""
        if not total_cost:
            total_cost = quantity * unit_cost
        
        item = PurchaseItem.create(
            purchase_id=self.id,
            product_id=product_id,
            quantity=quantity,
            unit_cost=unit_cost,
            total_cost=total_cost
        )
        
        # Clear cached items to force reload
        self._items = None

        # Recalculate totals
        self.calculate_totals()

        return item
    
    def calculate_totals(self):
        """Calculate purchase totals"""
        items = self.get_items()
        
        self.total_amount = sum(item.total_cost for item in items)
        self.net_amount = self.total_amount - self.discount_amount + self.tax_amount
        
        # Update in database
        query = """
        UPDATE purchases 
        SET total_amount = ?, net_amount = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        db.execute_update(query, (self.total_amount, self.net_amount, self.id))
    
    def update_status(self, status):
        """Update purchase status"""
        self.status = status
        
        query = """
        UPDATE purchases 
        SET status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        db.execute_update(query, (status, self.id))
        
        # If received, update product stock
        if status == 'received':
            self.update_product_stock()
    
    def update_product_stock(self):
        """Update product stock when purchase is received"""
        from .product import Product

        items = self.get_items()
        for item in items:
            product = Product.get_by_id(item.product_id)
            if product:
                new_stock = product.stock_quantity + item.quantity
                # Update using kwargs
                product.update(
                    stock_quantity=new_stock,
                    cost_price=item.unit_cost
                )


class PurchaseItem:
    """Purchase item model"""
    
    def __init__(self, id=None, purchase_id=None, product_id=None, 
                 quantity=0, unit_cost=0.0, total_cost=0.0, 
                 created_at=None, updated_at=None):
        self.id = id
        self.purchase_id = purchase_id
        self.product_id = product_id
        self.quantity = quantity
        self.unit_cost = unit_cost
        self.total_cost = total_cost
        self.created_at = created_at
        self.updated_at = updated_at
        
        # Related objects
        self._product = None
    
    @classmethod
    def create(cls, purchase_id, product_id, quantity, unit_cost, total_cost=None):
        """Create a new purchase item"""
        if not total_cost:
            total_cost = quantity * unit_cost
        
        query = """
        INSERT INTO purchase_items (purchase_id, product_id, quantity, unit_cost, total_cost)
        VALUES (?, ?, ?, ?, ?)
        """
        
        item_id = db.execute_update(query, (
            purchase_id, product_id, quantity, unit_cost, total_cost
        ))
        
        return cls.get_by_id(item_id)
    
    @classmethod
    def get_by_id(cls, item_id):
        """Get purchase item by ID"""
        query = "SELECT * FROM purchase_items WHERE id = ?"
        result = db.execute_query(query, (item_id,))
        
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def get_by_purchase_id(cls, purchase_id):
        """Get all items for a purchase"""
        query = "SELECT * FROM purchase_items WHERE purchase_id = ? ORDER BY id"
        results = db.execute_query(query, (purchase_id,))
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def _from_db_row(cls, row):
        """Create purchase item instance from database row"""
        return cls(
            id=row['id'],
            purchase_id=row['purchase_id'],
            product_id=row['product_id'],
            quantity=row['quantity'],
            unit_cost=float(row['unit_cost']),
            total_cost=float(row['total_cost']),
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )
    
    def get_product(self):
        """Get item product"""
        if not self._product and self.product_id:
            from .product import Product
            self._product = Product.get_by_id(self.product_id)
        return self._product
    
    def update(self):
        """Update purchase item"""
        self.total_cost = self.quantity * self.unit_cost
        
        query = """
        UPDATE purchase_items 
        SET quantity = ?, unit_cost = ?, total_cost = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        
        db.execute_update(query, (
            self.quantity, self.unit_cost, self.total_cost, self.id
        ))
    
    def delete(self):
        """Delete purchase item"""
        query = "DELETE FROM purchase_items WHERE id = ?"
        db.execute_update(query, (self.id,))
