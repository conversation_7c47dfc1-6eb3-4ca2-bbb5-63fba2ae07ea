"""
Translatable Widget Base Class for OnePos POS System
Provides automatic translation updates for all UI components
"""

from PyQt5.QtWidgets import <PERSON><PERSON><PERSON>t, QLabel, QPushButton, QGroupBox, QTabWidget, QTableWidget, QComboBox
from PyQt5.QtCore import QObject
from utils.translator import tr, register_for_translation, unregister_from_translation, is_rtl


class TranslatableWidget(QWidget):
    """Base class for widgets that support automatic translation updates"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.translation_keys = {}  # Store original translation keys for each widget
        register_for_translation(self)
    
    def closeEvent(self, event):
        """Clean up when widget is closed"""
        unregister_from_translation(self)
        super().closeEvent(event)
    
    def on_language_changed(self, old_language: str, new_language: str):
        """Called when language changes - override in subclasses"""
        self.retranslate_ui()
        self.update_layout_direction()
    
    def retranslate_ui(self):
        """Retranslate all UI elements - override in subclasses"""
        self._retranslate_children(self)
    
    def update_layout_direction(self):
        """Update layout direction based on language"""
        if is_rtl():
            self.setLayoutDirection(2)  # Qt.RightToLeft
        else:
            self.setLayoutDirection(0)  # Qt.LeftToRight
    
    def _retranslate_children(self, parent):
        """Recursively retranslate all child widgets"""
        for child in parent.findChildren(QObject):
            self._retranslate_widget(child)
    
    def _retranslate_widget(self, widget):
        """Retranslate a specific widget"""
        widget_id = id(widget)
        
        # Handle different widget types
        if isinstance(widget, QLabel):
            if widget_id in self.translation_keys:
                key = self.translation_keys[widget_id]
                widget.setText(tr(key))
        
        elif isinstance(widget, QPushButton):
            if widget_id in self.translation_keys:
                key = self.translation_keys[widget_id]
                widget.setText(tr(key))
        
        elif isinstance(widget, QGroupBox):
            if widget_id in self.translation_keys:
                key = self.translation_keys[widget_id]
                widget.setTitle(tr(key))
        
        elif isinstance(widget, QTabWidget):
            if widget_id in self.translation_keys:
                tab_keys = self.translation_keys[widget_id]
                for i, key in enumerate(tab_keys):
                    if i < widget.count():
                        widget.setTabText(i, tr(key))
        
        elif isinstance(widget, QTableWidget):
            if widget_id in self.translation_keys:
                header_keys = self.translation_keys[widget_id]
                for i, key in enumerate(header_keys):
                    if i < widget.columnCount():
                        widget.setHorizontalHeaderItem(i, widget.horizontalHeaderItem(i))
                        if widget.horizontalHeaderItem(i):
                            widget.horizontalHeaderItem(i).setText(tr(key))
        
        elif isinstance(widget, QComboBox):
            if widget_id in self.translation_keys:
                item_keys = self.translation_keys[widget_id]
                current_index = widget.currentIndex()
                widget.clear()
                for key in item_keys:
                    widget.addItem(tr(key))
                if current_index < widget.count():
                    widget.setCurrentIndex(current_index)
    
    def set_translatable_text(self, widget, translation_key: str):
        """Set translatable text for a widget"""
        widget_id = id(widget)
        self.translation_keys[widget_id] = translation_key
        
        # Set initial text
        if isinstance(widget, (QLabel, QPushButton)):
            widget.setText(tr(translation_key))
        elif isinstance(widget, QGroupBox):
            widget.setTitle(tr(translation_key))
    
    def set_translatable_tab_texts(self, tab_widget: QTabWidget, translation_keys: list):
        """Set translatable tab texts"""
        widget_id = id(tab_widget)
        self.translation_keys[widget_id] = translation_keys
        
        # Set initial tab texts
        for i, key in enumerate(translation_keys):
            if i < tab_widget.count():
                tab_widget.setTabText(i, tr(key))
    
    def set_translatable_headers(self, table_widget: QTableWidget, translation_keys: list):
        """Set translatable table headers"""
        widget_id = id(table_widget)
        self.translation_keys[widget_id] = translation_keys
        
        # Set initial headers
        headers = []
        for key in translation_keys:
            headers.append(tr(key))
        table_widget.setHorizontalHeaderLabels(headers)
    
    def set_translatable_items(self, combo_widget: QComboBox, translation_keys: list):
        """Set translatable combo box items"""
        widget_id = id(combo_widget)
        self.translation_keys[widget_id] = translation_keys
        
        # Set initial items
        current_index = combo_widget.currentIndex()
        combo_widget.clear()
        for key in translation_keys:
            combo_widget.addItem(tr(key))
        if current_index < combo_widget.count():
            combo_widget.setCurrentIndex(current_index)


class TranslatableObject(QObject):
    """Base class for non-widget objects that support translation"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        register_for_translation(self)
    
    def on_language_changed(self, old_language: str, new_language: str):
        """Called when language changes - override in subclasses"""
        pass


def create_translatable_label(translation_key: str, parent=None) -> QLabel:
    """Create a label with automatic translation"""
    label = QLabel(tr(translation_key), parent)
    if hasattr(parent, 'set_translatable_text'):
        parent.set_translatable_text(label, translation_key)
    return label


def create_translatable_button(translation_key: str, parent=None) -> QPushButton:
    """Create a button with automatic translation"""
    button = QPushButton(tr(translation_key), parent)
    if hasattr(parent, 'set_translatable_text'):
        parent.set_translatable_text(button, translation_key)
    return button


def create_translatable_group(translation_key: str, parent=None) -> QGroupBox:
    """Create a group box with automatic translation"""
    group = QGroupBox(tr(translation_key), parent)
    if hasattr(parent, 'set_translatable_text'):
        parent.set_translatable_text(group, translation_key)
    return group


def create_dark_translatable_label(translation_key: str, parent=None) -> QLabel:
    """Create a dark label with automatic translation"""
    label = create_translatable_label(translation_key, parent)
    label.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
    return label
