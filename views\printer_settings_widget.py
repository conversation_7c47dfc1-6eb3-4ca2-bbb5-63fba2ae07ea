"""
واجهة إعدادات الطابعات - OnePos
Printer Settings Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QComboBox, QPushButton, QGroupBox, QTableWidget,
                            QTableWidgetItem, QMessageBox, QFrame, QTextEdit,
                            QSplitter, QHeaderView, QCheckBox, QRadioButton,
                            QButtonGroup)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from utils.translator import tr

class PrinterSettingsWidget(QWidget):
    """واجهة إعدادات الطابعات"""
    
    settings_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_saved_settings()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم المقسمة إلى قسمين"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(25)

        # العنوان الرئيسي
        header_layout = QHBoxLayout()

        title_label = QLabel("🖨️ إعدادات الطابعات")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 12px;
                margin-bottom: 15px;
            }
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # القسم الأول: عرض جميع الطابعات المتاحة
        printers_section = self.create_printers_display_section()
        layout.addWidget(printers_section)

        # القسم الثاني: اختيار نوع الفاتورة
        invoice_section = self.create_invoice_type_section()
        layout.addWidget(invoice_section)

        # زر الحفظ الرئيسي
        save_button_layout = self.create_main_save_button()
        layout.addLayout(save_button_layout)

        # مساحة فارغة
        layout.addStretch()

    def create_printers_display_section(self):
        """القسم الأول: عرض جميع الطابعات المتاحة"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 3px solid #e3f2fd;
                border-radius: 15px;
                padding: 25px;
                margin: 10px;
            }
        """)

        layout = QVBoxLayout(section)
        layout.setSpacing(20)

        # عنوان القسم الأول
        title_layout = QHBoxLayout()

        title_icon = QLabel("🖨️")
        title_icon.setFont(QFont("Segoe UI", 20))
        title_layout.addWidget(title_icon)

        title_label = QLabel("القسم الأول: الطابعات المتاحة")
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #1976d2;
                margin-left: 10px;
                padding: 10px;
                background-color: #e3f2fd;
                border-radius: 8px;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # زر التحديث
        self.refresh_button = QPushButton("🔄 تحديث قائمة الطابعات")
        self.refresh_button.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #42a5f5, stop:1 #1976d2);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                min-width: 180px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976d2, stop:1 #1565c0);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1565c0, stop:1 #0d47a1);
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_printers)
        title_layout.addWidget(self.refresh_button)

        layout.addLayout(title_layout)

        # وصف القسم
        desc_label = QLabel("يعرض هذا القسم جميع الطابعات المتصلة بجهاز الكمبيوتر تلقائياً")
        desc_label.setFont(QFont("Segoe UI", 11))
        desc_label.setStyleSheet("""
            QLabel {
                color: #666;
                padding: 10px;
                background-color: #f5f5f5;
                border-radius: 6px;
                border-left: 4px solid #42a5f5;
            }
        """)
        layout.addWidget(desc_label)

        # جدول الطابعات
        self.printers_table = QTableWidget()
        self.printers_table.setColumnCount(4)
        self.printers_table.setHorizontalHeaderLabels([
            "🖨️ اسم الطابعة",
            "🔧 النوع",
            "📊 الحالة",
            "✅ اختيار"
        ])

        # تنسيق الجدول
        self.printers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: #fafafa;
                alternate-background-color: #f0f0f0;
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #42a5f5, stop:1 #1976d2);
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
        """)

        # تعيين خصائص الجدول
        header = self.printers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)

        self.printers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.printers_table.setAlternatingRowColors(True)
        self.printers_table.setMinimumHeight(200)

        layout.addWidget(self.printers_table)

        # تحديث الجدول عند التحميل
        self.update_printers_table()

        return section

    def create_invoice_type_section(self):
        """القسم الثاني: اختيار نوع الفاتورة"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 3px solid #e8f5e8;
                border-radius: 15px;
                padding: 25px;
                margin: 10px;
            }
        """)

        layout = QVBoxLayout(section)
        layout.setSpacing(20)

        # عنوان القسم الثاني
        title_layout = QHBoxLayout()

        title_icon = QLabel("📄")
        title_icon.setFont(QFont("Segoe UI", 20))
        title_layout.addWidget(title_icon)

        title_label = QLabel("القسم الثاني: نوع الفاتورة")
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #388e3c;
                margin-left: 10px;
                padding: 10px;
                background-color: #e8f5e8;
                border-radius: 8px;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        layout.addLayout(title_layout)

        # وصف القسم
        desc_label = QLabel("اختر نوع الفاتورة المناسب لطابعتك")
        desc_label.setFont(QFont("Segoe UI", 11))
        desc_label.setStyleSheet("""
            QLabel {
                color: #666;
                padding: 10px;
                background-color: #f5f5f5;
                border-radius: 6px;
                border-left: 4px solid #4caf50;
            }
        """)
        layout.addWidget(desc_label)

        # خيارات نوع الفاتورة
        options_layout = QHBoxLayout()
        options_layout.setSpacing(20)

        # خيار التيكت
        ticket_frame = QFrame()
        ticket_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f5e8, stop:1 #c8e6c9);
                border: 3px solid #4caf50;
                border-radius: 15px;
                padding: 25px;
                margin: 5px;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c8e6c9, stop:1 #a5d6a7);
                border-color: #388e3c;
            }
        """)
        ticket_layout = QVBoxLayout(ticket_frame)
        ticket_layout.setSpacing(15)

        # أيقونة وعنوان التيكت
        ticket_header = QHBoxLayout()
        ticket_icon = QLabel("🎫")
        ticket_icon.setFont(QFont("Segoe UI", 24))
        ticket_header.addWidget(ticket_icon)

        self.ticket_radio = QRadioButton("تيكت صغير")
        self.ticket_radio.setFont(QFont("Segoe UI", 14, QFont.Bold))
        self.ticket_radio.setStyleSheet("""
            QRadioButton {
                color: #2e7d32;
                spacing: 10px;
            }
            QRadioButton::indicator {
                width: 20px;
                height: 20px;
            }
            QRadioButton::indicator:unchecked {
                border: 3px solid #4caf50;
                border-radius: 10px;
                background-color: white;
            }
            QRadioButton::indicator:checked {
                border: 3px solid #4caf50;
                border-radius: 10px;
                background-color: #4caf50;
            }
        """)
        self.ticket_radio.setChecked(True)
        ticket_header.addWidget(self.ticket_radio)
        ticket_header.addStretch()

        ticket_layout.addLayout(ticket_header)

        # وصف التيكت
        ticket_desc = QLabel("""
        <div style='color: #2e7d32; line-height: 1.6;'>
        <b>للطابعات الحرارية الصغيرة:</b><br>
        • تيكت مدمج وصغير الحجم<br>
        • يحتوي على المعلومات الأساسية<br>
        • مناسب للطابعات الحرارية<br>
        • سريع في الطباعة<br>
        • يوفر الورق والحبر
        </div>
        """)
        ticket_desc.setFont(QFont("Segoe UI", 10))
        ticket_desc.setWordWrap(True)
        ticket_layout.addWidget(ticket_desc)

        options_layout.addWidget(ticket_frame)

        # خيار الفاتورة الكبيرة
        invoice_frame = QFrame()
        invoice_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fff3e0, stop:1 #ffe0b2);
                border: 3px solid #ff9800;
                border-radius: 15px;
                padding: 25px;
                margin: 5px;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffe0b2, stop:1 #ffcc02);
                border-color: #f57c00;
            }
        """)
        invoice_layout = QVBoxLayout(invoice_frame)
        invoice_layout.setSpacing(15)

        # أيقونة وعنوان الفاتورة
        invoice_header = QHBoxLayout()
        invoice_icon = QLabel("📋")
        invoice_icon.setFont(QFont("Segoe UI", 24))
        invoice_header.addWidget(invoice_icon)

        self.invoice_radio = QRadioButton("فاتورة كبيرة")
        self.invoice_radio.setFont(QFont("Segoe UI", 14, QFont.Bold))
        self.invoice_radio.setStyleSheet("""
            QRadioButton {
                color: #ef6c00;
                spacing: 10px;
            }
            QRadioButton::indicator {
                width: 20px;
                height: 20px;
            }
            QRadioButton::indicator:unchecked {
                border: 3px solid #ff9800;
                border-radius: 10px;
                background-color: white;
            }
            QRadioButton::indicator:checked {
                border: 3px solid #ff9800;
                border-radius: 10px;
                background-color: #ff9800;
            }
        """)
        invoice_header.addWidget(self.invoice_radio)
        invoice_header.addStretch()

        invoice_layout.addLayout(invoice_header)

        # وصف الفاتورة
        invoice_desc = QLabel("""
        <div style='color: #ef6c00; line-height: 1.6;'>
        <b>للطابعات الكبيرة العادية:</b><br>
        • فاتورة كاملة ومفصلة<br>
        • منظمة بجداول واضحة<br>
        • تحتوي على جميع التفاصيل<br>
        • مناسبة للطابعات العادية<br>
        • تصميم احترافي ومنظم
        </div>
        """)
        invoice_desc.setFont(QFont("Segoe UI", 10))
        invoice_desc.setWordWrap(True)
        invoice_layout.addWidget(invoice_desc)

        options_layout.addWidget(invoice_frame)

        layout.addLayout(options_layout)

        return section

    def create_main_save_button(self):
        """إنشاء زر الحفظ الرئيسي"""
        layout = QHBoxLayout()
        layout.setSpacing(20)

        layout.addStretch()

        # زر الحفظ الرئيسي
        self.main_save_button = QPushButton("💾 حفظ جميع الإعدادات")
        self.main_save_button.setFont(QFont("Segoe UI", 14, QFont.Bold))
        self.main_save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4caf50, stop:1 #388e3c);
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 20px 40px;
                border: none;
                border-radius: 12px;
                min-width: 250px;
                min-height: 25px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #388e3c, stop:1 #2e7d32);
                transform: translateY(-3px);
                box-shadow: 0 6px 12px rgba(0,0,0,0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2e7d32, stop:1 #1b5e20);
                transform: translateY(0px);
            }
        """)
        self.main_save_button.clicked.connect(self.save_all_settings)
        layout.addWidget(self.main_save_button)

        layout.addStretch()

        return layout

    def update_printers_table(self):
        """تحديث جدول الطابعات مع جميع الطابعات المتاحة"""
        try:
            # الحصول على الطابعات المتاحة
            try:
                from utils.printer_manager import printer_manager
                printers = printer_manager.get_available_printers()
            except Exception as e:
                print(f"خطأ في الحصول على الطابعات: {e}")
                printers = {}

            # مسح الجدول
            self.printers_table.setRowCount(0)

            if not printers:
                # إضافة صف يوضح عدم وجود طابعات
                self.printers_table.setRowCount(1)

                no_printers_item = QTableWidgetItem("⚠️ لا توجد طابعات متاحة")
                no_printers_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                no_printers_item.setForeground(Qt.red)
                self.printers_table.setItem(0, 0, no_printers_item)

                self.printers_table.setItem(0, 1, QTableWidgetItem(""))
                self.printers_table.setItem(0, 2, QTableWidgetItem(""))
                self.printers_table.setItem(0, 3, QTableWidgetItem(""))

                return

            # إضافة الطابعات إلى الجدول
            self.printers_table.setRowCount(len(printers))

            for row, (name, data) in enumerate(printers.items()):
                # اسم الطابعة
                name_item = QTableWidgetItem(f"🖨️ {name}")
                name_item.setFont(QFont("Segoe UI", 11, QFont.Bold))
                name_item.setForeground(Qt.darkBlue)
                self.printers_table.setItem(row, 0, name_item)

                # نوع الطابعة
                if data.get('is_thermal', False):
                    type_item = QTableWidgetItem("🎫 حرارية")
                    type_item.setForeground(Qt.darkGreen)
                else:
                    type_item = QTableWidgetItem("📄 عادية")
                    type_item.setForeground(Qt.darkMagenta)

                type_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                self.printers_table.setItem(row, 1, type_item)

                # حالة الطابعة
                try:
                    status = printer_manager.get_printer_status(name)
                    if "جاهزة" in status or "Ready" in status:
                        status_item = QTableWidgetItem("✅ جاهزة")
                        status_item.setForeground(Qt.darkGreen)
                    elif "مشغولة" in status or "Busy" in status:
                        status_item = QTableWidgetItem("🔄 مشغولة")
                        status_item.setForeground(Qt.darkYellow)
                    else:
                        status_item = QTableWidgetItem("⚠️ غير متاحة")
                        status_item.setForeground(Qt.red)
                except:
                    status_item = QTableWidgetItem("❓ غير معروف")
                    status_item.setForeground(Qt.gray)

                status_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
                self.printers_table.setItem(row, 2, status_item)

                # زر الاختيار
                select_button = QPushButton("📌 اختيار")
                select_button.setFont(QFont("Segoe UI", 9, QFont.Bold))
                select_button.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #42a5f5, stop:1 #1976d2);
                        color: white;
                        border: none;
                        padding: 8px 15px;
                        border-radius: 6px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #1976d2, stop:1 #1565c0);
                    }
                    QPushButton:pressed {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #1565c0, stop:1 #0d47a1);
                    }
                """)

                # ربط الزر بدالة الاختيار
                select_button.clicked.connect(lambda checked, printer_name=name: self.select_printer(printer_name))
                self.printers_table.setCellWidget(row, 3, select_button)

        except Exception as e:
            print(f"خطأ في تحديث جدول الطابعات: {e}")

    def select_printer(self, printer_name):
        """اختيار طابعة من الجدول"""
        try:
            # حفظ الطابعة المختارة
            self.selected_printer = printer_name

            # تحديث جميع أزرار الاختيار
            for row in range(self.printers_table.rowCount()):
                button = self.printers_table.cellWidget(row, 3)
                if button:
                    item = self.printers_table.item(row, 0)
                    if item and printer_name in item.text():
                        # الطابعة المختارة
                        button.setText("✅ مختارة")
                        button.setStyleSheet("""
                            QPushButton {
                                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                    stop:0 #4caf50, stop:1 #388e3c);
                                color: white;
                                border: none;
                                padding: 8px 15px;
                                border-radius: 6px;
                                font-weight: bold;
                            }
                        """)
                    else:
                        # الطابعات الأخرى
                        button.setText("📌 اختيار")
                        button.setStyleSheet("""
                            QPushButton {
                                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                    stop:0 #42a5f5, stop:1 #1976d2);
                                color: white;
                                border: none;
                                padding: 8px 15px;
                                border-radius: 6px;
                                font-weight: bold;
                            }
                            QPushButton:hover {
                                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                    stop:0 #1976d2, stop:1 #1565c0);
                            }
                        """)

            # إظهار رسالة تأكيد
            QMessageBox.information(self, "✅ تم الاختيار",
                                  f"تم اختيار الطابعة: {printer_name}\n\n"
                                  f"يمكنك الآن اختيار نوع الفاتورة والضغط على 'حفظ جميع الإعدادات'")

        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ", f"خطأ في اختيار الطابعة: {str(e)}")

    def save_all_settings(self):
        """حفظ جميع الإعدادات (الطابعة المختارة ونوع الفاتورة)"""
        try:
            # التحقق من اختيار طابعة
            if not hasattr(self, 'selected_printer') or not self.selected_printer:
                QMessageBox.warning(self, "⚠️ تحذير",
                                  "يرجى اختيار طابعة من القسم الأول أولاً!")
                return

            # تحديد نوع الفاتورة
            if self.ticket_radio.isChecked():
                invoice_type = "ticket"
                invoice_name = "تيكت صغير"
            else:
                invoice_type = "invoice"
                invoice_name = "فاتورة كبيرة"

            # حفظ الإعدادات
            from utils.config_manager import config
            config.set('printer.default_printer', self.selected_printer)
            config.set('printer.print_type', invoice_type)

            # حفظ في مدير الطابعات أيضاً
            try:
                from utils.printer_manager import printer_manager
                if invoice_type == "ticket":
                    printer_manager.set_receipt_printer(self.selected_printer)
                else:
                    printer_manager.set_invoice_printer(self.selected_printer)

                printer_manager.save_settings()
            except Exception as e:
                print(f"خطأ في حفظ إعدادات مدير الطابعات: {e}")

            # رسالة نجاح مفصلة
            QMessageBox.information(self, "✅ تم الحفظ بنجاح",
                                  f"تم حفظ الإعدادات بنجاح!\n\n"
                                  f"📋 الإعدادات المحفوظة:\n"
                                  f"🖨️ الطابعة المختارة: {self.selected_printer}\n"
                                  f"📄 نوع الفاتورة: {invoice_name}\n\n"
                                  f"✨ ستستخدم هذه الإعدادات في جميع عمليات الطباعة القادمة.")

            # إرسال إشارة التغيير
            self.settings_changed.emit()

        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ في الحفظ",
                               f"حدث خطأ أثناء حفظ الإعدادات:\n\n{str(e)}")

    def refresh_printers(self):
        """تحديث قائمة الطابعات"""
        try:
            # تغيير نص الزر أثناء التحديث
            original_text = self.refresh_button.text()
            self.refresh_button.setText("🔄 جاري البحث...")
            self.refresh_button.setEnabled(False)

            # محاولة تحديث قائمة الطابعات
            try:
                from utils.printer_manager import printer_manager
                printer_manager.refresh_printers()

                # تحديث الجدول
                self.update_printers_table()

                # عد الطابعات المتاحة
                printers = printer_manager.get_available_printers()
                printer_count = len(printers) if printers else 0

                # رسالة نجاح
                if printer_count > 0:
                    QMessageBox.information(self, "✅ تم التحديث",
                                          f"تم العثور على {printer_count} طابعة متاحة.\n\n"
                                          f"يمكنك الآن اختيار الطابعة المناسبة من الجدول.")
                else:
                    QMessageBox.warning(self, "⚠️ لا توجد طابعات",
                                      "لم يتم العثور على أي طابعات متاحة.\n\n"
                                      "تأكد من:\n"
                                      "• توصيل الطابعة بالكمبيوتر\n"
                                      "• تثبيت تعريفات الطابعة\n"
                                      "• تشغيل الطابعة")

            except Exception as e:
                QMessageBox.critical(self, "❌ خطأ في التحديث",
                                   f"حدث خطأ أثناء البحث عن الطابعات:\n\n{str(e)}")

        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ عام", f"حدث خطأ غير متوقع: {str(e)}")

        finally:
            # إعادة تفعيل الزر وإعادة النص الأصلي
            self.refresh_button.setText(original_text)
            self.refresh_button.setEnabled(True)

    def load_saved_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            # تحديث جدول الطابعات
            self.update_printers_table()

            # تحميل الطابعة المحفوظة
            from utils.config_manager import config
            saved_printer = config.get('printer.default_printer', None)

            if saved_printer:
                self.selected_printer = saved_printer
                # تحديث أزرار الجدول لإظهار الطابعة المختارة
                for row in range(self.printers_table.rowCount()):
                    item = self.printers_table.item(row, 0)
                    if item and saved_printer in item.text():
                        button = self.printers_table.cellWidget(row, 3)
                        if button:
                            button.setText("✅ مختارة")
                            button.setStyleSheet("""
                                QPushButton {
                                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                        stop:0 #4caf50, stop:1 #388e3c);
                                    color: white;
                                    border: none;
                                    padding: 8px 15px;
                                    border-radius: 6px;
                                    font-weight: bold;
                                }
                            """)

            # تحميل نوع الطباعة
            print_type = config.get('printer.print_type', 'ticket')
            if print_type == "ticket":
                self.ticket_radio.setChecked(True)
            else:
                self.invoice_radio.setChecked(True)

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات المحفوظة: {e}")

    def create_search_card(self):
        """إنشاء بطاقة البحث والتحديث"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
        """)

        layout = QHBoxLayout(card)
        layout.setSpacing(15)

        # أيقونة البحث
        search_icon = QLabel("🔍")
        search_icon.setFont(QFont("Segoe UI", 16))
        search_icon.setStyleSheet("color: #6c757d; margin-right: 10px;")
        layout.addWidget(search_icon)

        # نص البحث
        search_label = QLabel("البحث عن الطابعات المتاحة في النظام")
        search_label.setFont(QFont("Segoe UI", 12))
        search_label.setStyleSheet("color: #495057; font-weight: 500;")
        layout.addWidget(search_label)

        layout.addStretch()

        # زر التحديث
        self.refresh_button = QPushButton("🔄 تحديث الطابعات")
        self.refresh_button.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: bold;
                min-width: 140px;
            }
            QPushButton:hover {
                background-color: #138496;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #117a8b;
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_printers)
        layout.addWidget(self.refresh_button)

        return card

    def create_printer_selection_card(self):
        """إنشاء بطاقة اختيار الطابعة"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setSpacing(15)

        # عنوان البطاقة
        title_layout = QHBoxLayout()
        title_icon = QLabel("🖨️")
        title_icon.setFont(QFont("Segoe UI", 16))
        title_layout.addWidget(title_icon)

        title_label = QLabel("اختيار الطابعة الرئيسية")
        title_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-left: 10px;")
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        layout.addLayout(title_layout)

        # وصف
        desc_label = QLabel("اختر الطابعة التي تريد استخدامها كطابعة افتراضية للنظام")
        desc_label.setFont(QFont("Segoe UI", 10))
        desc_label.setStyleSheet("color: #6c757d; margin-bottom: 10px;")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # القائمة المنسدلة للطابعات
        self.printer_combo = QComboBox()
        self.printer_combo.setFont(QFont("Segoe UI", 12))
        self.printer_combo.setStyleSheet("""
            QComboBox {
                padding: 15px 20px;
                border: 2px solid #ced4da;
                border-radius: 8px;
                background-color: white;
                min-height: 20px;
                font-size: 12px;
                color: #495057;
            }
            QComboBox:focus {
                border-color: #80bdff;
                box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
            }
            QComboBox:hover {
                border-color: #adb5bd;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
                background-color: #f8f9fa;
                border-top-right-radius: 8px;
                border-bottom-right-radius: 8px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
                margin-right: 10px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
                selection-background-color: #e3f2fd;
                padding: 5px;
                font-size: 12px;
            }
            QComboBox QAbstractItemView::item {
                padding: 12px 15px;
                border-bottom: 1px solid #f1f3f4;
                color: #495057;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #f8f9fa;
                color: #2c3e50;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
                font-weight: bold;
            }
        """)
        self.printer_combo.currentTextChanged.connect(self.on_printer_selected)
        layout.addWidget(self.printer_combo)

        # معلومات الطابعة المختارة
        self.printer_info_label = QLabel("ℹ️ لم يتم اختيار طابعة بعد")
        self.printer_info_label.setFont(QFont("Segoe UI", 11))
        self.printer_info_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                padding: 15px;
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                border-left: 4px solid #17a2b8;
                margin-top: 10px;
            }
        """)
        self.printer_info_label.setWordWrap(True)
        layout.addWidget(self.printer_info_label)

        return card

    def create_print_options_card(self):
        """إنشاء بطاقة خيارات الطباعة"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setSpacing(20)

        # عنوان البطاقة
        title_layout = QHBoxLayout()
        title_icon = QLabel("📄")
        title_icon.setFont(QFont("Segoe UI", 16))
        title_layout.addWidget(title_icon)

        title_label = QLabel("نوع الطباعة المفضل")
        title_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-left: 10px;")
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        layout.addLayout(title_layout)

        # وصف
        desc_label = QLabel("اختر نوع الطباعة الذي تفضله للفواتير والإيصالات")
        desc_label.setFont(QFont("Segoe UI", 10))
        desc_label.setStyleSheet("color: #6c757d; margin-bottom: 15px;")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # خيارات الطباعة
        options_layout = QHBoxLayout()
        options_layout.setSpacing(20)

        # خيار التيكت
        ticket_frame = QFrame()
        ticket_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f5e8, stop:1 #d4edda);
                border: 2px solid #28a745;
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d4edda, stop:1 #c3e6cb);
                border-color: #20c997;
            }
        """)
        ticket_layout = QVBoxLayout(ticket_frame)

        self.ticket_radio = QRadioButton("🎫 تيكت صغير")
        self.ticket_radio.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.ticket_radio.setStyleSheet("""
            QRadioButton {
                color: #155724;
                spacing: 10px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
            QRadioButton::indicator:unchecked {
                border: 2px solid #28a745;
                border-radius: 9px;
                background-color: white;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #28a745;
                border-radius: 9px;
                background-color: #28a745;
            }
        """)
        self.ticket_radio.setChecked(True)
        ticket_layout.addWidget(self.ticket_radio)

        ticket_desc = QLabel("مناسب للطابعات الحرارية الصغيرة\nتيكت مدمج يحتوي على المعلومات الأساسية")
        ticket_desc.setFont(QFont("Segoe UI", 9))
        ticket_desc.setStyleSheet("color: #155724; margin-left: 25px; line-height: 1.4;")
        ticket_desc.setWordWrap(True)
        ticket_layout.addWidget(ticket_desc)

        options_layout.addWidget(ticket_frame)

        # خيار الفاتورة
        invoice_frame = QFrame()
        invoice_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fff3cd, stop:1 #ffeaa7);
                border: 2px solid #ffc107;
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffeaa7, stop:1 #fdcb6e);
                border-color: #e17055;
            }
        """)
        invoice_layout = QVBoxLayout(invoice_frame)

        self.invoice_radio = QRadioButton("📄 فاتورة كاملة")
        self.invoice_radio.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.invoice_radio.setStyleSheet("""
            QRadioButton {
                color: #856404;
                spacing: 10px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
            QRadioButton::indicator:unchecked {
                border: 2px solid #ffc107;
                border-radius: 9px;
                background-color: white;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #ffc107;
                border-radius: 9px;
                background-color: #ffc107;
            }
        """)
        invoice_layout.addWidget(self.invoice_radio)

        invoice_desc = QLabel("مناسب للطابعات العادية الكبيرة\nفاتورة مفصلة بجداول ومعلومات شاملة")
        invoice_desc.setFont(QFont("Segoe UI", 9))
        invoice_desc.setStyleSheet("color: #856404; margin-left: 25px; line-height: 1.4;")
        invoice_desc.setWordWrap(True)
        invoice_layout.addWidget(invoice_desc)

        options_layout.addWidget(invoice_frame)

        layout.addLayout(options_layout)

        return card

    def create_buttons_card(self):
        """إنشاء بطاقة الأزرار"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 12px;
                padding: 20px;
                margin: 5px;
            }
        """)

        layout = QHBoxLayout(card)
        layout.setSpacing(15)

        # زر الاختبار
        self.test_button = QPushButton("🧪 اختبار الطابعة")
        self.test_button.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.test_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffc107, stop:1 #e0a800);
                color: #212529;
                font-weight: bold;
                font-size: 12px;
                padding: 15px 30px;
                border: none;
                border-radius: 10px;
                min-width: 160px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e0a800, stop:1 #d39e00);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d39e00, stop:1 #c69500);
                transform: translateY(0px);
            }
        """)
        self.test_button.clicked.connect(self.test_printer)
        layout.addWidget(self.test_button)

        layout.addStretch()

        # زر الحفظ
        self.save_button = QPushButton("💾 حفظ الإعدادات")
        self.save_button.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #20c997);
                color: white;
                font-weight: bold;
                font-size: 12px;
                padding: 15px 30px;
                border: none;
                border-radius: 10px;
                min-width: 160px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #20c997, stop:1 #17a2b8);
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #17a2b8, stop:1 #138496);
                transform: translateY(0px);
            }
        """)
        self.save_button.clicked.connect(self.save_settings)
        layout.addWidget(self.save_button)

        return card

    def create_main_printer_settings(self):
        """إنشاء إعدادات الطابعة الرئيسية"""
        group = QGroupBox("🖨️ " + tr("printers.main_printer"))
        group.setFont(QFont("Segoe UI", 14, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                color: #2c3e50;
                background-color: #f8f9fa;
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(20)

        # البحث عن الطابعات
        search_layout = QHBoxLayout()

        search_label = QLabel("🔍 " + tr("printers.search_printers"))
        search_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        search_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        search_layout.addWidget(search_label)

        self.refresh_button = QPushButton("🔄 " + tr("printers.refresh"))
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_printers)
        search_layout.addWidget(self.refresh_button)

        layout.addLayout(search_layout)

        # قائمة الطابعات المتاحة
        printer_label = QLabel("📋 " + tr("printers.select_printer"))
        printer_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        printer_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(printer_label)

        self.printer_combo = QComboBox()
        self.printer_combo.setFont(QFont("Segoe UI", 12))
        self.printer_combo.setStyleSheet("""
            QComboBox {
                padding: 15px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                min-height: 25px;
                font-size: 12px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
        """)
        self.printer_combo.currentTextChanged.connect(self.on_printer_selected)
        layout.addWidget(self.printer_combo)

        # معلومات الطابعة المختارة
        self.printer_info_label = QLabel()
        self.printer_info_label.setFont(QFont("Segoe UI", 11))
        self.printer_info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border-left: 4px solid #95a5a6;
            }
        """)
        self.printer_info_label.setWordWrap(True)
        layout.addWidget(self.printer_info_label)

        group.setLayout(layout)
        return group

    def create_print_options(self):
        """إنشاء خيارات الطباعة"""
        group = QGroupBox("📄 " + tr("printers.print_options"))
        group.setFont(QFont("Segoe UI", 14, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #fdf2f2;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                color: #c0392b;
                background-color: #fdf2f2;
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(20)

        # خيار التيكت (للطابعات الصغيرة)
        ticket_frame = QFrame()
        ticket_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 2px solid #27ae60;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        ticket_layout = QVBoxLayout(ticket_frame)

        self.ticket_radio = QRadioButton("🎫 " + tr("printers.ticket_option"))
        self.ticket_radio.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.ticket_radio.setStyleSheet("color: #27ae60; margin-bottom: 10px;")
        self.ticket_radio.setChecked(True)  # افتراضي
        ticket_layout.addWidget(self.ticket_radio)

        ticket_desc = QLabel(tr("printers.ticket_description"))
        ticket_desc.setFont(QFont("Segoe UI", 10))
        ticket_desc.setStyleSheet("color: #2d5a2d; margin-left: 25px;")
        ticket_desc.setWordWrap(True)
        ticket_layout.addWidget(ticket_desc)

        layout.addWidget(ticket_frame)

        # خيار الفاتورة (للطابعات الكبيرة)
        invoice_frame = QFrame()
        invoice_frame.setStyleSheet("""
            QFrame {
                background-color: #fff3cd;
                border: 2px solid #f39c12;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        invoice_layout = QVBoxLayout(invoice_frame)

        self.invoice_radio = QRadioButton("📄 " + tr("printers.invoice_option"))
        self.invoice_radio.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.invoice_radio.setStyleSheet("color: #f39c12; margin-bottom: 10px;")
        invoice_layout.addWidget(self.invoice_radio)

        invoice_desc = QLabel(tr("printers.invoice_description"))
        invoice_desc.setFont(QFont("Segoe UI", 10))
        invoice_desc.setStyleSheet("color: #8b6914; margin-left: 25px;")
        invoice_desc.setWordWrap(True)
        invoice_layout.addWidget(invoice_desc)

        layout.addWidget(invoice_frame)

        group.setLayout(layout)
        return group

    def create_action_buttons(self):
        """إنشاء أزرار الحفظ والاختبار"""
        layout = QHBoxLayout()
        layout.setSpacing(15)

        # زر الاختبار
        self.test_button = QPushButton("🧪 " + tr("printers.test_printer"))
        self.test_button.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.test_button.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #000000;
                font-weight: bold;
                font-size: 12px;
                padding: 15px 25px;
                border: none;
                border-radius: 8px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.test_button.clicked.connect(self.test_printer)
        layout.addWidget(self.test_button)

        # زر الحفظ
        self.save_button = QPushButton("💾 " + tr("printers.save_settings"))
        self.save_button.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 15px 25px;
                border: none;
                border-radius: 8px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.save_button.clicked.connect(self.save_settings)
        layout.addWidget(self.save_button)

        layout.addStretch()
        return layout

    def refresh_printers(self):
        """تحديث قائمة الطابعات مع رسائل تحسينية"""
        try:
            # تغيير نص الزر أثناء التحديث
            original_text = self.refresh_button.text()
            self.refresh_button.setText("🔄 جاري البحث...")
            self.refresh_button.setEnabled(False)

            # تحديث معلومات الطابعة
            self.printer_info_label.setText("""
            <div style='background-color: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;'>
                <div style='font-weight: bold; color: #856404;'>
                    🔄 جاري البحث عن الطابعات...
                </div>
                <div style='color: #856404; margin-top: 8px;'>
                    يرجى الانتظار بينما نبحث عن الطابعات المتاحة في النظام
                </div>
            </div>
            """)

            # محاولة تحديث قائمة الطابعات
            try:
                from utils.printer_manager import printer_manager
                printer_manager.refresh_printers()

                # تحديث القائمة المنسدلة
                self.update_printer_combo()

                # عد الطابعات المتاحة
                printers = printer_manager.get_available_printers()
                printer_count = len(printers) if printers else 0

                # رسالة نجاح مع تفاصيل
                if printer_count > 0:
                    QMessageBox.information(self, "✅ تم التحديث بنجاح",
                                          f"تم العثور على {printer_count} طابعة متاحة.\n\n"
                                          f"يمكنك الآن اختيار الطابعة المناسبة من القائمة المنسدلة.")
                else:
                    QMessageBox.warning(self, "⚠️ لا توجد طابعات",
                                      "لم يتم العثور على أي طابعات متاحة.\n\n"
                                      "تأكد من:\n"
                                      "• توصيل الطابعة بالكمبيوتر\n"
                                      "• تثبيت تعريفات الطابعة\n"
                                      "• تشغيل الطابعة")

            except Exception as e:
                print(f"خطأ في تحديث الطابعات: {e}")
                QMessageBox.critical(self, "❌ خطأ في التحديث",
                                   f"حدث خطأ أثناء البحث عن الطابعات:\n\n{str(e)}\n\n"
                                   f"يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.")

                # عرض رسالة خطأ في معلومات الطابعة
                self.printer_info_label.setText(f"""
                <div style='background-color: #f8d7da; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;'>
                    <div style='font-weight: bold; color: #721c24;'>
                        ❌ فشل في البحث عن الطابعات
                    </div>
                    <div style='color: #721c24; margin-top: 8px;'>
                        {str(e)}
                    </div>
                </div>
                """)

        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ عام", f"حدث خطأ غير متوقع: {str(e)}")

        finally:
            # إعادة تفعيل الزر وإعادة النص الأصلي
            self.refresh_button.setText(original_text)
            self.refresh_button.setEnabled(True)

    def update_printer_combo(self):
        """تحديث قائمة الطابعات في القائمة المنسدلة المحسنة"""
        try:
            # مسح القائمة الحالية
            self.printer_combo.clear()

            # إضافة خيار "لا توجد طابعة" مع تصميم جميل
            self.printer_combo.addItem("🚫 لم يتم اختيار طابعة", None)

            # محاولة الحصول على الطابعات المتاحة
            try:
                from utils.printer_manager import printer_manager
                printers = printer_manager.get_available_printers()
            except Exception as e:
                print(f"خطأ في الحصول على الطابعات: {e}")
                printers = {}

            if printers:
                # إضافة الطابعات مع أيقونات جميلة
                for name, data in printers.items():
                    # تحديد نوع الطابعة مع أيقونة مناسبة
                    if data.get('is_thermal', False):
                        icon = "🎫"  # طابعة حرارية
                        type_text = "حرارية"
                    else:
                        icon = "🖨️"  # طابعة عادية
                        type_text = "عادية"

                    # إضافة الطابعة مع معلومات واضحة
                    display_text = f"{icon} {name} ({type_text})"
                    self.printer_combo.addItem(display_text, name)

                # تحديد الطابعة المحفوظة
                saved_printer = self.get_saved_printer()
                if saved_printer:
                    index = self.printer_combo.findData(saved_printer)
                    if index >= 0:
                        self.printer_combo.setCurrentIndex(index)
                        # تحديث معلومات الطابعة فوراً
                        self.on_printer_selected()
                    else:
                        self.printer_info_label.setText("ℹ️ لم يتم اختيار طابعة بعد")
                else:
                    self.printer_info_label.setText("ℹ️ لم يتم اختيار طابعة بعد")
            else:
                # لا توجد طابعات متاحة
                self.printer_combo.addItem("⚠️ لا توجد طابعات متاحة", None)
                self.printer_info_label.setText("""
                <div style='color: #dc3545; font-weight: bold;'>
                ⚠️ لم يتم العثور على طابعات
                </div>
                <div style='color: #6c757d; margin-top: 10px;'>
                • تأكد من توصيل الطابعة بالكمبيوتر<br>
                • تأكد من تثبيت تعريفات الطابعة<br>
                • اضغط على زر "تحديث الطابعات" للبحث مرة أخرى
                </div>
                """)

        except Exception as e:
            print(f"خطأ في تحديث قائمة الطابعات: {e}")
            self.printer_combo.clear()
            self.printer_combo.addItem("❌ خطأ في تحميل الطابعات", None)
            self.printer_info_label.setText(f"""
            <div style='color: #dc3545; font-weight: bold;'>
            ❌ خطأ في تحميل الطابعات
            </div>
            <div style='color: #6c757d; margin-top: 10px;'>
            تفاصيل الخطأ: {str(e)}
            </div>
            """)

    def on_printer_selected(self):
        """عند اختيار طابعة - عرض معلومات محسنة"""
        try:
            current_data = self.printer_combo.currentData()

            if current_data is None:
                self.printer_info_label.setText("ℹ️ لم يتم اختيار طابعة بعد")
                return

            # الحصول على معلومات الطابعة
            try:
                from utils.printer_manager import printer_manager
                printers = printer_manager.get_available_printers()
            except Exception as e:
                print(f"خطأ في الحصول على معلومات الطابعات: {e}")
                printers = {}

            if current_data in printers:
                printer_data = printers[current_data]

                # تحديد نوع الطابعة مع أيقونات جميلة
                if printer_data.get('is_thermal', False):
                    printer_icon = "🎫"
                    printer_type = "طابعة حرارية"
                    recommended_icon = "✅"
                    recommended_text = "موصى بها لطباعة التيكت الصغير"
                    recommended_color = "#28a745"
                    # تحديد خيار التيكت تلقائياً
                    self.ticket_radio.setChecked(True)
                else:
                    printer_icon = "🖨️"
                    printer_type = "طابعة عادية"
                    recommended_icon = "✅"
                    recommended_text = "موصى بها لطباعة الفاتورة الكاملة"
                    recommended_color = "#ffc107"
                    # تحديد خيار الفاتورة تلقائياً
                    self.invoice_radio.setChecked(True)

                # الحصول على حالة الطابعة
                try:
                    status = printer_manager.get_printer_status(current_data)
                    if "جاهزة" in status or "Ready" in status:
                        status_icon = "✅"
                        status_color = "#28a745"
                    elif "مشغولة" in status or "Busy" in status:
                        status_icon = "🔄"
                        status_color = "#ffc107"
                    else:
                        status_icon = "⚠️"
                        status_color = "#dc3545"
                except:
                    status = "غير معروف"
                    status_icon = "❓"
                    status_color = "#6c757d"

                # عرض معلومات الطابعة بتصميم جميل
                info_html = f"""
                <div style='background-color: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;'>
                    <div style='font-size: 14px; font-weight: bold; color: #2c3e50; margin-bottom: 12px;'>
                        {printer_icon} معلومات الطابعة المختارة
                    </div>

                    <div style='margin-bottom: 8px;'>
                        <span style='font-weight: bold; color: #495057;'>📝 الاسم:</span>
                        <span style='color: #2c3e50; margin-left: 10px;'>{current_data}</span>
                    </div>

                    <div style='margin-bottom: 8px;'>
                        <span style='font-weight: bold; color: #495057;'>🔧 النوع:</span>
                        <span style='color: #2c3e50; margin-left: 10px;'>{printer_type}</span>
                    </div>

                    <div style='margin-bottom: 12px;'>
                        <span style='font-weight: bold; color: #495057;'>📊 الحالة:</span>
                        <span style='color: {status_color}; margin-left: 10px; font-weight: bold;'>{status_icon} {status}</span>
                    </div>

                    <div style='background-color: {recommended_color}20; padding: 10px; border-radius: 6px; border-left: 3px solid {recommended_color};'>
                        <span style='font-weight: bold; color: {recommended_color};'>{recommended_icon} التوصية:</span>
                        <span style='color: #495057; margin-left: 10px;'>{recommended_text}</span>
                    </div>
                </div>
                """

                self.printer_info_label.setText(info_html)

            else:
                # الطابعة غير موجودة في القائمة
                self.printer_info_label.setText("""
                <div style='background-color: #f8d7da; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;'>
                    <div style='font-weight: bold; color: #721c24;'>
                        ⚠️ خطأ في معلومات الطابعة
                    </div>
                    <div style='color: #721c24; margin-top: 8px;'>
                        لا يمكن العثور على معلومات هذه الطابعة. يرجى تحديث قائمة الطابعات.
                    </div>
                </div>
                """)

        except Exception as e:
            print(f"خطأ في عرض معلومات الطابعة: {e}")
            self.printer_info_label.setText(f"""
            <div style='background-color: #f8d7da; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;'>
                <div style='font-weight: bold; color: #721c24;'>
                    ❌ خطأ في تحميل معلومات الطابعة
                </div>
                <div style='color: #721c24; margin-top: 8px;'>
                    {str(e)}
                </div>
            </div>
            """)

    def test_printer(self):
        """اختبار الطابعة المختارة"""
        try:
            current_data = self.printer_combo.currentData()

            if current_data is None:
                QMessageBox.warning(self, tr("common.warning"), tr("printers.select_printer_first"))
                return

            # اختبار الطابعة
            from utils.printer_manager import printer_manager
            success, message = printer_manager.test_printer(current_data)

            if success:
                QMessageBox.information(self, tr("common.success"),
                                      f"{tr('printers.test_successful')}\n\n{message}")
            else:
                QMessageBox.critical(self, tr("common.error"),
                                   f"{tr('printers.test_failed')}\n\n{message}")

        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"خطأ في اختبار الطابعة: {str(e)}")

    def save_settings(self):
        """حفظ إعدادات الطابعة"""
        try:
            current_data = self.printer_combo.currentData()

            if current_data is None:
                QMessageBox.warning(self, tr("common.warning"), tr("printers.select_printer_first"))
                return

            # تحديد نوع الطباعة
            print_type = "ticket" if self.ticket_radio.isChecked() else "invoice"

            # حفظ الإعدادات
            from utils.config_manager import config
            config.set('printer.default_printer', current_data)
            config.set('printer.print_type', print_type)

            # حفظ في مدير الطابعات أيضاً
            from utils.printer_manager import printer_manager
            if print_type == "ticket":
                printer_manager.set_receipt_printer(current_data)
            else:
                printer_manager.set_invoice_printer(current_data)

            printer_manager.save_settings()

            QMessageBox.information(self, tr("common.success"), tr("printers.settings_saved"))

            # إرسال إشارة التغيير
            self.settings_changed.emit()

        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"خطأ في حفظ الإعدادات: {str(e)}")

    def get_saved_printer(self):
        """الحصول على الطابعة المحفوظة"""
        try:
            from utils.config_manager import config
            return config.get('printer.default_printer', None)
        except:
            return None

    def load_saved_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            # تحديث قائمة الطابعات
            self.update_printer_combo()

            # تحميل نوع الطباعة
            from utils.config_manager import config
            print_type = config.get('printer.print_type', 'ticket')

            if print_type == "ticket":
                self.ticket_radio.setChecked(True)
            else:
                self.invoice_radio.setChecked(True)

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def create_printers_list(self):
        """إنشاء قائمة الطابعات"""
        self.printers_group = QGroupBox("📋 " + tr("printers.available_printers"))
        self.printers_group.setFont(QFont("Segoe UI", 12, QFont.Bold))
        layout = QVBoxLayout(self.printers_group)
        
        # جدول الطابعات
        self.printers_table = QTableWidget()
        self.printers_table.setColumnCount(4)
        self.printers_table.setHorizontalHeaderLabels([
            tr("printers.printer_name"),
            tr("printers.printer_type"),
            tr("printers.printer_status"),
            tr("printers.printer_description")
        ])
        
        # تعيين عرض الأعمدة
        header = self.printers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        self.printers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.printers_table.setAlternatingRowColors(True)
        self.printers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        layout.addWidget(self.printers_table)
        
        # أزرار الاختبار
        test_layout = QHBoxLayout()
        
        self.test_printer_button = QPushButton("🧪 اختبار الطابعة")
        self.test_printer_button.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #000000;
                font-weight: bold;
                font-size: 11px;
                padding: 8px 16px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.test_printer_button.clicked.connect(self.test_selected_printer)
        test_layout.addWidget(self.test_printer_button)
        
        test_layout.addStretch()
        layout.addLayout(test_layout)
        
        return self.printers_group
    
    def create_printer_settings(self):
        """إنشاء إعدادات الطابعات"""
        self.settings_group = QGroupBox("⚙️ " + tr("printers.printer_settings"))
        self.settings_group.setFont(QFont("Segoe UI", 12, QFont.Bold))
        layout = QVBoxLayout(self.settings_group)
        
        # طابعة الإيصالات
        receipt_frame = QFrame()
        receipt_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 2px solid #28a745;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        receipt_layout = QVBoxLayout(receipt_frame)
        
        self.receipt_label = QLabel("🧾 " + tr("printers.receipt_printer_setting"))
        self.receipt_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.receipt_label.setStyleSheet("color: #155724;")
        receipt_layout.addWidget(self.receipt_label)
        
        self.receipt_printer_combo = QComboBox()
        self.receipt_printer_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #28a745;
                border-radius: 5px;
                background-color: white;
                font-size: 11px;
            }
        """)
        receipt_layout.addWidget(self.receipt_printer_combo)
        
        layout.addWidget(receipt_frame)
        
        # طابعة الفواتير
        invoice_frame = QFrame()
        invoice_frame.setStyleSheet("""
            QFrame {
                background-color: #fff3cd;
                border: 2px solid #ffc107;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        invoice_layout = QVBoxLayout(invoice_frame)
        
        self.invoice_label = QLabel("📄 " + tr("printers.invoice_printer_setting"))
        self.invoice_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.invoice_label.setStyleSheet("color: #856404;")
        invoice_layout.addWidget(self.invoice_label)
        
        self.invoice_printer_combo = QComboBox()
        self.invoice_printer_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ffc107;
                border-radius: 5px;
                background-color: white;
                font-size: 11px;
            }
        """)
        invoice_layout.addWidget(self.invoice_printer_combo)
        
        layout.addWidget(invoice_frame)
        
        # طابعة الملصقات
        label_frame = QFrame()
        label_frame.setStyleSheet("""
            QFrame {
                background-color: #d1ecf1;
                border: 2px solid #17a2b8;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        label_layout = QVBoxLayout(label_frame)
        
        self.label_label = QLabel("🏷️ " + tr("printers.label_printer_setting"))
        self.label_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.label_label.setStyleSheet("color: #0c5460;")
        label_layout.addWidget(self.label_label)
        
        self.label_printer_combo = QComboBox()
        self.label_printer_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #17a2b8;
                border-radius: 5px;
                background-color: white;
                font-size: 11px;
            }
        """)
        label_layout.addWidget(self.label_printer_combo)
        
        layout.addWidget(label_frame)

        # إعدادات الطباعة الافتراضية
        default_settings_frame = QFrame()
        default_settings_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #6c757d;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        default_layout = QVBoxLayout(default_settings_frame)

        self.default_settings_label = QLabel("⚙️ " + tr("printers.default_print_settings"))
        self.default_settings_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.default_settings_label.setStyleSheet("color: #495057;")
        default_layout.addWidget(self.default_settings_label)

        # نوع الطباعة الافتراضي
        print_type_layout = QHBoxLayout()
        print_type_label = QLabel("📄 " + tr("printers.default_print_type") + ":")
        print_type_layout.addWidget(print_type_label)

        self.print_type_combo = QComboBox()
        self.print_type_combo.addItem("🧾 " + tr("printers.receipt"), "receipt")
        self.print_type_combo.addItem("📄 " + tr("printers.invoice"), "invoice")
        self.print_type_combo.setStyleSheet("""
            QComboBox {
                padding: 6px;
                border: 1px solid #6c757d;
                border-radius: 4px;
                background-color: white;
                font-size: 10px;
            }
        """)
        print_type_layout.addWidget(self.print_type_combo)
        print_type_layout.addStretch()
        default_layout.addLayout(print_type_layout)

        # الطباعة التلقائية
        auto_print_layout = QHBoxLayout()
        self.auto_print_checkbox = QCheckBox("🖨️ " + tr("printers.auto_print_after_sale"))
        self.auto_print_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 10px;
                color: #495057;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        auto_print_layout.addWidget(self.auto_print_checkbox)
        auto_print_layout.addStretch()
        default_layout.addLayout(auto_print_layout)

        layout.addWidget(default_settings_frame)

        # معلومات إضافية
        info_text = QTextEdit()
        info_text.setMaximumHeight(100)
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <div style="font-family: Arial; font-size: 10px; color: #6c757d;">
        <h4 style="color: #495057;">💡 نصائح:</h4>
        <ul>
        <li><strong>زر واحد:</strong> دفع وطباعة في نفس الوقت</li>
        <li><strong>نوع الطباعة:</strong> يحدد شكل الطباعة الافتراضي</li>
        <li><strong>الطباعة التلقائية:</strong> طباعة فورية بعد الدفع</li>
        </ul>
        </div>
        """)
        info_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 8px;
            }
        """)
        layout.addWidget(info_text)
        
        return self.settings_group
    
    def load_printers(self):
        """تحميل قائمة الطابعات"""
        try:
            # تحديث قائمة الطابعات
            from utils.printer_manager import printer_manager
            printer_manager.refresh_printers()

            # تحديث الجدول
            self.update_printers_table()

            # تحديث القوائم المنسدلة
            self.update_printer_combos()
        except Exception as e:
            print(f"خطأ في تحميل الطابعات: {e}")

    def update_printers_table(self):
        """تحديث جدول الطابعات"""
        try:
            from utils.printer_manager import printer_manager
            printers = printer_manager.get_available_printers()
        except Exception as e:
            print(f"خطأ في الحصول على الطابعات: {e}")
            printers = {}
        
        self.printers_table.setRowCount(len(printers))
        
        for row, (name, data) in enumerate(printers.items()):
            # اسم الطابعة
            name_item = QTableWidgetItem(name)
            name_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
            self.printers_table.setItem(row, 0, name_item)
            
            # النوع
            if data['is_thermal']:
                printer_type = "🔥 " + tr("printers.thermal_printer")
            else:
                printer_type = "📄 " + tr("printers.standard_printer")
            type_item = QTableWidgetItem(printer_type)
            self.printers_table.setItem(row, 1, type_item)
            
            # الحالة
            status = printer_manager.get_printer_status(name)
            status_item = QTableWidgetItem(status)
            if status == "جاهزة":
                status_item.setBackground(Qt.green)
            elif status == "خطأ":
                status_item.setBackground(Qt.red)
            else:
                status_item.setBackground(Qt.yellow)
            self.printers_table.setItem(row, 2, status_item)
            
            # الوصف
            description = data.get('description', 'غير متاح')
            desc_item = QTableWidgetItem(description)
            self.printers_table.setItem(row, 3, desc_item)
    
    def update_printer_combos(self):
        """تحديث القوائم المنسدلة للطابعات"""
        try:
            from utils.printer_manager import printer_manager
            printers = printer_manager.get_available_printers()
            thermal_printers = printer_manager.get_thermal_printers()
            standard_printers = printer_manager.get_standard_printers()
        except Exception as e:
            print(f"خطأ في الحصول على الطابعات: {e}")
            printers = {}
            thermal_printers = {}
            standard_printers = {}
        
        # مسح القوائم
        self.receipt_printer_combo.clear()
        self.invoice_printer_combo.clear()
        self.label_printer_combo.clear()
        
        # إضافة خيار "لا شيء"
        no_printer_text = "-- " + tr("printers.no_printer") + " --"
        self.receipt_printer_combo.addItem(no_printer_text, None)
        self.invoice_printer_combo.addItem(no_printer_text, None)
        self.label_printer_combo.addItem(no_printer_text, None)
        
        # إضافة الطابعات الحرارية لقائمة الإيصالات
        for name in thermal_printers.keys():
            thermal_text = "🔥 " + tr("printers.thermal_printer")
            self.receipt_printer_combo.addItem(f"🔥 {name}", name)

        # إضافة جميع الطابعات للفواتير والملصقات
        for name in printers.keys():
            if printers[name]['is_thermal']:
                printer_type = "🔥"
            else:
                printer_type = "📄"
            self.invoice_printer_combo.addItem(f"{printer_type} {name}", name)
            self.label_printer_combo.addItem(f"{printer_type} {name}", name)
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        try:
            from utils.config_manager import config

            # تحديد الطابعات المحددة حالياً
            try:
                from utils.printer_manager import printer_manager
                receipt_printer = printer_manager.get_receipt_printer()
                invoice_printer = printer_manager.get_invoice_printer()
                label_printer = printer_manager.get_label_printer()
            except Exception as e:
                print(f"خطأ في الحصول على إعدادات الطابعات: {e}")
                receipt_printer = None
                invoice_printer = None
                label_printer = None

            # تعيين القيم في القوائم المنسدلة (إذا كانت موجودة)
            if hasattr(self, 'receipt_printer_combo'):
                self.set_combo_value(self.receipt_printer_combo, receipt_printer)
            if hasattr(self, 'invoice_printer_combo'):
                self.set_combo_value(self.invoice_printer_combo, invoice_printer)
            if hasattr(self, 'label_printer_combo'):
                self.set_combo_value(self.label_printer_combo, label_printer)

            # تحميل إعدادات الطباعة الافتراضية
            try:
                default_print_type = config.get_default_print_type()
                if hasattr(self, 'print_type_combo'):
                    self.set_combo_value(self.print_type_combo, default_print_type)

                auto_print = config.get_auto_print_setting()
                if hasattr(self, 'auto_print_checkbox'):
                    self.auto_print_checkbox.setChecked(auto_print)
            except Exception as e:
                print(f"خطأ في تحميل إعدادات الطباعة: {e}")

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات الحالية: {e}")
    
    def set_combo_value(self, combo, value):
        """تعيين قيمة في قائمة منسدلة"""
        for i in range(combo.count()):
            if combo.itemData(i) == value:
                combo.setCurrentIndex(i)
                break
    
    def refresh_printers(self):
        """تحديث قائمة الطابعات"""
        self.load_printers()
        QMessageBox.information(self, "تحديث", "تم تحديث قائمة الطابعات بنجاح!")
    
    def test_selected_printer(self):
        """اختبار الطابعة المحددة"""
        current_row = self.printers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طابعة للاختبار!")
            return
        
        printer_name = self.printers_table.item(current_row, 0).text()
        
        # اختبار الطابعة
        try:
            from utils.printer_manager import printer_manager
            success, message = printer_manager.test_printer(printer_name)
        except Exception as e:
            success, message = False, f"خطأ في الاختبار: {e}"
        
        if success:
            QMessageBox.information(self, "نجح الاختبار", 
                                  f"تم اختبار الطابعة '{printer_name}' بنجاح!\n\n{message}")
        else:
            QMessageBox.critical(self, "فشل الاختبار", 
                               f"فشل في اختبار الطابعة '{printer_name}'!\n\n{message}")
    
    def save_settings(self):
        """حفظ إعدادات الطابعات"""
        try:
            from utils.config_manager import config

            # الحصول على القيم المحددة
            receipt_printer = self.receipt_printer_combo.currentData()
            invoice_printer = self.invoice_printer_combo.currentData()
            label_printer = self.label_printer_combo.currentData()

            # تعيين الطابعات
            try:
                from utils.printer_manager import printer_manager

                if receipt_printer:
                    printer_manager.set_receipt_printer(receipt_printer)

                if invoice_printer:
                    printer_manager.set_invoice_printer(invoice_printer)

                if label_printer:
                    printer_manager.set_label_printer(label_printer)

                # حفظ الإعدادات
                printer_manager.save_settings()
            except Exception as e:
                print(f"خطأ في حفظ إعدادات الطابعات: {e}")

            # حفظ إعدادات الطباعة الافتراضية
            try:
                from utils.config_manager import config
                default_print_type = self.print_type_combo.currentData()
                config.set_default_print_type(default_print_type)

                auto_print = self.auto_print_checkbox.isChecked()
                config.set_auto_print_setting(auto_print)

                config.save()
            except Exception as e:
                print(f"خطأ في حفظ الإعدادات: {e}")

            # إشعار النجاح
            QMessageBox.information(self, "تم الحفظ", "تم حفظ إعدادات الطابعات بنجاح!")

            # إرسال إشارة التغيير
            self.settings_changed.emit()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات:\n{str(e)}")

    def retranslate_ui(self):
        """تحديث النصوص عند تغيير اللغة"""
        try:
            # تحديث العنوان الرئيسي
            if hasattr(self, 'title_label'):
                self.title_label.setText("🖨️ " + tr("printers.title"))

            # تحديث أزرار التحكم
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setText("🔄 " + tr("printers.refresh_printers"))

            if hasattr(self, 'save_button'):
                self.save_button.setText("💾 " + tr("printers.save_settings"))

            if hasattr(self, 'test_printer_button'):
                self.test_printer_button.setText("🧪 " + tr("printers.test_printer"))

            # تحديث عناوين المجموعات
            if hasattr(self, 'printers_group'):
                self.printers_group.setTitle("📋 " + tr("printers.available_printers"))

            if hasattr(self, 'settings_group'):
                self.settings_group.setTitle("⚙️ " + tr("printers.printer_settings"))

            # تحديث رؤوس الجدول
            if hasattr(self, 'printers_table'):
                headers = [
                    tr("printers.printer_name"),
                    tr("printers.printer_type"),
                    tr("printers.printer_status"),
                    tr("printers.printer_description")
                ]
                self.printers_table.setHorizontalHeaderLabels(headers)

            # تحديث تسميات الطابعات
            if hasattr(self, 'receipt_label'):
                self.receipt_label.setText("🧾 " + tr("printers.receipt_printer_setting"))

            if hasattr(self, 'invoice_label'):
                self.invoice_label.setText("📄 " + tr("printers.invoice_printer_setting"))

            if hasattr(self, 'label_label'):
                self.label_label.setText("🏷️ " + tr("printers.label_printer_setting"))

            # تحديث خيار "لا شيء" في القوائم المنسدلة
            self.update_combo_no_printer_option()

            # تحديث محتوى الجدول
            self.update_printers_table()

        except Exception as e:
            print(f"خطأ في تحديث ترجمات الطابعات: {e}")

    def update_combo_no_printer_option(self):
        """تحديث خيار 'لا شيء' في القوائم المنسدلة"""
        try:
            combos = [self.receipt_printer_combo, self.invoice_printer_combo, self.label_printer_combo]

            for combo in combos:
                if combo.count() > 0:
                    # حفظ القيمة المحددة حالياً
                    current_data = combo.currentData()

                    # تحديث النص للعنصر الأول (لا شيء)
                    combo.setItemText(0, "-- " + tr("printers.no_printer") + " --")

                    # استعادة القيمة المحددة
                    for i in range(combo.count()):
                        if combo.itemData(i) == current_data:
                            combo.setCurrentIndex(i)
                            break
        except Exception as e:
            print(f"خطأ في تحديث خيارات القوائم المنسدلة: {e}")
