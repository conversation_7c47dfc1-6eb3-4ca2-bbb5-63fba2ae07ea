"""
واجهة إعدادات الطابعات - OnePos
Printer Settings Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QComboBox, QPushButton, QGroupBox, QTableWidget,
                            QTableWidgetItem, QMessageBox, QFrame, QTextEdit,
                            QSplitter, QHeaderView, QCheckBox, QRadioButton,
                            QButtonGroup)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from utils.translator import tr

class PrinterSettingsWidget(QWidget):
    """واجهة إعدادات الطابعات"""
    
    settings_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_printers()
        self.load_current_settings()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم المبسطة"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)

        # العنوان الرئيسي
        self.title_label = QLabel("🖨️ " + tr("printers.title"))
        self.title_label.setFont(QFont("Segoe UI", 20, QFont.Bold))
        self.title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                margin-bottom: 15px;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border-left: 4px solid #3498db;
            }
        """)
        layout.addWidget(self.title_label)

        # إنشاء إعدادات الطابعة الرئيسية
        main_settings = self.create_main_printer_settings()
        layout.addWidget(main_settings)

        # إنشاء خيارات الطباعة
        print_options = self.create_print_options()
        layout.addWidget(print_options)

        # أزرار الحفظ والاختبار
        buttons_layout = self.create_action_buttons()
        layout.addLayout(buttons_layout)

        # مساحة فارغة
        layout.addStretch()

    def create_main_printer_settings(self):
        """إنشاء إعدادات الطابعة الرئيسية"""
        group = QGroupBox("🖨️ " + tr("printers.main_printer"))
        group.setFont(QFont("Segoe UI", 14, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                color: #2c3e50;
                background-color: #f8f9fa;
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(20)

        # البحث عن الطابعات
        search_layout = QHBoxLayout()

        search_label = QLabel("🔍 " + tr("printers.search_printers"))
        search_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        search_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        search_layout.addWidget(search_label)

        self.refresh_button = QPushButton("🔄 " + tr("printers.refresh"))
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_printers)
        search_layout.addWidget(self.refresh_button)

        layout.addLayout(search_layout)

        # قائمة الطابعات المتاحة
        printer_label = QLabel("📋 " + tr("printers.select_printer"))
        printer_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        printer_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(printer_label)

        self.printer_combo = QComboBox()
        self.printer_combo.setFont(QFont("Segoe UI", 12))
        self.printer_combo.setStyleSheet("""
            QComboBox {
                padding: 15px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                min-height: 25px;
                font-size: 12px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
        """)
        self.printer_combo.currentTextChanged.connect(self.on_printer_selected)
        layout.addWidget(self.printer_combo)

        # معلومات الطابعة المختارة
        self.printer_info_label = QLabel()
        self.printer_info_label.setFont(QFont("Segoe UI", 11))
        self.printer_info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border-left: 4px solid #95a5a6;
            }
        """)
        self.printer_info_label.setWordWrap(True)
        layout.addWidget(self.printer_info_label)

        group.setLayout(layout)
        return group

    def create_print_options(self):
        """إنشاء خيارات الطباعة"""
        group = QGroupBox("📄 " + tr("printers.print_options"))
        group.setFont(QFont("Segoe UI", 14, QFont.Bold))
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #fdf2f2;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                color: #c0392b;
                background-color: #fdf2f2;
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(20)

        # خيار التيكت (للطابعات الصغيرة)
        ticket_frame = QFrame()
        ticket_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 2px solid #27ae60;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        ticket_layout = QVBoxLayout(ticket_frame)

        self.ticket_radio = QRadioButton("🎫 " + tr("printers.ticket_option"))
        self.ticket_radio.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.ticket_radio.setStyleSheet("color: #27ae60; margin-bottom: 10px;")
        self.ticket_radio.setChecked(True)  # افتراضي
        ticket_layout.addWidget(self.ticket_radio)

        ticket_desc = QLabel(tr("printers.ticket_description"))
        ticket_desc.setFont(QFont("Segoe UI", 10))
        ticket_desc.setStyleSheet("color: #2d5a2d; margin-left: 25px;")
        ticket_desc.setWordWrap(True)
        ticket_layout.addWidget(ticket_desc)

        layout.addWidget(ticket_frame)

        # خيار الفاتورة (للطابعات الكبيرة)
        invoice_frame = QFrame()
        invoice_frame.setStyleSheet("""
            QFrame {
                background-color: #fff3cd;
                border: 2px solid #f39c12;
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }
        """)
        invoice_layout = QVBoxLayout(invoice_frame)

        self.invoice_radio = QRadioButton("📄 " + tr("printers.invoice_option"))
        self.invoice_radio.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.invoice_radio.setStyleSheet("color: #f39c12; margin-bottom: 10px;")
        invoice_layout.addWidget(self.invoice_radio)

        invoice_desc = QLabel(tr("printers.invoice_description"))
        invoice_desc.setFont(QFont("Segoe UI", 10))
        invoice_desc.setStyleSheet("color: #8b6914; margin-left: 25px;")
        invoice_desc.setWordWrap(True)
        invoice_layout.addWidget(invoice_desc)

        layout.addWidget(invoice_frame)

        group.setLayout(layout)
        return group

    def create_action_buttons(self):
        """إنشاء أزرار الحفظ والاختبار"""
        layout = QHBoxLayout()
        layout.setSpacing(15)

        # زر الاختبار
        self.test_button = QPushButton("🧪 " + tr("printers.test_printer"))
        self.test_button.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.test_button.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #000000;
                font-weight: bold;
                font-size: 12px;
                padding: 15px 25px;
                border: none;
                border-radius: 8px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.test_button.clicked.connect(self.test_printer)
        layout.addWidget(self.test_button)

        # زر الحفظ
        self.save_button = QPushButton("💾 " + tr("printers.save_settings"))
        self.save_button.setFont(QFont("Segoe UI", 12, QFont.Bold))
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 15px 25px;
                border: none;
                border-radius: 8px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.save_button.clicked.connect(self.save_settings)
        layout.addWidget(self.save_button)

        layout.addStretch()
        return layout

    def refresh_printers(self):
        """تحديث قائمة الطابعات"""
        try:
            # تحديث قائمة الطابعات من النظام
            from utils.printer_manager import printer_manager
            printer_manager.refresh_printers()

            # تحديث القائمة المنسدلة
            self.update_printer_combo()

            QMessageBox.information(self, tr("common.success"), tr("printers.printers_refreshed"))
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"خطأ في تحديث الطابعات: {str(e)}")

    def update_printer_combo(self):
        """تحديث قائمة الطابعات في القائمة المنسدلة"""
        try:
            from utils.printer_manager import printer_manager

            # مسح القائمة الحالية
            self.printer_combo.clear()

            # إضافة خيار "لا توجد طابعة"
            self.printer_combo.addItem("-- " + tr("printers.no_printer") + " --", None)

            # الحصول على الطابعات المتاحة
            printers = printer_manager.get_available_printers()

            if printers:
                for name, data in printers.items():
                    # تحديد نوع الطابعة
                    if data.get('is_thermal', False):
                        icon = "🎫"  # للطابعات الحرارية (تيكت)
                    else:
                        icon = "📄"  # للطابعات العادية (فاتورة)

                    self.printer_combo.addItem(f"{icon} {name}", name)

                # تحديد الطابعة المحفوظة
                saved_printer = self.get_saved_printer()
                if saved_printer:
                    index = self.printer_combo.findData(saved_printer)
                    if index >= 0:
                        self.printer_combo.setCurrentIndex(index)
            else:
                self.printer_info_label.setText("⚠️ " + tr("printers.no_printers_found"))

        except Exception as e:
            print(f"خطأ في تحديث قائمة الطابعات: {e}")

    def on_printer_selected(self):
        """عند اختيار طابعة"""
        try:
            current_data = self.printer_combo.currentData()

            if current_data is None:
                self.printer_info_label.setText("ℹ️ " + tr("printers.no_printer_selected"))
                return

            # الحصول على معلومات الطابعة
            from utils.printer_manager import printer_manager
            printers = printer_manager.get_available_printers()

            if current_data in printers:
                printer_data = printers[current_data]

                # تحديد نوع الطابعة
                if printer_data.get('is_thermal', False):
                    printer_type = "🎫 " + tr("printers.thermal_printer")
                    recommended = tr("printers.recommended_for_tickets")
                else:
                    printer_type = "📄 " + tr("printers.standard_printer")
                    recommended = tr("printers.recommended_for_invoices")

                # عرض معلومات الطابعة
                info_text = f"""
                <b>📋 {tr('printers.printer_info')}:</b><br>
                <b>{tr('printers.name')}:</b> {current_data}<br>
                <b>{tr('printers.type')}:</b> {printer_type}<br>
                <b>{tr('printers.status')}:</b> {printer_manager.get_printer_status(current_data)}<br>
                <b>{tr('printers.recommendation')}:</b> {recommended}
                """

                self.printer_info_label.setText(info_text)

                # تحديث خيارات الطباعة تلقائياً
                if printer_data.get('is_thermal', False):
                    self.ticket_radio.setChecked(True)
                else:
                    self.invoice_radio.setChecked(True)

        except Exception as e:
            print(f"خطأ في عرض معلومات الطابعة: {e}")

    def test_printer(self):
        """اختبار الطابعة المختارة"""
        try:
            current_data = self.printer_combo.currentData()

            if current_data is None:
                QMessageBox.warning(self, tr("common.warning"), tr("printers.select_printer_first"))
                return

            # اختبار الطابعة
            from utils.printer_manager import printer_manager
            success, message = printer_manager.test_printer(current_data)

            if success:
                QMessageBox.information(self, tr("common.success"),
                                      f"{tr('printers.test_successful')}\n\n{message}")
            else:
                QMessageBox.critical(self, tr("common.error"),
                                   f"{tr('printers.test_failed')}\n\n{message}")

        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"خطأ في اختبار الطابعة: {str(e)}")

    def save_settings(self):
        """حفظ إعدادات الطابعة"""
        try:
            current_data = self.printer_combo.currentData()

            if current_data is None:
                QMessageBox.warning(self, tr("common.warning"), tr("printers.select_printer_first"))
                return

            # تحديد نوع الطباعة
            print_type = "ticket" if self.ticket_radio.isChecked() else "invoice"

            # حفظ الإعدادات
            from utils.config_manager import config
            config.set('printer.default_printer', current_data)
            config.set('printer.print_type', print_type)

            # حفظ في مدير الطابعات أيضاً
            from utils.printer_manager import printer_manager
            if print_type == "ticket":
                printer_manager.set_receipt_printer(current_data)
            else:
                printer_manager.set_invoice_printer(current_data)

            printer_manager.save_settings()

            QMessageBox.information(self, tr("common.success"), tr("printers.settings_saved"))

            # إرسال إشارة التغيير
            self.settings_changed.emit()

        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"خطأ في حفظ الإعدادات: {str(e)}")

    def get_saved_printer(self):
        """الحصول على الطابعة المحفوظة"""
        try:
            from utils.config_manager import config
            return config.get('printer.default_printer', None)
        except:
            return None

    def load_saved_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            # تحديث قائمة الطابعات
            self.update_printer_combo()

            # تحميل نوع الطباعة
            from utils.config_manager import config
            print_type = config.get('printer.print_type', 'ticket')

            if print_type == "ticket":
                self.ticket_radio.setChecked(True)
            else:
                self.invoice_radio.setChecked(True)

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def create_printers_list(self):
        """إنشاء قائمة الطابعات"""
        self.printers_group = QGroupBox("📋 " + tr("printers.available_printers"))
        self.printers_group.setFont(QFont("Segoe UI", 12, QFont.Bold))
        layout = QVBoxLayout(self.printers_group)
        
        # جدول الطابعات
        self.printers_table = QTableWidget()
        self.printers_table.setColumnCount(4)
        self.printers_table.setHorizontalHeaderLabels([
            tr("printers.printer_name"),
            tr("printers.printer_type"),
            tr("printers.printer_status"),
            tr("printers.printer_description")
        ])
        
        # تعيين عرض الأعمدة
        header = self.printers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        self.printers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.printers_table.setAlternatingRowColors(True)
        self.printers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        layout.addWidget(self.printers_table)
        
        # أزرار الاختبار
        test_layout = QHBoxLayout()
        
        self.test_printer_button = QPushButton("🧪 اختبار الطابعة")
        self.test_printer_button.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #000000;
                font-weight: bold;
                font-size: 11px;
                padding: 8px 16px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.test_printer_button.clicked.connect(self.test_selected_printer)
        test_layout.addWidget(self.test_printer_button)
        
        test_layout.addStretch()
        layout.addLayout(test_layout)
        
        return self.printers_group
    
    def create_printer_settings(self):
        """إنشاء إعدادات الطابعات"""
        self.settings_group = QGroupBox("⚙️ " + tr("printers.printer_settings"))
        self.settings_group.setFont(QFont("Segoe UI", 12, QFont.Bold))
        layout = QVBoxLayout(self.settings_group)
        
        # طابعة الإيصالات
        receipt_frame = QFrame()
        receipt_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 2px solid #28a745;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        receipt_layout = QVBoxLayout(receipt_frame)
        
        self.receipt_label = QLabel("🧾 " + tr("printers.receipt_printer_setting"))
        self.receipt_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.receipt_label.setStyleSheet("color: #155724;")
        receipt_layout.addWidget(self.receipt_label)
        
        self.receipt_printer_combo = QComboBox()
        self.receipt_printer_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #28a745;
                border-radius: 5px;
                background-color: white;
                font-size: 11px;
            }
        """)
        receipt_layout.addWidget(self.receipt_printer_combo)
        
        layout.addWidget(receipt_frame)
        
        # طابعة الفواتير
        invoice_frame = QFrame()
        invoice_frame.setStyleSheet("""
            QFrame {
                background-color: #fff3cd;
                border: 2px solid #ffc107;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        invoice_layout = QVBoxLayout(invoice_frame)
        
        self.invoice_label = QLabel("📄 " + tr("printers.invoice_printer_setting"))
        self.invoice_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.invoice_label.setStyleSheet("color: #856404;")
        invoice_layout.addWidget(self.invoice_label)
        
        self.invoice_printer_combo = QComboBox()
        self.invoice_printer_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #ffc107;
                border-radius: 5px;
                background-color: white;
                font-size: 11px;
            }
        """)
        invoice_layout.addWidget(self.invoice_printer_combo)
        
        layout.addWidget(invoice_frame)
        
        # طابعة الملصقات
        label_frame = QFrame()
        label_frame.setStyleSheet("""
            QFrame {
                background-color: #d1ecf1;
                border: 2px solid #17a2b8;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        label_layout = QVBoxLayout(label_frame)
        
        self.label_label = QLabel("🏷️ " + tr("printers.label_printer_setting"))
        self.label_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.label_label.setStyleSheet("color: #0c5460;")
        label_layout.addWidget(self.label_label)
        
        self.label_printer_combo = QComboBox()
        self.label_printer_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #17a2b8;
                border-radius: 5px;
                background-color: white;
                font-size: 11px;
            }
        """)
        label_layout.addWidget(self.label_printer_combo)
        
        layout.addWidget(label_frame)

        # إعدادات الطباعة الافتراضية
        default_settings_frame = QFrame()
        default_settings_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #6c757d;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        default_layout = QVBoxLayout(default_settings_frame)

        self.default_settings_label = QLabel("⚙️ " + tr("printers.default_print_settings"))
        self.default_settings_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.default_settings_label.setStyleSheet("color: #495057;")
        default_layout.addWidget(self.default_settings_label)

        # نوع الطباعة الافتراضي
        print_type_layout = QHBoxLayout()
        print_type_label = QLabel("📄 " + tr("printers.default_print_type") + ":")
        print_type_layout.addWidget(print_type_label)

        self.print_type_combo = QComboBox()
        self.print_type_combo.addItem("🧾 " + tr("printers.receipt"), "receipt")
        self.print_type_combo.addItem("📄 " + tr("printers.invoice"), "invoice")
        self.print_type_combo.setStyleSheet("""
            QComboBox {
                padding: 6px;
                border: 1px solid #6c757d;
                border-radius: 4px;
                background-color: white;
                font-size: 10px;
            }
        """)
        print_type_layout.addWidget(self.print_type_combo)
        print_type_layout.addStretch()
        default_layout.addLayout(print_type_layout)

        # الطباعة التلقائية
        auto_print_layout = QHBoxLayout()
        self.auto_print_checkbox = QCheckBox("🖨️ " + tr("printers.auto_print_after_sale"))
        self.auto_print_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 10px;
                color: #495057;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        auto_print_layout.addWidget(self.auto_print_checkbox)
        auto_print_layout.addStretch()
        default_layout.addLayout(auto_print_layout)

        layout.addWidget(default_settings_frame)

        # معلومات إضافية
        info_text = QTextEdit()
        info_text.setMaximumHeight(100)
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <div style="font-family: Arial; font-size: 10px; color: #6c757d;">
        <h4 style="color: #495057;">💡 نصائح:</h4>
        <ul>
        <li><strong>زر واحد:</strong> دفع وطباعة في نفس الوقت</li>
        <li><strong>نوع الطباعة:</strong> يحدد شكل الطباعة الافتراضي</li>
        <li><strong>الطباعة التلقائية:</strong> طباعة فورية بعد الدفع</li>
        </ul>
        </div>
        """)
        info_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 8px;
            }
        """)
        layout.addWidget(info_text)
        
        return self.settings_group
    
    def load_printers(self):
        """تحميل قائمة الطابعات"""
        try:
            # تحديث قائمة الطابعات
            from utils.printer_manager import printer_manager
            printer_manager.refresh_printers()

            # تحديث الجدول
            self.update_printers_table()

            # تحديث القوائم المنسدلة
            self.update_printer_combos()
        except Exception as e:
            print(f"خطأ في تحميل الطابعات: {e}")

    def update_printers_table(self):
        """تحديث جدول الطابعات"""
        try:
            from utils.printer_manager import printer_manager
            printers = printer_manager.get_available_printers()
        except Exception as e:
            print(f"خطأ في الحصول على الطابعات: {e}")
            printers = {}
        
        self.printers_table.setRowCount(len(printers))
        
        for row, (name, data) in enumerate(printers.items()):
            # اسم الطابعة
            name_item = QTableWidgetItem(name)
            name_item.setFont(QFont("Segoe UI", 10, QFont.Bold))
            self.printers_table.setItem(row, 0, name_item)
            
            # النوع
            if data['is_thermal']:
                printer_type = "🔥 " + tr("printers.thermal_printer")
            else:
                printer_type = "📄 " + tr("printers.standard_printer")
            type_item = QTableWidgetItem(printer_type)
            self.printers_table.setItem(row, 1, type_item)
            
            # الحالة
            status = printer_manager.get_printer_status(name)
            status_item = QTableWidgetItem(status)
            if status == "جاهزة":
                status_item.setBackground(Qt.green)
            elif status == "خطأ":
                status_item.setBackground(Qt.red)
            else:
                status_item.setBackground(Qt.yellow)
            self.printers_table.setItem(row, 2, status_item)
            
            # الوصف
            description = data.get('description', 'غير متاح')
            desc_item = QTableWidgetItem(description)
            self.printers_table.setItem(row, 3, desc_item)
    
    def update_printer_combos(self):
        """تحديث القوائم المنسدلة للطابعات"""
        try:
            from utils.printer_manager import printer_manager
            printers = printer_manager.get_available_printers()
            thermal_printers = printer_manager.get_thermal_printers()
            standard_printers = printer_manager.get_standard_printers()
        except Exception as e:
            print(f"خطأ في الحصول على الطابعات: {e}")
            printers = {}
            thermal_printers = {}
            standard_printers = {}
        
        # مسح القوائم
        self.receipt_printer_combo.clear()
        self.invoice_printer_combo.clear()
        self.label_printer_combo.clear()
        
        # إضافة خيار "لا شيء"
        no_printer_text = "-- " + tr("printers.no_printer") + " --"
        self.receipt_printer_combo.addItem(no_printer_text, None)
        self.invoice_printer_combo.addItem(no_printer_text, None)
        self.label_printer_combo.addItem(no_printer_text, None)
        
        # إضافة الطابعات الحرارية لقائمة الإيصالات
        for name in thermal_printers.keys():
            thermal_text = "🔥 " + tr("printers.thermal_printer")
            self.receipt_printer_combo.addItem(f"🔥 {name}", name)

        # إضافة جميع الطابعات للفواتير والملصقات
        for name in printers.keys():
            if printers[name]['is_thermal']:
                printer_type = "🔥"
            else:
                printer_type = "📄"
            self.invoice_printer_combo.addItem(f"{printer_type} {name}", name)
            self.label_printer_combo.addItem(f"{printer_type} {name}", name)
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        try:
            from utils.config_manager import config

            # تحديد الطابعات المحددة حالياً
            try:
                from utils.printer_manager import printer_manager
                receipt_printer = printer_manager.get_receipt_printer()
                invoice_printer = printer_manager.get_invoice_printer()
                label_printer = printer_manager.get_label_printer()
            except Exception as e:
                print(f"خطأ في الحصول على إعدادات الطابعات: {e}")
                receipt_printer = None
                invoice_printer = None
                label_printer = None

            # تعيين القيم في القوائم المنسدلة (إذا كانت موجودة)
            if hasattr(self, 'receipt_printer_combo'):
                self.set_combo_value(self.receipt_printer_combo, receipt_printer)
            if hasattr(self, 'invoice_printer_combo'):
                self.set_combo_value(self.invoice_printer_combo, invoice_printer)
            if hasattr(self, 'label_printer_combo'):
                self.set_combo_value(self.label_printer_combo, label_printer)

            # تحميل إعدادات الطباعة الافتراضية
            try:
                default_print_type = config.get_default_print_type()
                if hasattr(self, 'print_type_combo'):
                    self.set_combo_value(self.print_type_combo, default_print_type)

                auto_print = config.get_auto_print_setting()
                if hasattr(self, 'auto_print_checkbox'):
                    self.auto_print_checkbox.setChecked(auto_print)
            except Exception as e:
                print(f"خطأ في تحميل إعدادات الطباعة: {e}")

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات الحالية: {e}")
    
    def set_combo_value(self, combo, value):
        """تعيين قيمة في قائمة منسدلة"""
        for i in range(combo.count()):
            if combo.itemData(i) == value:
                combo.setCurrentIndex(i)
                break
    
    def refresh_printers(self):
        """تحديث قائمة الطابعات"""
        self.load_printers()
        QMessageBox.information(self, "تحديث", "تم تحديث قائمة الطابعات بنجاح!")
    
    def test_selected_printer(self):
        """اختبار الطابعة المحددة"""
        current_row = self.printers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار طابعة للاختبار!")
            return
        
        printer_name = self.printers_table.item(current_row, 0).text()
        
        # اختبار الطابعة
        try:
            from utils.printer_manager import printer_manager
            success, message = printer_manager.test_printer(printer_name)
        except Exception as e:
            success, message = False, f"خطأ في الاختبار: {e}"
        
        if success:
            QMessageBox.information(self, "نجح الاختبار", 
                                  f"تم اختبار الطابعة '{printer_name}' بنجاح!\n\n{message}")
        else:
            QMessageBox.critical(self, "فشل الاختبار", 
                               f"فشل في اختبار الطابعة '{printer_name}'!\n\n{message}")
    
    def save_settings(self):
        """حفظ إعدادات الطابعات"""
        try:
            from utils.config_manager import config

            # الحصول على القيم المحددة
            receipt_printer = self.receipt_printer_combo.currentData()
            invoice_printer = self.invoice_printer_combo.currentData()
            label_printer = self.label_printer_combo.currentData()

            # تعيين الطابعات
            try:
                from utils.printer_manager import printer_manager

                if receipt_printer:
                    printer_manager.set_receipt_printer(receipt_printer)

                if invoice_printer:
                    printer_manager.set_invoice_printer(invoice_printer)

                if label_printer:
                    printer_manager.set_label_printer(label_printer)

                # حفظ الإعدادات
                printer_manager.save_settings()
            except Exception as e:
                print(f"خطأ في حفظ إعدادات الطابعات: {e}")

            # حفظ إعدادات الطباعة الافتراضية
            try:
                from utils.config_manager import config
                default_print_type = self.print_type_combo.currentData()
                config.set_default_print_type(default_print_type)

                auto_print = self.auto_print_checkbox.isChecked()
                config.set_auto_print_setting(auto_print)

                config.save()
            except Exception as e:
                print(f"خطأ في حفظ الإعدادات: {e}")

            # إشعار النجاح
            QMessageBox.information(self, "تم الحفظ", "تم حفظ إعدادات الطابعات بنجاح!")

            # إرسال إشارة التغيير
            self.settings_changed.emit()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات:\n{str(e)}")

    def retranslate_ui(self):
        """تحديث النصوص عند تغيير اللغة"""
        try:
            # تحديث العنوان الرئيسي
            if hasattr(self, 'title_label'):
                self.title_label.setText("🖨️ " + tr("printers.title"))

            # تحديث أزرار التحكم
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setText("🔄 " + tr("printers.refresh_printers"))

            if hasattr(self, 'save_button'):
                self.save_button.setText("💾 " + tr("printers.save_settings"))

            if hasattr(self, 'test_printer_button'):
                self.test_printer_button.setText("🧪 " + tr("printers.test_printer"))

            # تحديث عناوين المجموعات
            if hasattr(self, 'printers_group'):
                self.printers_group.setTitle("📋 " + tr("printers.available_printers"))

            if hasattr(self, 'settings_group'):
                self.settings_group.setTitle("⚙️ " + tr("printers.printer_settings"))

            # تحديث رؤوس الجدول
            if hasattr(self, 'printers_table'):
                headers = [
                    tr("printers.printer_name"),
                    tr("printers.printer_type"),
                    tr("printers.printer_status"),
                    tr("printers.printer_description")
                ]
                self.printers_table.setHorizontalHeaderLabels(headers)

            # تحديث تسميات الطابعات
            if hasattr(self, 'receipt_label'):
                self.receipt_label.setText("🧾 " + tr("printers.receipt_printer_setting"))

            if hasattr(self, 'invoice_label'):
                self.invoice_label.setText("📄 " + tr("printers.invoice_printer_setting"))

            if hasattr(self, 'label_label'):
                self.label_label.setText("🏷️ " + tr("printers.label_printer_setting"))

            # تحديث خيار "لا شيء" في القوائم المنسدلة
            self.update_combo_no_printer_option()

            # تحديث محتوى الجدول
            self.update_printers_table()

        except Exception as e:
            print(f"خطأ في تحديث ترجمات الطابعات: {e}")

    def update_combo_no_printer_option(self):
        """تحديث خيار 'لا شيء' في القوائم المنسدلة"""
        try:
            combos = [self.receipt_printer_combo, self.invoice_printer_combo, self.label_printer_combo]

            for combo in combos:
                if combo.count() > 0:
                    # حفظ القيمة المحددة حالياً
                    current_data = combo.currentData()

                    # تحديث النص للعنصر الأول (لا شيء)
                    combo.setItemText(0, "-- " + tr("printers.no_printer") + " --")

                    # استعادة القيمة المحددة
                    for i in range(combo.count()):
                        if combo.itemData(i) == current_data:
                            combo.setCurrentIndex(i)
                            break
        except Exception as e:
            print(f"خطأ في تحديث خيارات القوائم المنسدلة: {e}")
