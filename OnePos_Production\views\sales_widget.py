"""
Sales management widget for OnePos POS System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QComboBox, QDateEdit, QDialog, QFormLayout, 
                            QDialogButtonBox, QMessageBox, QHeaderView, 
                            QAbstractItemView, QGroupBox, QSplitter, QTabWidget,
                            QTextEdit, QFrame)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont

from models.sale import Sale, SaleItem
from models.customer import Customer
from models.user import User
from utils.translator import tr
from utils.print_manager import print_manager

def create_dark_label(text):
    """Create a dark label with proper styling"""
    label = QLabel(text)
    label.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 13px;")
    return label
from datetime import datetime, date


class SaleDetailsDialog(QDialog):
    """Dialog for viewing sale details"""
    
    def __init__(self, sale, parent=None):
        super().__init__(parent)
        self.sale = sale
        
        self.setWindowTitle(f"Sale Details - {sale.invoice_number}")
        self.setModal(True)
        self.resize(700, 500)
        
        self.setup_ui()
        self.load_sale_details()
    
    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()
        
        # Sale header info
        header_group = QGroupBox("Sale Information")
        header_layout = QFormLayout()
        
        self.invoice_label = QLabel()
        header_layout.addRow(create_dark_label("Invoice Number:"), self.invoice_label)

        self.date_label = QLabel()
        header_layout.addRow(create_dark_label("Date:"), self.date_label)

        self.customer_label = QLabel()
        header_layout.addRow(create_dark_label("Customer:"), self.customer_label)

        self.cashier_label = QLabel()
        header_layout.addRow(create_dark_label("Cashier:"), self.cashier_label)

        self.status_label = QLabel()
        header_layout.addRow(create_dark_label("Status:"), self.status_label)
        
        header_group.setLayout(header_layout)
        layout.addWidget(header_group)
        
        # Sale items
        items_group = QGroupBox("Sale Items")
        items_layout = QVBoxLayout()
        
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            "Product", "Quantity", "Unit Price", "Discount", "Total"
        ])
        
        # Configure table
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        items_layout.addWidget(self.items_table)
        items_group.setLayout(items_layout)
        layout.addWidget(items_group)
        
        # Totals
        totals_group = QGroupBox("Totals")
        totals_layout = QFormLayout()
        
        self.subtotal_label = QLabel()
        totals_layout.addRow(create_dark_label("Subtotal:"), self.subtotal_label)

        self.tax_label = QLabel()
        totals_layout.addRow(create_dark_label("Tax:"), self.tax_label)

        self.discount_label = QLabel()
        totals_layout.addRow(create_dark_label("Discount:"), self.discount_label)

        self.total_label = QLabel()
        self.total_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        totals_layout.addRow(create_dark_label("Total:"), self.total_label)

        self.paid_label = QLabel()
        totals_layout.addRow(create_dark_label("Paid:"), self.paid_label)

        self.change_label = QLabel()
        totals_layout.addRow(create_dark_label("Change:"), self.change_label)
        
        totals_group.setLayout(totals_layout)
        layout.addWidget(totals_group)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        self.print_button = QPushButton("Print Invoice")
        self.print_button.clicked.connect(self.print_invoice)
        button_layout.addWidget(self.print_button)
        
        if self.sale.status == 'completed':
            self.cancel_button = QPushButton("Cancel Sale")
            self.cancel_button.setStyleSheet("background-color: #e74c3c; color: white;")
            self.cancel_button.clicked.connect(self.cancel_sale)
            button_layout.addWidget(self.cancel_button)
        
        button_layout.addStretch()
        
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def load_sale_details(self):
        """Load sale details"""
        # Header info
        self.invoice_label.setText(self.sale.invoice_number)
        self.date_label.setText(self.sale.created_at)
        self.status_label.setText(self.sale.status.title())
        
        # Customer
        customer = self.sale.get_customer()
        customer_name = customer.name if customer else "Walk-in Customer"
        self.customer_label.setText(customer_name)
        
        # Cashier
        from models.user import User
        cashier = User.get_by_id(self.sale.user_id)
        cashier_name = cashier.full_name if cashier else "Unknown"
        self.cashier_label.setText(cashier_name)
        
        # Items
        self.items_table.setRowCount(len(self.sale.items))
        for row, item in enumerate(self.sale.items):
            product = item.get_product()
            product_name = product.name if product else f"Product ID: {item.product_id}"
            
            self.items_table.setItem(row, 0, QTableWidgetItem(product_name))
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item.quantity)))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item.unit_price:.2f} DZD"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item.discount_amount:.2f} DZD"))
            self.items_table.setItem(row, 4, QTableWidgetItem(f"{item.total_amount:.2f} DZD"))
        
        # Totals
        self.subtotal_label.setText(f"{self.sale.subtotal:.2f} DZD")
        self.tax_label.setText(f"{self.sale.tax_amount:.2f} DZD")
        self.discount_label.setText(f"{self.sale.discount_amount:.2f} DZD")
        self.total_label.setText(f"{self.sale.total_amount:.2f} DZD")
        self.paid_label.setText(f"{self.sale.paid_amount:.2f} DZD")

        change = self.sale.get_change_amount()
        self.change_label.setText(f"{change:.2f} DZD")
    
    def print_invoice(self):
        """Print invoice for this sale"""
        try:
            print(f"🖨️ طباعة الفاتورة: {self.sale.invoice_number}")

            # Prepare sale data for printing
            sale_data = {
                'invoice_number': self.sale.invoice_number,
                'date': self.sale.created_at.split(' ')[0] if hasattr(self.sale, 'created_at') else '2024-01-01',
                'time': self.sale.created_at.split(' ')[1] if hasattr(self.sale, 'created_at') else '12:00:00',
                'cashier': 'Admin',
                'customer': 'Walk-in Customer',
                'customer_phone': '',
                'customer_address': '',
                'customer_tax_number': '',
                'items': [],
                'subtotal': float(self.sale.subtotal),
                'tax_amount': float(self.sale.tax_amount),
                'tax_rate': 19.0,
                'discount_amount': float(self.sale.discount_amount),
                'total_amount': float(self.sale.total_amount),
                'paid_amount': float(self.sale.total_amount),
                'change_amount': 0.0,
                'payment_method': self.sale.payment_method
            }

            # Add items
            for item in self.sale.items:
                try:
                    product = item.get_product()
                    sale_data['items'].append({
                        'name': product.name if product else 'Unknown Product',
                        'sku': product.sku if product else '',
                        'quantity': int(item.quantity),
                        'unit_price': float(item.unit_price),
                        'total_price': float(item.total_price)
                    })
                except Exception as item_error:
                    print(f"❌ خطأ في العنصر: {item_error}")
                    sale_data['items'].append({
                        'name': 'Unknown Product',
                        'sku': '',
                        'quantity': 1,
                        'unit_price': 0.0,
                        'total_price': 0.0
                    })

            print(f"📊 بيانات البيع: {sale_data}")

            # Print invoice
            print_manager.print_invoice(sale_data, preview=True)
            QMessageBox.information(self, "نجح", f"تم إرسال الفاتورة {self.sale.invoice_number} للطابعة!")

        except Exception as e:
            print(f"❌ خطأ في الطباعة: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"خطأ في طباعة الفاتورة:\n{str(e)}")
    
    def cancel_sale(self):
        """Cancel sale"""
        reply = QMessageBox.question(self, "Cancel Sale",
                                   "Are you sure you want to cancel this sale?\nThis action cannot be undone.",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                # Get current user (this should be passed from parent)
                success = self.sale.cancel(1, "Cancelled by user")  # TODO: Get actual user ID
                
                if success:
                    QMessageBox.information(self, "Success", "Sale cancelled successfully")
                    self.accept()  # Close dialog
                else:
                    QMessageBox.warning(self, "Warning", "Sale is already cancelled")
                    
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Error cancelling sale: {str(e)}")


class SalesWidget(QWidget):
    """Sales management widget"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_sales()
    
    def setup_ui(self):
        """Setup user interface"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Top toolbar
        toolbar_layout = QHBoxLayout()
        
        # Date filters
        from_date_label = create_dark_label(tr("reports.from_date") + ":")
        toolbar_layout.addWidget(from_date_label)
        
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        self.from_date.setCalendarPopup(True)
        self.from_date.dateChanged.connect(self.filter_sales)
        toolbar_layout.addWidget(self.from_date)
        
        to_date_label = create_dark_label(tr("reports.to_date") + ":")
        toolbar_layout.addWidget(to_date_label)
        
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.setCalendarPopup(True)
        self.to_date.dateChanged.connect(self.filter_sales)
        toolbar_layout.addWidget(self.to_date)
        
        # Status filter
        status_label = create_dark_label("Status:")
        toolbar_layout.addWidget(status_label)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["All", "Completed", "Cancelled", "Pending"])
        self.status_filter.currentTextChanged.connect(self.filter_sales)
        toolbar_layout.addWidget(self.status_filter)
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search by invoice number or customer")
        self.search_edit.textChanged.connect(self.search_sales)
        toolbar_layout.addWidget(self.search_edit)
        
        toolbar_layout.addStretch()
        
        # Action buttons
        self.view_button = QPushButton(tr("sales.view_sale"))
        self.view_button.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: #ffffff;
                font-weight: bold;
                font-size: 11px;
                padding: 10px 16px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
        """)
        self.view_button.clicked.connect(self.view_sale)
        toolbar_layout.addWidget(self.view_button)

        # Single print button that uses default printer settings
        self.print_button = QPushButton("🖨️ " + tr("sales.print_invoice"))
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
            QPushButton:pressed {
                background-color: #0a58ca;
            }
        """)
        self.print_button.clicked.connect(self.print_sale)
        toolbar_layout.addWidget(self.print_button)

        layout.addLayout(toolbar_layout)
        
        # Create splitter for main content
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Sales list
        left_panel = self.create_sales_list_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Sales summary
        right_panel = self.create_sales_summary_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([700, 300])
        
        layout.addWidget(splitter)
        self.setLayout(layout)
    
    def create_sales_list_panel(self):
        """Create sales list panel"""
        panel = QGroupBox(tr("sales.title"))
        layout = QVBoxLayout()
        
        # Sales table
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(8)
        self.sales_table.setHorizontalHeaderLabels([
            tr("sales.invoice_number"),
            tr("sales.date"),
            tr("sales.customer"),
            tr("sales.cashier"),
            tr("sales.amount"),
            tr("sales.payment_method"),
            tr("sales.status"),
            "Items"
        ])
        
        # Configure table
        header = self.sales_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Invoice
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # Customer
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Cashier
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Amount
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Payment
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Status
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Items
        
        self.sales_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sales_table.setAlternatingRowColors(True)
        self.sales_table.setSortingEnabled(True)
        self.sales_table.doubleClicked.connect(self.view_sale)
        
        layout.addWidget(self.sales_table)
        
        # Bottom info
        bottom_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton(tr("common.refresh"))
        self.refresh_button.clicked.connect(self.load_sales)
        bottom_layout.addWidget(self.refresh_button)
        
        bottom_layout.addStretch()
        
        self.count_label = QLabel()
        self.count_label.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 11px;")
        bottom_layout.addWidget(self.count_label)
        
        layout.addLayout(bottom_layout)
        
        panel.setLayout(layout)
        return panel
    
    def create_sales_summary_panel(self):
        """Create sales summary panel"""
        panel = QGroupBox("Sales Summary")
        layout = QVBoxLayout()
        
        # Summary info
        self.summary_label = QLabel()
        self.summary_label.setAlignment(Qt.AlignTop)
        self.summary_label.setStyleSheet("color: #000000 !important; font-weight: 700; font-size: 11px;")
        self.summary_label.setWordWrap(True)
        self.summary_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.summary_label)
        
        # Quick actions
        actions_group = QGroupBox("Quick Actions")
        actions_layout = QVBoxLayout()
        
        self.today_sales_button = QPushButton(tr("sales.daily_sales"))
        self.today_sales_button.clicked.connect(self.show_today_sales)
        actions_layout.addWidget(self.today_sales_button)
        
        self.export_button = QPushButton(tr("common.export"))
        self.export_button.clicked.connect(self.export_sales)
        actions_layout.addWidget(self.export_button)
        
        actions_group.setLayout(actions_layout)
        layout.addWidget(actions_group)
        
        layout.addStretch()
        
        panel.setLayout(layout)
        return panel
    
    def load_sales(self):
        """Load sales into table"""
        try:
            # Get date range
            from_date = self.from_date.date().toPyDate()
            to_date = self.to_date.date().toPyDate()
            
            # Get status filter
            status = self.status_filter.currentText().lower()
            if status == "all":
                status = None
            
            sales = Sale.get_all(date_from=from_date, date_to=to_date, status=status)
            self.populate_table(sales)
            self.update_summary(sales)
            
        except Exception as e:
            QMessageBox.critical(self, tr("common.error"), f"Error loading sales: {str(e)}")
    
    def populate_table(self, sales):
        """Populate table with sales"""
        self.sales_table.setRowCount(len(sales))
        
        for row, sale in enumerate(sales):
            # Invoice number
            self.sales_table.setItem(row, 0, QTableWidgetItem(sale.invoice_number))
            
            # Date
            date_str = sale.created_at[:10] if sale.created_at else ""
            self.sales_table.setItem(row, 1, QTableWidgetItem(date_str))
            
            # Customer
            customer = sale.get_customer()
            customer_name = customer.name if customer else "Walk-in"
            self.sales_table.setItem(row, 2, QTableWidgetItem(customer_name))
            
            # Cashier
            cashier = User.get_by_id(sale.user_id)
            cashier_name = cashier.full_name if cashier else "Unknown"
            self.sales_table.setItem(row, 3, QTableWidgetItem(cashier_name))
            
            # Amount
            amount_item = QTableWidgetItem(f"{sale.total_amount:.2f} DZD")
            if sale.status == 'cancelled':
                amount_item.setBackground(Qt.red)
            self.sales_table.setItem(row, 4, amount_item)
            
            # Payment method
            self.sales_table.setItem(row, 5, QTableWidgetItem(sale.payment_method.title()))
            
            # Status
            status_item = QTableWidgetItem(sale.status.title())
            if sale.status == 'completed':
                status_item.setBackground(Qt.green)
            elif sale.status == 'cancelled':
                status_item.setBackground(Qt.red)
            elif sale.status == 'pending':
                status_item.setBackground(Qt.yellow)
            self.sales_table.setItem(row, 6, status_item)
            
            # Items count
            self.sales_table.setItem(row, 7, QTableWidgetItem(str(len(sale.items))))
            
            # Store sale ID in first column
            self.sales_table.item(row, 0).setData(Qt.UserRole, sale.id)
        
        # Update count
        self.count_label.setText(f"Total: {len(sales)} sales")
    
    def update_summary(self, sales):
        """Update sales summary"""
        if not sales:
            self.summary_label.setText("No sales data")
            return
        
        # Calculate summary
        total_sales = len(sales)
        completed_sales = len([s for s in sales if s.status == 'completed'])
        cancelled_sales = len([s for s in sales if s.status == 'cancelled'])
        
        total_revenue = sum(s.total_amount for s in sales if s.status == 'completed')
        total_tax = sum(s.tax_amount for s in sales if s.status == 'completed')
        total_discount = sum(s.discount_amount for s in sales if s.status == 'completed')
        
        avg_sale = total_revenue / completed_sales if completed_sales > 0 else 0
        
        summary_text = f"""
        <h3>Sales Summary</h3>
        <p><b>Total Sales:</b> {total_sales}</p>
        <p><b>Completed:</b> {completed_sales}</p>
        <p><b>Cancelled:</b> {cancelled_sales}</p>
        <hr>
        <p><b>Total Revenue:</b> {total_revenue:.2f}</p>
        <p><b>Total Tax:</b> {total_tax:.2f}</p>
        <p><b>Total Discount:</b> {total_discount:.2f}</p>
        <p><b>Average Sale:</b> {avg_sale:.2f}</p>
        """
        
        self.summary_label.setText(summary_text)
    
    def filter_sales(self):
        """Filter sales by date and status"""
        self.load_sales()
    
    def search_sales(self):
        """Search sales"""
        # For now, just reload - implement proper search later
        self.load_sales()
    
    def show_today_sales(self):
        """Show today's sales"""
        today = QDate.currentDate()
        self.from_date.setDate(today)
        self.to_date.setDate(today)
        self.load_sales()
    
    def get_selected_sale(self):
        """Get currently selected sale"""
        current_row = self.sales_table.currentRow()
        if current_row >= 0:
            sale_id = self.sales_table.item(current_row, 0).data(Qt.UserRole)
            return Sale.get_by_id(sale_id)
        return None
    
    def view_sale(self):
        """View selected sale details"""
        sale = self.get_selected_sale()
        if not sale:
            QMessageBox.warning(self, tr("common.warning"), tr("messages.please_select_item"))
            return
        
        dialog = SaleDetailsDialog(sale, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            # Refresh if sale was modified
            self.load_sales()
    
    def print_sale(self):
        """Print selected sale using default printer settings"""
        sale = self.get_selected_sale()
        if not sale:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للطباعة")
            return

        try:
            print(f"🖨️ طباعة الفاتورة: {sale.invoice_number}")

            # Prepare sale data for printing
            sale_data = {
                'invoice_number': sale.invoice_number,
                'date': sale.created_at.split(' ')[0] if hasattr(sale, 'created_at') else '2024-01-01',
                'time': sale.created_at.split(' ')[1] if hasattr(sale, 'created_at') else '12:00:00',
                'cashier': 'Admin',
                'customer': 'Walk-in Customer',
                'customer_phone': '',
                'customer_address': '',
                'customer_tax_number': '',
                'items': [],
                'subtotal': float(sale.subtotal),
                'tax_amount': float(sale.tax_amount),
                'tax_rate': 19.0,
                'discount_amount': float(sale.discount_amount),
                'total_amount': float(sale.total_amount),
                'paid_amount': float(sale.total_amount),
                'change_amount': 0.0,
                'payment_method': sale.payment_method
            }

            # Add items
            for item in sale.items:
                try:
                    product = item.get_product()
                    sale_data['items'].append({
                        'name': product.name if product else 'Unknown Product',
                        'sku': product.sku if product else '',
                        'quantity': int(item.quantity),
                        'unit_price': float(item.unit_price),
                        'total_price': float(item.total_price)
                    })
                except Exception as item_error:
                    print(f"❌ خطأ في العنصر: {item_error}")
                    sale_data['items'].append({
                        'name': 'Unknown Product',
                        'sku': '',
                        'quantity': 1,
                        'unit_price': 0.0,
                        'total_price': 0.0
                    })

            print(f"📊 بيانات البيع: {sale_data}")

            # Print invoice
            print_manager.print_invoice(sale_data, preview=True)
            QMessageBox.information(self, "نجح", f"تم إرسال الفاتورة {sale.invoice_number} للطابعة!")

        except Exception as e:
            print(f"❌ خطأ في الطباعة: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"خطأ في طباعة الفاتورة:\n{str(e)}")



    def export_sales(self):
        """Export sales to Excel"""
        QMessageBox.information(self, tr("common.export"),
                              "Export functionality will be implemented")
