@echo off
echo ========================================
echo OnePos - Installer Build Script
echo ========================================
echo.

:: Set encoding to UTF-8
chcp 65001 > nul

:: Check if the application was built
if not exist "dist\OnePos\OnePos.exe" (
    echo ❌ Application not found!
    echo Please run build_app.bat first to build the application
    pause
    exit /b 1
)

echo ✅ Application found: dist\OnePos\OnePos.exe
echo.

:: Check if Inno Setup is installed
set "INNO_SETUP_PATH="
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
) else (
    echo ❌ Inno Setup not found!
    echo.
    echo Please download and install Inno Setup from:
    echo https://jrsoftware.org/isdl.php
    echo.
    echo Install Inno Setup and run this script again
    pause
    exit /b 1
)

echo ✅ Inno Setup found: %INNO_SETUP_PATH%
echo.

:: Create necessary directories
if not exist "installer_output" mkdir "installer_output"

:: Create missing files if they don't exist
echo 📝 Creating missing files...

if not exist "README.txt" (
    echo Creating README.txt...
    (
        echo OnePos - Professional Point of Sale System
        echo.
        echo Welcome to OnePos, a comprehensive POS solution for your business.
        echo.
        echo System Requirements:
        echo - Windows 10 or later
        echo - 4GB RAM minimum
        echo - 1GB free disk space
        echo - USB ports for printers/scanners
        echo.
        echo Features:
        echo - Multi-language support ^(Arabic, English, French^)
        echo - Advanced printer support ^(thermal and standard^)
        echo - Barcode scanner integration
        echo - Inventory management
        echo - Customer management
        echo - Sales reporting
        echo - Professional invoicing
        echo.
        echo For support, visit: https://www.assanaje.com
    ) > README.txt
)

if not exist "LICENSE.txt" (
    echo Creating LICENSE.txt...
    (
        echo OnePos Professional License
        echo Copyright ^(c^) 2025 ASSANAJE_APP
        echo All rights reserved.
        echo.
        echo This software is licensed for commercial use.
        echo Redistribution is not permitted without written permission.
    ) > LICENSE.txt
)

if not exist "INSTALL_COMPLETE.txt" (
    echo Creating INSTALL_COMPLETE.txt...
    (
        echo Installation Complete!
        echo.
        echo OnePos has been successfully installed on your computer.
        echo.
        echo Next Steps:
        echo 1. Launch OnePos from the desktop or Start menu
        echo 2. Complete the initial setup
        echo 3. Configure your hardware
        echo 4. Start using the system
        echo.
        echo Thank you for choosing OnePos!
    ) > INSTALL_COMPLETE.txt
)

:: Create default icon if missing
if not exist "app_icon.ico" (
    echo ⚠️ Warning: app_icon.ico not found
    echo The installer will use a default icon
)

echo ✅ Files prepared
echo.

:: Clean previous installer builds
echo 🧹 Cleaning previous installer builds...
if exist "installer_output\*.exe" del /q "installer_output\*.exe"

echo.
echo 🔨 Building installer with Inno Setup...
echo.

:: Build the installer
"%INNO_SETUP_PATH%" "onepos_installer.iss"

:: Check if build was successful
if %errorlevel% neq 0 (
    echo ❌ Installer build failed!
    echo Check the Inno Setup output for errors
    pause
    exit /b 1
)

echo.
echo ✅ Installer build completed!
echo.

:: Find the created installer
for %%f in (installer_output\*.exe) do (
    echo 🎉 Installer created: %%f
    echo 📁 Size: 
    dir "%%f" | find "OnePos_Setup"
    echo.
    
    echo 🧪 Testing installer...
    echo The installer will open for testing...
    echo ^(You can cancel the installation test^)
    timeout /t 3 > nul
    
    :: Open the installer for testing
    start "" "%%f"
    
    echo.
    echo 📋 Installer Information:
    echo - File: %%f
    echo - Ready for distribution
    echo - Includes all application files
    echo - Creates desktop and Start menu shortcuts
    echo - Supports custom installation path
    echo - Includes uninstaller
    echo.
    
    goto :installer_found
)

echo ❌ Installer file not found!
echo Something went wrong during the build process
pause
exit /b 1

:installer_found
echo ========================================
echo Installer Build Completed Successfully!
echo ========================================
echo.
echo 📦 Distribution Package Ready:
echo - Application: dist\OnePos\
echo - Installer: installer_output\OnePos_Setup_v1.0.0.exe
echo.
echo 🚀 Next Steps:
echo 1. Test the installer on a clean machine
echo 2. Test all application features
echo 3. Distribute to users
echo.
echo 💡 Tips:
echo - The installer includes automatic updates check
echo - Users can choose installation directory
echo - Desktop and Start menu shortcuts are created
echo - Uninstaller is automatically created
echo.
pause
