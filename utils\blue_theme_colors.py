"""
Blue theme color palette for OnePos POS System
Cool blue background with dark text for maximum readability
"""

class BlueThemeColors:
    """Blue theme color definitions"""
    
    # Main background colors
    MAIN_BACKGROUND = "#e6f3ff"      # Very light cool blue
    WIDGET_BACKGROUND = "#e6f3ff"    # Same as main
    INPUT_BACKGROUND = "#f0f8ff"     # Alice blue (very light)
    
    # Button colors
    BUTTON_BACKGROUND = "#cce7ff"    # Light blue
    BUTTON_HOVER = "#b3d9ff"         # Medium light blue
    BUTTON_PRESSED = "#99ccff"       # Medium blue
    
    # Table colors
    TABLE_BACKGROUND = "#f0f8ff"     # Alice blue
    TABLE_ALTERNATE = "#e6f3ff"      # Very light blue
    TABLE_SELECTED = "#b3d9ff"       # Medium light blue
    TABLE_HOVER = "#cce7ff"          # Light blue
    TABLE_HEADER = "#cce7ff"         # Light blue
    
    # Text colors
    TEXT_PRIMARY = "#000000"         # Black text
    TEXT_LABEL = "#000000"           # Black labels
    TEXT_INPUT = "#000000"           # Black input text
    
    # Border colors
    BORDER_PRIMARY = "#000000"       # Black borders
    BORDER_FOCUS = "#0066cc"         # Blue focus border
    
    # Special colors
    TOTAL_BACKGROUND = "#003366"     # Dark blue for total
    TOTAL_TEXT = "#ffffff"           # White text on dark blue
    
    # Group box colors
    GROUPBOX_BACKGROUND = "#e6f3ff"  # Very light blue
    GROUPBOX_TITLE_BG = "#e6f3ff"    # Same as background
    
    # Tab colors
    TAB_BACKGROUND = "#cce7ff"       # Light blue
    TAB_SELECTED = "#e6f3ff"         # Very light blue
    TAB_HOVER = "#b3d9ff"            # Medium light blue
    
    # Scrollbar colors
    SCROLLBAR_BACKGROUND = "#cce7ff" # Light blue
    SCROLLBAR_HANDLE = "#000000"     # Black handle
    
    # Status bar and menu colors
    STATUSBAR_BACKGROUND = "#cce7ff" # Light blue
    MENUBAR_BACKGROUND = "#cce7ff"   # Light blue
    MENU_BACKGROUND = "#e6f3ff"      # Very light blue
    MENU_SELECTED = "#b3d9ff"        # Medium light blue
    
    @classmethod
    def get_color_scheme(cls):
        """Get complete color scheme dictionary"""
        return {
            'main_bg': cls.MAIN_BACKGROUND,
            'widget_bg': cls.WIDGET_BACKGROUND,
            'input_bg': cls.INPUT_BACKGROUND,
            'button_bg': cls.BUTTON_BACKGROUND,
            'button_hover': cls.BUTTON_HOVER,
            'button_pressed': cls.BUTTON_PRESSED,
            'table_bg': cls.TABLE_BACKGROUND,
            'table_alt': cls.TABLE_ALTERNATE,
            'table_selected': cls.TABLE_SELECTED,
            'table_hover': cls.TABLE_HOVER,
            'table_header': cls.TABLE_HEADER,
            'text_primary': cls.TEXT_PRIMARY,
            'text_label': cls.TEXT_LABEL,
            'text_input': cls.TEXT_INPUT,
            'border_primary': cls.BORDER_PRIMARY,
            'border_focus': cls.BORDER_FOCUS,
            'total_bg': cls.TOTAL_BACKGROUND,
            'total_text': cls.TOTAL_TEXT,
            'groupbox_bg': cls.GROUPBOX_BACKGROUND,
            'groupbox_title_bg': cls.GROUPBOX_TITLE_BG,
            'tab_bg': cls.TAB_BACKGROUND,
            'tab_selected': cls.TAB_SELECTED,
            'tab_hover': cls.TAB_HOVER,
            'scrollbar_bg': cls.SCROLLBAR_BACKGROUND,
            'scrollbar_handle': cls.SCROLLBAR_HANDLE,
            'statusbar_bg': cls.STATUSBAR_BACKGROUND,
            'menubar_bg': cls.MENUBAR_BACKGROUND,
            'menu_bg': cls.MENU_BACKGROUND,
            'menu_selected': cls.MENU_SELECTED
        }
    
    @classmethod
    def get_gradient_backgrounds(cls):
        """Get gradient background styles"""
        return {
            'main_gradient': f"""
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {cls.MAIN_BACKGROUND}, 
                    stop: 1 #d9ecff);
            """,
            'button_gradient': f"""
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {cls.BUTTON_BACKGROUND}, 
                    stop: 1 #b3d9ff);
            """,
            'header_gradient': f"""
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {cls.TABLE_HEADER}, 
                    stop: 1 #99ccff);
            """
        }

def apply_blue_theme_to_widget(widget, widget_type="default"):
    """Apply blue theme to a specific widget"""
    colors = BlueThemeColors.get_color_scheme()
    
    if widget_type == "button":
        widget.setStyleSheet(f"""
            QPushButton {{
                background-color: {colors['button_bg']};
                border: 2px solid {colors['border_primary']};
                color: {colors['text_primary']};
                font-weight: 600;
                padding: 8px 16px;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {colors['button_hover']};
            }}
            QPushButton:pressed {{
                background-color: {colors['button_pressed']};
            }}
        """)
    
    elif widget_type == "input":
        widget.setStyleSheet(f"""
            QLineEdit, QTextEdit, QPlainTextEdit {{
                background-color: {colors['input_bg']};
                border: 2px solid {colors['border_primary']};
                color: {colors['text_input']};
                font-weight: 500;
                padding: 6px;
                border-radius: 4px;
            }}
            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
                border: 2px solid {colors['border_focus']};
            }}
        """)
    
    elif widget_type == "table":
        widget.setStyleSheet(f"""
            QTableWidget {{
                background-color: {colors['table_bg']};
                alternate-background-color: {colors['table_alt']};
                color: {colors['text_primary']};
                border: 2px solid {colors['border_primary']};
                gridline-color: {colors['border_primary']};
            }}
            QTableWidget::item:selected {{
                background-color: {colors['table_selected']};
            }}
            QTableWidget::item:hover {{
                background-color: {colors['table_hover']};
            }}
            QHeaderView::section {{
                background-color: {colors['table_header']};
                color: {colors['text_primary']};
                border: 2px solid {colors['border_primary']};
                font-weight: 800;
            }}
        """)
    
    elif widget_type == "label":
        widget.setStyleSheet(f"""
            QLabel {{
                color: {colors['text_label']} !important;
                font-weight: 700;
                font-size: 11px;
            }}
        """)
    
    elif widget_type == "groupbox":
        widget.setStyleSheet(f"""
            QGroupBox {{
                color: {colors['text_primary']};
                font-weight: 800;
                border: 2px solid {colors['border_primary']};
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: {colors['groupbox_bg']};
            }}
            QGroupBox::title {{
                color: {colors['text_primary']};
                font-weight: 800;
                background-color: {colors['groupbox_title_bg']};
                padding: 0 10px;
            }}
        """)

def get_blue_theme_stylesheet():
    """Get complete blue theme stylesheet"""
    colors = BlueThemeColors.get_color_scheme()
    
    return f"""
    /* Blue Theme for OnePos */
    * {{
        font-family: "Segoe UI", "Arial", sans-serif;
        font-size: 10px;
        font-weight: 500;
        color: {colors['text_primary']} !important;
    }}
    
    QMainWindow {{
        background-color: {colors['main_bg']};
        color: {colors['text_primary']};
    }}
    
    QWidget {{
        background-color: {colors['widget_bg']};
        color: {colors['text_primary']};
    }}
    
    QLabel {{
        color: {colors['text_label']} !important;
        font-weight: 700 !important;
        font-size: 11px !important;
    }}
    
    QPushButton {{
        background-color: {colors['button_bg']};
        border: 2px solid {colors['border_primary']};
        color: {colors['text_primary']} !important;
        font-weight: 600 !important;
        padding: 8px 16px;
        border-radius: 4px;
    }}
    
    QPushButton:hover {{
        background-color: {colors['button_hover']};
    }}
    
    QPushButton:pressed {{
        background-color: {colors['button_pressed']};
    }}
    
    QLineEdit, QTextEdit, QPlainTextEdit {{
        background-color: {colors['input_bg']};
        border: 2px solid {colors['border_primary']};
        color: {colors['text_input']} !important;
        font-weight: 500 !important;
        padding: 6px;
        border-radius: 4px;
    }}
    
    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
        border: 2px solid {colors['border_focus']};
    }}
    
    QTableWidget {{
        background-color: {colors['table_bg']};
        alternate-background-color: {colors['table_alt']};
        color: {colors['text_primary']} !important;
        border: 2px solid {colors['border_primary']};
        gridline-color: {colors['border_primary']};
    }}
    
    QTableWidget::item:selected {{
        background-color: {colors['table_selected']};
    }}
    
    QTableWidget::item:hover {{
        background-color: {colors['table_hover']};
    }}
    
    QHeaderView::section {{
        background-color: {colors['table_header']};
        color: {colors['text_primary']} !important;
        border: 2px solid {colors['border_primary']};
        font-weight: 800 !important;
    }}
    """
