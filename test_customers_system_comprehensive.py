#!/usr/bin/env python3
"""
اختبار شامل لنظام إدارة العملاء المتقدم - OnePos
Comprehensive Customer Management System Test
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.main_window import MainWindow
from models.user import User
from models.customer import Customer
from utils.translator import translator, tr

def test_customer_model():
    """اختبار موديل العملاء"""
    print("👥 اختبار موديل العملاء")
    print("=" * 30)
    
    try:
        # اختبار إنشاء عميل
        import time
        timestamp = str(int(time.time()))
        customer_data = {
            'name': f'Test Customer {timestamp}',
            'phone': f'+212-123-{timestamp}',
            'email': f'test{timestamp}@customer.com',
            'address': 'Test Address, Test City',
            'tax_number': f'TAX{timestamp}',
            'credit_limit': 1000.0
        }
        
        customer = Customer.create(**customer_data)
        if customer:
            print("✅ إنشاء عميل: نجح")
            print(f"   👤 اسم العميل: {customer.name}")
            print(f"   📞 الهاتف: {customer.phone}")
            print(f"   📧 البريد: {customer.email}")
            print(f"   💰 حد الائتمان: {customer.credit_limit}")
        else:
            print("❌ إنشاء عميل: فشل")
            return False
        
        # اختبار البحث عن العميل
        found_customers = Customer.search("Test")
        if found_customers and len(found_customers) > 0:
            print("✅ البحث عن العملاء: نجح")
            print(f"   📊 عدد النتائج: {len(found_customers)}")
        else:
            print("❌ البحث عن العملاء: فشل")
        
        # اختبار البحث بالهاتف
        phone_customer = Customer.get_by_phone(customer.phone)
        if phone_customer:
            print("✅ البحث بالهاتف: نجح")
        else:
            print("❌ البحث بالهاتف: فشل")
        
        # اختبار البحث بالبريد الإلكتروني
        email_customer = Customer.get_by_email(customer.email)
        if email_customer:
            print("✅ البحث بالبريد الإلكتروني: نجح")
        else:
            print("❌ البحث بالبريد الإلكتروني: فشل")
        
        # اختبار تحديث العميل
        customer.update(credit_limit=1500.0, address="Updated Address")
        updated_customer = Customer.get_by_id(customer.id)
        if updated_customer and updated_customer.credit_limit == 1500.0:
            print("✅ تحديث العميل: نجح")
        else:
            print("❌ تحديث العميل: فشل")
        
        # اختبار الحصول على جميع العملاء
        all_customers = Customer.get_all()
        print(f"📊 إجمالي العملاء: {len(all_customers)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار موديل العملاء: {e}")
        return False

def test_customer_balance_management():
    """اختبار إدارة أرصدة العملاء"""
    print("\n💰 اختبار إدارة أرصدة العملاء")
    print("=" * 40)
    
    try:
        # الحصول على عميل للاختبار
        customers = Customer.get_all()
        if not customers:
            print("❌ لا يوجد عملاء للاختبار")
            return False
        
        customer = customers[0]
        initial_balance = customer.current_balance
        
        # اختبار تحديث الرصيد
        new_balance = 500.0
        customer.update(current_balance=new_balance)
        updated_customer = Customer.get_by_id(customer.id)
        
        if updated_customer.current_balance == new_balance:
            print("✅ تحديث رصيد العميل: نجح")
            print(f"   💰 الرصيد السابق: {initial_balance}")
            print(f"   💰 الرصيد الحالي: {updated_customer.current_balance}")
        else:
            print("❌ تحديث رصيد العميل: فشل")
        
        # اختبار فحص حد الائتمان
        if hasattr(customer, 'credit_limit') and customer.credit_limit > 0:
            if customer.current_balance <= customer.credit_limit:
                print("✅ فحص حد الائتمان: ضمن الحد المسموح")
            else:
                print("⚠️ فحص حد الائتمان: تجاوز الحد المسموح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة الأرصدة: {e}")
        return False

def test_customers_ui():
    """اختبار واجهة إدارة العملاء"""
    print("\n🖼️ اختبار واجهة إدارة العملاء")
    print("=" * 40)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        main_window = MainWindow(admin_user)
        
        # الانتقال لقسم العملاء
        main_window.load_module('customers')
        app.processEvents()
        
        # البحث عن واجهة العملاء
        customers_widget = None
        for i in range(main_window.content_frame.count()):
            widget = main_window.content_frame.widget(i)
            if hasattr(widget, 'customers_table'):
                customers_widget = widget
                break
        
        if not customers_widget:
            print("❌ لم يتم العثور على واجهة العملاء")
            return False
        
        print("✅ تم العثور على واجهة العملاء")
        
        # فحص العناصر الأساسية
        elements_to_check = [
            ('customers_table', 'جدول العملاء'),
            ('search_edit', 'حقل البحث'),
            ('add_button', 'زر إضافة عميل'),
            ('edit_button', 'زر تعديل عميل'),
            ('delete_button', 'زر حذف عميل'),
            ('refresh_button', 'زر التحديث'),
            ('count_label', 'عداد العملاء')
        ]
        
        missing_elements = []
        for element_name, description in elements_to_check:
            if hasattr(customers_widget, element_name):
                print(f"   ✅ {description}: موجود")
            else:
                print(f"   ❌ {description}: مفقود")
                missing_elements.append(description)
        
        # فحص الجدول
        table = customers_widget.customers_table
        if table.columnCount() >= 6:
            print(f"✅ جدول العملاء: {table.columnCount()} أعمدة")
        else:
            print(f"❌ جدول العملاء: أعمدة ناقصة ({table.columnCount()}/6)")
        
        # اختبار تحميل البيانات
        customers_widget.load_customers()
        app.processEvents()
        
        if table.rowCount() >= 0:
            print(f"✅ تحميل البيانات: {table.rowCount()} عميل")
        else:
            print("❌ تحميل البيانات: فشل")
        
        # اختبار البحث
        customers_widget.search_edit.setText("test")
        customers_widget.search_customers()
        app.processEvents()
        print("✅ اختبار البحث: تم")
        
        # فحص تفاصيل العميل (إذا كانت متوفرة)
        if hasattr(customers_widget, 'details_panel'):
            print("✅ لوحة تفاصيل العميل: متوفرة")
        else:
            print("⚠️ لوحة تفاصيل العميل: غير متوفرة")
        
        return len(missing_elements) == 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة العملاء: {e}")
        return False

def test_customer_translations():
    """اختبار ترجمات العملاء"""
    print("\n🌍 اختبار ترجمات العملاء")
    print("=" * 35)
    
    languages = [
        ('ar', 'العربية'),
        ('en', 'English'),
        ('fr', 'Français')
    ]
    
    customer_keys = [
        'customers.name',
        'customers.phone',
        'customers.email',
        'customers.address',
        'customers.tax_number',
        'customers.credit_limit',
        'customers.add_customer',
        'customers.edit_customer',
        'customers.delete_customer'
    ]
    
    all_correct = True
    
    try:
        for lang_code, lang_name in languages:
            print(f"\n📝 اختبار اللغة: {lang_name} ({lang_code})")
            
            # تغيير اللغة
            translator.set_language(lang_code)
            
            missing_translations = []
            for key in customer_keys:
                translation = tr(key)
                if translation == key:  # لم يتم العثور على ترجمة
                    missing_translations.append(key)
                else:
                    print(f"   ✅ {key}: {translation}")
            
            if missing_translations:
                print(f"   ❌ ترجمات مفقودة: {len(missing_translations)}")
                for key in missing_translations:
                    print(f"      - {key}")
                all_correct = False
            else:
                print(f"   ✅ جميع الترجمات متوفرة")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ترجمات العملاء: {e}")
        return False

def test_customer_validation():
    """اختبار التحقق من صحة بيانات العملاء"""
    print("\n✅ اختبار التحقق من صحة البيانات")
    print("=" * 45)
    
    try:
        # اختبار إنشاء عميل بدون اسم (يجب أن يفشل)
        try:
            invalid_customer = Customer.create("")
            if invalid_customer:
                print("❌ التحقق من الاسم: فشل (تم قبول اسم فارغ)")
                return False
            else:
                print("✅ التحقق من الاسم: نجح (رفض اسم فارغ)")
        except:
            print("✅ التحقق من الاسم: نجح (رفض اسم فارغ)")
        
        # اختبار إنشاء عميل بهاتف مكرر
        existing_customers = Customer.get_all()
        if existing_customers:
            existing_phone = existing_customers[0].phone
            if existing_phone:
                try:
                    duplicate_customer = Customer.create("Duplicate Test", phone=existing_phone)
                    if duplicate_customer:
                        print("⚠️ التحقق من الهاتف المكرر: لم يتم التحقق")
                    else:
                        print("✅ التحقق من الهاتف المكرر: نجح")
                except:
                    print("✅ التحقق من الهاتف المكرر: نجح")
        
        # اختبار تنسيق البريد الإلكتروني
        valid_emails = ["<EMAIL>", "<EMAIL>"]
        invalid_emails = ["invalid-email", "@domain.com", "user@"]
        
        for email in valid_emails:
            try:
                test_customer = Customer.create(f"Test {email}", email=email)
                if test_customer:
                    print(f"✅ بريد صحيح '{email}': تم قبوله")
                    test_customer.delete()  # تنظيف
            except:
                print(f"❌ بريد صحيح '{email}': تم رفضه")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحقق من البيانات: {e}")
        return False

def test_customer_search_functionality():
    """اختبار وظائف البحث المتقدمة"""
    print("\n🔍 اختبار وظائف البحث المتقدمة")
    print("=" * 40)
    
    try:
        # إنشاء عملاء للاختبار
        test_customers = [
            {"name": "Ahmed Ali", "phone": "123456789", "email": "<EMAIL>"},
            {"name": "Sara Mohamed", "phone": "987654321", "email": "<EMAIL>"},
            {"name": "Omar Hassan", "phone": "555666777", "email": "<EMAIL>"}
        ]
        
        created_customers = []
        for customer_data in test_customers:
            customer = Customer.create(**customer_data)
            if customer:
                created_customers.append(customer)
        
        print(f"✅ تم إنشاء {len(created_customers)} عميل للاختبار")
        
        # اختبار البحث بالاسم
        name_results = Customer.search("Ahmed")
        if any(c.name == "Ahmed Ali" for c in name_results):
            print("✅ البحث بالاسم: نجح")
        else:
            print("❌ البحث بالاسم: فشل")
        
        # اختبار البحث بالهاتف
        phone_results = Customer.search("123456789")
        if any(c.phone == "123456789" for c in phone_results):
            print("✅ البحث بالهاتف: نجح")
        else:
            print("❌ البحث بالهاتف: فشل")
        
        # اختبار البحث بالبريد الإلكتروني
        email_results = Customer.search("<EMAIL>")
        if any(c.email == "<EMAIL>" for c in email_results):
            print("✅ البحث بالبريد الإلكتروني: نجح")
        else:
            print("❌ البحث بالبريد الإلكتروني: فشل")
        
        # اختبار البحث الجزئي
        partial_results = Customer.search("test.com")
        if len(partial_results) >= 3:
            print("✅ البحث الجزئي: نجح")
        else:
            print("❌ البحث الجزئي: فشل")
        
        # تنظيف العملاء المؤقتين
        for customer in created_customers:
            customer.delete()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظائف البحث: {e}")
        return False

def run_customers_system_comprehensive_test():
    """تشغيل جميع اختبارات نظام إدارة العملاء"""
    print("👥 بدء الاختبار الشامل لنظام إدارة العملاء المتقدم")
    print("=" * 80)
    
    results = []
    
    # اختبار 1: موديل العملاء
    results.append(("موديل العملاء", test_customer_model()))
    
    # اختبار 2: إدارة الأرصدة
    results.append(("إدارة الأرصدة", test_customer_balance_management()))
    
    # اختبار 3: واجهة العملاء
    results.append(("واجهة العملاء", test_customers_ui()))
    
    # اختبار 4: ترجمات العملاء
    results.append(("ترجمات العملاء", test_customer_translations()))
    
    # اختبار 5: التحقق من البيانات
    results.append(("التحقق من البيانات", test_customer_validation()))
    
    # اختبار 6: وظائف البحث
    results.append(("وظائف البحث", test_customer_search_functionality()))
    
    # النتائج النهائية
    print("\n" + "=" * 80)
    print("📊 نتائج الاختبار الشامل لنظام إدارة العملاء")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 نظام إدارة العملاء يعمل بشكل مثالي!")
        print("✨ جميع الميزات متوفرة ومكتملة")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_customers_system_comprehensive_test()
