"""
Simple RTL Layout Test for OnePos POS System
Quick test to verify RTL layout functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from utils.translator import change_language, get_current_language
from views.main_window import MainWindow
from models.user import User
import time

def test_simple_rtl_layout():
    """Simple test for RTL layout"""
    print("🔄 Testing Simple RTL Layout...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        main_window.show()
        app.processEvents()
        
        print(f"✅ Main window created")
        
        # Check if required components exist
        if not hasattr(main_window, 'main_layout'):
            print("❌ main_layout not found")
            return False
        
        if not hasattr(main_window, 'sidebar_frame'):
            print("❌ sidebar_frame not found")
            return False
        
        if not hasattr(main_window, 'content_frame'):
            print("❌ content_frame not found")
            return False
        
        print("✅ All required components found")
        
        # Test English layout (LTR)
        print("\n   Testing English (LTR)...")
        change_language('en')
        app.processEvents()
        time.sleep(0.2)
        
        # Check layout
        layout = main_window.main_layout
        if layout.count() >= 2:
            first_widget = layout.itemAt(0).widget()
            second_widget = layout.itemAt(1).widget()
            
            if first_widget == main_window.sidebar_frame and second_widget == main_window.content_frame:
                print("   ✅ English: Sidebar left, Content right")
            else:
                print("   ❌ English layout incorrect")
                return False
        else:
            print("   ❌ Layout doesn't have enough components")
            return False
        
        # Check layout direction
        direction = main_window.layoutDirection()
        if direction == Qt.LeftToRight:
            print("   ✅ English: Layout direction LTR")
        else:
            print(f"   ❌ English: Wrong layout direction {direction}")
            return False
        
        # Test Arabic layout (RTL)
        print("\n   Testing Arabic (RTL)...")
        change_language('ar')
        app.processEvents()
        time.sleep(0.2)
        
        # Check layout
        layout = main_window.main_layout
        if layout.count() >= 2:
            first_widget = layout.itemAt(0).widget()
            second_widget = layout.itemAt(1).widget()
            
            if first_widget == main_window.content_frame and second_widget == main_window.sidebar_frame:
                print("   ✅ Arabic: Content left, Sidebar right")
            else:
                print("   ❌ Arabic layout incorrect")
                print(f"     First widget: {type(first_widget).__name__}")
                print(f"     Second widget: {type(second_widget).__name__}")
                print(f"     Expected first: {type(main_window.content_frame).__name__}")
                print(f"     Expected second: {type(main_window.sidebar_frame).__name__}")
                return False
        else:
            print("   ❌ Layout doesn't have enough components")
            return False
        
        # Check layout direction
        direction = main_window.layoutDirection()
        if direction == Qt.RightToLeft:
            print("   ✅ Arabic: Layout direction RTL")
        else:
            print(f"   ❌ Arabic: Wrong layout direction {direction}")
            return False
        
        # Test French layout (LTR)
        print("\n   Testing French (LTR)...")
        change_language('fr')
        app.processEvents()
        time.sleep(0.2)
        
        # Check layout
        layout = main_window.main_layout
        if layout.count() >= 2:
            first_widget = layout.itemAt(0).widget()
            second_widget = layout.itemAt(1).widget()
            
            if first_widget == main_window.sidebar_frame and second_widget == main_window.content_frame:
                print("   ✅ French: Sidebar left, Content right")
            else:
                print("   ❌ French layout incorrect")
                return False
        else:
            print("   ❌ Layout doesn't have enough components")
            return False
        
        # Check layout direction
        direction = main_window.layoutDirection()
        if direction == Qt.LeftToRight:
            print("   ✅ French: Layout direction LTR")
        else:
            print(f"   ❌ French: Wrong layout direction {direction}")
            return False
        
        # Test rapid switching
        print("\n   Testing rapid switching...")
        languages = ['ar', 'en', 'fr', 'ar', 'en']
        
        for i, lang in enumerate(languages):
            change_language(lang)
            app.processEvents()
            
            # Quick check
            layout = main_window.main_layout
            if layout.count() >= 2:
                first_widget = layout.itemAt(0).widget()
                
                if lang == 'ar':
                    expected_first = main_window.content_frame
                else:
                    expected_first = main_window.sidebar_frame
                
                if first_widget == expected_first:
                    print(f"     ✅ Switch {i+1} ({lang}): Correct")
                else:
                    print(f"     ❌ Switch {i+1} ({lang}): Incorrect")
                    return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Simple RTL layout test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_layout_functions():
    """Test layout-related functions"""
    print("\n🔧 Testing Layout Functions...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create test user
        test_user = User()
        test_user.id = 1
        test_user.username = "test"
        test_user.full_name = "Test User"
        test_user.role = "admin"
        
        # Create main window
        main_window = MainWindow(test_user)
        
        # Test function existence
        functions_to_check = [
            'create_layout_components',
            'arrange_layout_components',
            'update_sidebar_styles',
            'update_children_layout_direction'
        ]
        
        for func_name in functions_to_check:
            if hasattr(main_window, func_name):
                print(f"   ✅ Function {func_name} exists")
            else:
                print(f"   ❌ Function {func_name} missing")
                return False
        
        # Test function calls
        try:
            main_window.arrange_layout_components()
            print("   ✅ arrange_layout_components() works")
        except Exception as e:
            print(f"   ❌ arrange_layout_components() failed: {e}")
            return False
        
        try:
            main_window.update_sidebar_styles(True)
            print("   ✅ update_sidebar_styles(True) works")
        except Exception as e:
            print(f"   ❌ update_sidebar_styles(True) failed: {e}")
            return False
        
        try:
            main_window.update_sidebar_styles(False)
            print("   ✅ update_sidebar_styles(False) works")
        except Exception as e:
            print(f"   ❌ update_sidebar_styles(False) failed: {e}")
            return False
        
        try:
            main_window.update_children_layout_direction(True)
            print("   ✅ update_children_layout_direction(True) works")
        except Exception as e:
            print(f"   ❌ update_children_layout_direction(True) failed: {e}")
            return False
        
        try:
            main_window.update_children_layout_direction(False)
            print("   ✅ update_children_layout_direction(False) works")
        except Exception as e:
            print(f"   ❌ update_children_layout_direction(False) failed: {e}")
            return False
        
        # Clean up
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Layout functions test failed: {e}")
        return False

def run_simple_rtl_test():
    """Run simple RTL tests"""
    print("🔄 STARTING SIMPLE RTL LAYOUT TEST")
    print("=" * 50)
    
    # Create QApplication first
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    test_results = []
    
    # Test 1: Simple RTL Layout
    test_results.append(("Simple RTL Layout", test_simple_rtl_layout()))
    
    # Test 2: Layout Functions
    test_results.append(("Layout Functions", test_layout_functions()))
    
    # Print results summary
    print("\n" + "=" * 50)
    print("🏁 SIMPLE RTL TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"📊 OVERALL RESULT: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL SIMPLE RTL TESTS PASSED!")
    else:
        print("⚠️  Some tests failed.")
    
    return passed == total

if __name__ == "__main__":
    run_simple_rtl_test()
