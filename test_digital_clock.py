#!/usr/bin/env python3
"""
اختبار الساعة الرقمية والتاريخ في شريط الحالة
Test Digital Clock and Date in Status Bar
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QLabel
from PyQt5.QtCore import QTimer
from datetime import datetime
from models.user import User
from views.main_window import MainWindow
from utils.translator import translator, tr


def test_digital_clock():
    """اختبار الساعة الرقمية والتاريخ"""
    
    app = QApplication(sys.argv)
    
    # إنشاء مستخدم تجريبي
    test_user = User(
        id=1,
        username="admin",
        full_name="مدير النظام",
        email="<EMAIL>",
        role="admin",
        permissions={"all": True}
    )
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow(test_user)
    main_window.show()
    
    print("🕒 اختبار الساعة الرقمية والتاريخ في شريط الحالة")
    print("=" * 60)
    
    # اختبار 1: التحقق من وجود عنصر التاريخ والساعة
    print("\n1. فحص وجود الساعة الرقمية:")
    
    datetime_found = False
    if hasattr(main_window, 'datetime_label'):
        datetime_found = True
        current_text = main_window.datetime_label.text()
        print(f"   ✅ تم العثور على الساعة الرقمية")
        print(f"   📊 النص الحالي: {current_text}")
        
        # التحقق من وجود الرموز المتوقعة
        if "📅" in current_text and "🕒" in current_text:
            print("   ✅ الرموز موجودة بشكل صحيح")
        else:
            print("   ❌ الرموز مفقودة")
    else:
        print("   ❌ لم يتم العثور على الساعة الرقمية")
    
    # اختبار 2: التحقق من Timer
    print("\n2. فحص Timer التحديث:")
    
    if hasattr(main_window, 'datetime_timer'):
        timer = main_window.datetime_timer
        if timer.isActive():
            print(f"   ✅ Timer نشط - يحدث كل {timer.interval()} مللي ثانية")
        else:
            print("   ❌ Timer غير نشط")
    else:
        print("   ❌ لم يتم العثور على Timer")
    
    # اختبار 3: اختبار تغيير اللغة وتأثيرها على التاريخ
    print("\n3. اختبار تغيير اللغة:")
    
    languages = [
        ("ar", "العربية"),
        ("en", "English"), 
        ("fr", "Français")
    ]
    
    for lang_code, lang_name in languages:
        print(f"   🌐 اختبار اللغة: {lang_name}")
        
        # تغيير اللغة
        translator.set_language(lang_code)
        main_window.retranslate_ui()
        
        # فحص النص المحدث
        if hasattr(main_window, 'datetime_label'):
            updated_text = main_window.datetime_label.text()
            print(f"      📊 النص: {updated_text}")
            
            # التحقق من تنسيق التاريخ حسب اللغة
            if lang_code == "ar":
                expected_format = "YYYY/MM/DD"
                if "/" in updated_text:
                    print(f"      ✅ تنسيق التاريخ العربي صحيح")
                else:
                    print(f"      ❌ تنسيق التاريخ العربي غير صحيح")
            elif lang_code == "en":
                expected_format = "MM/DD/YYYY"
                if "/" in updated_text:
                    print(f"      ✅ تنسيق التاريخ الإنجليزي صحيح")
                else:
                    print(f"      ❌ تنسيق التاريخ الإنجليزي غير صحيح")
            else:  # fr
                expected_format = "DD/MM/YYYY"
                if "/" in updated_text:
                    print(f"      ✅ تنسيق التاريخ الفرنسي صحيح")
                else:
                    print(f"      ❌ تنسيق التاريخ الفرنسي غير صحيح")
    
    # إعادة اللغة الأصلية
    translator.set_language("ar")
    main_window.retranslate_ui()
    
    # اختبار 4: اختبار أسماء الأيام
    print("\n4. اختبار أسماء الأيام:")
    
    # محاكاة أيام مختلفة
    test_weekdays = [0, 1, 2, 3, 4, 5, 6]  # Monday to Sunday
    
    for weekday in test_weekdays:
        ar_day = main_window.get_arabic_weekday(weekday)
        en_day = main_window.get_english_weekday(weekday)
        fr_day = main_window.get_french_weekday(weekday)
        
        print(f"   📅 يوم {weekday}: {ar_day} | {en_day} | {fr_day}")
    
    # اختبار 5: فحص شريط الحالة الكامل
    print("\n5. فحص شريط الحالة الكامل:")
    
    status_bar_widgets = []
    for widget in main_window.status_bar.children():
        if isinstance(widget, QLabel):
            widget_text = widget.text()
            if widget_text:
                status_bar_widgets.append(widget_text)
    
    print(f"   📊 إجمالي العناصر في شريط الحالة: {len(status_bar_widgets)}")
    for i, widget_text in enumerate(status_bar_widgets, 1):
        print(f"      {i}. {widget_text}")
    
    # اختبار 6: اختبار التحديث التلقائي
    print("\n6. اختبار التحديث التلقائي:")
    
    if hasattr(main_window, 'datetime_label'):
        initial_text = main_window.datetime_label.text()
        print(f"   📊 النص الأولي: {initial_text}")
        
        # انتظار ثانية واحدة للتحديث
        def check_update():
            updated_text = main_window.datetime_label.text()
            print(f"   📊 النص بعد التحديث: {updated_text}")
            
            if updated_text != initial_text:
                print("   ✅ التحديث التلقائي يعمل بشكل صحيح")
            else:
                print("   ⚠️ لم يتم تحديث النص (قد يكون نفس الثانية)")
        
        # جدولة فحص التحديث بعد ثانية
        QTimer.singleShot(1100, check_update)
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار")
    
    return app


def test_datetime_formats():
    """اختبار تنسيقات التاريخ والوقت"""
    print("\n7. اختبار تنسيقات التاريخ والوقت:")
    
    now = datetime.now()
    
    # تنسيقات مختلفة
    formats = {
        "العربية": {
            "date": now.strftime("%Y/%m/%d"),
            "time": now.strftime("%H:%M:%S"),
            "weekday": ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"][now.weekday()]
        },
        "English": {
            "date": now.strftime("%m/%d/%Y"),
            "time": now.strftime("%H:%M:%S"),
            "weekday": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"][now.weekday()]
        },
        "Français": {
            "date": now.strftime("%d/%m/%Y"),
            "time": now.strftime("%H:%M:%S"),
            "weekday": ["Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi", "Dimanche"][now.weekday()]
        }
    }
    
    for lang, format_data in formats.items():
        display_text = f"📅 {format_data['weekday']} {format_data['date']} | 🕒 {format_data['time']}"
        print(f"   {lang}: {display_text}")


def test_tooltip_translations():
    """اختبار ترجمات tooltip"""
    print("\n8. اختبار ترجمات Tooltip:")
    
    languages = [("ar", "العربية"), ("en", "English"), ("fr", "Français")]
    
    for lang_code, lang_name in languages:
        translator.set_language(lang_code)
        tooltip_text = tr("common.current_datetime")
        print(f"   {lang_name}: {tooltip_text}")
    
    # إعادة اللغة الأصلية
    translator.set_language("ar")


if __name__ == "__main__":
    try:
        print("🚀 بدء اختبار الساعة الرقمية والتاريخ")
        print("🎯 الهدف: التأكد من عمل الساعة الرقمية في شريط الحالة")
        
        app = test_digital_clock()
        
        # اختبارات إضافية
        test_datetime_formats()
        test_tooltip_translations()
        
        print("\n🎉 تم الانتهاء من جميع الاختبارات!")
        print("\n📝 ملخص النتائج:")
        print("   ✅ تم إضافة الساعة الرقمية والتاريخ")
        print("   ✅ التحديث التلقائي كل ثانية")
        print("   ✅ دعم متعدد اللغات للتاريخ والأيام")
        print("   ✅ تنسيقات مختلفة حسب اللغة")
        print("   ✅ واجهة جميلة ومنظمة")
        
        print("\n🎯 مكونات شريط الحالة الآن:")
        print("   📊 معلومات المستخدم")
        print("   🗄️ حالة قاعدة البيانات")
        print("   🔐 حالة الترخيص")
        print("   🕒 التاريخ والساعة الرقمية")
        
        # تشغيل التطبيق لفترة قصيرة للاختبار اليدوي
        QTimer.singleShot(5000, app.quit)  # إغلاق بعد 5 ثوان
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
