"""
Stock movement model for OnePos POS System
"""

from datetime import datetime
from .database import db


class StockMovement:
    """Stock movement model for tracking inventory changes"""
    
    def __init__(self, movement_id=None, product_id=None, movement_type=None, 
                 quantity=0, reference_id=None, reference_type=None, notes=None, user_id=None):
        self.id = movement_id
        self.product_id = product_id
        self.movement_type = movement_type  # 'in', 'out', 'adjustment', 'sale', 'purchase', 'return'
        self.quantity = quantity  # Positive for stock increase, negative for decrease
        self.reference_id = reference_id  # ID of related record (sale, purchase, etc.)
        self.reference_type = reference_type  # Type of reference (sale, purchase, adjustment, etc.)
        self.notes = notes
        self.user_id = user_id
        self.created_at = None
    
    @classmethod
    def create(cls, product_id, movement_type, quantity, reference_id=None, 
               reference_type=None, notes=None, user_id=None):
        """Create a new stock movement"""
        movement_id = db.execute_update("""
            INSERT INTO stock_movements (product_id, movement_type, quantity, reference_id, 
                                       reference_type, notes, user_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (product_id, movement_type, quantity, reference_id, reference_type, notes, user_id))
        
        return cls.get_by_id(movement_id)
    
    @classmethod
    def get_by_id(cls, movement_id):
        """Get stock movement by ID"""
        result = db.execute_query("SELECT * FROM stock_movements WHERE id = ?", (movement_id,))
        if result:
            return cls._from_db_row(result[0])
        return None
    
    @classmethod
    def get_by_product_id(cls, product_id, limit=None):
        """Get stock movements for a product"""
        query = "SELECT * FROM stock_movements WHERE product_id = ? ORDER BY created_at DESC"
        params = [product_id]
        
        if limit:
            query += " LIMIT ?"
            params.append(limit)
        
        results = db.execute_query(query, params)
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def get_all(cls, limit=None, offset=None, movement_type=None, user_id=None, 
                date_from=None, date_to=None):
        """Get all stock movements with filters"""
        query = "SELECT * FROM stock_movements WHERE 1=1"
        params = []
        
        if movement_type:
            query += " AND movement_type = ?"
            params.append(movement_type)
        
        if user_id:
            query += " AND user_id = ?"
            params.append(user_id)
        
        if date_from:
            query += " AND DATE(created_at) >= ?"
            params.append(date_from)
        
        if date_to:
            query += " AND DATE(created_at) <= ?"
            params.append(date_to)
        
        query += " ORDER BY created_at DESC"
        
        if limit:
            query += " LIMIT ?"
            params.append(limit)
            
            if offset:
                query += " OFFSET ?"
                params.append(offset)
        
        results = db.execute_query(query, params if params else None)
        return [cls._from_db_row(row) for row in results]
    
    @classmethod
    def get_stock_summary(cls, product_id=None, date_from=None, date_to=None):
        """Get stock movement summary"""
        query = """
            SELECT 
                movement_type,
                COUNT(*) as movement_count,
                COALESCE(SUM(CASE WHEN quantity > 0 THEN quantity ELSE 0 END), 0) as total_in,
                COALESCE(SUM(CASE WHEN quantity < 0 THEN ABS(quantity) ELSE 0 END), 0) as total_out,
                COALESCE(SUM(quantity), 0) as net_movement
            FROM stock_movements 
            WHERE 1=1
        """
        params = []
        
        if product_id:
            query += " AND product_id = ?"
            params.append(product_id)
        
        if date_from:
            query += " AND DATE(created_at) >= ?"
            params.append(date_from)
        
        if date_to:
            query += " AND DATE(created_at) <= ?"
            params.append(date_to)
        
        query += " GROUP BY movement_type ORDER BY movement_type"
        
        results = db.execute_query(query, params if params else None)
        return results
    
    @classmethod
    def get_daily_movements(cls, date=None):
        """Get stock movements for a specific date"""
        if date is None:
            date = datetime.now().date()
        
        return cls.get_all(date_from=date, date_to=date)
    
    def get_product(self):
        """Get the product associated with this movement"""
        from .product import Product
        return Product.get_by_id(self.product_id)
    
    def get_user(self):
        """Get the user who created this movement"""
        from .user import User
        if self.user_id:
            return User.get_by_id(self.user_id)
        return None
    
    def to_dict(self):
        """Convert stock movement to dictionary"""
        return {
            'id': self.id,
            'product_id': self.product_id,
            'movement_type': self.movement_type,
            'quantity': self.quantity,
            'reference_id': self.reference_id,
            'reference_type': self.reference_type,
            'notes': self.notes,
            'user_id': self.user_id,
            'created_at': self.created_at
        }
    
    @classmethod
    def _from_db_row(cls, row):
        """Create StockMovement object from database row"""
        movement = cls(
            movement_id=row['id'],
            product_id=row['product_id'],
            movement_type=row['movement_type'],
            quantity=row['quantity'],
            reference_id=row['reference_id'],
            reference_type=row['reference_type'],
            notes=row['notes'],
            user_id=row['user_id']
        )
        movement.created_at = row['created_at']
        return movement


class StockAdjustment:
    """Stock adjustment utilities"""
    
    @staticmethod
    def adjust_stock(product_id, new_quantity, reason, user_id):
        """Adjust product stock to a specific quantity"""
        from .product import Product
        
        product = Product.get_by_id(product_id)
        if not product:
            raise ValueError("Product not found")
        
        current_quantity = product.stock_quantity
        adjustment_quantity = new_quantity - current_quantity
        
        if adjustment_quantity != 0:
            # Update product stock
            product.update(stock_quantity=new_quantity)
            
            # Record stock movement
            StockMovement.create(
                product_id=product_id,
                movement_type='adjustment',
                quantity=adjustment_quantity,
                notes=f"Stock adjustment: {reason}",
                user_id=user_id
            )
        
        return adjustment_quantity
    
    @staticmethod
    def bulk_stock_adjustment(adjustments, user_id, reason="Bulk adjustment"):
        """Perform bulk stock adjustments"""
        results = []
        
        for adjustment in adjustments:
            try:
                product_id = adjustment['product_id']
                new_quantity = adjustment['new_quantity']
                item_reason = adjustment.get('reason', reason)
                
                adjustment_quantity = StockAdjustment.adjust_stock(
                    product_id, new_quantity, item_reason, user_id
                )
                
                results.append({
                    'product_id': product_id,
                    'adjustment_quantity': adjustment_quantity,
                    'success': True,
                    'error': None
                })
                
            except Exception as e:
                results.append({
                    'product_id': adjustment.get('product_id'),
                    'adjustment_quantity': 0,
                    'success': False,
                    'error': str(e)
                })
        
        return results
    
    @staticmethod
    def stock_take(stock_counts, user_id, notes="Stock take"):
        """Perform stock take and adjust quantities"""
        adjustments = []
        
        for product_id, counted_quantity in stock_counts.items():
            adjustments.append({
                'product_id': product_id,
                'new_quantity': counted_quantity,
                'reason': notes
            })
        
        return StockAdjustment.bulk_stock_adjustment(adjustments, user_id, notes)


class StockAlert:
    """Stock alert utilities"""
    
    @staticmethod
    def get_low_stock_products():
        """Get products with low stock"""
        from .product import Product
        return Product.get_low_stock()
    
    @staticmethod
    def get_out_of_stock_products():
        """Get products that are out of stock"""
        results = db.execute_query("""
            SELECT * FROM products 
            WHERE stock_quantity <= 0 AND is_active = 1
            ORDER BY name
        """)
        
        from .product import Product
        return [Product._from_db_row(row) for row in results]
    
    @staticmethod
    def get_overstock_products(threshold_multiplier=5):
        """Get products with excessive stock"""
        results = db.execute_query("""
            SELECT * FROM products 
            WHERE stock_quantity > (min_stock_level * ?) AND min_stock_level > 0 AND is_active = 1
            ORDER BY (stock_quantity / min_stock_level) DESC
        """, (threshold_multiplier,))
        
        from .product import Product
        return [Product._from_db_row(row) for row in results]
    
    @staticmethod
    def check_stock_alerts():
        """Check all stock alerts and return summary"""
        return {
            'low_stock': StockAlert.get_low_stock_products(),
            'out_of_stock': StockAlert.get_out_of_stock_products(),
            'overstock': StockAlert.get_overstock_products()
        }
