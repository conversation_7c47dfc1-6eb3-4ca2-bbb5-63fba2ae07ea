"""
نظام إدارة الطابعات المتقدم - OnePos
Advanced Printer Management System
"""

import os
import sys
import platform
from PyQt5.QtPrintSupport import QPrinter, QPrinterInfo
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal
from utils.config_manager import config
from utils.translator import tr
import subprocess
import json

class PrinterManager(QObject):
    """مدير الطابعات المتقدم"""
    
    printer_connected = pyqtSignal(str)  # عند اتصال طابعة
    printer_disconnected = pyqtSignal(str)  # عند انقطاع طابعة
    
    def __init__(self):
        super().__init__()
        self.available_printers = {}
        self.thermal_printers = {}
        self.default_printer = None
        self.receipt_printer = None
        self.invoice_printer = None
        self.label_printer = None
        
        # تحديث قائمة الطابعات
        self.refresh_printers()
        
        # تحميل الإعدادات المحفوظة
        self.load_printer_settings()
    
    def refresh_printers(self):
        """تحديث قائمة الطابعات المتاحة"""
        try:
            self.available_printers.clear()
            self.thermal_printers.clear()
            
            # الحصول على جميع الطابعات المتاحة
            printers = QPrinterInfo.availablePrinters()
            
            for printer_info in printers:
                printer_name = printer_info.printerName()
                
                printer_data = {
                    'name': printer_name,
                    'description': printer_info.description(),
                    'location': printer_info.location(),
                    'make_model': printer_info.makeAndModel(),
                    'is_default': printer_info.isDefault(),
                    'is_thermal': self.detect_thermal_printer(printer_name),
                    'supported_sizes': self.get_supported_paper_sizes(printer_info),
                    'status': self.get_printer_status_from_info(printer_info)
                }
                
                self.available_printers[printer_name] = printer_data
                
                # إضافة للطابعات الحرارية إذا كانت كذلك
                if printer_data['is_thermal']:
                    self.thermal_printers[printer_name] = printer_data
                
                # تعيين الطابعة الافتراضية
                if printer_info.isDefault():
                    self.default_printer = printer_name
            
            print(f"✅ تم العثور على {len(self.available_printers)} طابعة")
            print(f"🖨️ منها {len(self.thermal_printers)} طابعة حرارية")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحديث الطابعات: {e}")
            return False
    
    def detect_thermal_printer(self, printer_name):
        """كشف الطابعات الحرارية"""
        thermal_keywords = [
            'thermal', 'receipt', 'pos', 'tm-', 'rp-', 'tsp-',
            'star', 'epson', 'citizen', 'bixolon', 'custom',
            'xprinter', 'gprinter', 'zjiang', 'munbyn',
            'حرارية', 'إيصال', 'فاتورة'
        ]
        
        printer_name_lower = printer_name.lower()
        
        for keyword in thermal_keywords:
            if keyword in printer_name_lower:
                return True
        
        return False
    
    def get_supported_paper_sizes(self, printer_info):
        """الحصول على أحجام الورق المدعومة"""
        try:
            supported_sizes = []
            
            # أحجام الورق الشائعة للطابعات الحرارية
            thermal_sizes = ['58mm', '80mm', '112mm']
            
            # أحجام الورق العادية
            standard_sizes = ['A4', 'A5', 'Letter', 'Legal']
            
            # إذا كانت طابعة حرارية
            if self.detect_thermal_printer(printer_info.printerName()):
                supported_sizes.extend(thermal_sizes)
            else:
                supported_sizes.extend(standard_sizes)
            
            return supported_sizes
            
        except Exception as e:
            print(f"خطأ في الحصول على أحجام الورق: {e}")
            return ['A4']  # افتراضي
    
    def get_available_printers(self):
        """الحصول على قائمة الطابعات المتاحة"""
        return self.available_printers
    
    def get_thermal_printers(self):
        """الحصول على الطابعات الحرارية فقط"""
        return self.thermal_printers
    
    def get_standard_printers(self):
        """الحصول على الطابعات العادية فقط"""
        standard_printers = {}
        for name, data in self.available_printers.items():
            if not data['is_thermal']:
                standard_printers[name] = data
        return standard_printers
    
    def set_receipt_printer(self, printer_name):
        """تعيين طابعة الإيصالات"""
        if printer_name in self.available_printers:
            self.receipt_printer = printer_name
            config.set('printers.receipt_printer', printer_name)
            print(f"✅ تم تعيين طابعة الإيصالات: {printer_name}")
            return True
        return False
    
    def set_invoice_printer(self, printer_name):
        """تعيين طابعة الفواتير"""
        if printer_name in self.available_printers:
            self.invoice_printer = printer_name
            config.set('printers.invoice_printer', printer_name)
            print(f"✅ تم تعيين طابعة الفواتير: {printer_name}")
            return True
        return False
    
    def set_label_printer(self, printer_name):
        """تعيين طابعة الملصقات"""
        if printer_name in self.available_printers:
            self.label_printer = printer_name
            config.set('printers.label_printer', printer_name)
            print(f"✅ تم تعيين طابعة الملصقات: {printer_name}")
            return True
        return False
    
    def get_receipt_printer(self):
        """الحصول على طابعة الإيصالات المحددة"""
        return self.receipt_printer
    
    def get_invoice_printer(self):
        """الحصول على طابعة الفواتير المحددة"""
        return self.invoice_printer
    
    def get_label_printer(self):
        """الحصول على طابعة الملصقات المحددة"""
        return self.label_printer
    
    def load_printer_settings(self):
        """تحميل إعدادات الطابعات المحفوظة"""
        try:
            receipt_setting = config.get('printers.receipt_printer', None)
            invoice_setting = config.get('printers.invoice_printer', None)
            label_setting = config.get('printers.label_printer', None)

            # التعامل مع الإعدادات كنصوص فقط
            if isinstance(receipt_setting, str):
                self.receipt_printer = receipt_setting
            elif isinstance(receipt_setting, dict):
                self.receipt_printer = None  # تجاهل الإعدادات القديمة
            else:
                self.receipt_printer = receipt_setting

            if isinstance(invoice_setting, str):
                self.invoice_printer = invoice_setting
            elif isinstance(invoice_setting, dict):
                self.invoice_printer = None  # تجاهل الإعدادات القديمة
            else:
                self.invoice_printer = invoice_setting

            if isinstance(label_setting, str):
                self.label_printer = label_setting
            elif isinstance(label_setting, dict):
                self.label_printer = None  # تجاهل الإعدادات القديمة
            else:
                self.label_printer = label_setting

            # التحقق من وجود الطابعات المحفوظة
            if self.receipt_printer and self.receipt_printer not in self.available_printers:
                print(f"⚠️ طابعة الإيصالات المحفوظة غير متاحة: {self.receipt_printer}")
                self.receipt_printer = None

            if self.invoice_printer and self.invoice_printer not in self.available_printers:
                print(f"⚠️ طابعة الفواتير المحفوظة غير متاحة: {self.invoice_printer}")
                self.invoice_printer = None

            if self.label_printer and self.label_printer not in self.available_printers:
                print(f"⚠️ طابعة الملصقات المحفوظة غير متاحة: {self.label_printer}")
                self.label_printer = None

            # تعيين طابعات افتراضية إذا لم تكن محددة
            self.auto_assign_printers()

        except Exception as e:
            print(f"خطأ في تحميل إعدادات الطابعات: {e}")
            # تعيين قيم افتراضية في حالة الخطأ
            self.receipt_printer = None
            self.invoice_printer = None
            self.label_printer = None
    
    def auto_assign_printers(self):
        """تعيين الطابعات تلقائياً"""
        try:
            # إذا لم تكن طابعة الإيصالات محددة، ابحث عن طابعة حرارية
            if not self.receipt_printer and self.thermal_printers:
                thermal_printer = list(self.thermal_printers.keys())[0]
                self.set_receipt_printer(thermal_printer)
                print(f"🔄 تم تعيين طابعة الإيصالات تلقائياً: {thermal_printer}")
            
            # إذا لم تكن طابعة الفواتير محددة، استخدم الطابعة الافتراضية
            if not self.invoice_printer and self.default_printer:
                self.set_invoice_printer(self.default_printer)
                print(f"🔄 تم تعيين طابعة الفواتير تلقائياً: {self.default_printer}")
            
            # إذا لم تكن طابعة الملصقات محددة، استخدم طابعة حرارية أو الافتراضية
            if not self.label_printer:
                if self.thermal_printers:
                    thermal_printer = list(self.thermal_printers.keys())[0]
                    self.set_label_printer(thermal_printer)
                    print(f"🔄 تم تعيين طابعة الملصقات تلقائياً: {thermal_printer}")
                elif self.default_printer:
                    self.set_label_printer(self.default_printer)
                    print(f"🔄 تم تعيين طابعة الملصقات تلقائياً: {self.default_printer}")
            
        except Exception as e:
            print(f"خطأ في التعيين التلقائي للطابعات: {e}")
    
    def test_printer(self, printer_name):
        """اختبار طابعة محسن"""
        try:
            if printer_name not in self.available_printers:
                return False, "الطابعة غير متاحة"

            # إنشاء طابعة للاختبار
            printer = QPrinter()
            printer.setPrinterName(printer_name)

            if not printer.isValid():
                return False, "الطابعة غير صالحة أو غير متصلة"

            # تحديد نوع الطباعة حسب نوع الطابعة
            printer_data = self.available_printers[printer_name]
            is_thermal = printer_data.get('is_thermal', False)

            if is_thermal:
                return self.test_thermal_printer(printer, printer_name)
            else:
                return self.test_standard_printer(printer, printer_name)

        except Exception as e:
            return False, f"خطأ في اختبار الطابعة: {str(e)}"

    def test_thermal_printer(self, printer, printer_name):
        """اختبار طابعة حرارية"""
        try:
            from PyQt5.QtGui import QPainter, QFont, QFontMetrics
            from PyQt5.QtCore import QRect

            # إعداد الطابعة للطباعة الحرارية
            printer.setPageSize(QPrinter.Custom)
            printer.setPaperSize(80, 200, QPrinter.Millimeter)  # 80mm width

            painter = QPainter()
            if not painter.begin(printer):
                return False, "فشل في بدء الطباعة"

            try:
                # إعداد الخط
                font = QFont("Arial", 10)
                painter.setFont(font)

                y = 50
                line_height = 30

                # عنوان الاختبار
                painter.drawText(50, y, "🖨️ اختبار طابعة حرارية")
                y += line_height * 2

                # معلومات الطابعة
                painter.drawText(50, y, f"الطابعة: {printer_name}")
                y += line_height

                painter.drawText(50, y, f"التاريخ: {self.get_current_datetime()}")
                y += line_height

                painter.drawText(50, y, "النوع: طابعة حرارية")
                y += line_height

                # خط فاصل
                painter.drawText(50, y, "=" * 30)
                y += line_height

                # اختبار الأحرف
                painter.drawText(50, y, "اختبار الأحرف العربية: ✅")
                y += line_height

                painter.drawText(50, y, "English Text Test: ✅")
                y += line_height

                painter.drawText(50, y, "Numbers: 1234567890")
                y += line_height

                # خط فاصل
                painter.drawText(50, y, "=" * 30)
                y += line_height

                painter.drawText(50, y, "OnePos POS System")
                y += line_height

                painter.drawText(50, y, "© 2025 ASSANAJE_APP")

                painter.end()
                return True, "تم اختبار الطابعة الحرارية بنجاح"

            except Exception as e:
                painter.end()
                return False, f"خطأ أثناء الطباعة: {str(e)}"

        except Exception as e:
            return False, f"خطأ في إعداد الطباعة الحرارية: {str(e)}"

    def test_standard_printer(self, printer, printer_name):
        """اختبار طابعة عادية"""
        try:
            from PyQt5.QtGui import QTextDocument

            # إعداد الطابعة للطباعة العادية
            printer.setPageSize(QPrinter.A4)

            test_content = f"""
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body {{
                        font-family: Arial, sans-serif;
                        margin: 40px;
                        direction: rtl;
                    }}
                    .header {{
                        text-align: center;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                        margin-bottom: 30px;
                    }}
                    .info {{
                        background-color: #f0f0f0;
                        padding: 20px;
                        border-radius: 8px;
                        margin: 20px 0;
                    }}
                    .footer {{
                        text-align: center;
                        margin-top: 40px;
                        color: #666;
                    }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>🖨️ اختبار الطابعة</h1>
                    <h2>OnePos POS System</h2>
                </div>

                <div class="info">
                    <h3>معلومات الطابعة:</h3>
                    <p><strong>اسم الطابعة:</strong> {printer_name}</p>
                    <p><strong>التاريخ والوقت:</strong> {self.get_current_datetime()}</p>
                    <p><strong>نوع الطابعة:</strong> طابعة عادية</p>
                    <p><strong>حالة الاختبار:</strong> ✅ نجح الاختبار</p>
                </div>

                <div class="info">
                    <h3>اختبار الطباعة:</h3>
                    <p>✅ اختبار الأحرف العربية: مرحباً بكم في نظام OnePos</p>
                    <p>✅ English Text Test: Welcome to OnePos System</p>
                    <p>✅ اختبار الأرقام: 1234567890</p>
                    <p>✅ اختبار الرموز: !@#$%^&*()</p>
                </div>

                <div class="info">
                    <h3>معلومات النظام:</h3>
                    <p>نظام نقاط البيع المتقدم</p>
                    <p>يدعم الطباعة العربية والإنجليزية</p>
                    <p>طباعة الفواتير والإيصالات</p>
                </div>

                <div class="footer">
                    <p>تم إنشاء هذه الصفحة تلقائياً لاختبار الطابعة</p>
                    <p>© 2025 ASSANAJE_APP - جميع الحقوق محفوظة</p>
                </div>
            </body>
            </html>
            """

            document = QTextDocument()
            document.setHtml(test_content)

            # طباعة الاختبار
            document.print_(printer)

            return True, "تم اختبار الطابعة العادية بنجاح"

        except Exception as e:
            return False, f"خطأ في اختبار الطابعة العادية: {str(e)}"

    def print_receipt(self, sale_data):
        """طباعة إيصال"""
        try:
            if not self.receipt_printer:
                return False, "لم يتم تعيين طابعة الإيصالات"

            printer = QPrinter()
            printer.setPrinterName(self.receipt_printer)

            if not printer.isValid():
                return False, "طابعة الإيصالات غير متاحة"

            # تحديد نوع الطباعة
            printer_data = self.available_printers.get(self.receipt_printer, {})
            is_thermal = printer_data.get('is_thermal', False)

            if is_thermal:
                return self.print_thermal_receipt(printer, sale_data)
            else:
                return self.print_standard_receipt(printer, sale_data)

        except Exception as e:
            return False, f"خطأ في طباعة الإيصال: {str(e)}"

    def print_thermal_receipt(self, printer, sale_data):
        """طباعة إيصال حراري"""
        try:
            from PyQt5.QtGui import QPainter, QFont

            # إعداد الطابعة الحرارية
            printer.setPageSize(QPrinter.Custom)
            printer.setPaperSize(80, 200, QPrinter.Millimeter)

            painter = QPainter()
            if not painter.begin(printer):
                return False, "فشل في بدء الطباعة"

            try:
                font = QFont("Arial", 9)
                painter.setFont(font)

                y = 50
                line_height = 25

                # عنوان المحل
                painter.drawText(50, y, "OnePos POS System")
                y += line_height

                painter.drawText(50, y, "=" * 32)
                y += line_height

                # معلومات الفاتورة
                painter.drawText(50, y, f"رقم الفاتورة: {sale_data.get('invoice_number', 'N/A')}")
                y += line_height

                painter.drawText(50, y, f"التاريخ: {self.get_current_datetime()}")
                y += line_height

                painter.drawText(50, y, f"الكاشير: {sale_data.get('cashier', 'Admin')}")
                y += line_height

                if sale_data.get('customer'):
                    painter.drawText(50, y, f"العميل: {sale_data['customer']}")
                    y += line_height

                painter.drawText(50, y, "-" * 32)
                y += line_height

                # المنتجات
                total = 0
                for item in sale_data.get('items', []):
                    name = item['name'][:20] + "..." if len(item['name']) > 20 else item['name']
                    painter.drawText(50, y, name)
                    y += line_height

                    qty = item.get('quantity', 1)
                    price = item.get('unit_price', 0)
                    item_total = qty * price
                    total += item_total

                    painter.drawText(50, y, f"  {qty} x {price:.2f} = {item_total:.2f}")
                    y += line_height

                painter.drawText(50, y, "=" * 32)
                y += line_height

                painter.drawText(50, y, f"الإجمالي: {total:.2f}")
                y += line_height

                painter.drawText(50, y, "=" * 32)
                y += line_height

                painter.drawText(50, y, "شكراً لزيارتكم!")

                painter.end()
                return True, "تم طباعة الإيصال الحراري بنجاح"

            except Exception as e:
                painter.end()
                return False, f"خطأ أثناء الطباعة: {str(e)}"

        except Exception as e:
            return False, f"خطأ في إعداد الطباعة الحرارية: {str(e)}"

    def print_standard_receipt(self, printer, sale_data):
        """طباعة إيصال عادي"""
        try:
            from PyQt5.QtGui import QTextDocument

            printer.setPageSize(QPrinter.A4)

            # إنشاء محتوى الإيصال
            items_html = ""
            total = 0

            for item in sale_data.get('items', []):
                qty = item.get('quantity', 1)
                price = item.get('unit_price', 0)
                item_total = qty * price
                total += item_total

                items_html += f"""
                <tr>
                    <td>{item['name']}</td>
                    <td>{qty}</td>
                    <td>{price:.2f}</td>
                    <td>{item_total:.2f}</td>
                </tr>
                """

            receipt_content = f"""
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body {{ font-family: Arial; direction: rtl; margin: 20px; }}
                    .header {{ text-align: center; border-bottom: 2px solid #333; padding-bottom: 15px; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                    th {{ background-color: #f2f2f2; }}
                    .total {{ font-size: 18px; font-weight: bold; text-align: right; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>OnePos POS System</h2>
                    <p>إيصال البيع</p>
                </div>

                <p><strong>رقم الفاتورة:</strong> {sale_data.get('invoice_number', 'N/A')}</p>
                <p><strong>التاريخ:</strong> {self.get_current_datetime()}</p>
                <p><strong>الكاشير:</strong> {sale_data.get('cashier', 'Admin')}</p>
                {f"<p><strong>العميل:</strong> {sale_data['customer']}</p>" if sale_data.get('customer') else ""}

                <table>
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        {items_html}
                    </tbody>
                </table>

                <div class="total">
                    <p>الإجمالي: {total:.2f}</p>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <p>شكراً لزيارتكم!</p>
                    <p>© 2025 ASSANAJE_APP</p>
                </div>
            </body>
            </html>
            """

            document = QTextDocument()
            document.setHtml(receipt_content)
            document.print_(printer)

            return True, "تم طباعة الإيصال بنجاح"

        except Exception as e:
            return False, f"خطأ في طباعة الإيصال: {str(e)}"
    
    def get_current_datetime(self):
        """الحصول على التاريخ والوقت الحالي"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def get_printer_status_from_info(self, printer_info):
        """الحصول على حالة الطابعة من معلومات الطابعة"""
        try:
            # استخدام الطريقة الآمنة للحصول على الحالة
            if hasattr(printer_info, 'state'):
                state = printer_info.state()
                # استخدام القيم الرقمية بدلاً من الثوابت
                if state == 0:  # Idle
                    return "جاهزة"
                elif state == 1:  # Active
                    return "تطبع"
                elif state == 2:  # Aborted
                    return "متوقفة"
                elif state == 3:  # Error
                    return "خطأ"
                else:
                    return "غير معروفة"
            else:
                return "متاح"
        except:
            return "متاح"

    def get_printer_status(self, printer_name):
        """الحصول على حالة الطابعة"""
        if printer_name not in self.available_printers:
            return "غير متاحة"

        try:
            printer_info = QPrinterInfo.printerInfoForName(printer_name)
            return self.get_printer_status_from_info(printer_info)
        except:
            return "غير متاحة"
    
    def save_settings(self):
        """حفظ إعدادات الطابعات"""
        try:
            if self.receipt_printer:
                config.set('printers.receipt_printer', self.receipt_printer)
            if self.invoice_printer:
                config.set('printers.invoice_printer', self.invoice_printer)
            if self.label_printer:
                config.set('printers.label_printer', self.label_printer)

            # حفظ الملف (إذا كانت الطريقة متاحة)
            if hasattr(config, 'save'):
                config.save()

            return True
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الطابعات: {e}")
            return False


# إنشاء مثيل عام لمدير الطابعات
printer_manager = PrinterManager()
