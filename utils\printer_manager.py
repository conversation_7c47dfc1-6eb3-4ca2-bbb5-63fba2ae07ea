"""
نظام إدارة الطابعات المتقدم - OnePos
Advanced Printer Management System
"""

import os
import sys
import platform
from PyQt5.QtPrintSupport import QPrinter, QPrinterInfo
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal
from utils.config_manager import config
from utils.translator import tr
import subprocess
import json

class PrinterManager(QObject):
    """مدير الطابعات المتقدم"""
    
    printer_connected = pyqtSignal(str)  # عند اتصال طابعة
    printer_disconnected = pyqtSignal(str)  # عند انقطاع طابعة
    
    def __init__(self):
        super().__init__()
        self.available_printers = {}
        self.thermal_printers = {}
        self.default_printer = None
        self.receipt_printer = None
        self.invoice_printer = None
        self.label_printer = None
        
        # تحديث قائمة الطابعات
        self.refresh_printers()
        
        # تحميل الإعدادات المحفوظة
        self.load_printer_settings()
    
    def refresh_printers(self):
        """تحديث قائمة الطابعات المتاحة"""
        try:
            self.available_printers.clear()
            self.thermal_printers.clear()
            
            # الحصول على جميع الطابعات المتاحة
            printers = QPrinterInfo.availablePrinters()
            
            for printer_info in printers:
                printer_name = printer_info.printerName()
                
                printer_data = {
                    'name': printer_name,
                    'description': printer_info.description(),
                    'location': printer_info.location(),
                    'make_model': printer_info.makeAndModel(),
                    'is_default': printer_info.isDefault(),
                    'is_thermal': self.detect_thermal_printer(printer_name),
                    'supported_sizes': self.get_supported_paper_sizes(printer_info),
                    'status': self.get_printer_status_from_info(printer_info)
                }
                
                self.available_printers[printer_name] = printer_data
                
                # إضافة للطابعات الحرارية إذا كانت كذلك
                if printer_data['is_thermal']:
                    self.thermal_printers[printer_name] = printer_data
                
                # تعيين الطابعة الافتراضية
                if printer_info.isDefault():
                    self.default_printer = printer_name
            
            print(f"✅ تم العثور على {len(self.available_printers)} طابعة")
            print(f"🖨️ منها {len(self.thermal_printers)} طابعة حرارية")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحديث الطابعات: {e}")
            return False
    
    def detect_thermal_printer(self, printer_name):
        """كشف الطابعات الحرارية"""
        thermal_keywords = [
            'thermal', 'receipt', 'pos', 'tm-', 'rp-', 'tsp-',
            'star', 'epson', 'citizen', 'bixolon', 'custom',
            'xprinter', 'gprinter', 'zjiang', 'munbyn',
            'حرارية', 'إيصال', 'فاتورة'
        ]
        
        printer_name_lower = printer_name.lower()
        
        for keyword in thermal_keywords:
            if keyword in printer_name_lower:
                return True
        
        return False
    
    def get_supported_paper_sizes(self, printer_info):
        """الحصول على أحجام الورق المدعومة"""
        try:
            supported_sizes = []
            
            # أحجام الورق الشائعة للطابعات الحرارية
            thermal_sizes = ['58mm', '80mm', '112mm']
            
            # أحجام الورق العادية
            standard_sizes = ['A4', 'A5', 'Letter', 'Legal']
            
            # إذا كانت طابعة حرارية
            if self.detect_thermal_printer(printer_info.printerName()):
                supported_sizes.extend(thermal_sizes)
            else:
                supported_sizes.extend(standard_sizes)
            
            return supported_sizes
            
        except Exception as e:
            print(f"خطأ في الحصول على أحجام الورق: {e}")
            return ['A4']  # افتراضي
    
    def get_available_printers(self):
        """الحصول على قائمة الطابعات المتاحة"""
        return self.available_printers
    
    def get_thermal_printers(self):
        """الحصول على الطابعات الحرارية فقط"""
        return self.thermal_printers
    
    def get_standard_printers(self):
        """الحصول على الطابعات العادية فقط"""
        standard_printers = {}
        for name, data in self.available_printers.items():
            if not data['is_thermal']:
                standard_printers[name] = data
        return standard_printers
    
    def set_receipt_printer(self, printer_name):
        """تعيين طابعة الإيصالات"""
        if printer_name in self.available_printers:
            self.receipt_printer = printer_name
            config.set('printers.receipt_printer', printer_name)
            print(f"✅ تم تعيين طابعة الإيصالات: {printer_name}")
            return True
        return False
    
    def set_invoice_printer(self, printer_name):
        """تعيين طابعة الفواتير"""
        if printer_name in self.available_printers:
            self.invoice_printer = printer_name
            config.set('printers.invoice_printer', printer_name)
            print(f"✅ تم تعيين طابعة الفواتير: {printer_name}")
            return True
        return False
    
    def set_label_printer(self, printer_name):
        """تعيين طابعة الملصقات"""
        if printer_name in self.available_printers:
            self.label_printer = printer_name
            config.set('printers.label_printer', printer_name)
            print(f"✅ تم تعيين طابعة الملصقات: {printer_name}")
            return True
        return False
    
    def get_receipt_printer(self):
        """الحصول على طابعة الإيصالات المحددة"""
        return self.receipt_printer
    
    def get_invoice_printer(self):
        """الحصول على طابعة الفواتير المحددة"""
        return self.invoice_printer
    
    def get_label_printer(self):
        """الحصول على طابعة الملصقات المحددة"""
        return self.label_printer
    
    def load_printer_settings(self):
        """تحميل إعدادات الطابعات المحفوظة"""
        try:
            receipt_setting = config.get('printers.receipt_printer', None)
            invoice_setting = config.get('printers.invoice_printer', None)
            label_setting = config.get('printers.label_printer', None)

            # التعامل مع الإعدادات كنصوص فقط
            if isinstance(receipt_setting, str):
                self.receipt_printer = receipt_setting
            elif isinstance(receipt_setting, dict):
                self.receipt_printer = None  # تجاهل الإعدادات القديمة
            else:
                self.receipt_printer = receipt_setting

            if isinstance(invoice_setting, str):
                self.invoice_printer = invoice_setting
            elif isinstance(invoice_setting, dict):
                self.invoice_printer = None  # تجاهل الإعدادات القديمة
            else:
                self.invoice_printer = invoice_setting

            if isinstance(label_setting, str):
                self.label_printer = label_setting
            elif isinstance(label_setting, dict):
                self.label_printer = None  # تجاهل الإعدادات القديمة
            else:
                self.label_printer = label_setting

            # التحقق من وجود الطابعات المحفوظة
            if self.receipt_printer and self.receipt_printer not in self.available_printers:
                print(f"⚠️ طابعة الإيصالات المحفوظة غير متاحة: {self.receipt_printer}")
                self.receipt_printer = None

            if self.invoice_printer and self.invoice_printer not in self.available_printers:
                print(f"⚠️ طابعة الفواتير المحفوظة غير متاحة: {self.invoice_printer}")
                self.invoice_printer = None

            if self.label_printer and self.label_printer not in self.available_printers:
                print(f"⚠️ طابعة الملصقات المحفوظة غير متاحة: {self.label_printer}")
                self.label_printer = None

            # تعيين طابعات افتراضية إذا لم تكن محددة
            self.auto_assign_printers()

        except Exception as e:
            print(f"خطأ في تحميل إعدادات الطابعات: {e}")
            # تعيين قيم افتراضية في حالة الخطأ
            self.receipt_printer = None
            self.invoice_printer = None
            self.label_printer = None
    
    def auto_assign_printers(self):
        """تعيين الطابعات تلقائياً"""
        try:
            # إذا لم تكن طابعة الإيصالات محددة، ابحث عن طابعة حرارية
            if not self.receipt_printer and self.thermal_printers:
                thermal_printer = list(self.thermal_printers.keys())[0]
                self.set_receipt_printer(thermal_printer)
                print(f"🔄 تم تعيين طابعة الإيصالات تلقائياً: {thermal_printer}")
            
            # إذا لم تكن طابعة الفواتير محددة، استخدم الطابعة الافتراضية
            if not self.invoice_printer and self.default_printer:
                self.set_invoice_printer(self.default_printer)
                print(f"🔄 تم تعيين طابعة الفواتير تلقائياً: {self.default_printer}")
            
            # إذا لم تكن طابعة الملصقات محددة، استخدم طابعة حرارية أو الافتراضية
            if not self.label_printer:
                if self.thermal_printers:
                    thermal_printer = list(self.thermal_printers.keys())[0]
                    self.set_label_printer(thermal_printer)
                    print(f"🔄 تم تعيين طابعة الملصقات تلقائياً: {thermal_printer}")
                elif self.default_printer:
                    self.set_label_printer(self.default_printer)
                    print(f"🔄 تم تعيين طابعة الملصقات تلقائياً: {self.default_printer}")
            
        except Exception as e:
            print(f"خطأ في التعيين التلقائي للطابعات: {e}")
    
    def test_printer(self, printer_name):
        """اختبار طابعة"""
        try:
            if printer_name not in self.available_printers:
                return False, "الطابعة غير متاحة"
            
            # إنشاء طابعة للاختبار
            printer = QPrinter()
            printer.setPrinterName(printer_name)
            
            if not printer.isValid():
                return False, "الطابعة غير صالحة"
            
            # اختبار بسيط - طباعة صفحة اختبار
            from PyQt5.QtGui import QPainter, QTextDocument
            
            test_content = f"""
            <html>
            <body style="font-family: Arial; text-align: center; padding: 20px;">
                <h2>🖨️ اختبار الطابعة</h2>
                <p><strong>اسم الطابعة:</strong> {printer_name}</p>
                <p><strong>التاريخ:</strong> {self.get_current_datetime()}</p>
                <p><strong>الحالة:</strong> تعمل بشكل صحيح ✅</p>
                <hr>
                <p>OnePos POS System</p>
            </body>
            </html>
            """
            
            document = QTextDocument()
            document.setHtml(test_content)
            
            # طباعة الاختبار
            document.print_(printer)
            
            return True, "تم اختبار الطابعة بنجاح"
            
        except Exception as e:
            return False, f"خطأ في اختبار الطابعة: {str(e)}"
    
    def get_current_datetime(self):
        """الحصول على التاريخ والوقت الحالي"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def get_printer_status_from_info(self, printer_info):
        """الحصول على حالة الطابعة من معلومات الطابعة"""
        try:
            # استخدام الطريقة الآمنة للحصول على الحالة
            if hasattr(printer_info, 'state'):
                state = printer_info.state()
                # استخدام القيم الرقمية بدلاً من الثوابت
                if state == 0:  # Idle
                    return "جاهزة"
                elif state == 1:  # Active
                    return "تطبع"
                elif state == 2:  # Aborted
                    return "متوقفة"
                elif state == 3:  # Error
                    return "خطأ"
                else:
                    return "غير معروفة"
            else:
                return "متاح"
        except:
            return "متاح"

    def get_printer_status(self, printer_name):
        """الحصول على حالة الطابعة"""
        if printer_name not in self.available_printers:
            return "غير متاحة"

        try:
            printer_info = QPrinterInfo.printerInfoForName(printer_name)
            return self.get_printer_status_from_info(printer_info)
        except:
            return "غير متاحة"
    
    def save_settings(self):
        """حفظ إعدادات الطابعات"""
        try:
            if self.receipt_printer:
                config.set('printers.receipt_printer', self.receipt_printer)
            if self.invoice_printer:
                config.set('printers.invoice_printer', self.invoice_printer)
            if self.label_printer:
                config.set('printers.label_printer', self.label_printer)

            # حفظ الملف (إذا كانت الطريقة متاحة)
            if hasattr(config, 'save'):
                config.save()

            return True
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الطابعات: {e}")
            return False


# إنشاء مثيل عام لمدير الطابعات
printer_manager = PrinterManager()
