"""
نافذة تفعيل التطبيق
Application Activation Dialog
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTextEdit, QFrame,
                            QMessageBox, QGroupBox, QGridLayout)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon
from utils.license_manager import license_manager
from utils.translator import tr


class ActivationDialog(QDialog):
    """نافذة تفعيل التطبيق"""
    
    activation_successful = pyqtSignal()
    
    def __init__(self, parent=None, force_activation=False):
        super().__init__(parent)
        self.force_activation = force_activation
        self.setup_ui()
        self.load_license_status()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔐 تفعيل OnePos")
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        # منع إغلاق النافذة إذا كان التفعيل إجباري
        if self.force_activation:
            self.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)
        
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # العنوان الرئيسي
        title_label = QLabel("🔐 تفعيل تطبيق OnePos")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات الحالة
        self.status_frame = QFrame()
        self.status_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        status_layout = QVBoxLayout(self.status_frame)
        
        self.status_label = QLabel()
        self.status_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(self.status_frame)
        
        # مجموعة التفعيل
        activation_group = QGroupBox("🔑 تفعيل التطبيق")
        activation_group.setFont(QFont("Arial", 12, QFont.Bold))
        activation_layout = QGridLayout(activation_group)
        
        # تسمية كود السيريال
        serial_label = QLabel("كود السيريال:")
        serial_label.setFont(QFont("Arial", 11, QFont.Bold))
        activation_layout.addWidget(serial_label, 0, 0)
        
        # حقل إدخال كود السيريال
        self.serial_input = QLineEdit()
        self.serial_input.setPlaceholderText("أدخل كود السيريال هنا...")
        self.serial_input.setFont(QFont("Courier", 11))
        self.serial_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #667eea;
            }
        """)
        activation_layout.addWidget(self.serial_input, 1, 0, 1, 2)
        
        # زر التفعيل
        self.activate_button = QPushButton("🚀 تفعيل التطبيق")
        self.activate_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.activate_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 12px 20px;
                border: none;
                border-radius: 6px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        self.activate_button.clicked.connect(self.activate_license)
        activation_layout.addWidget(self.activate_button, 2, 0, 1, 2)
        
        layout.addWidget(activation_group)
        
        # معلومات الجهاز
        machine_group = QGroupBox("💻 معلومات الجهاز")
        machine_group.setFont(QFont("Arial", 12, QFont.Bold))
        machine_layout = QVBoxLayout(machine_group)
        
        machine_id = license_manager._get_machine_id()
        machine_info = QLabel(f"معرف الجهاز: {machine_id}")
        machine_info.setFont(QFont("Courier", 10))
        machine_info.setStyleSheet("color: #666; padding: 5px;")
        machine_layout.addWidget(machine_info)
        
        # Note: Test serial generation removed for production version
        
        layout.addWidget(machine_group)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        if not self.force_activation:
            # زر المتابعة في فترة التجربة
            self.continue_button = QPushButton("⏰ متابعة فترة التجربة")
            self.continue_button.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    padding: 10px 15px;
                    border: none;
                    border-radius: 5px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)
            self.continue_button.clicked.connect(self.accept)
            buttons_layout.addWidget(self.continue_button)
        
        # زر الخروج
        exit_button = QPushButton("❌ خروج")
        exit_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 10px 15px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        exit_button.clicked.connect(self.reject)
        buttons_layout.addWidget(exit_button)
        
        layout.addLayout(buttons_layout)
        
        # معلومات إضافية
        info_text = QTextEdit()
        info_text.setMaximumHeight(100)
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <div style="font-family: Arial; font-size: 11px; color: #666;">
        <h4 style="color: #495057;">💡 معلومات مهمة:</h4>
        <ul>
        <li><strong>فترة التجربة:</strong> 3 أيام مجاناً</li>
        <li><strong>التفعيل:</strong> أدخل كود السيريال للاستخدام الكامل</li>
        <li><strong>الدعم:</strong> تواصل معنا للحصول على كود التفعيل</li>
        </ul>
        </div>
        """)
        info_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        layout.addWidget(info_text)
        
        self.setLayout(layout)
    
    def load_license_status(self):
        """تحميل حالة الترخيص"""
        status = license_manager.get_license_status()
        
        if status['status'] == 'activated':
            self.status_label.setText("✅ التطبيق مفعل بنجاح")
            self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
            self.serial_input.setEnabled(False)
            self.activate_button.setEnabled(False)
            if hasattr(self, 'continue_button'):
                self.continue_button.setText("✅ متابعة")
        
        elif status['status'] == 'trial':
            days = status['days_remaining']
            self.status_label.setText(f"⏰ فترة تجربة - متبقي {days} أيام")
            self.status_label.setStyleSheet("color: #ffc107; font-weight: bold;")
        
        elif status['status'] == 'expired':
            self.status_label.setText("❌ فترة التجربة منتهية - يجب التفعيل")
            self.status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
            if hasattr(self, 'continue_button'):
                self.continue_button.setEnabled(False)
    
    def activate_license(self):
        """تفعيل الترخيص"""
        serial_code = self.serial_input.text().strip()
        
        if not serial_code:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كود السيريال")
            return
        
        success, message = license_manager.activate_license(serial_code)
        
        if success:
            QMessageBox.information(self, "نجح التفعيل", message)
            self.activation_successful.emit()
            self.accept()
        else:
            QMessageBox.critical(self, "فشل التفعيل", message)
    

    
    def closeEvent(self, event):
        """منع إغلاق النافذة إذا كان التفعيل إجباري"""
        if self.force_activation:
            status = license_manager.get_license_status()
            if status['status'] == 'expired':
                event.ignore()
                QMessageBox.warning(self, "تفعيل مطلوب", 
                                  "يجب تفعيل التطبيق للمتابعة")
                return
        
        event.accept()
