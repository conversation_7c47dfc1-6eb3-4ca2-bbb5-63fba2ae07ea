@echo off
echo ========================================
echo OnePos - Professional Build Script
echo ========================================
echo.

:: Set encoding to UTF-8
chcp 65001 > nul

:: Check if Python is installed
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

echo ✅ Python found
echo.

:: Check if PyInstaller is installed
python -c "import PyInstaller" > nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Installing PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ❌ Failed to install PyInstaller
        pause
        exit /b 1
    )
)

echo ✅ PyInstaller ready
echo.

:: Clean previous builds
echo 🧹 Cleaning previous builds...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

:: Find and clean all __pycache__ directories
for /d /r . %%d in (__pycache__) do @if exist "%%d" rmdir /s /q "%%d"

echo ✅ Cleanup completed
echo.

:: Check if icon file exists
if not exist "app_icon.ico" (
    echo ⚠️ Warning: app_icon.ico not found
    echo Creating a default icon placeholder...
    echo. > app_icon.ico
)

:: Check if version info exists
if not exist "version_info.txt" (
    echo ⚠️ Warning: version_info.txt not found
    echo The build will continue without version information
)

:: Install required packages
echo 📦 Installing/updating required packages...
pip install --upgrade pip
pip install pyinstaller
pip install PyQt5
pip install reportlab
pip install pillow
pip install pyserial
pip install hidapi
pip install numpy

echo.
echo 🔨 Starting PyInstaller build...
echo.

:: Run PyInstaller with the spec file
pyinstaller --clean --noconfirm onepos.spec

:: Check if build was successful
if %errorlevel% neq 0 (
    echo ❌ Build failed!
    echo Check the output above for errors
    pause
    exit /b 1
)

echo.
echo ✅ Build completed successfully!
echo.

:: Check if the executable was created
if exist "dist\OnePos\OnePos.exe" (
    echo 🎉 Executable created: dist\OnePos\OnePos.exe
    echo 📁 Full application folder: dist\OnePos\
    echo.
    echo 🧪 Testing the executable...
    
    :: Test run the executable (timeout after 5 seconds)
    timeout /t 2 > nul
    start "" "dist\OnePos\OnePos.exe"
    timeout /t 3 > nul
    
    echo ✅ Application launched successfully
    echo.
    echo 📋 Next steps:
    echo 1. Test the application thoroughly
    echo 2. Run build_installer.bat to create the installer
    echo 3. Distribute the installer to users
    
) else (
    echo ❌ Executable not found!
    echo Something went wrong during the build process
)

echo.
echo ========================================
echo Build process completed
echo ========================================
pause
