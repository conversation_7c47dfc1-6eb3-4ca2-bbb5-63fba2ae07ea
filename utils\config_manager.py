"""
Configuration manager for OnePos POS System
"""

import json
import os
from typing import Any, Dict


class ConfigManager:
    """Configuration manager for application settings"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.create_default_config()
        except Exception as e:
            print(f"Error loading config: {e}")
            self.create_default_config()
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key (supports dot notation)"""
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """Set configuration value by key (supports dot notation)"""
        keys = key.split('.')
        config = self.config
        
        # Navigate to the parent of the target key
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
        self.save_config()
    
    def get_app_name(self) -> str:
        """Get application name"""
        return self.get('app.name', 'OnePos')
    
    def get_app_version(self) -> str:
        """Get application version"""
        return self.get('app.version', '1.0.0')
    
    def get_language(self) -> str:
        """Get current language"""
        return self.get('app.language', 'ar')
    
    def set_language(self, language: str):
        """Set application language"""
        self.set('app.language', language)
    
    def get_theme(self) -> str:
        """Get current theme"""
        return self.get('app.theme', 'dark')
    
    def set_theme(self, theme: str):
        """Set application theme"""
        self.set('app.theme', theme)
    
    def get_currency(self) -> str:
        """Get currency symbol"""
        return self.get('app.currency', 'MAD')

    def set_currency(self, currency: str):
        """Set currency symbol"""
        self.set('app.currency', currency)

    def get_tax_rate(self) -> float:
        """Get tax rate percentage"""
        return self.get('app.tax_rate', 20.0)
    
    def get_company_info(self) -> Dict:
        """Get company information"""
        return self.get('company', {})
    
    def get_database_path(self) -> str:
        """Get database file path"""
        # Use user's Documents directory for better permissions
        import os
        user_docs = os.path.expanduser("~/Documents")
        data_dir = os.path.join(user_docs, "OnePos")
        default_path = os.path.join(data_dir, "onepos.db")
        return self.get('database.path', default_path)
    
    def get_backup_path(self) -> str:
        """Get backup directory path"""
        return self.get('database.backup_path', 'backups/')
    
    def get_company_config(self) -> Dict:
        """Get company configuration"""
        return self.get('company', {})

    def get_printer_config(self, printer_type: str = None) -> Dict:
        """Get printer configuration"""
        if printer_type:
            return self.get(f'printers.{printer_type}', {})
        else:
            return self.get('printers', {})

    def get_printer_setting(self, setting_name: str, default_value=None):
        """Get specific printer setting"""
        return self.get(f'printer_settings.{setting_name}', default_value)

    def set_printer_setting(self, setting_name: str, value):
        """Set specific printer setting"""
        self.set(f'printer_settings.{setting_name}', value)

    def get_default_print_type(self) -> str:
        """Get default print type (receipt or invoice)"""
        return self.get_printer_setting('default_print_type', 'receipt')

    def set_default_print_type(self, print_type: str):
        """Set default print type"""
        self.set_printer_setting('default_print_type', print_type)

    def get_auto_print_setting(self) -> bool:
        """Get auto print after sale setting"""
        return self.get_printer_setting('auto_print_after_sale', True)

    def set_auto_print_setting(self, auto_print: bool):
        """Set auto print after sale setting"""
        self.set_printer_setting('auto_print_after_sale', auto_print)
    
    def get_barcode_config(self) -> Dict:
        """Get barcode configuration"""
        return self.get('barcode', {})
    
    def get_ui_config(self) -> Dict:
        """Get UI configuration"""
        return self.get('ui', {})
    
    def get_security_config(self) -> Dict:
        """Get security configuration"""
        return self.get('security', {})
    
    def is_fullscreen(self) -> bool:
        """Check if application should start in fullscreen"""
        return self.get('ui.fullscreen', False)
    
    def get_window_size(self) -> tuple:
        """Get window size"""
        width = self.get('ui.window_width', 1200)
        height = self.get('ui.window_height', 800)
        return (width, height)
    
    def create_default_config(self):
        """Create default configuration"""
        self.config = {
            "app": {
                "name": "OnePos",
                "version": "1.0.0",
                "language": "ar",
                "theme": "dark",
                "currency": "MAD",
                "tax_rate": 20.0,
                "auto_backup": True,
                "backup_interval_days": 7
            },
            "company": {
                "name": "Your Company Name",
                "address": "Company Address",
                "phone": "+212 XXX XXX XXX",
                "email": "<EMAIL>",
                "tax_number": "TAX123456",
                "logo_path": "assets/logo.png"
            },
            "database": {
                "path": "data/onepos.db",
                "backup_path": "backups/"
            },
            "printers": {
                "receipt_printer": {
                    "enabled": False,
                    "type": "thermal",
                    "interface": "usb",
                    "vendor_id": "",
                    "product_id": "",
                    "width": 58
                },
                "kitchen_printer": {
                    "enabled": False,
                    "type": "thermal",
                    "interface": "usb",
                    "vendor_id": "",
                    "product_id": "",
                    "width": 80
                },
                "label_printer": {
                    "enabled": False,
                    "type": "thermal",
                    "interface": "usb",
                    "vendor_id": "",
                    "product_id": "",
                    "width": 40
                }
            },
            "barcode": {
                "scanner_enabled": True,
                "camera_enabled": True,
                "camera_index": 0,
                "auto_generate": True,
                "prefix": "ONP"
            },
            "ui": {
                "window_width": 1200,
                "window_height": 800,
                "fullscreen": False,
                "sidebar_width": 250,
                "animations": True,
                "sound_effects": True
            },
            "security": {
                "session_timeout": 3600,
                "password_min_length": 6,
                "auto_logout": True,
                "require_login": True
            }
        }
        self.save_config()


# Global configuration manager instance
config = ConfigManager()
