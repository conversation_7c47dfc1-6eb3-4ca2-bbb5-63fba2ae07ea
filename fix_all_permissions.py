#!/usr/bin/env python3
"""
إصلاح شامل لجميع مشاكل الصلاحيات
Comprehensive fix for all permission issues
"""

import os
import re

def fix_all_data_paths():
    """Fix all files that use 'data' directory"""
    
    print("🔧 إصلاح شامل لمسارات البيانات")
    print("=" * 50)
    
    files_to_fix = [
        "models/database.py",
        "utils/license_manager.py", 
        "utils/config_manager.py"
    ]
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"\n📝 إصلاح ملف: {file_path}")
            fix_file(file_path)
        else:
            print(f"\n⚠️ ملف غير موجود: {file_path}")

def fix_file(file_path):
    """Fix a specific file"""
    try:
        # Read file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix patterns
        fixes_applied = []
        
        # Fix 1: Replace hardcoded "data" directory creation
        if 'os.makedirs("data"' in content:
            content = content.replace(
                'os.makedirs("data", exist_ok=True)',
                '''try:
            os.makedirs(self.data_dir, exist_ok=True)
        except PermissionError:
            import tempfile
            self.data_dir = os.path.join(tempfile.gettempdir(), "OnePos")
            os.makedirs(self.data_dir, exist_ok=True)'''
            )
            fixes_applied.append("Fixed hardcoded data directory creation")
        
        # Fix 2: Replace "data/license.dat" with dynamic path
        if '"data/license.dat"' in content:
            content = content.replace(
                'self.license_file = "data/license.dat"',
                'self.license_file = os.path.join(self.data_dir, "license.dat")'
            )
            fixes_applied.append("Fixed license file path")
        
        # Fix 3: Replace "data/onepos.db" with dynamic path
        if '"data/onepos.db"' in content:
            content = content.replace(
                'db_path="data/onepos.db"',
                'db_path=None'
            )
            fixes_applied.append("Fixed database default path")
        
        # Fix 4: Add data_dir initialization if missing
        if 'self.data_dir =' not in content and 'license_manager.py' in file_path:
            # Add data_dir initialization after super().__init__()
            content = content.replace(
                'super().__init__()',
                '''super().__init__()
        
        # Use user's Documents directory for license data
        user_docs = os.path.expanduser("~/Documents")
        self.data_dir = os.path.join(user_docs, "OnePos")'''
            )
            fixes_applied.append("Added data_dir initialization")
        
        # Write back if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"   ✅ تم إصلاح الملف")
            for fix in fixes_applied:
                print(f"      - {fix}")
        else:
            print(f"   ℹ️ الملف لا يحتاج إصلاح")
            
    except Exception as e:
        print(f"   ❌ خطأ في إصلاح الملف: {e}")

def test_fixes():
    """Test the fixes"""
    print("\n🧪 اختبار الإصلاحات...")
    
    try:
        # Test database
        from models.database import Database
        db = Database()
        print(f"   ✅ قاعدة البيانات: {db.db_path}")
        db.connection.close()
        
        # Test license manager
        from utils.license_manager import LicenseManager
        lm = LicenseManager()
        print(f"   ✅ مدير الترخيص: {lm.license_file}")
        
        print("   🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"   ❌ فشل الاختبار: {e}")
        return False

def create_build_script():
    """Create a build script with the fixes"""
    script_content = '''@echo off
echo Building OnePos with permission fixes...

echo Cleaning previous builds...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

echo Building application...
pyinstaller --onedir --windowed --icon=app_icon.ico --name=OnePos main.py --clean

if exist "dist\\OnePos\\OnePos.exe" (
    echo Build successful!
    echo Executable: dist\\OnePos\\OnePos.exe
) else (
    echo Build failed!
    pause
    exit /b 1
)

echo Building installer...
if exist "C:\\Program Files (x86)\\Inno Setup 6\\ISCC.exe" (
    "C:\\Program Files (x86)\\Inno Setup 6\\ISCC.exe" "onepos_simple.iss"
    echo Installer build completed!
) else (
    echo Inno Setup not found. Install from: https://jrsoftware.org/isdl.php
)

pause
'''
    
    with open('build_fixed.bat', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("📝 تم إنشاء سكريبت البناء المحدث: build_fixed.bat")

if __name__ == "__main__":
    print("🚀 بدء الإصلاح الشامل لمشاكل الصلاحيات")
    print("=" * 60)
    
    # Apply fixes
    fix_all_data_paths()
    
    # Test fixes
    if test_fixes():
        print("\n✅ تم الإصلاح بنجاح!")
        
        # Create build script
        create_build_script()
        
        print("\n🎯 الخطوات التالية:")
        print("1. شغل: build_fixed.bat")
        print("2. اختبر التطبيق الجديد")
        print("3. ثبت النسخة المحدثة")
        
    else:
        print("\n❌ فشل الإصلاح - تحقق من الأخطاء أعلاه")
    
    print("\n" + "=" * 60)
