#!/usr/bin/env python3
"""
اختبار الثيم الجميل الجديد
Test Beautiful Theme
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from utils.beautiful_theme import get_beautiful_theme_stylesheet
from views.main_window import MainWindow
from models.user import User
import time

def test_beautiful_theme():
    """اختبار الثيم الجميل الجديد"""
    print("🎨 اختبار الثيم الجميل الجديد")
    print("=" * 40)
    
    try:
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # تطبيق الثيم الجميل
        app.setStyleSheet(get_beautiful_theme_stylesheet())
        print("✅ تم تطبيق الثيم الجميل")
        
        # الحصول على مستخدم للاختبار
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow(admin_user)
        
        print("🖼️ عرض النافذة مع الثيم الجديد...")
        main_window.show()
        
        # معالجة الأحداث
        app.processEvents()
        
        print("\n🎨 ميزات الثيم الجديد:")
        print("   ✨ ألوان متدرجة جميلة")
        print("   🌈 قائمة جانبية بألوان البنفسجي والأزرق")
        print("   🔵 أزرار بألوان متنوعة حسب الوظيفة:")
        print("      - أخضر للإضافة والنجاح")
        print("      - أحمر للحذف والخطر")
        print("      - برتقالي للتحذير والانتظار")
        print("      - أزرق للعمليات العادية")
        print("   📝 حقول إدخال أنيقة مع تأثيرات التركيز")
        print("   📊 جداول جميلة مع ألوان متناوبة")
        print("   🎯 تسميات واضحة بألوان متباينة")
        print("   ✨ تأثيرات hover وانتقالات سلسة")
        
        print("\n🔍 فحص العناصر:")
        
        # فحص القائمة الجانبية
        if hasattr(main_window, 'nav_buttons'):
            print(f"   ✅ القائمة الجانبية: {len(main_window.nav_buttons)} أزرار")
        
        # فحص الثيم
        stylesheet = app.styleSheet()
        if "qlineargradient" in stylesheet:
            print("   ✅ الألوان المتدرجة مطبقة")
        
        if "#667eea" in stylesheet:
            print("   ✅ ألوان القائمة الجانبية مطبقة")
        
        if "success" in stylesheet:
            print("   ✅ ألوان الأزرار المتخصصة مطبقة")
        
        print("\n🎉 الثيم الجديد جاهز!")
        print("   يمكنك الآن رؤية التطبيق بألوان جميلة ومتناسقة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الثيم: {e}")
        return False

if __name__ == "__main__":
    success = test_beautiful_theme()
    if success:
        print("\n✨ تم تطبيق الثيم الجميل بنجاح!")
    else:
        print("\n❌ فشل في تطبيق الثيم الجميل")
