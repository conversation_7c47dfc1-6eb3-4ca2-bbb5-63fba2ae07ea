#!/usr/bin/env python3
"""
اختبار ترجمات إعدادات الطابعات المحسنة - OnePos
Test Enhanced Printer Settings Translations
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from views.main_window import MainWindow
from models.user import User
from utils.translator import translator, tr

def test_printer_settings_ui_translations():
    """اختبار ترجمات واجهة إعدادات الطابعات"""
    print("🖨️ اختبار ترجمات واجهة إعدادات الطابعات")
    print("=" * 50)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        if not admin_user:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        main_window = MainWindow(admin_user)
        
        # الانتقال لقسم الإعدادات
        main_window.load_module('settings')
        app.processEvents()
        
        # البحث عن واجهة الإعدادات
        settings_widget = None
        for i in range(main_window.content_frame.count()):
            widget = main_window.content_frame.widget(i)
            if hasattr(widget, 'tabs'):
                settings_widget = widget
                break
        
        if not settings_widget:
            print("❌ لم يتم العثور على واجهة الإعدادات")
            return False
        
        # البحث عن تبويب الطابعات والتبديل إليه
        printer_tab_index = -1
        for i in range(settings_widget.tabs.count()):
            tab_text = settings_widget.tabs.tabText(i)
            if 'طابعات' in tab_text or 'Printer' in tab_text:
                printer_tab_index = i
                break
        
        if printer_tab_index == -1:
            print("❌ لم يتم العثور على تبويب الطابعات")
            return False
        
        settings_widget.tabs.setCurrentIndex(printer_tab_index)
        app.processEvents()
        
        printer_widget = settings_widget.tabs.currentWidget()
        
        languages = [
            ('ar', 'العربية'),
            ('en', 'English'),
            ('fr', 'Français')
        ]
        
        expected_translations = {
            'ar': {
                'title': 'إعدادات الطابعات',
                'available_printers': 'الطابعات المتاحة',
                'refresh_printers': 'تحديث الطابعات',
                'save_settings': 'حفظ الإعدادات',
                'printer_settings': 'تخصيص الطابعات'
            },
            'en': {
                'title': 'Printer Settings',
                'available_printers': 'Available Printers',
                'refresh_printers': 'Refresh Printers',
                'save_settings': 'Save Settings',
                'printer_settings': 'Printer Configuration'
            },
            'fr': {
                'title': 'Paramètres d\'Imprimante',
                'available_printers': 'Imprimantes Disponibles',
                'refresh_printers': 'Actualiser les Imprimantes',
                'save_settings': 'Enregistrer les Paramètres',
                'printer_settings': 'Configuration d\'Imprimante'
            }
        }
        
        all_correct = True
        
        for lang_code, lang_name in languages:
            print(f"\n📝 اختبار اللغة: {lang_name} ({lang_code})")
            
            # تغيير اللغة
            translator.set_language(lang_code)
            
            # تحديث الترجمات
            if hasattr(printer_widget, 'retranslate_ui'):
                printer_widget.retranslate_ui()
            
            app.processEvents()
            
            # فحص العنوان الرئيسي
            if hasattr(printer_widget, 'title_label'):
                actual_title = printer_widget.title_label.text()
                expected_title = "🖨️ " + expected_translations[lang_code]['title']
                
                if expected_title in actual_title:
                    print(f"   ✅ العنوان: {actual_title}")
                else:
                    print(f"   ❌ العنوان: متوقع '{expected_title}', الحالي '{actual_title}'")
                    all_correct = False
            
            # فحص أزرار التحكم
            if hasattr(printer_widget, 'refresh_button'):
                refresh_text = printer_widget.refresh_button.text()
                expected_refresh = "🔄 " + expected_translations[lang_code]['refresh_printers']
                
                if expected_refresh in refresh_text:
                    print(f"   ✅ زر التحديث: {refresh_text}")
                else:
                    print(f"   ❌ زر التحديث: متوقع '{expected_refresh}', الحالي '{refresh_text}'")
                    all_correct = False
            
            if hasattr(printer_widget, 'save_button'):
                save_text = printer_widget.save_button.text()
                expected_save = "💾 " + expected_translations[lang_code]['save_settings']
                
                if expected_save in save_text:
                    print(f"   ✅ زر الحفظ: {save_text}")
                else:
                    print(f"   ❌ زر الحفظ: متوقع '{expected_save}', الحالي '{save_text}'")
                    all_correct = False
            
            # فحص عناوين المجموعات
            if hasattr(printer_widget, 'printers_group'):
                group_title = printer_widget.printers_group.title()
                expected_group = "📋 " + expected_translations[lang_code]['available_printers']
                
                if expected_group in group_title:
                    print(f"   ✅ مجموعة الطابعات: {group_title}")
                else:
                    print(f"   ❌ مجموعة الطابعات: متوقع '{expected_group}', الحالي '{group_title}'")
                    all_correct = False
            
            # فحص رؤوس الجدول
            if hasattr(printer_widget, 'printers_table'):
                table = printer_widget.printers_table
                if table.columnCount() >= 4:
                    header_texts = []
                    for col in range(4):
                        header_texts.append(table.horizontalHeaderItem(col).text())
                    
                    print(f"   📋 رؤوس الجدول: {', '.join(header_texts)}")
                    
                    # التحقق من وجود الترجمات الصحيحة
                    expected_headers = [
                        expected_translations[lang_code].get('printer_name', 'Printer Name'),
                        'Type', 'Status', 'Description'  # هذه قد تكون مترجمة أيضاً
                    ]
                    
                    # على الأقل العنوان الأول يجب أن يكون مترجم
                    if any(tr(f"printers.printer_name") in header for header in header_texts):
                        print(f"   ✅ رؤوس الجدول مترجمة")
                    else:
                        print(f"   ⚠️ رؤوس الجدول قد تحتاج ترجمة أفضل")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ترجمات واجهة الطابعات: {e}")
        return False

def test_dynamic_translation_update():
    """اختبار التحديث الديناميكي للترجمات"""
    print("\n🔄 اختبار التحديث الديناميكي للترجمات")
    print("=" * 45)
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        admin_user = User.authenticate('admin', 'admin123')
        main_window = MainWindow(admin_user)
        
        # الانتقال لقسم الإعدادات وتبويب الطابعات
        main_window.load_module('settings')
        app.processEvents()
        
        settings_widget = None
        for i in range(main_window.content_frame.count()):
            widget = main_window.content_frame.widget(i)
            if hasattr(widget, 'tabs'):
                settings_widget = widget
                break
        
        # البحث عن تبويب الطابعات
        for i in range(settings_widget.tabs.count()):
            tab_text = settings_widget.tabs.tabText(i)
            if 'طابعات' in tab_text or 'Printer' in tab_text:
                settings_widget.tabs.setCurrentIndex(i)
                break
        
        app.processEvents()
        printer_widget = settings_widget.tabs.currentWidget()
        
        # اختبار التبديل السريع بين اللغات
        languages = ['ar', 'en', 'fr', 'ar']  # العودة للعربية
        
        for lang in languages:
            print(f"🔄 تغيير اللغة إلى: {lang}")
            
            # تغيير اللغة
            translator.set_language(lang)
            
            # تحديث الترجمات
            if hasattr(printer_widget, 'retranslate_ui'):
                printer_widget.retranslate_ui()
            
            app.processEvents()
            
            # فحص سريع للعنوان
            if hasattr(printer_widget, 'title_label'):
                title = printer_widget.title_label.text()
                print(f"   📝 العنوان الحالي: {title}")
                
                # التحقق من وجود النص المترجم
                if lang == 'ar' and 'إعدادات الطابعات' in title:
                    print("   ✅ الترجمة العربية تعمل")
                elif lang == 'en' and 'Printer Settings' in title:
                    print("   ✅ الترجمة الإنجليزية تعمل")
                elif lang == 'fr' and 'Paramètres' in title:
                    print("   ✅ الترجمة الفرنسية تعمل")
                else:
                    print("   ⚠️ الترجمة قد تحتاج تحسين")
        
        print("✅ اختبار التحديث الديناميكي مكتمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحديث الديناميكي: {e}")
        return False

def run_printer_settings_translations_test():
    """تشغيل جميع اختبارات ترجمات إعدادات الطابعات"""
    print("🌍 بدء اختبار ترجمات إعدادات الطابعات المحسنة")
    print("=" * 70)
    
    results = []
    
    # اختبار 1: ترجمات واجهة إعدادات الطابعات
    results.append(("ترجمات واجهة الطابعات", test_printer_settings_ui_translations()))
    
    # اختبار 2: التحديث الديناميكي للترجمات
    results.append(("التحديث الديناميكي", test_dynamic_translation_update()))
    
    # النتائج النهائية
    print("\n" + "=" * 70)
    print("📊 نتائج اختبار ترجمات إعدادات الطابعات")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print("=" * 70)
    print(f"📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 جميع ترجمات إعدادات الطابعات تعمل بشكل مثالي!")
        print("✨ التحديث الديناميكي للترجمات يعمل بنجاح")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة المشاكل أعلاه.")
    
    return passed == total

if __name__ == "__main__":
    run_printer_settings_translations_test()
