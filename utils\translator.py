"""
Translation utilities for OnePos POS System
Multi-language support for Arabic, French, and English
"""

import json
import os
from typing import Dict, Any
from utils.config_manager import config


class Translator:
    """Translation manager for multi-language support"""

    def __init__(self):
        self.translations = {}
        self.current_language = config.get_language()
        self.fallback_language = 'en'
        self.registered_widgets = []  # List of widgets to notify on language change
        self.load_translations()
    
    def load_translations(self):
        """Load translation files"""
        translations_dir = "translations"
        
        # Supported languages
        languages = ['ar', 'fr', 'en']
        
        for lang in languages:
            translation_file = os.path.join(translations_dir, f"{lang}.json")
            
            try:
                if os.path.exists(translation_file):
                    with open(translation_file, 'r', encoding='utf-8') as f:
                        self.translations[lang] = json.load(f)
                else:
                    # Create default translation file if it doesn't exist
                    self.create_default_translation(lang)
                    
            except Exception as e:
                print(f"Error loading translation file {translation_file}: {e}")
                self.translations[lang] = {}
    
    def create_default_translation(self, language):
        """Create default translation file"""
        translations_dir = "translations"
        os.makedirs(translations_dir, exist_ok=True)
        
        # Default translations
        default_translations = self.get_default_translations(language)
        
        translation_file = os.path.join(translations_dir, f"{language}.json")
        
        try:
            with open(translation_file, 'w', encoding='utf-8') as f:
                json.dump(default_translations, f, indent=4, ensure_ascii=False)
            
            self.translations[language] = default_translations
            
        except Exception as e:
            print(f"Error creating translation file {translation_file}: {e}")
            self.translations[language] = {}
    
    def get_default_translations(self, language):
        """Get default translations for a language"""
        
        if language == 'ar':
            return {
                "app": {
                    "name": "ونبوس",
                    "title": "نظام نقاط البيع",
                    "version": "الإصدار"
                },
                "common": {
                    "ok": "موافق",
                    "cancel": "إلغاء",
                    "save": "حفظ",
                    "delete": "حذف",
                    "edit": "تعديل",
                    "add": "إضافة",
                    "search": "بحث",
                    "close": "إغلاق",
                    "yes": "نعم",
                    "no": "لا",
                    "loading": "جاري التحميل...",
                    "error": "خطأ",
                    "success": "نجح",
                    "warning": "تحذير",
                    "info": "معلومات"
                },
                "login": {
                    "title": "تسجيل الدخول",
                    "username": "اسم المستخدم",
                    "password": "كلمة المرور",
                    "remember_me": "تذكرني",
                    "login": "دخول",
                    "invalid_credentials": "اسم المستخدم أو كلمة المرور غير صحيحة"
                },
                "navigation": {
                    "pos": "نقطة البيع",
                    "products": "المنتجات",
                    "sales": "المبيعات",
                    "customers": "العملاء",
                    "purchases": "المشتريات",
                    "users": "المستخدمون",
                    "reports": "التقارير",
                    "settings": "الإعدادات"
                },
                "pos": {
                    "title": "نقطة البيع",
                    "barcode": "الباركود",
                    "product": "المنتج",
                    "quantity": "الكمية",
                    "price": "السعر",
                    "total": "المجموع",
                    "subtotal": "المجموع الفرعي",
                    "tax": "الضريبة",
                    "discount": "الخصم",
                    "grand_total": "المجموع الكلي",
                    "cash": "نقدي",
                    "card": "بطاقة",
                    "payment": "الدفع",
                    "change": "الباقي",
                    "print_receipt": "طباعة الفاتورة"
                },
                "products": {
                    "title": "إدارة المنتجات",
                    "name": "اسم المنتج",
                    "description": "الوصف",
                    "category": "الفئة",
                    "cost_price": "سعر التكلفة",
                    "selling_price": "سعر البيع",
                    "stock": "المخزون",
                    "min_stock": "الحد الأدنى للمخزون",
                    "unit": "الوحدة",
                    "add_product": "إضافة منتج",
                    "edit_product": "تعديل منتج"
                },
                "customers": {
                    "title": "إدارة العملاء",
                    "name": "الاسم",
                    "phone": "الهاتف",
                    "email": "البريد الإلكتروني",
                    "address": "العنوان",
                    "add_customer": "إضافة عميل",
                    "edit_customer": "تعديل عميل"
                }
            }
        
        elif language == 'fr':
            return {
                "app": {
                    "name": "OnePos",
                    "title": "Système de Point de Vente",
                    "version": "Version"
                },
                "common": {
                    "ok": "OK",
                    "cancel": "Annuler",
                    "save": "Enregistrer",
                    "delete": "Supprimer",
                    "edit": "Modifier",
                    "add": "Ajouter",
                    "search": "Rechercher",
                    "close": "Fermer",
                    "yes": "Oui",
                    "no": "Non",
                    "loading": "Chargement...",
                    "error": "Erreur",
                    "success": "Succès",
                    "warning": "Avertissement",
                    "info": "Information"
                },
                "login": {
                    "title": "Connexion",
                    "username": "Nom d'utilisateur",
                    "password": "Mot de passe",
                    "remember_me": "Se souvenir de moi",
                    "login": "Connexion",
                    "invalid_credentials": "Nom d'utilisateur ou mot de passe invalide"
                },
                "navigation": {
                    "pos": "Point de Vente",
                    "products": "Produits",
                    "sales": "Ventes",
                    "customers": "Clients",
                    "purchases": "Achats",
                    "users": "Utilisateurs",
                    "reports": "Rapports",
                    "settings": "Paramètres"
                },
                "pos": {
                    "title": "Point de Vente",
                    "barcode": "Code-barres",
                    "product": "Produit",
                    "quantity": "Quantité",
                    "price": "Prix",
                    "total": "Total",
                    "subtotal": "Sous-total",
                    "tax": "Taxe",
                    "discount": "Remise",
                    "grand_total": "Total général",
                    "cash": "Espèces",
                    "card": "Carte",
                    "payment": "Paiement",
                    "change": "Monnaie",
                    "print_receipt": "Imprimer reçu"
                },
                "products": {
                    "title": "Gestion des Produits",
                    "name": "Nom du produit",
                    "description": "Description",
                    "category": "Catégorie",
                    "cost_price": "Prix de revient",
                    "selling_price": "Prix de vente",
                    "stock": "Stock",
                    "min_stock": "Stock minimum",
                    "unit": "Unité",
                    "add_product": "Ajouter produit",
                    "edit_product": "Modifier produit"
                },
                "customers": {
                    "title": "Gestion des Clients",
                    "name": "Nom",
                    "phone": "Téléphone",
                    "email": "Email",
                    "address": "Adresse",
                    "add_customer": "Ajouter client",
                    "edit_customer": "Modifier client"
                }
            }
        
        else:  # English (default)
            return {
                "app": {
                    "name": "OnePos",
                    "title": "Point of Sale System",
                    "version": "Version"
                },
                "common": {
                    "ok": "OK",
                    "cancel": "Cancel",
                    "save": "Save",
                    "delete": "Delete",
                    "edit": "Edit",
                    "add": "Add",
                    "search": "Search",
                    "close": "Close",
                    "yes": "Yes",
                    "no": "No",
                    "loading": "Loading...",
                    "error": "Error",
                    "success": "Success",
                    "warning": "Warning",
                    "info": "Information"
                },
                "login": {
                    "title": "Login",
                    "username": "Username",
                    "password": "Password",
                    "remember_me": "Remember me",
                    "login": "Login",
                    "invalid_credentials": "Invalid username or password"
                },
                "navigation": {
                    "pos": "Point of Sale",
                    "products": "Products",
                    "sales": "Sales",
                    "customers": "Customers",
                    "purchases": "Purchases",
                    "users": "Users",
                    "reports": "Reports",
                    "settings": "Settings"
                },
                "pos": {
                    "title": "Point of Sale",
                    "barcode": "Barcode",
                    "product": "Product",
                    "quantity": "Quantity",
                    "price": "Price",
                    "total": "Total",
                    "subtotal": "Subtotal",
                    "tax": "Tax",
                    "discount": "Discount",
                    "grand_total": "Grand Total",
                    "cash": "Cash",
                    "card": "Card",
                    "payment": "Payment",
                    "change": "Change",
                    "print_receipt": "Print Receipt"
                },
                "products": {
                    "title": "Product Management",
                    "name": "Product Name",
                    "description": "Description",
                    "category": "Category",
                    "cost_price": "Cost Price",
                    "selling_price": "Selling Price",
                    "stock": "Stock",
                    "min_stock": "Min Stock",
                    "unit": "Unit",
                    "add_product": "Add Product",
                    "edit_product": "Edit Product"
                },
                "customers": {
                    "title": "Customer Management",
                    "name": "Name",
                    "phone": "Phone",
                    "email": "Email",
                    "address": "Address",
                    "add_customer": "Add Customer",
                    "edit_customer": "Edit Customer"
                }
            }
    
    def translate(self, key: str, **kwargs) -> str:
        """Translate a key to current language"""
        # Get translation from current language
        translation = self._get_translation(key, self.current_language)
        
        # Fallback to English if not found
        if translation == key and self.current_language != self.fallback_language:
            translation = self._get_translation(key, self.fallback_language)
        
        # Format with kwargs if provided
        if kwargs:
            try:
                translation = translation.format(**kwargs)
            except (KeyError, ValueError):
                pass
        
        return translation
    
    def _get_translation(self, key: str, language: str) -> str:
        """Get translation for specific language"""
        if language not in self.translations:
            return key
        
        keys = key.split('.')
        value = self.translations[language]
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return key
    
    def set_language(self, language: str):
        """Set current language and notify all widgets"""
        if language in ['ar', 'fr', 'en']:
            old_language = self.current_language
            self.current_language = language
            config.set_language(language)

            # Notify all registered widgets about language change
            self._notify_language_change(old_language, language)
    
    def get_current_language(self) -> str:
        """Get current language"""
        return self.current_language
    
    def get_available_languages(self) -> Dict[str, str]:
        """Get available languages"""
        return {
            'ar': 'العربية',
            'fr': 'Français',
            'en': 'English'
        }
    
    def is_rtl(self) -> bool:
        """Check if current language is right-to-left"""
        return self.current_language == 'ar'

    def register_widget(self, widget):
        """Register a widget to receive language change notifications"""
        # Clean up dead references first
        self._cleanup_dead_references()

        # Use weak references to avoid memory leaks
        import weakref

        # Check if widget is already registered
        for ref in self.registered_widgets:
            if ref() is widget:
                return

        # Add weak reference
        self.registered_widgets.append(weakref.ref(widget))

    def unregister_widget(self, widget):
        """Unregister a widget from language change notifications"""
        import weakref

        # Find and remove the widget reference
        to_remove = []
        for ref in self.registered_widgets:
            if ref() is widget:
                to_remove.append(ref)

        for ref in to_remove:
            self.registered_widgets.remove(ref)

    def _notify_language_change(self, old_language: str, new_language: str):
        """Notify all registered widgets about language change"""
        # Clean up dead references first
        self._cleanup_dead_references()

        for widget_ref in self.registered_widgets[:]:  # Copy list to avoid modification during iteration
            widget = widget_ref()
            if widget is None:
                continue

            try:
                if hasattr(widget, 'on_language_changed'):
                    widget.on_language_changed(old_language, new_language)
                elif hasattr(widget, 'retranslate_ui'):
                    widget.retranslate_ui()
                elif hasattr(widget, 'update_translations'):
                    widget.update_translations()
            except Exception as e:
                print(f"Error notifying widget about language change: {e}")

    def _cleanup_dead_references(self):
        """Clean up dead widget references"""
        self.registered_widgets = [ref for ref in self.registered_widgets if ref() is not None]

    def force_refresh_all(self):
        """Force refresh all registered widgets"""
        self._notify_language_change(self.current_language, self.current_language)


# Global translator instance
translator = Translator()

# Convenience functions
def tr(key: str, **kwargs) -> str:
    """Translate a key (convenience function)"""
    return translator.translate(key, **kwargs)

def register_for_translation(widget):
    """Register a widget for automatic translation updates"""
    translator.register_widget(widget)

def unregister_from_translation(widget):
    """Unregister a widget from automatic translation updates"""
    translator.unregister_widget(widget)

def change_language(language: str):
    """Change language and update all registered widgets"""
    translator.set_language(language)

def get_current_language() -> str:
    """Get current language code"""
    return translator.get_current_language()

def is_rtl() -> bool:
    """Check if current language is right-to-left"""
    return translator.is_rtl()
