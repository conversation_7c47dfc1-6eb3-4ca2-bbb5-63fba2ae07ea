#!/usr/bin/env python3
"""
أداة إدارة أكواد الترخيص
License Management Tool
"""

import sys
import os
from datetime import datetime

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.license_database import license_db


def main_menu():
    """القائمة الرئيسية"""
    while True:
        print("\n" + "="*60)
        print("🔐 أداة إدارة أكواد الترخيص OnePos")
        print("="*60)
        print("1. 📊 عرض الإحصائيات")
        print("2. 🔍 البحث عن كود")
        print("3. 📋 عرض التفعيلات")
        print("4. 📁 تصدير الأكواد")
        print("5. 🔑 توليد أكواد جديدة")
        print("6. ❌ إلغاء تفعيل كود")
        print("7. 📈 تقرير مفصل")
        print("0. 🚪 خروج")
        print("-"*60)
        
        choice = input("اختر رقم العملية: ").strip()
        
        if choice == "1":
            show_statistics()
        elif choice == "2":
            search_code()
        elif choice == "3":
            show_activations()
        elif choice == "4":
            export_codes()
        elif choice == "5":
            generate_new_codes()
        elif choice == "6":
            deactivate_code()
        elif choice == "7":
            detailed_report()
        elif choice == "0":
            print("👋 وداعاً!")
            break
        else:
            print("❌ اختيار غير صحيح!")


def show_statistics():
    """عرض الإحصائيات"""
    print("\n📊 إحصائيات الترخيص:")
    print("-"*40)
    
    stats = license_db.get_statistics()
    print(f"📦 إجمالي الأكواد: {stats['total_codes']:,}")
    print(f"✅ الأكواد المفعلة: {stats['activated_codes']:,}")
    print(f"🆓 الأكواد المتاحة: {stats['available_codes']:,}")
    print(f"🔄 إجمالي التفعيلات: {stats['total_activations']:,}")
    print(f"📈 معدل التفعيل: {stats['activation_rate']:.1f}%")


def search_code():
    """البحث عن كود"""
    code = input("\n🔍 أدخل كود السيريال للبحث: ").strip()
    
    if not code:
        print("❌ يرجى إدخال كود صحيح")
        return
    
    import sqlite3
    conn = sqlite3.connect("data/licenses.db")
    cursor = conn.cursor()
    
    # البحث في الأكواد
    cursor.execute('''
        SELECT serial_code, license_type, validity_days, created_date, expiry_date, is_active
        FROM license_codes 
        WHERE serial_code LIKE ?
    ''', (f'%{code}%',))
    
    license_info = cursor.fetchone()
    
    if license_info:
        print(f"\n📋 معلومات الكود:")
        print(f"🔑 الكود: {license_info[0]}")
        print(f"📝 النوع: {license_info[1]}")
        print(f"⏰ مدة الصلاحية: {license_info[2]} يوم")
        print(f"📅 تاريخ الإنشاء: {license_info[3]}")
        print(f"⏳ تاريخ انتهاء الصلاحية: {license_info[4]}")
        print(f"🔄 الحالة: {'نشط' if license_info[5] else 'معطل'}")
        
        # البحث في التفعيلات
        cursor.execute('''
            SELECT machine_id, ip_address, computer_name, activation_date, is_active
            FROM activations 
            WHERE serial_code = ?
        ''', (license_info[0],))
        
        activations = cursor.fetchall()
        
        if activations:
            print(f"\n🖥️ التفعيلات ({len(activations)}):")
            for i, activation in enumerate(activations, 1):
                status = "نشط" if activation[4] else "معطل"
                print(f"  {i}. الجهاز: {activation[0]}")
                print(f"     IP: {activation[1] or 'غير محدد'}")
                print(f"     اسم الكمبيوتر: {activation[2] or 'غير محدد'}")
                print(f"     تاريخ التفعيل: {activation[3]}")
                print(f"     الحالة: {status}")
        else:
            print("\n🔍 لم يتم تفعيل هذا الكود بعد")
    else:
        print("❌ لم يتم العثور على الكود")
    
    conn.close()


def show_activations():
    """عرض التفعيلات الحديثة"""
    import sqlite3
    conn = sqlite3.connect("data/licenses.db")
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT a.serial_code, a.machine_id, a.ip_address, a.computer_name, 
               a.activation_date, l.license_type
        FROM activations a
        JOIN license_codes l ON a.serial_code = l.serial_code
        WHERE a.is_active = 1
        ORDER BY a.activation_date DESC
        LIMIT 20
    ''')
    
    activations = cursor.fetchall()
    
    print(f"\n🔄 آخر {len(activations)} تفعيل:")
    print("-"*80)
    
    for i, activation in enumerate(activations, 1):
        print(f"{i:2d}. {activation[0][:20]}... | {activation[1]} | {activation[2] or 'N/A'}")
        print(f"    {activation[3] or 'Unknown'} | {activation[4]} | {activation[5]}")
        print()
    
    conn.close()


def export_codes():
    """تصدير الأكواد"""
    print("\n📁 تصدير الأكواد:")
    print("1. ملف نصي")
    print("2. ملف CSV")
    print("3. ملف JSON")
    
    choice = input("اختر نوع التصدير: ").strip()
    
    if choice == "1":
        count = license_db.export_codes_to_file("exported_codes.txt")
        print(f"✅ تم تصدير {count} كود إلى: exported_codes.txt")
    elif choice == "2":
        # تصدير CSV
        import sqlite3
        import csv
        
        conn = sqlite3.connect("data/licenses.db")
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT serial_code, license_type, validity_days, created_date, expiry_date
            FROM license_codes 
            WHERE is_active = 1
        ''')
        
        codes = cursor.fetchall()
        
        with open("exported_codes.csv", 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Serial Code', 'License Type', 'Validity Days', 'Created Date', 'Expiry Date'])
            writer.writerows(codes)
        
        conn.close()
        print(f"✅ تم تصدير {len(codes)} كود إلى: exported_codes.csv")
    elif choice == "3":
        print("📄 تصدير JSON قيد التطوير...")
    else:
        print("❌ اختيار غير صحيح")


def generate_new_codes():
    """توليد أكواد جديدة"""
    try:
        count = int(input("\n🔢 عدد الأكواد المطلوب توليدها: "))
        if count <= 0 or count > 10000:
            print("❌ العدد يجب أن يكون بين 1 و 10000")
            return
        
        print(f"🔄 توليد {count} كود...")
        generated_codes = license_db.generate_bulk_licenses(count)
        print(f"✅ تم توليد {len(generated_codes)} كود بنجاح!")
        
        # عرض عينة
        print("\n🔑 عينة من الأكواد:")
        for i, code in enumerate(generated_codes[:5]):
            print(f"  {i+1}. {code}")
        
        if len(generated_codes) > 5:
            print(f"  ... و {len(generated_codes) - 5} كود آخر")
            
    except ValueError:
        print("❌ يرجى إدخال رقم صحيح")


def deactivate_code():
    """إلغاء تفعيل كود"""
    code = input("\n❌ أدخل كود السيريال لإلغاء تفعيله: ").strip()
    
    if not code:
        print("❌ يرجى إدخال كود صحيح")
        return
    
    import sqlite3
    conn = sqlite3.connect("data/licenses.db")
    cursor = conn.cursor()
    
    # إلغاء تفعيل الكود
    cursor.execute('''
        UPDATE license_codes 
        SET is_active = 0 
        WHERE serial_code = ?
    ''', (code,))
    
    # إلغاء تفعيل جميع التفعيلات
    cursor.execute('''
        UPDATE activations 
        SET is_active = 0 
        WHERE serial_code = ?
    ''', (code,))
    
    if cursor.rowcount > 0:
        conn.commit()
        print(f"✅ تم إلغاء تفعيل الكود: {code}")
    else:
        print("❌ لم يتم العثور على الكود")
    
    conn.close()


def detailed_report():
    """تقرير مفصل"""
    import sqlite3
    conn = sqlite3.connect("data/licenses.db")
    cursor = conn.cursor()
    
    print("\n📈 تقرير مفصل:")
    print("="*60)
    
    # إحصائيات عامة
    stats = license_db.get_statistics()
    print(f"📊 الإحصائيات العامة:")
    print(f"   • إجمالي الأكواد: {stats['total_codes']:,}")
    print(f"   • الأكواد المفعلة: {stats['activated_codes']:,}")
    print(f"   • الأكواد المتاحة: {stats['available_codes']:,}")
    print(f"   • معدل التفعيل: {stats['activation_rate']:.1f}%")
    
    # التفعيلات حسب التاريخ
    cursor.execute('''
        SELECT DATE(activation_date) as date, COUNT(*) as count
        FROM activations 
        WHERE is_active = 1
        GROUP BY DATE(activation_date)
        ORDER BY date DESC
        LIMIT 7
    ''')
    
    daily_activations = cursor.fetchall()
    
    if daily_activations:
        print(f"\n📅 التفعيلات اليومية (آخر 7 أيام):")
        for date, count in daily_activations:
            print(f"   • {date}: {count} تفعيل")
    
    conn.close()


if __name__ == "__main__":
    try:
        main_menu()
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء البرنامج")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
