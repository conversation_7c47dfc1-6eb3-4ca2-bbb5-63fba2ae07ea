"""
Label styling utilities for OnePos POS System
Ensures all labels are dark black for maximum readability
"""

def get_dark_label_style():
    """Get dark label style for maximum visibility"""
    return "color: #000000 !important; font-weight: 700; font-size: 11px;"

def get_input_label_style():
    """Get style for input field labels"""
    return "color: #000000 !important; font-weight: 700; font-size: 10px;"

def get_section_label_style():
    """Get style for section headers"""
    return "color: #000000 !important; font-weight: 800; font-size: 12px;"

def apply_dark_label_style(label):
    """Apply dark style to a QLabel widget"""
    label.setStyleSheet(get_dark_label_style())

def apply_input_label_style(label):
    """Apply input label style to a QLabel widget"""
    label.setStyleSheet(get_input_label_style())

def apply_section_label_style(label):
    """Apply section label style to a QLabel widget"""
    label.setStyleSheet(get_section_label_style())
