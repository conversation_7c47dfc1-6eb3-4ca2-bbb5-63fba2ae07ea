"""
نظام ألوان جميل وجذاب لتطبيق OnePos
Beautiful and Attractive Color Theme for OnePos
"""

def get_beautiful_theme_stylesheet():
    """الحصول على نظام ألوان جميل ومتناسق"""
    return """
    /* ========== الألوان الأساسية ========== */
    QMainWindow {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
            stop:0 #f8f9fa, stop:1 #e9ecef);
        color: #2c3e50;
    }

    QWidget {
        background-color: transparent;
        color: #2c3e50;
        font-family: 'Segoe UI', Arial, sans-serif;
        font-weight: 500;
    }



    /* أزرار القائمة الجانبية */
    QPushButton[objectName*="nav_"] {
        background: transparent;
        color: #ffffff;
        text-align: left;
        padding: 12px 20px;
        border: none;
        border-radius: 8px;
        margin: 2px 8px;
        font-weight: 600;
        font-size: 13px;
    }

    QPushButton[objectName*="nav_"]:hover {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 rgba(255,255,255,0.2), stop:1 rgba(255,255,255,0.1));
        color: #ffffff;
        transform: translateX(5px);
    }

    QPushButton[objectName*="nav_"]:checked {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 #ff6b6b, stop:1 #ee5a24);
        color: #ffffff;
        font-weight: bold;
        border-left: 4px solid #ffffff;
    }

    /* ========== الشريط العلوي الجميل ========== */
    QFrame[objectName="top_navigation_frame"] {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 #667eea, stop:0.5 #764ba2, stop:1 #667eea);
        border: none;
        border-bottom: 3px solid #ffffff;
    }

    /* ========== الأزرار الجميلة ========== */
    QPushButton {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #74b9ff, stop:1 #0984e3);
        color: #ffffff;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 600;
        font-size: 12px;
        min-height: 35px;
    }

    QPushButton:hover {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #81ecec, stop:1 #00b894);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    QPushButton:pressed {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #636e72, stop:1 #2d3436);
        transform: translateY(0px);
    }

    /* أزرار خاصة بألوان مختلفة */
    QPushButton[objectName*="success"], QPushButton[objectName*="add"] {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #55efc4, stop:1 #00b894);
    }

    QPushButton[objectName*="success"]:hover, QPushButton[objectName*="add"]:hover {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #81ecec, stop:1 #00cec9);
    }

    QPushButton[objectName*="danger"], QPushButton[objectName*="delete"], QPushButton[objectName*="clear"] {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #ff7675, stop:1 #e17055);
    }

    QPushButton[objectName*="danger"]:hover, QPushButton[objectName*="delete"]:hover, QPushButton[objectName*="clear"]:hover {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #fd79a8, stop:1 #e84393);
    }

    QPushButton[objectName*="warning"], QPushButton[objectName*="hold"] {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #fdcb6e, stop:1 #f39c12);
    }

    QPushButton[objectName*="warning"]:hover, QPushButton[objectName*="hold"]:hover {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #ffeaa7, stop:1 #fdcb6e);
    }

    /* ========== حقول الإدخال الجميلة ========== */
    QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #ffffff, stop:1 #f8f9fa);
        border: 2px solid #ddd;
        border-radius: 8px;
        padding: 8px 12px;
        color: #2c3e50;
        font-size: 13px;
        font-weight: 500;
        selection-background-color: #74b9ff;
    }

    QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
        border: 2px solid #74b9ff;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #ffffff, stop:1 #f1f2f6);
        box-shadow: 0 0 10px rgba(116, 185, 255, 0.3);
    }

    /* ========== القوائم المنسدلة ========== */
    QComboBox {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #ffffff, stop:1 #f8f9fa);
        border: 2px solid #ddd;
        border-radius: 8px;
        padding: 8px 12px;
        color: #2c3e50;
        font-size: 13px;
        font-weight: 500;
        min-height: 25px;
    }

    QComboBox:focus {
        border: 2px solid #74b9ff;
        box-shadow: 0 0 10px rgba(116, 185, 255, 0.3);
    }

    QComboBox::drop-down {
        border: none;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #74b9ff, stop:1 #0984e3);
        border-radius: 6px;
        width: 30px;
    }

    QComboBox::down-arrow {
        image: none;
        border: 2px solid #ffffff;
        border-top: none;
        border-right: none;
        width: 8px;
        height: 8px;
        transform: rotate(45deg);
    }

    /* ========== الجداول الجميلة ========== */
    QTableWidget {
        background-color: #ffffff;
        alternate-background-color: #f8f9fa;
        gridline-color: #e9ecef;
        border: 2px solid #ddd;
        border-radius: 10px;
        color: #2c3e50;
        font-weight: 500;
        selection-background-color: #74b9ff;
        selection-color: #ffffff;
    }

    QTableWidget::item {
        padding: 8px;
        border-bottom: 1px solid #e9ecef;
        color: #2c3e50;
        font-weight: 500;
    }

    QTableWidget::item:selected {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 #74b9ff, stop:1 #0984e3);
        color: #ffffff;
        font-weight: 600;
    }

    QHeaderView::section {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #667eea, stop:1 #764ba2);
        color: #ffffff;
        padding: 10px;
        border: none;
        font-weight: bold;
        font-size: 12px;
    }

    /* ========== التسميات والنصوص ========== */
    QLabel {
        color: #2c3e50;
        font-weight: 600;
        font-size: 13px;
    }

    QLabel[objectName*="title"] {
        color: #667eea;
        font-weight: bold;
        font-size: 16px;
    }

    QLabel[objectName*="total"] {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 #667eea, stop:1 #764ba2);
        color: #ffffff;
        padding: 15px;
        border-radius: 10px;
        font-weight: bold;
        font-size: 18px;
        border: 3px solid #ffffff;
    }

    /* ========== المجموعات والإطارات ========== */
    QGroupBox {
        color: #2c3e50;
        font-weight: bold;
        font-size: 14px;
        border: 2px solid #ddd;
        border-radius: 10px;
        margin-top: 15px;
        padding-top: 10px;
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #ffffff, stop:1 #f8f9fa);
    }

    QGroupBox::title {
        subcontrol-origin: margin;
        left: 15px;
        padding: 5px 10px;
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 #667eea, stop:1 #764ba2);
        color: #ffffff;
        border-radius: 5px;
        font-weight: bold;
    }

    /* ========== شريط الحالة ========== */
    QStatusBar {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 #667eea, stop:1 #764ba2);
        color: #ffffff;
        font-weight: 600;
        padding: 5px;
    }

    /* ========== شريط القوائم ========== */
    QMenuBar {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 #667eea, stop:1 #764ba2);
        color: #ffffff;
        font-weight: 600;
        padding: 5px;
    }

    QMenuBar::item {
        background: transparent;
        padding: 8px 15px;
        border-radius: 5px;
    }

    QMenuBar::item:selected {
        background: rgba(255,255,255,0.2);
    }

    /* ========== مربعات الاختيار ========== */
    QCheckBox {
        color: #2c3e50;
        font-weight: 600;
        spacing: 8px;
    }

    QCheckBox::indicator {
        width: 18px;
        height: 18px;
        border: 2px solid #ddd;
        border-radius: 4px;
        background: #ffffff;
    }

    QCheckBox::indicator:checked {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
            stop:0 #74b9ff, stop:1 #0984e3);
        border: 2px solid #0984e3;
    }

    /* ========== أشرطة التقدم ========== */
    QProgressBar {
        border: none;
        border-radius: 8px;
        background: #e9ecef;
        text-align: center;
        color: #2c3e50;
        font-weight: bold;
    }

    QProgressBar::chunk {
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
            stop:0 #74b9ff, stop:1 #0984e3);
        border-radius: 8px;
    }

    /* ========== التبويبات ========== */
    QTabWidget::pane {
        border: 2px solid #ddd;
        border-radius: 10px;
        background: #ffffff;
    }

    QTabBar::tab {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #f8f9fa, stop:1 #e9ecef);
        color: #2c3e50;
        padding: 10px 20px;
        margin-right: 2px;
        border-radius: 8px 8px 0 0;
        font-weight: 600;
    }

    QTabBar::tab:selected {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #667eea, stop:1 #764ba2);
        color: #ffffff;
        font-weight: bold;
    }

    QTabBar::tab:hover {
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
            stop:0 #81ecec, stop:1 #74b9ff);
        color: #ffffff;
    }
    """
