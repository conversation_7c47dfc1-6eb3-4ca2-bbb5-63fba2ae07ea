"""
Enhanced Print Manager for OnePos POS System
Professional receipt and invoice printing with customizable templates
"""

from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from PyQt5.QtWidgets import QApplication, QTextEdit, QVBoxLayout, QDialog, QPushButton, QHBoxLayout
from PyQt5.QtCore import QRect, QDate, QTime, Qt
from PyQt5.QtGui import QPainter, QFont, QPixmap, QTextDocument, QTextCursor
from datetime import datetime
import os
from utils.config_manager import config
from utils.translator import tr
from utils.printer_manager import printer_manager


class PrintManager:
    """Enhanced print manager with professional templates"""
    
    def __init__(self):
        self.printer = None
        self._printer_initialized = False
    
    def _ensure_printer_initialized(self):
        """Ensure printer is initialized"""
        if not self._printer_initialized:
            self.printer = QPrinter(QPrinter.HighResolution)
            self.printer.setPageSize(QPrinter.A4)
            self.setup_printer()
            self._printer_initialized = True

    def setup_printer(self):
        """Setup printer with default settings"""
        if self.printer is None:
            return

        # Set margins (in millimeters)
        self.printer.setPageMargins(10, 10, 10, 10, QPrinter.Millimeter)

        # Set print quality
        self.printer.setResolution(300)  # 300 DPI

        # Set color mode
        self.printer.setColorMode(QPrinter.Color)
    
    def print_receipt(self, sale_data, preview=True):
        """Print a professional receipt using thermal printer if available"""
        self._ensure_printer_initialized()

        # استخدام طابعة الإيصالات المحددة
        receipt_printer = printer_manager.get_receipt_printer()
        if receipt_printer:
            self.printer.setPrinterName(receipt_printer)
            print(f"🖨️ استخدام طابعة الإيصالات: {receipt_printer}")

        html_content = self.generate_receipt_html(sale_data)

        if preview:
            self.show_print_preview(html_content, "🧾 معاينة الإيصال المبسط")
        else:
            self.print_html_content(html_content)
    
    def print_invoice(self, sale_data, preview=True):
        """Print a professional invoice using standard printer"""
        self._ensure_printer_initialized()

        # استخدام طابعة الفواتير المحددة
        invoice_printer = printer_manager.get_invoice_printer()
        if invoice_printer:
            self.printer.setPrinterName(invoice_printer)
            print(f"🖨️ استخدام طابعة الفواتير: {invoice_printer}")

        html_content = self.generate_invoice_html(sale_data)

        if preview:
            self.show_print_preview(html_content, "📄 معاينة الفاتورة الاحترافية")
        else:
            self.print_html_content(html_content)
    
    def generate_receipt_html(self, sale_data):
        """Generate HTML content for receipt"""
        company_info = self.get_company_info()
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{
                    font-family: 'Arial', sans-serif;
                    margin: 0;
                    padding: 20px;
                    font-size: 12px;
                    line-height: 1.4;
                    color: #000;
                }}
                .receipt-container {{
                    max-width: 300px;
                    margin: 0 auto;
                    border: 2px solid #000;
                    padding: 15px;
                }}
                .header {{
                    text-align: center;
                    border-bottom: 2px solid #000;
                    padding-bottom: 10px;
                    margin-bottom: 15px;
                }}
                .company-name {{
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 5px;
                }}
                .company-details {{
                    font-size: 10px;
                    color: #666;
                }}
                .receipt-title {{
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                    margin: 15px 0;
                    text-decoration: underline;
                }}
                .sale-info {{
                    margin-bottom: 15px;
                    font-size: 11px;
                }}
                .sale-info div {{
                    margin-bottom: 3px;
                }}
                .items-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 15px;
                }}
                .items-table th,
                .items-table td {{
                    padding: 5px 2px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                    font-size: 10px;
                }}
                .items-table th {{
                    font-weight: bold;
                    border-bottom: 2px solid #000;
                }}
                .qty-col {{ width: 15%; text-align: center; }}
                .price-col {{ width: 20%; text-align: right; }}
                .total-col {{ width: 25%; text-align: right; }}
                .totals {{
                    border-top: 2px solid #000;
                    padding-top: 10px;
                    margin-top: 10px;
                }}
                .total-row {{
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 5px;
                    font-size: 11px;
                }}
                .grand-total {{
                    font-weight: bold;
                    font-size: 14px;
                    border-top: 1px solid #000;
                    padding-top: 5px;
                    margin-top: 5px;
                }}
                .payment-info {{
                    margin-top: 15px;
                    padding-top: 10px;
                    border-top: 1px dashed #000;
                    font-size: 11px;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 20px;
                    padding-top: 10px;
                    border-top: 1px dashed #000;
                    font-size: 10px;
                    color: #666;
                }}
                .thank-you {{
                    font-weight: bold;
                    margin-bottom: 10px;
                }}
            </style>
        </head>
        <body>
            <div class="receipt-container">
                <!-- Header -->
                <div class="header">
                    <div class="company-name">{company_info['name']}</div>
                    <div class="company-details">
                        {company_info['address']}<br>
                        {tr('common.phone')}: {company_info['phone']}<br>
                        {tr('common.email')}: {company_info['email']}
                    </div>
                </div>
                
                <!-- Receipt Title -->
                <div class="receipt-title">{tr('pos.receipt')}</div>
                
                <!-- Sale Information -->
                <div class="sale-info">
                    <div><strong>{tr('sales.invoice_number')}:</strong> {sale_data.get('invoice_number', 'N/A')}</div>
                    <div><strong>{tr('sales.date')}:</strong> {sale_data.get('date', datetime.now().strftime('%Y-%m-%d'))}</div>
                    <div><strong>{tr('sales.time')}:</strong> {sale_data.get('time', datetime.now().strftime('%H:%M:%S'))}</div>
                    <div><strong>{tr('sales.cashier')}:</strong> {sale_data.get('cashier', 'Admin')}</div>
                    <div><strong>{tr('customers.customer')}:</strong> {sale_data.get('customer', tr('customers.walk_in'))}</div>
                </div>
                
                <!-- Items Table -->
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>{tr('products.name')}</th>
                            <th class="qty-col">{tr('pos.quantity')}</th>
                            <th class="price-col">{tr('pos.price')}</th>
                            <th class="total-col">{tr('pos.total')}</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        # Add items
        for item in sale_data.get('items', []):
            html += f"""
                        <tr>
                            <td>{item.get('name', 'Unknown')}</td>
                            <td class="qty-col">{item.get('quantity', 0)}</td>
                            <td class="price-col">{item.get('unit_price', 0):.2f}</td>
                            <td class="total-col">{item.get('total_price', 0):.2f}</td>
                        </tr>
            """
        
        # Add totals
        html += f"""
                    </tbody>
                </table>
                
                <!-- Totals -->
                <div class="totals">
                    <div class="total-row">
                        <span>{tr('pos.subtotal')}:</span>
                        <span>{sale_data.get('subtotal', 0):.2f} {config.get_currency()}</span>
                    </div>
                    <div class="total-row">
                        <span>{tr('pos.tax')} ({sale_data.get('tax_rate', 0):.1f}%):</span>
                        <span>{sale_data.get('tax_amount', 0):.2f} {config.get_currency()}</span>
                    </div>
                    <div class="total-row">
                        <span>{tr('pos.discount')}:</span>
                        <span>-{sale_data.get('discount_amount', 0):.2f} {config.get_currency()}</span>
                    </div>
                    <div class="total-row grand-total">
                        <span>{tr('pos.grand_total')}:</span>
                        <span>{sale_data.get('total_amount', 0):.2f} {config.get_currency()}</span>
                    </div>
                </div>
                
                <!-- Payment Information -->
                <div class="payment-info">
                    <div class="total-row">
                        <span>{tr('pos.payment')}:</span>
                        <span>{sale_data.get('payment_method', 'Cash').title()}</span>
                    </div>
                    <div class="total-row">
                        <span>{tr('pos.paid')}:</span>
                        <span>{sale_data.get('paid_amount', 0):.2f} {config.get_currency()}</span>
                    </div>
                    <div class="total-row">
                        <span>{tr('pos.change')}:</span>
                        <span>{sale_data.get('change_amount', 0):.2f} {config.get_currency()}</span>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="footer">
                    <div class="thank-you">{tr('messages.thank_you')}</div>
                    <div>{tr('messages.visit_again')}</div>
                    <div style="margin-top: 10px;">
                        {tr('common.powered_by')} OnePos POS System
                    </div>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def generate_invoice_html(self, sale_data):
        """Generate HTML content for professional invoice"""
        company_info = self.get_company_info()
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{
                    font-family: 'Arial', sans-serif;
                    margin: 0;
                    padding: 30px;
                    font-size: 12px;
                    line-height: 1.6;
                    color: #000;
                }}
                .invoice-container {{
                    max-width: 800px;
                    margin: 0 auto;
                    border: 1px solid #ddd;
                    padding: 30px;
                }}
                .invoice-header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 3px solid #0066cc;
                }}
                .company-info {{
                    flex: 1;
                }}
                .company-name {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #0066cc;
                    margin-bottom: 10px;
                }}
                .company-details {{
                    font-size: 11px;
                    color: #666;
                    line-height: 1.4;
                }}
                .invoice-title {{
                    flex: 1;
                    text-align: right;
                }}
                .invoice-title h1 {{
                    font-size: 36px;
                    color: #0066cc;
                    margin: 0;
                    font-weight: bold;
                }}
                .invoice-number {{
                    font-size: 14px;
                    color: #666;
                    margin-top: 5px;
                }}
                .invoice-details {{
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 30px;
                }}
                .bill-to, .invoice-info {{
                    flex: 1;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 5px;
                }}
                .bill-to {{
                    margin-right: 15px;
                }}
                .invoice-info {{
                    margin-left: 15px;
                }}
                .section-title {{
                    font-weight: bold;
                    font-size: 14px;
                    color: #0066cc;
                    margin-bottom: 10px;
                    border-bottom: 1px solid #0066cc;
                    padding-bottom: 5px;
                }}
                .items-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 30px;
                    border: 1px solid #ddd;
                }}
                .items-table th {{
                    background-color: #0066cc;
                    color: white;
                    padding: 12px 8px;
                    text-align: left;
                    font-weight: bold;
                    font-size: 11px;
                }}
                .items-table td {{
                    padding: 10px 8px;
                    border-bottom: 1px solid #eee;
                    font-size: 11px;
                }}
                .items-table tr:nth-child(even) {{
                    background-color: #f8f9fa;
                }}
                .text-center {{ text-align: center; }}
                .text-right {{ text-align: right; }}
                .totals-section {{
                    float: right;
                    width: 300px;
                    margin-top: 20px;
                }}
                .totals-table {{
                    width: 100%;
                    border-collapse: collapse;
                }}
                .totals-table td {{
                    padding: 8px 12px;
                    border-bottom: 1px solid #eee;
                }}
                .totals-table .label {{
                    font-weight: bold;
                    text-align: right;
                    background-color: #f8f9fa;
                }}
                .totals-table .amount {{
                    text-align: right;
                    font-weight: bold;
                }}
                .grand-total {{
                    background-color: #0066cc !important;
                    color: white !important;
                    font-size: 14px !important;
                }}
                .payment-terms {{
                    clear: both;
                    margin-top: 40px;
                    padding: 20px;
                    background-color: #f8f9fa;
                    border-left: 4px solid #0066cc;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                    font-size: 10px;
                    color: #666;
                }}
            </style>
        </head>
        <body>
            <div class="invoice-container">
                <!-- Header -->
                <div class="invoice-header">
                    <div class="company-info">
                        <div class="company-name">{company_info['name']}</div>
                        <div class="company-details">
                            {company_info['address']}<br>
                            {tr('common.phone')}: {company_info['phone']}<br>
                            {tr('common.email')}: {company_info['email']}<br>
                            {tr('settings.company_tax_number')}: {company_info.get('tax_number', 'N/A')}
                        </div>
                    </div>
                    <div class="invoice-title">
                        <h1>{tr('sales.invoice')}</h1>
                        <div class="invoice-number">#{sale_data.get('invoice_number', 'N/A')}</div>
                    </div>
                </div>
                
                <!-- Invoice Details -->
                <div class="invoice-details">
                    <div class="bill-to">
                        <div class="section-title">{tr('sales.bill_to')}</div>
                        <div><strong>{sale_data.get('customer', tr('customers.walk_in'))}</strong></div>
                        <div>{sale_data.get('customer_phone', '')}</div>
                        <div>{sale_data.get('customer_address', '')}</div>
                        <div>{sale_data.get('customer_tax_number', '')}</div>
                    </div>
                    <div class="invoice-info">
                        <div class="section-title">{tr('sales.invoice_details')}</div>
                        <div><strong>{tr('sales.date')}:</strong> {sale_data.get('date', datetime.now().strftime('%Y-%m-%d'))}</div>
                        <div><strong>{tr('sales.time')}:</strong> {sale_data.get('time', datetime.now().strftime('%H:%M:%S'))}</div>
                        <div><strong>{tr('sales.cashier')}:</strong> {sale_data.get('cashier', 'Admin')}</div>
                        <div><strong>{tr('pos.payment')}:</strong> {sale_data.get('payment_method', 'Cash').title()}</div>
                    </div>
                </div>
                
                <!-- Items Table -->
                <table class="items-table">
                    <thead>
                        <tr>
                            <th style="width: 5%;">#</th>
                            <th style="width: 45%;">{tr('products.name')}</th>
                            <th style="width: 15%;" class="text-center">{tr('pos.quantity')}</th>
                            <th style="width: 15%;" class="text-right">{tr('pos.price')}</th>
                            <th style="width: 20%;" class="text-right">{tr('pos.total')}</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        # Add items
        for index, item in enumerate(sale_data.get('items', []), 1):
            html += f"""
                        <tr>
                            <td class="text-center">{index}</td>
                            <td>
                                <strong>{item.get('name', 'Unknown')}</strong><br>
                                <small>{item.get('sku', '')}</small>
                            </td>
                            <td class="text-center">{item.get('quantity', 0)}</td>
                            <td class="text-right">{item.get('unit_price', 0):.2f}</td>
                            <td class="text-right">{item.get('total_price', 0):.2f}</td>
                        </tr>
            """
        
        # Add totals
        html += f"""
                    </tbody>
                </table>
                
                <!-- Totals -->
                <div class="totals-section">
                    <table class="totals-table">
                        <tr>
                            <td class="label">{tr('pos.subtotal')}:</td>
                            <td class="amount">{sale_data.get('subtotal', 0):.2f} {config.get_currency()}</td>
                        </tr>
                        <tr>
                            <td class="label">{tr('pos.tax')} ({sale_data.get('tax_rate', 0):.1f}%):</td>
                            <td class="amount">{sale_data.get('tax_amount', 0):.2f} {config.get_currency()}</td>
                        </tr>
                        <tr>
                            <td class="label">{tr('pos.discount')}:</td>
                            <td class="amount">-{sale_data.get('discount_amount', 0):.2f} {config.get_currency()}</td>
                        </tr>
                        <tr class="grand-total">
                            <td class="label">{tr('pos.grand_total')}:</td>
                            <td class="amount">{sale_data.get('total_amount', 0):.2f} {config.get_currency()}</td>
                        </tr>
                    </table>
                </div>
                
                <!-- Payment Terms -->
                <div class="payment-terms">
                    <div class="section-title">{tr('sales.payment_info')}</div>
                    <div><strong>{tr('pos.payment')}:</strong> {sale_data.get('payment_method', 'Cash').title()}</div>
                    <div><strong>{tr('pos.paid')}:</strong> {sale_data.get('paid_amount', 0):.2f} {config.get_currency()}</div>
                    <div><strong>{tr('pos.change')}:</strong> {sale_data.get('change_amount', 0):.2f} {config.get_currency()}</div>
                </div>
                
                <!-- Footer -->
                <div class="footer">
                    <div><strong>{tr('messages.thank_you')}</strong></div>
                    <div>{tr('messages.visit_again')}</div>
                    <div style="margin-top: 10px;">
                        {tr('common.powered_by')} OnePos POS System | {tr('common.printed_on')}: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                    </div>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def get_company_info(self):
        """Get company information from config"""
        return {
            'name': config.get('company.name', 'OnePos Store'),
            'address': config.get('company.address', '123 Main Street, City, Country'),
            'phone': config.get('company.phone', '+1234567890'),
            'email': config.get('company.email', '<EMAIL>'),
            'tax_number': config.get('company.tax_number', 'TAX123456789'),
            'logo_path': config.get('company.logo_path', '')
        }
    
    def show_print_preview(self, html_content, title="Print Preview"):
        """Show print preview dialog"""
        preview_dialog = PrintPreviewDialog(html_content, title)
        preview_dialog.exec_()
    
    def print_html_content(self, html_content):
        """Print HTML content directly"""
        self._ensure_printer_initialized()
        document = QTextDocument()
        document.setHtml(html_content)
        document.print_(self.printer)

    def show_printer_dialog(self):
        """Show printer selection dialog"""
        self._ensure_printer_initialized()
        dialog = QPrintDialog(self.printer)
        if dialog.exec_() == QPrintDialog.Accepted:
            return True
        return False


class PrintPreviewDialog(QDialog):
    """Print preview dialog with HTML content"""
    
    def __init__(self, html_content, title="Print Preview"):
        super().__init__()
        self.html_content = html_content
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(800, 900)
        self.init_ui()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # Preview area
        self.preview_area = QTextEdit()
        self.preview_area.setHtml(self.html_content)
        self.preview_area.setReadOnly(True)
        layout.addWidget(self.preview_area)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.print_button = QPushButton("🖨️ " + tr("common.print"))
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
        """)
        self.print_button.clicked.connect(self.print_document)
        button_layout.addWidget(self.print_button)
        
        self.close_button = QPushButton("❌ " + tr("common.close"))
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: #ffffff;
                font-weight: bold;
                font-size: 12px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def print_document(self):
        """Print the document"""
        printer = QPrinter(QPrinter.HighResolution)
        printer.setPageSize(QPrinter.A4)
        
        dialog = QPrintDialog(printer, self)
        if dialog.exec_() == QPrintDialog.Accepted:
            document = QTextDocument()
            document.setHtml(self.html_content)
            document.print_(printer)
            self.close()


# Global print manager instance
print_manager = PrintManager()
